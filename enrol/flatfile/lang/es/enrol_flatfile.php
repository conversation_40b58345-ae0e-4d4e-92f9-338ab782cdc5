<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'enrol_flatfile', language 'en'.
 *
 * @package    enrol_flatfile
 * @copyright  1999 onwards <PERSON>  {@link http://moodle.com}
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

 $string['encoding'] = 'Codificación del archivo';
 $string['expiredaction'] = 'Acción al expirar la matrícula';
 $string['expiredaction_help'] = 'Seleccione la acción a realizar cuando expire la matrícula del usuario. Tenga en cuenta que algunos datos y configuraciones del usuario se eliminan del curso durante la baja de matrícula.';
 $string['filelockedmail'] = 'El archivo de texto que está utilizando para matrículas basadas en archivos ({$a}) no puede ser eliminado por el proceso cron. Esto generalmente significa que los permisos son incorrectos. Por favor, corrija los permisos para que Moodle pueda eliminar el archivo, de lo contrario podría ser procesado repetidamente.';
 $string['filelockedmailsubject'] = 'Error importante: Archivo de matrícula';
 $string['flatfile:manage'] = 'Gestionar matrículas de usuarios manualmente';
 $string['flatfile:unenrol'] = 'Dar de baja usuarios del curso manualmente';
 $string['flatfileenrolments'] = 'Matrículas de archivo plano (CSV)';
 $string['flatfilesync'] = 'Sincronización de matrículas de archivo plano';
 $string['location'] = 'Ubicación del archivo';
 $string['location_desc'] = 'Especifique la ruta completa al archivo de matrícula. El archivo se elimina automáticamente después del procesamiento.';
 $string['notifyadmin'] = 'Notificar al administrador';
 $string['notifyenrolled'] = 'Notificar a usuarios matriculados';
 $string['notifyenroller'] = 'Notificar al usuario responsable de las matrículas';
 $string['messageprovider:flatfile_enrolment'] = 'Mensajes de matrícula de archivo plano';
 $string['mapping'] = 'Mapeo de roles de archivo plano';
 $string['pluginname'] = 'Archivo plano (CSV)';
 $string['pluginname_desc'] = 'Este método verificará y procesará repetidamente un archivo de texto con formato especial en la ubicación que especifique.
 El archivo es un archivo separado por comas que se supone tiene cuatro o seis campos por línea:
 
     operación, rol, número de identificación de usuario, número de identificación de curso [, tiempo de inicio [, tiempo de finalización]]
 
 donde:
 
 * operación - add | del
 * rol - student | teacher | teacheredit
 * número de identificación de usuario - idnumber en la tabla de usuarios NB no id
 * número de identificación de curso - idnumber en la tabla de cursos NB no id
 * tiempo de inicio - tiempo de inicio (en segundos desde epoch) - opcional
 * tiempo de finalización - tiempo de finalización (en segundos desde epoch) - opcional
 
 Podría verse algo así:
 <pre class="informationbox">
    add, student, 5, CF101
    add, teacher, 6, CF101
    add, teacheredit, 7, CF101
    del, student, 8, CF101
    del, student, 17, CF101
    add, student, 21, CF101, 1091115000, 1091215000
 </pre>';
 $string['privacy:metadata:enrol_flatfile'] = 'El plugin de matrícula de archivo plano (CSV) puede almacenar datos personales relacionados con futuras matrículas en la tabla enrol_flatfile.';
 $string['privacy:metadata:enrol_flatfile:action'] = 'La acción de matrícula esperada en la fecha dada';
 $string['privacy:metadata:enrol_flatfile:courseid'] = 'El ID del curso al que se relaciona la matrícula';
 $string['privacy:metadata:enrol_flatfile:roleid'] = 'El ID del rol a asignar o desasignar';
 $string['privacy:metadata:enrol_flatfile:timestart'] = 'El tiempo cuando comienza la matrícula';
 $string['privacy:metadata:enrol_flatfile:timeend'] = 'El tiempo cuando finaliza la matrícula';
 $string['privacy:metadata:enrol_flatfile:timemodified'] = 'El tiempo cuando se modifica la matrícula';
 $string['privacy:metadata:enrol_flatfile:userid'] = 'El ID del usuario al que se relaciona la asignación de rol';
