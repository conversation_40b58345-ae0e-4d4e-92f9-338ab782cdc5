<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * @package    enrol_attributes
 * <AUTHOR> <Nicolas.<PERSON>nan<PERSON>@unil.ch>
 * @copyright  2012-2015 Université de Lausanne (@link http://www.unil.ch}
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

 $string['pluginname'] = 'Matrícula por campos de perfil de usuario';
 $string['defaultrole'] = 'Rol predeterminado';
 $string['defaultrole_desc'] = 'Rol predeterminado utilizado para matricular personas con este plugin (cada instancia puede anular esto).';
 $string['attrsyntax'] = 'Reglas de campos de perfil de usuario';
 $string['attrsyntax_help'] = '<p>Estas reglas solo pueden usar campos de perfil de usuario personalizados.</p>';
 $string['attributes:config'] = 'Configurar instancias del plugin';
 $string['attributes:manage'] = 'Gestionar usuarios matriculados';
 $string['attributes:unenrol'] = 'Dar de baja usuarios del curso';
 $string['attributes:unenrolself'] = 'Darse de baja del curso';
 $string['ajax-error'] = 'Ocurrió un error';
 $string['ajax-okpurged'] = 'OK, las matrículas han sido purgadas';
 $string['ajax-okforced'] = 'OK, {$a} usuarios han sido matriculados';
 $string['purge'] = 'Purgar matrículas';
 $string['force'] = 'Forzar matrículas ahora';
 $string['confirmforce'] = 'Esto (re)matriculará a todos los usuarios que correspondan a esta regla.';
 $string['confirmpurge'] = 'Esto eliminará todas las matrículas correspondientes a esta regla.';
 $string['mappings'] = 'Mapeos de Shibboleth';
 $string['mappings_desc'] = 'Cuando se usa autenticación Shibboleth, este plugin puede actualizar automáticamente el perfil de un usuario en cada inicio de sesión.<br><br>Por ejemplo, si desea actualizar el campo de perfil <code>homeorganizationtype</code> del usuario con el atributo Shibboleth <code>Shib-HomeOrganizationType</code> (siempre que esa sea la variable de entorno disponible para el servidor durante el inicio de sesión), puede ingresar en una línea: <code>Shib-HomeOrganizationType:homeorganizationtype</code><br>Puede ingresar tantas líneas como necesite.<br><br>Para no usar esta función o si no usa autenticación Shibboleth, simplemente deje esto vacío.';
 $string['profilefields'] = 'Campos de perfil a usar en el selector';
 $string['profilefields_desc'] = '¿Qué campos de perfil de usuario se pueden usar al configurar una instancia de matrícula?<br><br><div class="alert alert-warning alert-block fade in" role="alert" data-aria-autofocus="true">Si no selecciona ningún atributo aquí, esto hace que el plugin sea inútil y, por lo tanto, deshabilita su uso en los cursos.</div><br>Sin embargo, la función a continuación aún puede usarse en este caso.';
 $string['removewhenexpired'] = 'Dar de baja después de la expiración de atributos';
 $string['removewhenexpired_help'] = 'Dar de baja a los usuarios al iniciar sesión si ya no coinciden con la regla de atributos.';
 $string['addcondition'] = "Agregar condición";
 $string['addgroup'] = "Agregar grupo";
 $string['deletecondition'] = "Eliminar condición";
 $string['privacy:metadata'] = 'El plugin de matrícula por campos de perfil de usuario no almacena ningún dato personal.';
 $string['defaultwhenexpired'] = 'Comportamiento predeterminado después de la expiración de atributos';
 $string['defaultwhenexpired_desc'] = 'Qué hacer con los usuarios que ya no coinciden con la regla de atributos. Esta configuración puede ser anulada en cada instancia de matrícula.';
 $string['whenexpired'] = 'Comportamiento después de la expiración de atributos';
 $string['whenexpired_help'] = 'Qué hacer con los usuarios que ya no coinciden con la regla de atributos.';
 $string['whenexpireddonothing'] = 'Dejar usuario matriculado';
 $string['whenexpiredremove'] = 'Dar de baja usuario';
 $string['whenexpiredsuspend'] = 'Suspender usuario';
 $string['observelogins'] = 'Matricular usuarios inmediatamente al iniciar sesión';
 $string['observelogins_desc'] = 'Intentar matricular usuarios inmediatamente cuando inician sesión. Esto puede tener un impacto en el rendimiento de su sitio, desactive esto si muchos usuarios inician sesión al mismo tiempo y su matriculación simultánea se convierte en un cuello de botella.';
 $string['cachedef_dbquerycache'] = 'Caché de consultas DB';
 $string['group'] = 'Asignación de grupo';
 $string['group_help'] = 'Puede asignar ninguno o múltiples grupos';
 $string['no_custom_field'] = 'Parece que no hay campos personalizados. Diríjase a <a href="{$a}" target="_blank">configuración de usuario</a> para agregar uno.';
 $string['no_profile_field_selected'] = 'No se ha seleccionado ningún campo de perfil en la configuración del plugin enrol_attributes.';
 $string['and'] = 'Y';
 $string['or'] = 'O';
 $string['invalidatecachetask'] = 'Invalidar caché para Matrícula por campos de perfil de usuario';
 $string['processrulestask'] = 'Procesar todas las reglas y matrículas para Matrícula por campos de perfil de usuario';
