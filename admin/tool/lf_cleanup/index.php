<?php

require('../../../config.php');

$select = "mimetype = :mimetype AND timecreated <= :timestamp";

$params = [
    'mimetype' => 'application/vnd.moodle.backup',
    'timestamp' => strtotime('-30 days'),
];

$fs = get_file_storage(); 
$backup_files = $DB->get_records_select('files', $select, $params);

echo "<pre>";
foreach ($backup_files as $backup_file) {
    $file = $fs->get_file_instance($backup_file);
    $real_path = $fs->get_file_system()->get_local_path_from_storedfile($file);

    print_r([
        'file' => $file,
        'real_path' => $real_path,
        'file_exists' => file_exists($real_path) ? 'true' : 'false',
        'file_size' => filesize($real_path),
    ]);
}
echo "</pre>";