/**
 * Expose the user details
 *
 * @module     tool_lfxp/user
 * @copyright  2024 Revvo
 * @license    www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

/* 
ref: referência this passada no mobile.
Arquivo acompanha outro com o ref inicializado.
*/

/* eslint-disable-next-line space-before-function-paren */
const tool_lfxp_userNS = {
    getUser: async () => {
        try {
            return ref.CoreSitesProvider.currentSite.read('tool_lfxp_get_user', {}, {});
        } catch (error) {
            /* eslint no-console: ["error", { allow: ["warn", "error"] }] */
            console.error("Error: ", error);
            return error;
        }
    }
};