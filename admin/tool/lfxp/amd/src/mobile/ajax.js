const tool_lfxp_ajaxNS = {
    unloading: false,

    /**
     * Success handler. Called when the ajax call succeeds. Checks each response and
     * resolves or rejects the deferred from that request.
     *
     * @method requestSuccess
     * @private
     * @param {Object[]} responses Array of responses containing error, exception and data attributes.
     */
    requestSuccess: function(responses) {
        var requests = this,
            exception = null,
            i = 0,
            request,
            response,
            noSessionUpdate;

        if (responses.error) {
            for (; i < requests.length; i++) {
                request = requests[i];
                request.deferred.reject(responses);
            }

            return;
        }

        for (i = 0; i < requests.length; i++) {
            request = requests[i];

            response = responses[i];
            if (typeof response !== "undefined") {
                if (response.error === false) {
                    request.deferred.resolve(response.data);
                } else {
                    exception = response.exception;
                    noSessionUpdate = requests[i].noSessionUpdate;
                    break;
                }
            } else {
                exception = new Error('missing response');
                break;
            }
        }
        if (exception !== null) {
            if (exception.errorcode === "servicerequireslogin" && !noSessionUpdate) {
                ref.CoreToastsService.show({ message: 'Login required!', type: 'danger' });
            } else {
                requests.forEach(function(request) {
                    request.deferred.reject(exception);
                });
            }
        }
    },

    /**
     * Fail handler. Called when the ajax call fails. Rejects all deferreds.
     *
     * @method requestFail
     * @private
     * @param {jqXHR} jqXHR The ajax object.
     * @param {string} textStatus The status string.
     * @param {Error|Object} exception The error thrown.
     */
    requestFail: function(jqXHR, textStatus, exception) {
        var requests = this;
        console.log(this);
        var i = 0;
        for (i = 0; i < requests.length; i++) {
            var request = requests[i];

            if (tool_lfxp_ajaxNS.unloading) {
                /* Log.error("Page unloaded.");
                Log.error(exception); */
            } else {
                request.deferred.reject(exception);
            }
        }
    },

    call: function(requests, async, loginRequired, noSessionUpdate, timeout, cachekey){
        window.addEventListener('beforeunload', function(event) {
            tool_lfxp_ajaxNS.unloading = true;
        });
        
        var ajaxRequestData = [],
            i,
            promises = [],
            methodInfo = [],
            requestInfo = '';

        var maxUrlLength = 2000;

        if (typeof loginRequired === "undefined") {
            loginRequired = true;
        }
        if (typeof async === "undefined") {
            async = true;
        }
        if (typeof timeout === 'undefined') {
            timeout = 0;
        }
        if (typeof cachekey === 'undefined') {
            cachekey = null;
        } else {
            cachekey = parseInt(cachekey);
            if (cachekey <= 0) {
                cachekey = null;
            } else if (!cachekey) {
                cachekey = null;
            }
        }

        if (typeof noSessionUpdate === "undefined") {
            noSessionUpdate = false;
        }
        for (i = 0; i < requests.length; i++) {
            var request = requests[i];
            ajaxRequestData.push({
                index: i,
                methodname: request.methodname,
                args: request.args
            });
            request.noSessionUpdate = noSessionUpdate;
            request.promise = new Promise(function(resolve, reject) {
                request.resolve = resolve;
                request.reject = reject;
            });
            promises.push(request.promise);
    
            if (typeof request.done !== "undefined") {
                request.promise.then(request.done);
            }
            if (typeof request.fail !== "undefined") {
                request.promise.catch(request.fail);
            }
            request.index = i;
            methodInfo.push(request.methodname);
        }

        if (methodInfo.length <= 5) {
            requestInfo = methodInfo.sort().join();
        } else {
            requestInfo = methodInfo.length + '-method-calls';
        }

        ajaxRequestData = JSON.stringify(ajaxRequestData);
        var settings = {
            type: 'POST',
            context: requests,
            dataType: 'json',
            processData: false,
            async: async,
            contentType: "application/json",
            timeout: timeout
        };

        var script = 'service.php';
        var url = ref.CoreSitesProvider.currentSite.publicConfig.wwwroot + '/lib/ajax/';
        if (!loginRequired) {
            script = 'service-nologin.php';
            url += script + '?info=' + requestInfo;
            if (cachekey) {
                url += '&cachekey=' + cachekey;
                settings.type = 'GET';
            }
        } else {
            url += script + '?sesskey=' + ref.CoreSitesProvider.currentSite.publicConfig.token + '&info=' + requestInfo;
        }

        if (noSessionUpdate) {
            url += '&noSessionUpdate=true';
        }

        if (settings.type === 'POST') {
            settings.data = ajaxRequestData;
        } else {
            var urlUseGet = url + '&args=' + encodeURIComponent(ajaxRequestData);

            if (urlUseGet.length > maxUrlLength) {
                settings.type = 'POST';
                settings.data = ajaxRequestData;
            } else {
                url = urlUseGet;
            }
        }

        if (async) {
            fetch(url, settings)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok ' + response.statusText);
                    }
                    return response.json();
                })
                .then(tool_lfxp_ajaxNS.requestSuccess.bind(this))
                .catch(tool_lfxp_ajaxNS.requestFail.bind(this));
        } else {
            async function fetchData() {
                try {
                    let response = await fetch(url, settings);
                    if (!response.ok) {
                        throw new Error('Network response was not ok ' + response.statusText);
                    }
                    let data = await response.json();
                    tool_lfxp_ajaxNS.requestSuccess(data);
                } catch (error) {
                    tool_lfxp_ajaxNS.requestFail(error);
                }
            }
            fetchData();
        }
        

        return promises;
    },

    /**
     * Parses the Ajax response and format
     * exceptions, if needed.
     *
     * @param {object} response
     * @returns
     */
    _singleRequestHandler: function(response){
        try {
            if(!response){
                throw new Error('Missing response');
            }

            response = JSON.parse(response);

        } catch (error) {
            throw {
                error : error.message,
                stacktrace : error.stack,
                errorcode : 'error',
                debuginfo : null,
            };
        }

        if (!response.error) {
            return response.data;
        }

        let errorcode = !response.exception ? response.errorcode : response.exception.errorcode;
        if (errorcode === "servicerequireslogin" && !noSessionUpdate) {
            ref.CoreToastsService.show({ message: 'Login required!', type: 'danger' });
        }

        if (tool_lfxp_ajaxNS.unloading) {
            return;
        }

        throw response.exception;
    },

    /**
     * Make a POST Ajax Call to an external service API
     *
     * @param {object} request {
     *      methodname : {string},
     *      args : {object},
     *      onprogress : {function}
     * }
     * @param {object} files {
     *      FILE_KEY : {Blob|File},
     *      ANOTHER_FILE_KEY : {Blob|File},
     *      ...,
     * }
     * @param {bool} noSessionUpdate Defaults to false
     * @param {int} timeout Defaults to 0
     * @returns {Promise<object>}
     *
     */
    callWithFiles: function(request, files = {}, noSessionUpdate = false, timeout = 0){
        window.addEventListener('beforeunload', function(event) {
            tool_lfxp_ajaxNS.unloading = true;
        });

        if(!request.hasOwnProperty('methodname')){
            throw new Error('Missing request.methodname!');
        }

        let formData = new FormData();
        formData.enctype = "multipart/form-data";
        formData.append('methodname', request.methodname);

        if(request.hasOwnProperty('args')){
            formData.append("args", encodeURIComponent(JSON.stringify(request.args)));
        }

        Object.keys(files).forEach(function (key) {
            if(!isNaN(key)){
                throw new Error("The files object keys must not be numeric!");
            }

            let isFile = files[key] instanceof File;
            let isBlob = files[key] instanceof Blob;

            if(!isBlob && !isFile){
                throw new Error("The files object must be populated by instances of Blob or File!");
            }

            formData.append(key, files[key]);
        });

        let settings = {
            type: 'POST',
            data : formData,
            async: true,
            contentType : false,
            processData : false,
            timeout: timeout
        };

        if(request.hasOwnProperty('onprogress')){
            settings.xhr = function(){
                let xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener("progress", request.onprogress, false);
                return xhr;
            }
        }

        let url = ref.CoreSitesProvider.currentSite.publicConfig.wwwroot + '/admin/tool/lfxp/ajax/service.php';
        url += '?sesskey=' + ref.CoreSitesProvider.currentSite.publicConfig.token;

        if (noSessionUpdate) {
            url += '&nosessionupdate=true';
        }

        return fetch(url, settings)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok ' + response.statusText);
            }
            return response.json();
        })
        .then(tool_lfxp_ajaxNS._singleRequestHandler);

    }

};