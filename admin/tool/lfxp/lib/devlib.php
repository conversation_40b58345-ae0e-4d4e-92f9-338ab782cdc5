<?php

/**
 * Debugguer for web and cli
 */
if (!function_exists('dd')){
    function dd(){
        $args = func_get_args();
        if (CLI_SCRIPT) {
            fwrite(STDOUT, PHP_EOL);
            foreach ($args as $arg) {
                fwrite(STDOUT, var_dump($arg) . PHP_EOL);
            }

            die(1);
        }

        echo "<pre>";
        call_user_func_array('var_dump', $args);
        debugging('debug trace:');
        echo "</pre>";

        die(1);
    }
}

/**
 * Returns a installed plugin's root path
 *
 * @param string $component like "tool_lfxp"
 * @throws moodle_exception
 * @return string
 */
function tool_lfxp_get_plugin_path(string $component) : string {
    $plugin_manager = core_plugin_manager::instance();
    $plugin_info = $plugin_manager->get_plugin_info($component);

    if(empty($plugin_info)){
        throw new moodle_exception("Plug-in \"$component\" not found!");
    }

    return $plugin_info->rootdir;
}

/**
 * Returns the path of a plugin's lang file.
 *
 * If it does not exist, it will be created.
 *
 * @param string $component
 * @param string $lang
 * @return void
 */
function tool_lfxp_get_plugin_lang_file_path(string $component, string $lang = 'en', ?string $plugin_path = null) : string {
    if(empty($plugin_path)){
        $plugin_path = tool_lfxp_get_plugin_path($component);
    }

    $lang_directory = "$plugin_path/lang/$lang";
    $lang_file = "$plugin_path/lang/$lang/$component.php";
    if(!file_exists($lang_file)){
        if (!is_dir($lang_directory)) {
            mkdir($lang_directory, 0775, true);
        }

        file_put_contents($lang_filepath, "<?php\n\n\$string['pluginname'] = '$component';");
    }
    return $lang_file;
}

/**
 * Extracts lang string keys from a given
 * plugin's files.
 *
 * Returns all string keys that are not present on the
 * desired plugin's lang file.
 *
 * @param string $component
 * @param string $lang
 * @param boolean $detect_exceptions
 * @return array
 */
function tool_lfxp_detect_missing_lang_strings(string $component, string $lang = 'en', bool $detect_exceptions = false) : array {
    $found_strings = [];
    $missing_strings = [];

    $plugin_path = tool_lfxp_get_plugin_path($component);

    //Lang file
    $lang_file = tool_lfxp_get_plugin_lang_file_path($component, $lang, $plugin_path);

    // Loading lang file strings
    $string = [];
    require_once($lang_file);

    // Iterating over files
    $directory_iterator = new RecursiveDirectoryIterator($plugin_path);

    $custom_filter_iterator = new class($directory_iterator) extends RecursiveFilterIterator {
        public function accept() : bool {
            $pathname = $this->getBasename();
            $forbidden_directories = ['node_modules', 'vendor', 'dist'];
            return !in_array($pathname, $forbidden_directories, true);
        }
    };

    $iterator = new RecursiveIteratorIterator(
        $custom_filter_iterator,
        RecursiveIteratorIterator::SELF_FIRST
    );

    foreach ($iterator as $file) {
        if (!$file->isFile()) {
            continue;
        }

        // if(!in_array($file->getExtension(), ['php', 'js', 'mustache'])){
        //     continue;
        // }

        $contents = file_get_contents($file->getPathname());

        $not_quotes = "[^'" .'\"]+';
        $quotes = "['" .'\"]';
        $spaces = '[\s\n\t]*';
        $key_pattern = $quotes.'('.$not_quotes.')'.$quotes;
        $component_pattern = $quotes.$component.$quotes;

        if($file->getExtension() == 'php'){
            // Get string pattern
            $pattern = "/get_string\(" . $key_pattern . "$spaces,$spaces" . $component_pattern . "/";
            // print_r($pattern);
            preg_match_all($pattern, $contents, $matches);
            foreach ($matches[1] as $string_key) {
                $found_strings[$string_key] = true;
            }

            // *xception pattern
            if($detect_exceptions){
                $exception_key_pattern = $quotes.'([^'. "\'". '\"]+)'.$quotes;
                $pattern = "/xception\(" . $spaces . $exception_key_pattern . "$spaces,$spaces" . $component_pattern . "/";
                preg_match_all($pattern, $contents, $matches);
                foreach ($matches[1] as $string_key) {
                    $found_strings[$string_key] = true;
                }
            }

            continue;
        }

        if($file->getExtension() == 'mustache'){
            $pattern = '/{{#str}}' . $spaces . "([^,{]+)" . "$spaces,$spaces" . $component . "/";
            preg_match_all($pattern, $contents, $matches);
            foreach ($matches[1] as $string_key) {
                $found_strings[$string_key] = true;
            }

            continue;
        }

        if($file->getExtension() == 'js'){
            if(preg_match('/core\/str/', $contents)){
                $pattern = '/' . $spaces . 'key' . $spaces . ':' . $spaces . $key_pattern . "$spaces,$spaces" . "component" . $spaces . ':' . $spaces . $component_pattern . '/';
                preg_match_all($pattern, $contents, $matches);
                foreach ($matches[1] as $string_key) {
                    $found_strings[$string_key] = true;
                }

                $pattern = "/get_string\(" . $spaces . $key_pattern . "$spaces,$spaces" . $component_pattern . "/";
                preg_match_all($pattern, $contents, $matches);
                foreach ($matches[1] as $string_key) {
                    $found_strings[$string_key] = true;
                }

                continue;
            }
        }
    }

    foreach ($found_strings as $string_key => $value) {
        if(isset($string[$string_key])){
            continue;
        }

        $missing_strings[] = $string_key;
    }

    return $missing_strings;
}


/**
 * Applies table prefixes and params to a SQL string
 *
 * @param string $sql
 * @param array|object $params
 * @return string
 */
function tool_lfxp_format_sql(string $sql, array|object $params) : string {
    global $DB;

    $params = (array) $params;

    $replacements = [
        "{" => $DB->prefix,
        "}" => '',
    ];

    foreach ($params as $key => $value) {
        $replacements[":$key"] = is_numeric($value) ? $value : "'$value'";
    }

    return str_replace(array_keys($replacements), array_values($replacements), $sql);
}
