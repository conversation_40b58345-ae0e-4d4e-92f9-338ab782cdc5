<?php namespace tool_lfxp\core\form;

require_once($CFG->libdir . '/formslib.php');
use \tool_lfxp\core\form\custom_quick_form;

/**
 * moodleform @since 1.7
 *      - display()
 *      MoodleQuickForm @since 1.7
 *          HTML_QuickForm_DHTMLRulesTableless @since 1.7
 *              HTML_QuickForm @since 1.7
 *              - toHtml()
 *              - &defaultRenderer() : $GLOBALS['_HTML_QuickForm_default_renderer']
 *                  HTML_Common @since 1.2.2
 *                  - abstract toHtml()
 *                  - display()
 *
 *
 * $GLOBALS['_HTML_QuickForm_default_renderer'] = new MoodleQuickForm_Renderer();
 *
 *
 * MoodleQuickForm_Renderer @since 1.7
 *      HTML_QuickForm_Renderer_Tableless @since 1.7
 *          HTML_QuickForm_Renderer_Default @since 1.7
 *              HTML_QuickForm_Renderer @since 1.7
 *
 */

abstract class customform extends \moodleform {

    /**
     * The constructor function calls the abstract function definition() and it will then
     * process and clean and attempt to validate incoming data.
     *
     * It will call your custom validate method to validate data and will also check any rules
     * you have specified in definition using addRule
     *
     * The name of the form (id attribute of the form) is automatically generated depending on
     * the name you gave the class extending moodleform. You should call your class something
     * like
     *
     * @param mixed $action the action attribute for the form. If empty defaults to auto detect the
     *              current url. If a moodle_url object then outputs params as hidden variables.
     * @param mixed $customdata if your form defintion method needs access to data such as $course
     *              $cm, etc. to construct the form definition then pass it in this array. You can
     *              use globals for somethings.
     * @param string $method if you set this to anything other than 'post' then _GET and _POST will
     *               be merged and used as incoming data to the form.
     * @param string $target target frame for form submission. You will rarely use this. Don't use
     *               it if you don't need to as the target attribute is deprecated in xhtml strict.
     * @param mixed $attributes you can pass a string of html attributes here or an array.
     *               Special attribute 'data-random-ids' will randomise generated elements ids. This
     *               is necessary when there are several forms on the same page.
     *               Special attribute 'data-double-submit-protection' set to 'off' will turn off
     *               double-submit protection JavaScript - this may be necessary if your form sends
     *               downloadable files in response to a submit button, and can't call
     *               \core_form\util::form_download_complete();
     * @param bool $editable
     * @param array $ajaxformdata Forms submitted via ajax, must pass their data here, instead of relying on _GET and _POST.
     */
    public function __construct($action=null, $customdata=null, $method='post', $target='', $attributes=null, $editable=true,
                                $ajaxformdata=null) {
        global $CFG, $FULLME;
        // no standard mform in moodle should allow autocomplete with the exception of user signup
        if (empty($attributes)) {
            $attributes = array('autocomplete'=>'off');
        } else if (is_array($attributes)) {
            $attributes['autocomplete'] = 'off';
        } else {
            if (strpos($attributes, 'autocomplete') === false) {
                $attributes .= ' autocomplete="off" ';
            }
        }


        if (empty($action)){
            // do not rely on PAGE->url here because dev often do not setup $actualurl properly in admin_externalpage_setup()
            $action = strip_querystring($FULLME);
            if (!empty($CFG->sslproxy)) {
                // return only https links when using SSL proxy
                $action = preg_replace('/^http:/', 'https:', $action, 1);
            }
            //TODO: use following instead of FULLME - see MDL-33015
            //$action = strip_querystring(qualified_me());
        }
        // Assign custom data first, so that get_form_identifier can use it.
        $this->_customdata = $customdata;
        $this->_formname = $this->get_form_identifier();
        $this->_ajaxformdata = $ajaxformdata;

        $this->_form = new custom_quick_form($this->_formname, $method, $action, $target, $attributes, $ajaxformdata);
        if (!$editable){
            $this->_form->hardFreeze();
        }

        $this->definition();

        $this->_form->addElement('hidden', 'sesskey', null); // automatic sesskey protection
        $this->_form->setType('sesskey', PARAM_RAW);
        $this->_form->setDefault('sesskey', sesskey());
        $this->_form->addElement('hidden', '_qf__'.$this->_formname, null);   // form submission marker
        $this->_form->setType('_qf__'.$this->_formname, PARAM_RAW);
        $this->_form->setDefault('_qf__'.$this->_formname, 1);
        $this->_form->_setDefaultRuleMessages();

        // Hook to inject logic after the definition was provided.
        $this->after_definition();

        // we have to know all input types before processing submission ;-)
        $this->_process_submission($method);
    }


    public function set_form_template(string $mustache){
        $this->_form->getRenderer()->set_template($mustache);
    }

    public function set_extra_data(array|object $data){
        $this->_form->getRenderer()->set_extra_data($data);
    }

    /**
     * Export the form's data to be used as context
     * for a template.
     *
     * @return array
     */
    public function export_template_data() : array {
        $this->render();
        return $this->_form->getRenderer()->export_to_template();
    }


    public function get_form_identifier_key() : string {
        return '_qf__' . $this->get_form_identifier();
    }

    /**
     * Removes the image and the submit keys from $_POST
     * as if the form was never submitted
     *
     * @return void
     */
    public function clean_this_form_submit_post_keys(){
        $form_identifier = $this->get_form_identifier_key();
        if(isset($_POST[$form_identifier])){
            unset($_POST[$form_identifier]);
        }
        unset($_POST['imagefile']);
    }
}
