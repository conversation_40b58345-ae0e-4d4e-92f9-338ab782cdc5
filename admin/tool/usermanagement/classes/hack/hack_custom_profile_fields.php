<?php

namespace tool_usermanagement\hack;

class hack_custom_profile_fields
{

    protected static bool $prepared = false;
    protected static array $modules = [];
    protected static array $arguments = [];
    protected static array $scripts = [];
    protected static array $css = [];

    public static function prepare_fields()
    {
        if (self::$prepared) {
            return;
        }

        self::prepare_profilefield_phone();
        self::prepare_profilefield_masked();
        self::prepare_profilefield_position();

        self::$prepared = true;
    }

    protected static function prepare_profilefield_phone()
    {
        global $CFG;

        $field_class_path = $CFG->dirroot . '/user/profile/field/phone/field.class.php';
        if (!file_exists($field_class_path)) {
            return;
        }

        self::$modules[] = "'profilefield_phone/phone'";
        self::$arguments[] = 'ProfilefieldPhone';
        self::$scripts[] = 'ProfilefieldPhone.initAll();';
        self::$css[] = '/user/profile/field/phone/styles/intl_tel_input.css';
    }

    protected static function prepare_profilefield_masked()
    {
        global $CFG;

        $field_class_path = $CFG->dirroot . '/user/profile/field/masked/field.class.php';
        if (!file_exists($field_class_path)) {
            return;
        }

        self::$modules[] = "'profilefield_phone/inputmask'";
        self::$arguments[] = 'ProfilefieldMask';
        self::$scripts[] = 'ProfilefieldMask.init();';
    }

    protected static function prepare_profilefield_position()
    {
        global $CFG;

        $field_class_path = $CFG->dirroot . '/user/profile/field/position/field.class.php';
        if (!file_exists($field_class_path)) {
            return;
        }

        self::$modules[] = "'profilefield_position/structure_positions'";
        self::$arguments[] = 'ProfilefieldPosition';
        self::$scripts[] = 'ProfilefieldPosition.init();';
    }

    public static function require_css()
    {
        global $PAGE;

        self::prepare_fields();

        foreach (self::$css as $path) {
            $PAGE->requires->css($path);
        }
    }

    public static function export_javascript_for_template(): object
    {
        self::prepare_fields();

        return (object) [
            'modules' => implode(', ', self::$modules),
            'arguments' => implode(', ', self::$arguments),
            'scripts' => implode("\n", self::$scripts),
        ];
    }
}
