<?php
/**
 * @package    tool
 * @subpackage usermanagement
 * @copyright  2023 Revvo
 */
 
namespace tool_usermanagement\output;

use plugin_renderer_base;
use \tool_usermanagement\hack\hack_custom_profile_fields;
use \theme_smart\output\spark_paging_bar;

class renderer extends plugin_renderer_base{
	protected object $data;

	public function __construct() {
		global $CFG;
		
        $this->data = new \stdClass();
		$this->data->wwwroot = $CFG->wwwroot;
		$this->data->sesskey = sesskey();
	}
	
	public function output(){
		global $OUTPUT, $DB, $CFG;
		
		$this->table_data();
		$this->data->avatar_placeholder = $OUTPUT->image_url("u/f3", 'core');
		$this->data->javascript = hack_custom_profile_fields::export_javascript_for_template();
		
		return $OUTPUT->render_from_template('tool_usermanagement/view', $this->data);	
	}
	
	public function table_data(){
		global $OUTPUT, $DB, $CFG;
		
		// paging parameters
		$page    = optional_param('page', 0, PARAM_INT);
		$perpage = optional_param('perpage', 30, PARAM_INT);
		
		// filters
		$params = [];
		$query_filter = "";

		if ($search = optional_param('search', '', PARAM_RAW)) {
			$params["search"] = $search;
			$query_filter .= "AND (firstname LIKE '%{$search}%' OR lastname LIKE '%{$search}%' OR email LIKE '%{$search}%')
				OR (CONCAT(firstname, ' ', lastname) LIKE '%{$search}%' AND deleted = 0)
			";
		}		
		
		// sorting
        $sort = $params["sort"] = optional_param('sort', 'firstname', PARAM_RAW);
        $dir  = $params["dir"] = optional_param('dir', 'ASC', PARAM_ALPHA);
		
        $table_columns = [
            'fullname' => "Nome/ Sobrenome",
            'email' => "E-mail",
            'city' => "Cidade",
            'country' => "País",
            'lastaccess' => "Último acesso"
        ];

		$admins = array_column(get_admins(),'id');
		sort($admins);
		$admins = implode(',',$admins);

		$query = "SELECT *,
			CONCAT(firstname, ' ', lastname) as fullname
			FROM {user} 
			WHERE 1 = 1
			AND deleted = 0 
			AND id NOT IN ({$admins})
			{$query_filter}
			ORDER BY {$sort} {$dir}
		";

		$rows = $DB->get_recordset_sql($query, null, $page*$perpage, $perpage);
		$count = $DB->count_records_sql("SELECT COUNT(u.id) FROM ({$query}) u");
		$table_rows = [];
		
		foreach($rows as $row){
			$row->userpicture = $OUTPUT->user_picture($row, array('courseid' => SITEID, 'size' => 32));
			$row->lastaccess = $row->lastaccess ? date("d/m/Y H:i:s", $row->lastaccess) : '';
			
			$row->suspend_icon = $row->suspended ? "fa-eye-slash" : "fa-eye";
			$row->suspend_text = $row->suspended ? get_string('unsuspenduser', 'admin') : get_string('suspenduser', 'admin');
			
			$table_rows[] = $row;
		}
		
		$rows->close();
		
		$this->data->current_url = new \moodle_url("/admin/tool/usermanagement/index.php", $params);
        $this->data->total_users = $count;
        $this->data->search = $search;
        $this->data->sort = $sort;
        $this->data->dir = $dir;
		$this->data->table = new \stdClass();
		$this->data->table->has_data = $count || false;
		$this->data->table->columns = [];
		$this->data->table->rows = array_values($table_rows);
		$this->data->paging_bar = $OUTPUT->render(new spark_paging_bar($count, $page, $perpage, new \moodle_url("/admin/tool/usermanagement/index.php", $params)));

		foreach ($table_columns as $field => $name) {
			$toggleSort = $dir == "ASC" ? "DESC" : "ASC";
			$sortDirection = $dir == "ASC" ? "up" : "down";

			$this->data->table->columns[] = (object)[
				"field" => $field,
				"name" => $name,
				"sort" => $sort == $field ? $toggleSort : "ASC",
				"sorted" => $sort == $field ? $sortDirection : "",
				"sorted_up" => $sort == $field && $sortDirection == "up" || false,
				"sorted_down" => $sort == $field && $sortDirection == "down" || false,
				"sortable" => true
			];
		}
		
		return true;
	}
}