<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Store management UI lang strings.
 *
 * @package    tool_log
 * @copyright  2013 Petr Skoda {@link http://skodak.org}
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['actlogshdr'] = 'Almacenes de registros disponibles';
$string['configlogplugins'] = 'Por favor, habilite todos los plugins requeridos y organícelos en el orden apropiado.';
$string['exportlog'] = 'Incluir registros al exportar';
$string['exportlogdetail'] = 'Incluir registros relacionados con el usuario al exportar.';
$string['logging'] = 'Registro';
$string['managelogging'] = 'Gestionar almacenes de registros';
$string['pluginname'] = 'Gestor de almacenes de registros';
$string['privacy:metadata:logstore'] = 'Los almacenes de registros';
$string['privacy:path:logs'] = 'Registros';
$string['privacy:request:origin:cli'] = 'Herramienta de línea de comandos';
$string['privacy:request:origin:restore'] = 'Copia de seguridad siendo restaurada';
$string['privacy:request:origin:web'] = 'Solicitud web estándar';
$string['privacy:request:origin:ws'] = 'Aplicación móvil o servicio web';
$string['reportssupported'] = 'Informes soportados';
$string['subplugintype_logstore'] = 'Almacén de registros';
$string['subplugintype_logstore_plural'] = 'Almacenes de registros';