<?php namespace tool_platformmetrics;

final class config {
    const PLUGIN_COMPONENT = 'tool_platformmetrics';

    const ENABLED_FIELD = 'enabled';

    /**
     * Return a plugin config by key
     *
     * @param string $key
     * @return mixed|null
     */
    public static function get($key, $default = null){
        try {
            return get_config(self::PLUGIN_COMPONENT, $key) ?: $default;
        } catch (\Throwable $th) {
            return $default;
        }
    }

    /**
     * Sets a value to a config
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public static function set(string $key, $value){
        set_config($key, $value, self::PLUGIN_COMPONENT);
    }

    public static function is_enabled() : bool {
        return (bool) self::get(self::ENABLED_FIELD, false);
    }

    public static function is_master_instance() : bool {
        global $CFG;

        if(empty($CFG->platformmetrics_master_instance)){
            return false;
        }

        $master = preg_replace('/^https?:\/\//', '', rtrim($CFG->platformmetrics_master_instance, '/'));
        $current = preg_replace('/^https?:\/\//', '', rtrim($CFG->wwwroot, '/'));

        return $master == $current;
    }

    public static function get_database_config() : object {
        global $CFG;

        try {
            $cfg = (object)[
                'dbtype' => $CFG->platformmetrics_dbtype,
                'dblibrary' => $CFG->platformmetrics_dblibrary ?? 'native',
                'dbhost' => $CFG->platformmetrics_dbhost,
                'dbname' => $CFG->platformmetrics_dbname,
                'dbuser' => $CFG->platformmetrics_dbuser ?? '',
                'dbpass' => $CFG->platformmetrics_dbpass ?? '',
                'prefix' => 'pm_',
                'dboptions' => $CFG->dboptions ?? [],
            ];

            // Adjusting db options
            if(isset($CFG->dbpersist)){
                $cfg->dboptions['dbpersist'] = $CFG->dbpersist;
            }

            // Adjusting dbtype
            switch ($CFG->dbtype) {
                case 'postgres7' :
                    $CFG->dbtype = 'pgsql';
                    break;

                case 'oci8po':
                    $CFG->dbtype = 'oci';
                    break;

                case 'mysql' :
                    $CFG->dbtype = 'mysqli';
                    break;
            }

            return $cfg;
            
        } catch (\Throwable $th) {
            throw new \moodle_exception('exception:invalid_database_config', self::PLUGIN_COMPONENT);
        }        
    }
}