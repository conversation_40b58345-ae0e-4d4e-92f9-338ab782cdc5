<?php namespace tool_platformmetrics\reports;

use \moodle_database;
use \Psr\Log\LoggerInterface;
use \tool_platformmetrics\helpers\date\date_helper;
use \tool_platformmetrics\helpers\date\brazil_date_helper;
use \tool_platformmetrics\loggers\mtrace_logger;
use \tool_platformmetrics\platform_info;

abstract class abstract_report implements report_interface {
    protected moodle_database $db;
    protected date_helper $date;
    protected platform_info $platform;
    protected LoggerInterface $logger;

    public function __construct(moodle_database $db){
        $this->db = $db;
        $this->date = new brazil_date_helper();
        $this->platform = new platform_info();
        $this->logger = new mtrace_logger($this->platform->get_identifier());
    }

    public function set_logger(LoggerInterface $logger) : static {
        $this->logger = $logger;
        return $this;
    }

    abstract public function get_name(): string;

    abstract public function generate();

    abstract public function generate_retroactive();

    protected function record_exists(string $table, array $conditions){
        return $this->db->record_exists($table, $conditions);
    }

    protected function insert_records(string $table, array $records){
        try {
            $transaction = $this->db->start_delegated_transaction();
            $this->db->insert_records($table, $records);
            $transaction->allow_commit();
        } catch (\Throwable $e) {
            $transaction->rollback($e);
        }
    }

    protected function update_records(string $table, array $records){
        try {
            $transaction = $this->db->start_delegated_transaction();
            
            foreach ($records as $record) {
                $this->db->update_record($table, $record);
            }

            $transaction->allow_commit();
        } catch (\Throwable $e) {
            $transaction->rollback($e);
        }
    }
}