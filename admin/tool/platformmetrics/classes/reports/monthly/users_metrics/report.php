<?php namespace tool_platformmetrics\reports\monthly\users_metrics;

use \tool_platformmetrics\reports\abstract_report;
use \tool_platformmetrics\reports\monthly_report_interface;

class report extends abstract_report implements monthly_report_interface {

    const TABLE = 'users_metrics';

    public function get_name(): string {
        return get_string('report:users_metrics', 'tool_platformmetrics');
    }

    public function enabled() : bool {
        return true;
    }

    public function generate(){
        $platform_uri = $this->platform->get_identifier();
        if(!$this->db->record_exists(self::TABLE, ['platform_uri' => $platform_uri])){
            return $this->generate_retroactive();
        }

        [$starttime, $endtime] = $this->date->get_last_month_timestamps();

        if($this->record_exists(self::TABLE, ['date_br' => $starttime, 'platform_uri' => $platform_uri])){
            return; // Already executed for this day
        }

        $record = $this->generate_record($starttime, $endtime);
        $this->db->insert_record(self::TABLE, $record);
    }

    protected function generate_record(int $starttime, int $endtime) : array {
        return [
            'platform_uri'      => $this->platform->get_identifier(),
            'date_br'           => $starttime,
            'users_count'       => $this->count_users($starttime, $endtime),
            'users_active'      => $this->count_active_users($starttime, $endtime),
            'users_created'     => $this->count_users_created($starttime, $endtime),
            'users_deleted'     => $this->count_users_deleted($starttime, $endtime),
        ];
    }

    public function generate_retroactive(){
        global $DB;
        
        // Determine the start time based on the oldest log of user_created.
        $sql = "SELECT MIN(timecreated) AS timecreated 
                FROM {logstore_standard_log} 
                WHERE eventname = '\\\\core\\\\event\\\\user_created'";
        $result = $DB->get_record_sql($sql);
        
        if (!$result || empty($result->timecreated)) {
            // Nothing to generate.
            return;
        }

        // Create a DateTime object from the earliest log timestamp
        $start_datetime = $this->date->timestamp_to_datetime($result->timecreated);

        // Determine the starting year and month.
        $start_year = (int) $start_datetime->format('Y');
        $start_month = (int) $start_datetime->format('m');

        // Retrieve timestamp ranges for each month from the starting month until last month.
        $month_timestamp_ranges = $this->date->get_month_timestamps_until_last_month($start_year, $start_month);
                
        $records = [];
        foreach ($month_timestamp_ranges as $range) {
            list($month_start, $month_end) = $range;
            $records[] = $this->generate_record($month_start, $month_end);
        }

        // Inserting
        $this->insert_records(self::TABLE, $records);
    }

    protected function count_users(int $starttime, int $endtime) : int {
        global $DB;

        $sql = "SELECT COUNT(*)
                FROM {user}
                WHERE timecreated < :endtime";

        $params = [
            'starttime' => $starttime,
            'endtime'   => $endtime,
        ];
        return $DB->count_records_sql($sql, $params);
    }

    protected function count_active_users(int $starttime, int $endtime) : int {
        global $DB;

        $sql = "SELECT COUNT(*)
                FROM {tool_activeusers_user_actvt}
                WHERE year = :year AND month = :month
                GROUP BY year, month";

        $start_datetime = $this->date->timestamp_to_datetime($starttime);

        $params = [
            'year' => $start_datetime->format('Y'),
            'month'   => $start_datetime->format('m'),
        ];
        return $DB->count_records_sql($sql, $params);
    }

    protected function count_users_created(int $starttime, int $endtime) : int {
        global $DB;

        $sql = "SELECT 
                    COUNT(*) AS instances_created
                FROM {logstore_standard_log} l
                WHERE l.eventname = '\\\\core\\\\event\\\\user_created'
                    AND l.timecreated BETWEEN :starttime AND :endtime";

        $params = ['starttime' => $starttime, 'endtime' => $endtime];
        return $DB->count_records_sql($sql, $params);
    }

    protected function count_users_deleted(int $starttime, int $endtime) : int {
        global $DB;

        $sql = "SELECT 
                    COUNT(*) AS instances_updated
                FROM {logstore_standard_log} l
                WHERE l.eventname = '\\\\core\\\\event\\\\user_deleted'
                    AND l.timecreated BETWEEN :starttime AND :endtime";

        $params = ['starttime' => $starttime, 'endtime' => $endtime];
        return $DB->count_records_sql($sql, $params);
    }
}
