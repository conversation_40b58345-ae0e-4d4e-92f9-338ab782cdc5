<?php namespace tool_platformmetrics\reports\monthly\local_trails_metrics;

use \tool_platformmetrics\reports\abstract_report;
use \tool_platformmetrics\reports\monthly_report_interface;

class report extends abstract_report implements monthly_report_interface {

    const TABLE = 'local_trails_usage';

    public function get_name(): string {
        return get_string('report:local_trails_usage', 'tool_platformmetrics');
    }

    public function enabled() : bool {
        return true;
    }

    public function generate(){
        $platform_uri = $this->platform->get_identifier();
        if(!$this->db->record_exists(self::TABLE, ['platform_uri' => $platform_uri])){
            return $this->generate_retroactive();
        }

        [$starttime, $endtime] = $this->date->get_last_month_timestamps();

        if($this->record_exists(self::TABLE, ['date_br' => $starttime, 'platform_uri' => $platform_uri])){
            return; // Already executed for this day
        }

        $record = $this->generate_record($starttime, $endtime);
        $this->db->insert_record(self::TABLE, $record);
    }

    protected function generate_record(int $starttime, int $endtime) : array {
        $instances = $this->count_instances($starttime, $endtime);

        return [
            'platform_uri'      => $this->platform->get_identifier(),
            'date_br'           => $starttime,
            'instances_count'       => $instances->total,
            'instances_published'   => $instances->published,
        ];
    }

    public function generate_retroactive(){
        global $DB;
        
        // Determine the start time based on the oldest log of user_created.
        $sql = "SELECT MIN(timecreated) AS timecreated 
                FROM {lf_trails}";
        $result = $DB->get_record_sql($sql);
        
        if (!$result || empty($result->timecreated)) {
            // Nothing to generate.
            return;
        }

        // Create a DateTime object from the earliest log timestamp
        $start_datetime = $this->date->timestamp_to_datetime($result->timecreated);

        // Determine the starting year and month.
        $start_year = (int) $start_datetime->format('Y');
        $start_month = (int) $start_datetime->format('m');

        // Retrieve timestamp ranges for each month from the starting month until last month.
        $month_timestamp_ranges = $this->date->get_month_timestamps_until_last_month($start_year, $start_month);
                
        $records = [];
        foreach ($month_timestamp_ranges as $range) {
            list($month_start, $month_end) = $range;
            $records[] = $this->generate_record($month_start, $month_end);
        }

        // Inserting
        $this->insert_records(self::TABLE, $records);
    }

    protected function count_instances(int $starttime, int $endtime) : object {
        global $DB;

        $sql = "SELECT 
                    COUNT(*) AS total,
                    SUM(publishedtime > 0) AS published
                FROM {lf_trails}
                WHERE timecreated < :endtime";

        $params = [
            'starttime' => $starttime,
            'endtime'   => $endtime,
        ];

        return $DB->get_record_sql($sql, $params);
    }
}
