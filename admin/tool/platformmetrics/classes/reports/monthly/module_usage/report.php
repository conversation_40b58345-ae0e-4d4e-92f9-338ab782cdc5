<?php namespace tool_platformmetrics\reports\monthly\module_usage;

use \tool_platformmetrics\reports\abstract_report;
use \core_plugin_manager;
use \tool_platformmetrics\reports\monthly_report_interface;

class report extends abstract_report implements monthly_report_interface{

    const TABLE = 'module_usage_metrics';

    protected array $created = [];
    protected array $deleted = [];

    public function get_name(): string {
        return get_string('report:module_usage_report', 'tool_platformmetrics');
    }

    public function generate(){
        $platform_uri = $this->platform->get_identifier();
        if(!$this->db->record_exists(self::TABLE, ['platform_uri' => $platform_uri])){
            return $this->generate_retroactive();
        }

        [$starttime, $endtime] = $this->date->get_last_month_timestamps();

        if($this->record_exists(self::TABLE, ['date_br' => $starttime, 'platform_uri' => $platform_uri])){
            return; // Already executed for this day
        }

        // First, we count the created and deleted modules
        $this->created = $this->count_instances_created($starttime, $endtime);
        $this->deleted = $this->count_instances_deleted($starttime, $endtime);

        // Now we proccess each module plugin
        $records = [];
        foreach ($this->get_modules() as $module) {
            $records[] = $this->generate_for_module($module, $starttime, $endtime);
        }

        if(!empty($records)){
            $this->insert_records(self::TABLE, $records);
        }
    }

    protected function get_modules() : array {
        return array_keys(core_plugin_manager::instance()->get_installed_plugins('mod'));
    }

    public function enabled() : bool {
        return true;
    }

    protected function generate_for_module(string $module, int $starttime, int $endtime) : array {
        $record = [
            'platform_uri'      => $this->platform->get_identifier(),
            'component'         => "mod_$module",
            'date_br'           => $starttime,
            'instances_count'   => 0,
            'instances_active'  => 0,
            'instances_created' => 0,
            'instances_deleted' => 0,
            'instances_views'   => 0,
        ];

        if($result = $this->count_instances_and_active_instances($module, $starttime, $endtime)){
            $record['instances_count'] = $result->instances_count;
            $record['instances_active'] = $result->instances_active;
        }

        if(isset($this->created[$module])){
            $record['instances_created'] = $this->created[$module];
        }

        if(isset($this->deleted[$module])){
            $record['instances_deleted'] = $this->deleted[$module];
        }

        $record['instances_views'] = $this->count_instances_views($module, $starttime, $endtime);
        return $record;
    }

    protected function count_instances_and_active_instances(string $module, int $starttime, int $endtime) : ?object {
        global $DB;

        $sql = "SELECT
                    COUNT(cm.id) AS instances_count,
                    COUNT(used.instance) AS instances_active
                FROM {course_modules} cm
                JOIN {modules} m ON cm.module = m.id
                LEFT JOIN (
                    SELECT DISTINCT l.objectid AS instance
                    FROM {logstore_standard_log} l
                    WHERE l.component = :component
                        AND l.timecreated BETWEEN :starttime AND :endtime
                ) used ON used.instance = cm.instance
                WHERE m.name = :modulename
                    AND cm.added < :endtime2";

        $params = [
            'starttime'  => $starttime,
            'endtime'    => $endtime,
            'starttime2'  => $starttime,
            'endtime2'    => $endtime,
            'component'  => "mod_$module",
            'modulename' => $module
        ];
        return $DB->get_record_sql($sql, $params) ?: null;
    }
    
    protected function count_instances_created(int $starttime, int $endtime) : array {
        global $DB;

        $sql = "SELECT 
                    m.name AS modulename,
                    COUNT(*) AS instances_created
                FROM {logstore_standard_log} l
                JOIN {course_modules} cm ON l.objectid = cm.id
                JOIN {modules} m ON cm.module = m.id
                WHERE l.eventname = '\\\\core\\\\event\\\\course_module_created'
                    AND l.timecreated BETWEEN :starttime AND :endtime
                GROUP BY m.name";

        $params = ['starttime' => $starttime, 'endtime' => $endtime];
        return $DB->get_records_sql_menu($sql, $params);
    }

    protected function count_instances_deleted(int $starttime, int $endtime) : array {
        global $DB;

        $sql = "SELECT 
                    m.name AS modulename,
                    COUNT(*) AS instances_deleted
                FROM {logstore_standard_log} l
                JOIN {course_modules} cm ON l.objectid = cm.id
                JOIN {modules} m ON cm.module = m.id
                WHERE l.eventname = '\\\\core\\\\event\\\\course_module_deleted'
                    AND l.timecreated BETWEEN :starttime AND :endtime
                GROUP BY m.name";

        $params = ['starttime' => $starttime, 'endtime' => $endtime];
        return $DB->get_records_sql_menu($sql, $params);
    }

    protected function count_instances_views(string $module, int $starttime, int $endtime) : int {
        global $DB;

        $sql = "SELECT COUNT(*) AS total_viewed
                FROM {logstore_standard_log}
                WHERE action = 'viewed'
                    AND component = :component
                    AND timecreated BETWEEN :starttime AND :endtime";

        $params = [
            'starttime' => $starttime,
            'endtime'   => $endtime,
            'component' => "mod_$module"
        ];
        return $DB->count_records_sql($sql, $params);
    }



    public function generate_retroactive() {
        global $DB;
        
        // Determine the start time based on the oldest log of course_module_created.
        $sql = "SELECT MIN(timecreated) AS timecreated 
                FROM {logstore_standard_log} 
                WHERE eventname = '\\\\core\\\\event\\\\course_module_created'";
        $result = $DB->get_record_sql($sql);
        
        if (!$result || empty($result->timecreated)) {
            // Nothing to generate.
            return;
        }
                
        // Create a DateTime object from the earliest log timestamp
        $start_datetime = $this->date->timestamp_to_datetime($result->timecreated);
        
        // Determine the starting year and month.
        $start_year = (int) $start_datetime->format('Y');
        $start_month = (int) $start_datetime->format('m');
        
        // Retrieve timestamp ranges for each month from the starting month until last month.
        $month_timestamp_ranges = $this->date->get_month_timestamps_until_last_month($start_year, $start_month);
                
        foreach ($month_timestamp_ranges as $range) {
            list($month_start, $month_end) = $range;
            $records = [];

            // First, we count the created and deleted modules for this month range
            $this->created = $this->count_instances_created($month_start, $month_end);
            $this->deleted = $this->count_instances_deleted($month_start, $month_end);
            
            // Generate a record for each module for this month range.
            foreach ($this->get_modules() as $module) {
                $records[] = $this->generate_for_module($module, $month_start, $month_end);
            }

            // Inserting
            $this->insert_records(self::TABLE, $records);
        }
    }
}