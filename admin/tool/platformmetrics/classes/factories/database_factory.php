<?php namespace tool_platformmetrics\factories;

use \moodle_database;
use \tool_platformmetrics\config;
use \dml_exception;

final class database_factory {

    /**
     * @throws moodle_exception
     * @throws dml_exception
     * @return moodle_database
     */
    public static function make_moodle_database() : moodle_database {
        $cfg = config::get_database_config();

        if (!$db = moodle_database::get_driver_instance($cfg->dbtype, $cfg->dblibrary)) {
            throw new dml_exception('dbdriverproblem', "Unknown driver $cfg->dblibrary/$cfg->dbtype");
        }
    
        $db->connect($cfg->dbhost, $cfg->dbuser, $cfg->dbpass, $cfg->dbname, $cfg->prefix, $cfg->dboptions);
        return $db;
    }
}
