<?php namespace tool_platformmetrics;


class platform_info {

    private static $instance;

    protected string $identifier;
    protected string $url;
    protected string $version;
    protected string $name;

    public function __construct(){
        global $CFG;

        $this->identifier = preg_replace('/^https?:\/\//', '', rtrim($CFG->wwwroot, '/'));
        $this->url = $CFG->wwwroot;
        $this->version = get_config('moodle', 'version');
        $this->name = get_site()->fullname;
    }

    /**
     * Returns the unique identifier of the platform.
     */
    public function get_identifier(): string {
        return $this->identifier;
    }

    /**
     * Returns the Moodle site URL.
     */
    public function get_url(): string {
        return $this->url;
    }

    /**
     * Returns the Moodle version.
     */
    public function get_version(): string {
        return $this->version;
    }

    /**
     * Returns the site name.
     */
    public function get_name(): string {
        return $this->name;
    }

    public static function instance() : static {
        if(!isset(self::$instance)){
            self::$instance = new static();
        }
        return self::$instance;
    }
}
