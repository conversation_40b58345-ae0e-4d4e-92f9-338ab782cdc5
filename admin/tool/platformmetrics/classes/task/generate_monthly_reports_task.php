<?php namespace tool_platformmetrics\task;

use \tool_platformmetrics\config;
use \tool_platformmetrics\factories\database_factory;
use \tool_platformmetrics\repositories\report_repository;
use \tool_platformmetrics\loggers\database_logger;
use \tool_platformmetrics\platform_info;

use core\task\scheduled_task;

class generate_monthly_reports_task extends scheduled_task {

    public function get_name(): string {
        return get_string('task:generate_monthly_reports_task', 'tool_platformmetrics');
    }

    public function execute() {       
        if(!config::is_enabled()){
            return;
        }

        raise_memory_limit(MEMORY_HUGE);
        \core_php_time_limit::raise();

        // Initialization
        $db = database_factory::make_moodle_database();
        $report_repository = new report_repository($db);
        $platform_info = platform_info::instance();
        $logger = new database_logger($platform_info->get_identifier(), $db);

        global $DB;
        $DB->set_debug(true);

        /** @var \tool_platformmetrics\reports\report_interface */
        foreach ($report_repository->get_monthly_tasks() as $report){
            if(!$report->enabled()){
                continue;
            }
            
            $report->set_logger($logger);
            
            try {
                $report->generate();
            } catch (\Throwable $th) {
                $logger->error("Error while generate {report}: {message}", [
                    'report' => $report->get_name(),
                    'message' => $th->getMessage(),
                    'trace' => $th->getTrace(),
                ]);
            }
        }

        $DB->set_debug(false);
    }
}