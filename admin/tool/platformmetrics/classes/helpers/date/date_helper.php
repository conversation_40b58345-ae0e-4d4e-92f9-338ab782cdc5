<?php namespace tool_platformmetrics\helpers\date;

use \DateTime;
use \DateTimeZone;

class date_helper {

    protected function get_timezone() : DateTimeZone {
        return \core_date::get_server_timezone_object();
    }
    
    public function timestamp_to_datetime(int $timestamp) : DateTime {
        $datetime = new DateTime('@' . $timestamp);
        $datetime->setTimezone($this->get_timezone());
        return $datetime;
    }

    public function get_yesterday_start_datetime() : DateTime {
        return new DateTime('yesterday midnight', $this->get_timezone());
    }

    public function get_today_start_datetime() : DateTime {
        return new DateTime('today midnight', $this->get_timezone());
    }

    public function get_yesterday_start_timestamp() : int {
        return $this->get_yesterday_start_datetime()->getTimestamp();
    }

    public function get_today_start_timestamp() : int {
        return $this->get_today_start_datetime()->getTimestamp();
    }

    public function get_current_datetime() : DateTime {
        return new DateTime('now', $this->get_timezone());
    }

    public function get_current_timestamp() : int {
        return $this->get_current_datetime()->getTimestamp();
    }

    public function get_datetime(string $date) : DateTime {
        return new DateTime($date, $this->get_timezone());
    }

    public function get_timestamp(string $date) : int {
        return $this->get_datetime($date)->getTimestamp();;
    }

    public function get_start_of_month(int $year, int $month) : DateTime {
        $date = sprintf('%04d-%02d-01', $year, $month);
        $dt = new DateTime($date, $this->get_timezone());
        $dt->setTime(0, 0, 0);
        return $dt;
    }
    
    public function get_end_of_month(int $year, int $month) : DateTime {
        $date = sprintf('%04d-%02d-01', $year, $month);
        $dt = new DateTime($date, $this->get_timezone());
        $dt->modify('last day of this month');
        $dt->setTime(23, 59, 59);
        return $dt;
    }
    
    /**
     * @return DateTime[] [startdate, enddate]
     */
    public function get_last_month_datetimes() : array {
        $start = new DateTime('now', $this->get_timezone());
        $start->modify('first day of last month');
        $start->setTime(0, 0, 0);

        $end = new DateTime('now', $this->get_timezone());
        $end->modify('last day of last month');
        $end->setTime(23, 59, 59);

        return [$start, $end];
    }

    /**
     * @return int[] [starttime, endtime]
     */
    public function get_last_month_timestamps() : array {
        return array_map(fn($date) => $date->getTimestamp(), $this->get_last_month_datetimes());
    }

    /**
     * Returns an array of [startdate, enddate] pairs for each month starting from
     * a given initial month until last month.
     *
     * @param int $start_year  The starting year.
     * @param int $start_month The starting month.
     * @return DateTime[][] An array of date range pairs.
     */
    public function get_month_dates_until_last_month(int $start_year, int $start_month) : array {
        $date_ranges = [];
        
        // Get current date and determine last month to include.
        $current_date = new DateTime('now', $this->get_timezone());
        $last_month_date = clone $current_date;
        $last_month_date->modify('first day of last month');
        $last_year = (int) $last_month_date->format('Y');
        $last_month = (int) $last_month_date->format('m');
        
        $year = $start_year;
        $month = $start_month;
        
        // Loop until we reach last month.
        while ($year < $last_year || ($year === $last_year && $month <= $last_month)) {
            // Create the start date for the month.
            $startdate = new DateTime(sprintf('%04d-%02d-01', $year, $month), $this->get_timezone());
            $startdate->setTime(0, 0, 0);
            
            // Create the end date for the month.
            $enddate = clone $startdate;
            $enddate->modify('last day of this month');
            $enddate->setTime(23, 59, 59);
            
            $date_ranges[] = [$startdate, $enddate];
            
            if ($month === 12) {
                $month = 1;
                $year++;
            } else {
                $month++;
            }
        }
        
        return $date_ranges;
    }

    /**
     * Returns an array of [start_timestamp, end_timestamp] pairs for each month starting from
     * a given initial month until last month.
     *
     * @param int $start_year  The starting year.
     * @param int $start_month The starting month.
     * @return int[][] An array of timestamp pairs.
     */
    public function get_month_timestamps_until_last_month(int $start_year, int $start_month) : array {
        $date_ranges = $this->get_month_dates_until_last_month($start_year, $start_month);
        $timestamp_ranges = [];
        
        foreach ($date_ranges as $range) {
            list($startdate, $enddate) = $range;
            $timestamp_ranges[] = [$startdate->getTimestamp(), $enddate->getTimestamp()];
        }
        
        return $timestamp_ranges;
    }
}
