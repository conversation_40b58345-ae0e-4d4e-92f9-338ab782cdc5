<?php namespace tool_platformmetrics\repositories;

use \moodle_database;
use \moodle_exception;
use \tool_platformmetrics\reports\daily_report_interface;
use \tool_platformmetrics\reports\monthly_report_interface;

class report_repository {
    protected moodle_database $db;

    protected static array $daily_reports = [
    ];

    protected static array $monthly_reports = [
        'users_metrics' => \tool_platformmetrics\reports\monthly\users_metrics\report::class,
        'module_usage' => \tool_platformmetrics\reports\monthly\module_usage\report::class,
        'local_trails_metrics' => \tool_platformmetrics\reports\monthly\local_trails_metrics\report::class,
    ];

    public function __construct(moodle_database $db){
        $this->db = $db;
    }

    /**
     * Retrieves an instance of a specific daily report by identifier.
     * 
     * @param string $identifier
     * @return daily_report_interface
     */
    public function get_daily_task(string $identifier): daily_report_interface {
        if (!isset(self::$reports[$identifier])) {
            throw new moodle_exception("exception:report_not_found", 'tool_platformmetrics');
        }
        return new self::$daily_reports[$identifier]($this->db);
    }

    /**
     * Retrieves all registered daily report classes.
     * 
     * @param string $identifier
     * @return daily_report_interface[]
     */
    public function get_daily_tasks(): array {
        return array_map(fn($class) => new $class($this->db), self::$daily_reports);
    }

    /**
     * Retrieves an instance of a specific monthly report by identifier.
     * 
     * @return monthly_report_interface
     */
    public function get_monthly_task(string $identifier): monthly_report_interface {
        if (!isset(self::$reports[$identifier])) {
            throw new moodle_exception("exception:report_not_found", 'tool_platformmetrics');
        }
        return new self::$monthly_reports[$identifier]($this->db);
    }

    /**
     * Retrieves all registered monthly report classes.
     * 
     * @return monthly_report_interface[]
     */
    public function get_monthly_tasks(): array {
        return array_map(fn($class) => new $class($this->db), self::$monthly_reports);
    }
}