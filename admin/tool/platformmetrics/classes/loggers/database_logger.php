<?php namespace tool_platformmetrics\loggers;

require_once($CFG->dirroot . '/admin/tool/platformmetrics/vendor/autoload.php');

use \moodle_database;
use \Psr\Log\LoggerInterface;
use \Psr\Log\LogLevel;
use \tool_platformmetrics\helpers\date\date_helper;
use \tool_platformmetrics\helpers\date\utc_date_helper;

class database_logger implements LoggerInterface {
    const TABLE = 'logs';

    protected string $channel;
    protected moodle_database $db;
    protected date_helper $date;

    /**
     * Logger constructor.
     *
     * @param string $channel.
     * @param moodle_database $db Moodle database instance.
     */
    public function __construct(string $channel, moodle_database $db) {
        $this->channel = $channel;
        $this->db = $db;
        $this->date = new utc_date_helper();
    }

    /**
     * System is unusable.
     *
     * @param string $message Log message.
     * @param array $context Context data.
     */
    public function emergency($message, array $context = []): void {
        $this->log(LogLevel::EMERGENCY, $message, $context);
    }

    /**
     * Action must be taken immediately.
     *
     * @param string $message Log message.
     * @param array $context Context data.
     */
    public function alert($message, array $context = []): void {
        $this->log(LogLevel::ALERT, $message, $context);
    }

    /**
     * Critical conditions.
     *
     * @param string $message Log message.
     * @param array $context Context data.
     */
    public function critical($message, array $context = []): void {
        $this->log(LogLevel::CRITICAL, $message, $context);
    }

    /**
     * Runtime errors that do not require immediate action but should typically be logged and monitored.
     *
     * @param string $message Log message.
     * @param array $context Context data.
     */
    public function error($message, array $context = []): void {
        $this->log(LogLevel::ERROR, $message, $context);
    }

    /**
     * Exceptional occurrences that are not errors.
     *
     * @param string $message Log message.
     * @param array $context Context data.
     */
    public function warning($message, array $context = []): void {
        $this->log(LogLevel::WARNING, $message, $context);
    }

    /**
     * Normal but significant events.
     *
     * @param string $message Log message.
     * @param array $context Context data.
     */
    public function notice($message, array $context = []): void {
        $this->log(LogLevel::NOTICE, $message, $context);
    }

    /**
     * Interesting events.
     *
     * @param string $message Log message.
     * @param array $context Context data.
     */
    public function info($message, array $context = []): void {
        $this->log(LogLevel::INFO, $message, $context);
    }

    /**
     * Detailed debug information.
     *
     * @param string $message Log message.
     * @param array $context Context data.
     */
    public function debug($message, array $context = []): void {
        $this->log(LogLevel::DEBUG, $message, $context);
    }

    /**
     * Logs with an arbitrary level.
     *
     * @param mixed $level Log level.
     * @param string $message Log message.
     * @param array $context Context data.
     */
    public function log($level, $message, array $context = []): void {
        // Interpolate context values into the message if provided.
        if (!empty($context)) {
            $message = $this->interpolate($message, $context);
        }

        $entry = [
            'channel' => $this->channel,
            'level' => strtoupper($level),
            'message' => $message,
            'context' => json_encode($context),
            'time' => $this->date->get_current_timestamp(),
        ];

        $this->db->insert_record(self::TABLE, $entry);
    }

    /**
     * Interpolates context values into the message placeholders.
     *
     * Example:
     *   Message: "User {username} logged in."
     *   Context: ['username' => 'john']
     *   Result: "User john logged in."
     *
     * @param string $message Log message.
     * @param array $context Context data.
     * @return string
     */
    protected function interpolate($message, array $context = []): string {
        $replace = [];
        foreach($context as $key => $val){
            if(!is_array($val) && (!is_object($val) || method_exists($val, '__toString'))){
                $replace['{' . $key . '}'] = $val;
            }
        }
        return strtr($message, $replace);
    }
}
