<?php

defined('MOODLE_INTERNAL') || die();

/**
 * Installs the metrics database
 *
 * @return void
 */
function xmldb_tool_platformmetrics_install_metrics_database(){
    if(!\tool_platformmetrics\config::is_master_instance()){
        return true; // Only the master instance can install the metrics database
    }

    $db = \tool_platformmetrics\factories\database_factory::make_moodle_database();
    $dbman = $db->get_manager();

    if(!$xmldb_file = xmldb_tool_platformmetrics_get_metrics_xmldb_file()){
        return false;
    }

    $dbman->install_from_xmldb_structure($xmldb_file->getStructure());

    return true;
}


function xmldb_tool_platformmetrics_get_metrics_xmldb_file() : ?xmldb_file {
    $filepath = __DIR__ . '/metrics_install.xml';
    if(!is_readable($filepath)){
        return null;
    }
    
    $xmldb_file = new xmldb_file($filepath);
    $xmldb_file->loadXMLStructure();
    return $xmldb_file;
}

/**
 * Automatically creates missing tables from metrics_install.
 * Existing tables must be upgraded manually
 *
 * @param moodle_database $db
 * @return void
 */
function xmldb_tool_platformmetrics_metrics_auto_install(moodle_database $db){
    if(!\tool_platformmetrics\config::is_master_instance()){
        return; // Only the master instance can upgrade the database
    }

    if(!$xmldb_file = xmldb_tool_platformmetrics_get_metrics_xmldb_file()){
        return false;
    }

    $dbman = $db->get_manager();
    
    foreach ($xmldb_file->getStructure()->getTables() as $table) {
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }
    }
}