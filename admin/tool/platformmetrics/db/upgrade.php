<?php

function xmldb_tool_platformmetrics_upgrade($oldversion) {
    require_once(__DIR__ . '/upgradelib.php');

    // Plugin upgrades
    xmldb_tool_platformmetrics_local_upgrade($oldversion);

    // Metrics datatabase upgrades
    if(!\tool_platformmetrics\config::is_master_instance()){
        return true;
    }

    $db = \tool_platformmetrics\factories\database_factory::make_moodle_database();

    xmldb_tool_platformmetrics_metrics_auto_install($db);
    xmldb_tool_platformmetrics_metrics_database_upgrade($db, $oldversion);

    return true;
}

/**
 * Any LOCAL upgrades go in here!
 *
 * @param int $oldversion
 * @return void
 */
function xmldb_tool_platformmetrics_local_upgrade(int $oldversion){
    global $DB;


}

/**
 * Any METRICS DATABASE upgrades must go in here.
 * 
 * Please remember that new tables are automatically created before this.
 * Only do modifications in existing tables.
 *
 * @param int $oldversion
 * @return void
 */
function xmldb_tool_platformmetrics_metrics_database_upgrade(moodle_database $db, int $oldversion){
    $dbman = $db->get_manager();

    // Do upgrades in here...
}