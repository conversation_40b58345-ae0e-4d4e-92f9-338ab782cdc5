<?php

/**
 * Strings for component 'videos', language 'en', branch 'MOODLE_20_STABLE'
 *
 * MetroAula Module Videos
 * Copyright SmartLMS (http://www.leolearning.com.br/)
 *
 * @package    mod_videos
 * @copyright  2018 SmartLMS (http://www.leolearning.com.br/)
 * @license    proprietary software
 */

defined('MOODLE_INTERNAL') || die();

$string['modulename'] = 'Video';
$string['modulenameplural'] = 'Videos';
$string['modulename_help'] = 'Usa el módulo de video para... | El módulo de video permite...';
$string['videos:addinstance'] = 'Añadir un nuevo video';
$string['videos:submit'] = 'Enviar video';
$string['videos:view'] = 'Ver video';
$string['videosfieldset'] = 'Conjunto de campos de ejemplo personalizado';
$string['videosname'] = 'Nombre del video';
$string['videosname_help'] = 'El título del video es obligatorio y solo sirve para ayudar en la identificación';
$string['videos'] = 'Video';
$string['externalurl'] = 'URL del video';
$string['externalurl_help'] = 'Servicios soportados: Youtube, Vimeo y DailyMotion';
$string['pluginadministration'] = 'Administración de video';
$string['pluginname'] = 'Video';

$string['provider_url'] = 'Regex de Lizza';
$string['provider_url:desc'] = "Regex de la URL que muestra el video de Lizza";

$string['provider_embed_url'] = 'URL de incrustación';
$string['provider_embed_url:desc'] = "Endpoint para acceder al servicio oEmbed de Lizza";
