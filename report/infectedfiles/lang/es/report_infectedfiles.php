<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Infected file report
 *
 * @package    report_infectedfiles
 * <AUTHOR> <<EMAIL>>
 * @copyright  Catalyst IT
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
$string['author'] = 'Autor';
$string['confirmdelete'] = '¿Está seguro de que desea eliminar este archivo?';
$string['confirmdeleteall'] = '¿Está seguro de que desea eliminar todos los archivos?';
$string['confirmdownload'] = '¿Está seguro de que desea descargar este archivo?';
$string['confirmdownloadall'] = '¿Está seguro de que desea descargar todos los archivos?';
$string['filename'] = 'Nombre del archivo';
$string['infectedfiles'] = 'Fallos del antivirus';
$string['privacy:metadata:infected_files'] = 'Esta tabla almacena información sobre los fallos del antivirus detectados por el sistema.';
$string['privacy:metadata:infected_files:filename'] = 'El nombre del archivo infectado subido por el usuario.';
$string['privacy:metadata:infected_files:timecreated'] = 'La marca de tiempo de cuando un usuario subió un archivo infectado.';
$string['privacy:metadata:infected_files:userid'] = 'El ID del usuario que subió un archivo infectado.';
$string['privacy:metadata:infected_files_subcontext'] = 'Fallos del antivirus';
$string['pluginname'] = 'Archivos infectados';
$string['quarantinedfile'] = 'Archivo en cuarentena';
$string['reason'] = 'Motivo del fallo';
$string['timecreated'] = 'Fecha de creación';
