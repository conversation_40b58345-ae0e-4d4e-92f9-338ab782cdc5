<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Lang strings.
 *
 * Language strings used by report_loglive
 *
 * @package    report_loglive
 * @copyright  2011 Petr Skoda
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['eventreportviewed'] = 'Informe de registros en vivo visto';
$string['eventcomponent'] = 'Componente';
$string['eventcontext'] = 'Contexto del evento';
$string['eventloggedas'] = '{$a->realusername} como {$a->asusername}';
$string['eventorigin'] = 'Origen';
$string['eventrelatedfullnameuser'] = 'Usuario afectado';
$string['livelogs'] = 'Registros en vivo de la última hora';
$string['livelogswithupdate'] = 'Registros en vivo de la última hora ({$a})';
$string['loglive:view'] = 'Ver registros en vivo';
$string['nologreaderenabled'] = 'No hay lector de registros habilitado';
$string['pause'] = 'Pausar actualizaciones en vivo';
$string['pluginname'] = 'Registros en vivo';
$string['resume'] = 'Reanudar actualizaciones en vivo';
$string['selectlogreader'] = 'Seleccionar lector de registros';
$string['privacy:metadata'] = 'El complemento de Registros en vivo no almacena ningún dato personal.';
