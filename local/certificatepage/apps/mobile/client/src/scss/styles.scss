::ng-deep #mobile_certificatepage_root {
    padding: 0 16px !important;

    .toUpperCase{
      text-transform: uppercase;
    }

    .text-center {
      text-align: center !important;
    }

    .mb-5, .my-5 {
      margin-bottom: 2rem !important;
    }

    .mb-2, .my-2 {
      margin-bottom: 0.5rem !important;
    }

    .p-3 {
      padding: 1rem !important;
    }

    h6{
      font-weight: 700;
      margin-top: 0;
    }

    .col-12 {
      flex: 0 0 auto;
      width: 100%;
    }

    @media (min-width: 576px) {
      .col-sm-4 {
        flex: 0 0 auto;
        width: 33.333333%;
      }
    }

    @media (min-width: 768px) {
      .col-md-6 {
        flex: 0 0 auto;
        width: 50%;
      }
    }

    @media (min-width: 992px) {
      .col-lg-4 {
        flex: 0 0 auto;
        width: 33.333333%;
      }
    }

    @media (min-width: 1200px) {
      .col-xl-3 {
        flex: 0 0 auto;
        width: 25%;
      }
    }
 
    nav {
        background-color: transparent;
        padding: 10px 0;
        border-bottom: 1px solid #adb5bd;
        margin-bottom: 30px;
        display: block;

        #inline-nav{
          ul{
            padding: 0;
            margin: 0;
            list-style: none;
            display: flex;
            align-items: center;

            li{
              list-style: none;
              a{
                text-decoration: none;
                font-size: 16px;
                font-weight: 500;
                position: relative;
                padding: 5px 16px;
                transition: all .3s;
                display: flex;
                gap: 10px;
                align-items: center;
                box-shadow: unset;
                &.router-link-active {
                  color: #fff;
                  &::after {
                    background-color: #6ea8fe;
                    content: "";
                    position: absolute;
                    bottom: -11px;
                    left: 50%;
                    transform: translate(-50%);
                    width: 100%;
                    height: 4px;
                    transition: background-color .3s;
                  }
                }
              }
            }
          }
        }

        .displayNoneInitial{
          display: none !important;
        }
    
        .custom-select {
            background-color: #212529;
            color: #fff;
            padding: 8px;
            border-radius: 4px;
            width: 100%;
            word-wrap: normal;
            transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            text-transform: none;
            display: inline-block;
            height: calc(1.5em + 0.75rem + 2px);
            font-weight: 400;
            line-height: 1.5;
            vertical-align: middle;
            border: 1px solid #495057;
            margin: 0;
            font-family: inherit;
            box-sizing: border-box;
        }
        @media (max-width: 767.98px) {
            max-width: 100%;
        }
        @media (max-width: 1200px) {
            font-size: calc(0.90375rem + 0.045vw);
        }
    }

    #body{
      #listItems{
        .bubble {
          margin-bottom: 30px !important;
        }
      
        .icomoon-cloud-download {
          color: #fff;
          font-size: 20px !important;
          display: flex !important;
          align-items: center;
          margin-right: 10px;
        }
      
        .card-title a {
          color: #878787 !important;
        }
      
        .course-certificate>a:link {
          text-decoration: none;
        }
      
        .card img {
          max-width: 100%;
          max-height: 150px;
        }
      
        .button-download {
          text-decoration: none;
          display: flex !important;
          align-items: center;
          justify-content: center;
          width: 100% !important;
          background-color: #0573FA !important;
          border-color: #0573FA !important;
          color: #fff !important;
          font-weight: 400;
          text-align: center;
          vertical-align: middle;
          border: 1px solid transparent;
          padding: .375rem .75rem;
          line-height: 1.5;
          border-radius: .2rem;
          transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
      
        .button-download a {
          color: #fff;
          text-decoration: none;
        }
      
        .button-download a:hover {
          color: #fff;
        }
      
        .bubble .card {
          width: 100%;
          max-width: 362px;
          height: 100%;
          background-color: #212529;
          border: 1px solid rgba(255, 255, 255, .125);
          border-radius: .5rem;
          position: relative;
          display: flex;
          flex-direction: column;
          min-width: 0;
          word-wrap: break-word;
        }
      
      
        .bubble .card .card-thumbnail {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          flex: 1;
        }
      
        .bubble .card .card-thumbnail .course-image {
          max-height: 226px;
          min-height: 198px;
          width: 100%;
          height: 100%;
        }
      
        .bubble .card .card-thumbnail .course-image.default-image {
          filter: brightness(0.85);
        }
      
        .bubble .card .card-thumbnail .icon {
          position: absolute;
          width: 34px;
          height: 50px;
          left: 15px;
          top: 15px;
          margin: 0;
          filter: unset;
        }
      
        .bubble .card .card-title {
          position: absolute;
          margin: 0;
        }
      
        .bubble .card .card-title a {
          color: #fff !important;
          font-weight: bold;
          font-size: 20px;
          text-transform: uppercase;
        }
      
        .bubble .card .card-body {
          flex: unset;
          min-height: 1px;
          padding: 1.25rem;
          @media (max-width: 767.98px) {
            padding: .625rem;
          }
        }
      
        .card-body h6 {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: default;
        }
      
        .wrapper-filters {
          @media(max-width: 768px) {
            flex-direction: column;
          }
        }
      
        .filter-container {
          display: flex;
          gap: 12px;
      
          @media (max-width: 768px) {
            flex-direction: column;
            width: 100%;
          }
        }
      }
    }

    #empty-list {
        display: flex;
        justify-content: center;
        padding: 3rem;
      
        .content {
          width: 100%;
          text-align: center;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }
      
        img {
          width: 100px;
          height: auto;
        }
      }
}