const apiNS = {
  get_ajax_function: (component) => {
    let functions = {
      1: "local_certificatepage_list_user_certificates",
      2: "local_certificatepage_list_user_trail_certificates"
    };
    return functions[component];
  },
  /**
   * Get a list of certificates.
   *
   * Valid args are:
   * Array courses  list of certificates id numbers.
   *
   * @param {string} component component name.
   * @param {Object} args Arguments send to the webservice.
   * @param {Object} preSets Arguments to prepare the request.
   * @return {Promise} Resolve with warnings.
   */
  get_certificates: (component = 1, args = {}, preSets = {}) => {
    if(args == undefined || args == '') args = loader.params;
    return ref.CoreSitesProvider.currentSite.read(apiNS.get_ajax_function(component), args, preSets);
  },

  API: (functionName) => {
    console.log('Call: '+functionName);
    ref.CoreToastsService.show({'message': 'Call: '+ functionName});
  }

};