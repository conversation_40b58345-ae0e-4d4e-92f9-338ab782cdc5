const certificates_pageNS = {
  searchComponent: {
    currentTab: 1,
    currentPage: 1,
    totalCertificates: 0,
    totalPages: 0,
    itemsPage: 8,
    finishList: false
  },
  $ionContent: null,
  loadingList: false,
  notifyAlreadyLoaded: false,
  notifyLoadingNow: false,
  init: async () => {
    $(document).ready(function() {
      certificates_pageNS.$ionContent = $("ion-content:has(.local_certificatepage)");
    });
    let loaderPage = await ref.LoadingController.ctrl.create();
    await loaderPage.present();
    try {
      certificates_pageNS.loadingList = false;
      certificates_pageNS.searchComponent.currentTab = loader.initialTab;
      certificates_pageNS.mountNoCertificates(false);
      certificates_pageNS.mountCertificates(false, false, []);
      $(document).ready(certificates_pageNS.alternarComponentesPorTamanho);
      $(window).on('resize', certificates_pageNS.alternarComponentesPorTamanho);
      certificates_pageNS.searchChangeCertificates();
      certificates_pageNS.selectTab();
      certificates_pageNS.infiniteScrollConfig();
    } catch (error) {
      alert('Algum erro ocorreu');
      console.log(error);
    } finally {
      await loaderPage.dismiss();
    }
  },

  selectTab: () => {
    $(loader.component + ' #select-nav select').on('change', async function() {
      certificates_pageNS.loadingList = false;
      let loaderPage = await ref.LoadingController.ctrl.create();
      await loaderPage.present();
      let valor = $(this).val();
      certificates_pageNS.searchComponent.currentTab = parseInt(valor);
      certificates_pageNS.searchComponent.currentPage = 1;
      certificates_pageNS.searchComponent.totalCertificates = 0;
      certificates_pageNS.searchComponent.totalPages = 0;
      certificates_pageNS.searchComponent.itemsPage = 8;
      certificates_pageNS.searchComponent.finishList = false;
      certificates_pageNS.searchChangeCertificates();
      await loaderPage.dismiss();
    });
    $(loader.component + ' #inline-nav ul li a').on('click', async function(e) {
      e.preventDefault();
      certificates_pageNS.loadingList = false;
      let loaderPage = await ref.LoadingController.ctrl.create();
      await loaderPage.present();
      let valor = $(this).attr('value');

      let container = $(loader.component + ' #inline-nav');
      container.find('a').removeClass('router-link-active router-link-exact-active');
      container.find('a[value="'+valor+'"]').addClass('router-link-active router-link-exact-active');

      certificates_pageNS.searchComponent.currentTab = parseInt(valor);
      certificates_pageNS.searchComponent.currentPage = 1;
      certificates_pageNS.searchComponent.totalCertificates = 0;
      certificates_pageNS.searchComponent.totalPages = 0;
      certificates_pageNS.searchComponent.itemsPage = 8;
      certificates_pageNS.searchComponent.finishList = false;     
      certificates_pageNS.searchChangeCertificates();
      await loaderPage.dismiss();
    });  
  },

  alternarComponentesPorTamanho: () => {
    let largura = $(window).width();

    let $mobile = $(loader.component + ' #select-nav');
    let $desktop = $(loader.component + ' #inline-nav');
    
    if($mobile.hasClass('displayNoneInitial')){
      $mobile.removeClass('displayNoneInitial');
      $desktop.removeClass('displayNoneInitial');
    }

    if (largura > 768) {
        $desktop.show();
        $mobile.hide();
    } else {
        $desktop.hide();
        $mobile.show();
    }
  },

  searchChangeCertificates: async () => {
    if(certificates_pageNS.searchComponent.finishList){
      ref.CoreToastsService.show({'message': 'Todos os Certificados foram carregados'});
      return;
    }
    certificates_pageNS.loadingList = true;
    let loaderPage = await ref.LoadingController.ctrl.create();
    await loaderPage.present();
    try {
      let response = await apiNS.get_certificates(certificates_pageNS.searchComponent.currentTab, {filters: {status: -1},
        per_page: certificates_pageNS.searchComponent.itemsPage,
        current_page: certificates_pageNS.searchComponent.currentPage}, {});

        console.log(response);

        let initializingList = false;
        if(certificates_pageNS.searchComponent.currentPage <= 1) initializingList = true;
        
        certificates_pageNS.searchComponent.currentPage++;
        certificates_pageNS.searchComponent.totalCertificates = response.total;
        certificates_pageNS.searchComponent.totalPages = response.total_pages;
        certificates_pageNS.searchComponent.finishList = response.current_page == response.total_pages;

        certificates_pageNS.mountNoCertificates(response.total <= 0);
        certificates_pageNS.mountCertificates(response.total > 0, initializingList, response.certificates);
    } catch (error) {
      console.log(error);
    } finally {
      console.log(certificates_pageNS.searchComponent);
      certificates_pageNS.loadingList = false;
      certificates_pageNS.notifyLoadingNow = false;
      await loaderPage.dismiss();
      if(certificates_pageNS.searchComponent.finishList){
        ref.CoreToastsService.show({'message': 'Todos os Certificados foram carregados'});
      }
    }
  },

  infiniteScrollConfig: () => {
    $(document).ready(function() {
      let $ionContent = certificates_pageNS.$ionContent;
    
      if ($ionContent.length > 0) {
        console.log('Encontrado ion-content certo.');
    
        let scrollEl = $ionContent[0].shadowRoot 
          ? $ionContent[0].shadowRoot.querySelector('.inner-scroll') 
          : $ionContent.find('.scroll-content')[0];
    
        if (!scrollEl) {
          console.warn('Elemento real de scroll não encontrado!');
          return;
        }
    
        console.log('Elemento real de scroll identificado.');
    
        let lastScrollTop = 0;
    
        $(scrollEl).on('scroll', function() {
          
          if (certificates_pageNS.searchComponent.finishList) {
            if(!certificates_pageNS.notifyAlreadyLoaded && !certificates_pageNS.notifyLoadingNow) {
              ref.CoreToastsService.show({'message': 'Todos os Certificados foram carregados'});
              certificates_pageNS.notifyAlreadyLoaded = true;
            }
            return;
          }

          let scrollTop = $(this).scrollTop();
          let scrollHeight = $(this)[0].scrollHeight;
          let clientHeight = $(this)[0].clientHeight;
    
          if (scrollTop > lastScrollTop) {             
            if (scrollTop + clientHeight >= scrollHeight - 60) {
    
              if (certificates_pageNS.loadingList) {
                if(!certificates_pageNS.notifyLoadingNow) {
                  ref.CoreToastsService.show({'message': 'Carregando certificados...'});
                  certificates_pageNS.notifyLoadingNow = true;
                }
                return;
              }
    
              console.log('Chamando searchChangeCertificates...');
              certificates_pageNS.searchChangeCertificates();
            }
          }
          lastScrollTop = scrollTop;
        });
    
      } else {
        console.warn('ion-content esperado não encontrado!');
      }
    }); 
  },

  mountNoCertificates: (show = false) => {
    let nocertificateTag = $(loader.component + ' #empty-list');

    if (show) {
      nocertificateTag.show();
    } else {
      nocertificateTag.hide();
    }
  },

  formatCertificateName: (certificateName) => {
    const indexHifen = certificateName.indexOf('-');
    if (indexHifen !== -1) {
      const formattedName = certificateName.slice(indexHifen + 1).trim();
      return formattedName.split(' ').map(word => {
        return word.charAt(0).toUpperCase() + word.slice(1);
      }).join(' ');
    } else {
      return certificateName;
    }
  },

  mountCertificates: (show = true, newList = false, items = []) => {
    let bodyCertificatesList = $(loader.component + ' #listItems');
    if (show) {
      let lisToShow = '';
      items.forEach((certificado) => {
        console.log(certificado);
        if(certificates_pageNS.searchComponent.currentTab == 1){



          let certificateLink = `${ref.CoreSitesProvider.currentSite.publicConfig.wwwroot}/mod/${certificado.modname}/wmsendfile.php?code=${certificado.code}`;
          let courseLink = `${ref.CoreSitesProvider.currentSite.publicConfig.wwwroot}/course/view.php?id=${certificado.course}`;

          if (bodyCertificatesList.find('div[key="'+certificado.id+'"]').length > 0) {
            console.log(bodyCertificatesList.find('div[key="'+certificado.id+'"]'));
            return;
          }

          let item = `<div key="`+certificado.id+`"
            class="bubble col-12 col-sm-4 col-md-6 col-lg-4 col-xl-3">
            <div class="card">
              <div class="card-thumbnail">
                <img class="course-image" src="`+certificado.cover+`" `;
                  if(certificado.defaultImage) item += 'class="default-image" ';
                  item += `alt="">
                <div class="icon">
                  <img src="`+loader.medalSvg+`" alt="Ícone" />
                </div>`;
                if(certificado.defaultImage) item += '<h5 class="card-title p-3 text-center"><a href="'+courseLink+'" style="text-shadow: 0 1px #000;" title="Ir para o curso">'+certificado.coursename+'</a></h5>';
              item += `</div>
              <div class="card-body">
                <a href="`+certificateLink+`" class="btn btn-primary button-download" aria-label="Baixar seu certificado `+certificado.coursename+`" download>
                  <ion-icon name="fas-cloud-arrow-down"></ion-icon> &nbsp;&nbsp;
                  Baixar seu certificado
                </a>
              </div>
            </div>
          </div>`;

          lisToShow += item;

        } else if(certificates_pageNS.searchComponent.currentTab == 2){
          
          certificado.certificatename = certificates_pageNS.formatCertificateName(certificado.certificatename).replaceAll('_', ' - ');

          let item = `<div key="`+certificado.id+`"
            class="bubble col-12 col-sm-4 col-md-6 col-lg-4 col-xl-3">
            <div class="card">
              <div class="card-thumbnail">
                <img class="course-image"  src="`+certificado.image_url+`"
                  alt="">
                <div class="icon">
                  <img src="`+loader.medalSvg+`" alt="Ícone" />
                </div>
              </div>
              <div class="card-body">
                <h6 class="mb-2 text-center toUpperCase" title="`+certificado.trailname+`">`+certificado.trailname+`</h6>
                <h6 class="mb-5 text-center" title="`+certificado.certificatename+`">`+certificado.certificatename+`</h6>
                <a href="`+certificado.download_url+`" class="btn btn-primary button-download" aria-label="Baixar seu certificado `+certificado.trailname+` `+certificado.certificatename+`" download>
                  <ion-icon name="fas-cloud-arrow-down"></ion-icon> &nbsp;&nbsp; Baixar seu certificado
                  </a>
              </div>
            </div>
          </div>`;

          lisToShow += item;
        }
      });

      if(newList) {
        bodyCertificatesList.html(lisToShow);
      } else {
        bodyCertificatesList.append(lisToShow);
      }

      bodyCertificatesList.show();
    } else {
      bodyCertificatesList.hide();
    }
  }
};