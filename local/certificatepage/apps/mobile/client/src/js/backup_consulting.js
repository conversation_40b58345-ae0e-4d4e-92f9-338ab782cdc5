const blockNS = {
  init: (component) => {
    if(component == '') component = loader.component;
    cardNS.calc_card_height(component, 'courses-block');
  },
  
  update_courses: (component, args = {}) => {
    if(component == '') component = loader.component;
    if(args == undefined || args == '') args = loader.params;
    apiNS.get_courses(component, args)
      .then((result) => {
        apiNS.get_render_template({
          'template': 'local_courseblockapi/mobile/block-cards', 
          'parameters': {
            'data': JSON.stringify(JSON.stringify(result))
          }
        }).then(function (html) {
            let block = document.querySelector(`.${component} .courses-block .cards`);
            block.innerHTML = html;
            cardNS.calc_card_height(component, 'courses-block');
            return true;
          })
          .catch((error) => {
            console.error(error);
            ref.CoreToastsService.show({ message: '(update_courses) catch do render: '+error });
          });
      })
      .catch((error) => {
        console.error(error);
        ref.CoreToastsService.show({ message: '(update_courses) catch do get_courses: '+error });
      });
  
    return false;
  }
};