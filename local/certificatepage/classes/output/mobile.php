<?php

namespace local_certificatepage\output;

require(__DIR__ . '/../../../../config.php');

class mobile {

    public static function configChanges(){
        global $CFG;
        $CFG->dirroot = explode('/', dirname(__DIR__));
        $arrayKeyLocalCetificatePage = array_search('local', $CFG->dirroot);
        if($arrayKeyLocalCetificatePage && $CFG->dirroot[$arrayKeyLocalCetificatePage+1] == 'certificatepage') {
            $CFG->dirroot = implode('/', array_slice($CFG->dirroot, 0, $arrayKeyLocalCetificatePage));
            $CFG->libdir = $CFG->dirroot.'/lib';
            $CFG->localcachedir = $CFG->dataroot.'/localcache';
            $CFG->tempdir = $CFG->dataroot.'/temp';
        } else {
            unset($CFG->dirroot);
        }
        $CFG->forced_plugin_settings = array();
    }

    public static function mobile_view_local_certificatepage($args) {
        global $CFG;
        self::configChanges();

        $path = $args['tab'] ?: 1;
        $content = self::get_content($path);
        
        $css = file_get_contents($CFG->wwwroot . '/local/certificatepage/apps/mobile/client/styles-mobile.css');

        return [
            'templates' => [
                [
                    'id' => 'main-local_certificatepage',
                    'html' => '<style>' . $css . '</style>'.$content->text,
                ],
            ],
            'javascript' => $content->javascripts,
            'otherdata' => $content->otherdata,
        ];
    }

    public static function get_content($pathLoad = 1) {
        global $OUTPUT, $PAGE;

        if (!isloggedin() or isguestuser()) return;

        $params['title'] = 'local_certificatepage';
        $params['initial_tab'] = $pathLoad;
        $idMasterElement = 'mobile_certificatepage_root';
        $urlNoCertificates = $OUTPUT->image_url('courses', 'block_myoverview')->out();

        $text = <<<HTML
        <div id="{$idMasterElement}">
            <nav id="header" class="mobile_certificatepage_root">
                <div id="inline-nav" class="displayNoneInitial">
                <ul>
                    <li>
                        <a value="1" class="router-link-active router-link-exact-active router-link-active">
                            <ion-icon name="ribbon-outline"></ion-icon> Meus Certificados
                        </a>
                    </li>
                    <li>
                        <a value="2">
                        <ion-icon name="fas-stairs"></ion-icon> Certificados de Trilhas
                        </a>
                    </li>
                </ul>
                </div>
                <div id="select-nav" class="displayNoneInitial">
                <select class="custom-select">
                    <option value="1">Meus Certificados</option>
                    <option value="2">Certificados de Trilhas</option>
                </select>
                </div>
            </nav>
            <div id="body">
                <div id="listItems">
                </div>
                <div id="empty-list">
                    <div class="content">
                        <img class="" src="{$urlNoCertificates}" role="presentation">
                        <p>Nenhum Certificado</p>
                    </div>
                </div>
            </div>
        </div>
        HTML;
        

        $content = new \stdClass();
        $content->text = $text;
        $content->footer = '';

        $pathMedalSvg = $OUTPUT->image_url('icon-medal', 'local_certificatepage')->out(true);
        
        $content->otherdata = [
            "component" => '#'.$idMasterElement,
            "params" => json_encode($params),
            "medal_svg" => $pathMedalSvg
        ];
        $content->javascripts = self::get_mobile_javascripts();

        return $content;
    }

    public static function get_mobile_javascripts($page = 'index') {
        global $CFG;
        $jsNameFiles = [
            ['url' => $CFG->dirroot . '/local/certificatepage/apps/mobile/client/src/js/loader.js', 'title' => 'mobile-loader', 'page' => 'all'],
            ['url' => $CFG->dirroot . '/local/certificatepage/apps/mobile/client/src/js/jquery-3.7.1.min.js', 'title' => 'jQuery', 'page' => 'all'],
            ['url' => $CFG->dirroot . '/admin/tool/lfxp/amd/src/mobile/user.js', 'title' => 'user_tool_lfxp', 'page' => 'all'],
            ['url' => $CFG->dirroot . '/admin/tool/lfxp/amd/src/mobile/ajax.js', 'title' => 'ajax_tool_lfxp', 'page' => 'all'],
            ['url' => $CFG->dirroot . '/local/certificatepage/apps/mobile/client/src/js/api.js', 'title' => 'mobile-api', 'page' => 'all'],
            ['url' => $CFG->dirroot . '/local/certificatepage/apps/mobile/client/src/js/certificates_page.js', 'title' => 'mobile-certificates_page', 'call' => 'init', 'page' => 'all']
        ];
        $jsContent = "";
        $runFinalCalls = "";
        foreach ($jsNameFiles as $jsFile) {
           if($jsFile['page'] == $page || $jsFile['page'] == 'all'){
               if (file_exists($jsFile['url'])) {
                   $jsContent .= file_get_contents($jsFile['url']) . "\n";
                   if (isset($jsFile['call'])) {
                       $jsFileName = explode('/', $jsFile['url']);
                       $jsFileName = $jsFileName[count($jsFileName) - 1];
                       $jsFileName = explode('.', $jsFileName);
                       $jsFileName = $jsFileName[0] . 'NS';
                       $callInitFunction = $jsFile['call'];
                       $runFinalCalls .= <<<JS
                       {$jsFileName}.{$callInitFunction}();
                       JS;
                   }
               } else {
                   $titleFileErro = $jsFile['title'];
                   $titleBlockErro = 'Certificate_Page';
                   $jsContent .= <<<JS
                   (async function (t) {
                       const alert = await t.AlertController.create({
                       header: '{$titleFileErro}',
                       subHeader: 'For: {$titleBlockErro}',
                       message: 'Módulo do plugin não foi carregado!',
                       buttons: ['OK'],
                       });
                       await alert.present();
                   })(this);
                   JS;
               }
           }
        }
        $jsContent .= $runFinalCalls;
        $jsContent = str_replace(["\r", "\n", "\t"], '', $jsContent);
        return $jsContent;
    }
}