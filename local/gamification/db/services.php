<?php

defined('MOODLE_INTERNAL') || die();

$functions = [

    'local_gamification_get_product' => [
        'classname' => '\local_gamification\external\get_product',
        'methodname' => 'api',
        'description' => 'Returns a single produtc by id',
        'type' => 'read',
        'ajax' => true,
    ],

    'local_gamification_get_goal' => [
        'classname' => '\local_gamification\external\get_goal',
        'methodname' => 'api',
        'description' => 'Returns a single goal by id',
        'type' => 'read',
        'ajax' => true,
    ],


    'local_gamification_delete_user_draft_file' => [
        'classname' => '\local_gamification\external\delete_user_draft_file',
        'methodname' => 'api',
        'description' => 'Deletes a user draft file',
        'type' => 'write',
        'ajax' => true,
    ],

    'local_gamification_get_address' => [
        'classname' => '\local_gamification\external\get_address',
        'methodname' => 'api',
        'description' => 'Returns a single address by id',
        'type' => 'read',
        'ajax' => true,
    ],

    'local_gamification_get_address_app' => [
        'classname' => '\local_gamification\external\get_address',
        'methodname' => 'api',
        'description' => 'Returns a single address by id',
        'type' => 'read',
        'ajax' => true,
        'services' => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],

    'local_gamification_checkout_product_transaction' => [
        'classname' => '\local_gamification\external\checkout_product_transaction',
        'methodname' => 'api',
        'classpath'   => 'local/gamification/classes/external/checkout_product_transaction.php',
        'description' => 'Sell products to users',
        'type' => 'write',
        'ajax' => true,
        'services' => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],

    'local_gamification_ranking_app' => [
        'classname' => '\local_gamification\external\app_datas',
        'methodname' => 'ranking_app',
        'classpath'   => 'local/gamification/classes/external/app_datas.php',
        'description' => 'get ranking data for app',
        'type' => 'read',
        'ajax' => true,
        'services' => [MOODLE_OFFICIAL_MOBILE_SERVICE],
    ],
];
