<?php

namespace local_gamification\controllers;

use \local_gamification\controllers\abstract_controller;
use local_gamification\output\renderables\user_goals;
use local_gamification\repositories\goal_repository;
use local_gamification\repositories\order_repository;
use local_gamification\repositories\user_goal_repository;

class user_goals_controller extends abstract_controller
{
    const PAGE_SIZE = 10;
    const SORT = 'timecreated';

    protected goal_repository $repository;

    /**
     * Constructs a new instance of the class.
     *
     * @param goal_repository $repository The setting repository instance.
     */
    public function __construct(goal_repository $repository)
    {
        parent::__construct();

        $this->repository = $repository;

        $this->update_repository_params();
    }

    protected function init_common_params()
    {
        // Pagination and sorting
        $this->params['page'] = optional_param('page', 0, PARAM_INT);
        $this->params['pagesize'] = optional_param('pagesize', self::PAGE_SIZE, PARAM_INT);
        $this->params['sort'] = optional_param('tsort', self::SORT, PARAM_TEXT);
        $this->params['order'] = optional_param('tdir', SORT_DESC, PARAM_INT);
    }

    protected function init_common_params_mobile($params){
        // Pagination and sorting - mobile
        $this->params['page'] = $params['paginationPage'] ?? 0;
        $this->update_repository_params();
    }

    protected function common_security_verifications()
    {
        require_login();
    }

    protected function apply_common_page_configuration()
    {
        // Title
        $this->page->set_title(get_string('goal:title', 'local_gamification'));

        // Breadcrumbs
        $this->add_breadcrumb_item();
        $this->add_current_breadcrumb_item('goal:goals');

        $this->page->requires->css('/local/gamification/styles.css');

        $this->page->requires->js_call_amd('local_gamification/user_goals', 'init');
    }

    public function update_repository_params()
    {
        $this->repository->set_page($this->params['page']);
        $this->repository->set_pagesize($this->params['pagesize']);
    }

    public function view($isMobile = false, $params = [])
    {
        global $USER;

        if($isMobile) $this->init_common_params_mobile($params);

        $data = $this->repository->get_user_paginted_data($this->user->id);
        $total = $this->repository->get_user_paginted_total($this->user->id);

        // Init renderable
        $renderable = new user_goals($isMobile);
        $renderable->set_url($this->get_url());
        $renderable->set_user($USER);
        $renderable->set_data($data, $total, $this->params['pagesize']);
        $renderable->set_pagination($this->params['page']);
        $renderable->set_returnurl($this->get_returnto_url('index'));

        // Display content
        $html = $this->renderer->render($renderable);
        if($isMobile) {
            return $html;
        } else {
            $this->output($html);
        }
    }
}
