<?php

namespace local_gamification\controllers\store;

use core\plugininfo\mod;
use \local_gamification\controllers\abstract_controller;
use local_gamification\forms\manager\delivery_form;
use local_gamification\output\renderables\store\checkout;
use local_gamification\repositories\order_repository;
use local_gamification\repositories\product_repository;
use local_gamification\repositories\transaction_repository;
use local_gamification\repositories\user_address_repository;
use local_gamification\services\order_service;
use local_gamification\services\product_service;
use moodle_exception;
use stdClass;

class checkout_controller extends abstract_controller
{
    protected order_service $service;

    protected order_repository $repository;

    protected product_repository $product_repository;

    protected transaction_repository $transaction_repository;

    protected user_address_repository $user_address_repository;

    /**
     * Constructs a new instance of the class.
     *
     * @param order_service $service The setting service instance.
     * @param order_repository $repository The setting repository instance.
     */
    public function __construct(order_service $service, order_repository $repository, product_repository $product_repository, transaction_repository $transaction_repository, user_address_repository $user_address_repository)
    {
        parent::__construct();

        $this->service = $service;
        $this->repository = $repository;
        $this->product_repository = $product_repository;
        $this->transaction_repository = $transaction_repository;
        $this->user_address_repository = $user_address_repository;
    }

    protected function init_common_params()
    {
        $this->params['productid'] = optional_param('productid', NULL, PARAM_INT);
    }

    protected function init_common_params_mobile($params){
        // product id to checkout - mobile
        $this->params['productid'] = $params['productid'] ?? 0;
    }

    protected function common_security_verifications()
    {
        require_login();
    }

    protected function apply_common_page_configuration()
    {
        // Title
        $this->page->set_title(get_string('store:checkout:title', 'local_gamification'));

        // Breadcrumbs
        $this->add_breadcrumb_item();
        $this->add_breadcrumb_item('store:title', $this->get_returnto_url('store_index'));
        $this->add_current_breadcrumb_item('store:checkout:title');

        $this->page->requires->css('/local/gamification/styles.css');

        $this->page->requires->js_call_amd('local_gamification/store/checkout', 'init');
    }

    public function view($isMobile = false, $params = [])
    {
        global $OUTPUT;

        if($isMobile) $this->init_common_params_mobile($params);

        if (empty($this->params['productid'])) {
            if($isMobile) {
                return $OUTPUT->render_from_template('local_gamification/mobile/error_template', ['message' => get_string('store:checkout:missing_product', 'local_gamification')]);
            } else {
                redirect($this->get_returnto_url('store_index'), get_string('store:checkout:missing_product', 'local_gamification'), null, \core\output\notification::NOTIFY_ERROR);
            }
        }

        if (!$this->user_has_sufficient_balance()) {
            if($isMobile) {
                return $OUTPUT->render_from_template('local_gamification/mobile/error_template', ['message' => get_string('store:not_enough_coins', 'local_gamification')]);
            } else {
                redirect($this->get_returnto_url('store_index'), get_string('store:not_enough_coins', 'local_gamification'), null, \core\output\notification::NOTIFY_ERROR);
            }
        }

        $customdata = $this->get_customdata();
        $form = new delivery_form($this->get_url(), $customdata);
        $this->process_page_form_submit($form);

        // Init renderable
        $renderable = new checkout($this->product_repository, $this->transaction_repository, $isMobile);
        $renderable->set_form($form);
        $renderable->set_url($this->get_url());
        $renderable->set_returnurl($this->get_returnto_url('store_index'));
        $renderable->set_productid($this->params['productid']);

        // Display content
        $html = $this->renderer->render($renderable);
        if($isMobile) {
            return $html;
        } else {
            $this->output($html);
        }
    }

    protected function process_page_form_submit($form)
    {
        global $USER;

        if ($form->is_cancelled()) {
            redirect($this->get_returnto_url('store_index'));
        } elseif ($data = $form->get_data()) {
            try {
                $data->userid = $USER->id;

                $item = new stdClass();
                $item->productid = $this->params['productid'];
                $item->quantity = 1;

                $data->items = [$item];
                $order = $this->service->create($data);

                $stringparams = ['code' => $order->get('code'), 'link' => $this->get_returnto_url('my_purchases')->out()];

                redirect($this->get_returnto_url('store_index'), get_string('store:checkout:creation_message', 'local_gamification', $stringparams), null, \core\output\notification::NOTIFY_SUCCESS);
            } catch (moodle_exception $e) {
                // dd($e);
                redirect($this->get_url(), $e->getMessage(), null, \core\output\notification::NOTIFY_ERROR);
            }
        }
    }

    public function process_page_form_submit_from_mobile($dataRequest) {
        global $USER;

        if (empty($dataRequest['productid'])) {
            return ['success' => false, 'message' => get_string('store:checkout:missing_product', 'local_gamification')];
        }

        $this->init_common_params_mobile($dataRequest);
        if (!$this->user_has_sufficient_balance()) {
            return ['success' => false, 'message' => get_string('store:not_enough_coins', 'local_gamification')];
        }

        $customdata = $this->get_customdata();
        $form = new delivery_form($this->get_url(), $customdata);

        $validacaoFields = $form->validation($dataRequest, null);
        if(count($validacaoFields) > 0) return ['success' => false, 'message' => get_string('validation:store:checkout:invalid_fields', 'local_gamification'), 'data' => ['invalid' => $validacaoFields]];

        if ($data = (object) $dataRequest) {
            try {
                $data->userid = $USER->id;

                $item = new stdClass();
                $item->productid = $dataRequest['productid'];
                $item->quantity = 1;

                $data->items = [$item];
                $order = $this->service->create($data);

                $stringparams = ['code' => $order->get('code'), 'link' => $this->get_returnto_url('my_purchases')->out()];
                $retString = get_string('store:checkout:creation_message_app', 'local_gamification', $stringparams);
                $stringparams['toGo'] = ['link' => 'store/my_purchases', 'btnText' => get_string('store:my_purchases:title', 'local_gamification')];
                $stringparams['title'] = get_string('store:my_purchases:title', 'local_gamification', $stringparams);
                $stringparams['onDoneText'] = get_string('store:checkout:onDoneAction_app', 'local_gamification', $stringparams);

                return ['success' => true, 'data' => $stringparams, 'message' => $retString];
            } catch (moodle_exception $e) {
                return ['success' => false, 'message' =>'Erro no WS de checkout!', 'data' => $e];
            }
        } else {
            return ['success' => false, 'message' =>'Erro no WS de checkout! (No data)', 'data' => ''];
        }
    }

    /**
     * Gets custom data for form.
     *
     * @return array
     */
    protected function get_customdata()
    {
        $options = [0 => 'Adicionar novo endereço'];
        $currentaddress = NULL;
        $addresses = $this->user_address_repository->get_addresses_by_user($this->user->id);

        foreach ($addresses as $key => $address) {
            if ($key === 0) {
                $currentaddress = $address->to_record();
            }

            $options[$address->get('id')] = $address->get('street') . ", " . $address->get('number') . " - " . $address->get('city') . " - " . $address->get('state');
        }

        $customdata = [];
        $customdata['product'] = $this->product_repository->find($this->params['productid']);
        $customdata['addresses'] = $options;
        $customdata['current'] = $currentaddress ?? new stdClass();

        return $customdata;
    }


    /**
     * Verifies if the user has sufficient balance to buy the product.
     *
     * @return bool
     */
    public function user_has_sufficient_balance(): bool
    {
        global $USER;

        $product = $this->product_repository->find($this->params['productid']);
        $balance = $this->transaction_repository->get_balance($USER->id);

        return $balance['coins'] >= $product->get('price');
    }
}
