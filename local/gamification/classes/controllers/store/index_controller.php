<?php

namespace local_gamification\controllers\store;

use \local_gamification\controllers\abstract_controller;
use local_gamification\output\renderables\store\store;
use local_gamification\repositories\order_repository;
use local_gamification\repositories\product_repository;
use local_gamification\repositories\transaction_repository;
use local_gamification\services\order_service;
use local_gamification\services\product_service;

class index_controller extends abstract_controller
{
    const PAGE_SIZE = 3;

    protected product_service $service;

    protected product_repository $repository;

    protected transaction_repository $transaction_repository;

    /**
     * Constructs a new instance of the class.
     *
     * @param order_service $service The setting service instance.
     * @param order_repository $repository The setting repository instance.
     */
    public function __construct(product_service $service, product_repository $repository, transaction_repository $transaction_repository)
    {
        parent::__construct();

        $this->service = $service;
        $this->repository = $repository;
        $this->transaction_repository = $transaction_repository;
    }

    protected function init_common_params()
    {
        $this->params['view'] = optional_param('view', NULL, PARAM_RAW);
    }

    protected function common_security_verifications()
    {
        require_login();
    }

    protected function apply_common_page_configuration()
    {
        // Title
        $this->page->set_title(get_string('store:title', 'local_gamification'));

        // Breadcrumbs
        $this->add_breadcrumb_item();
        $this->add_current_breadcrumb_item('store:title');

        $this->page->requires->css('/local/gamification/styles.css');

        $this->page->requires->js_call_amd('local_gamification/store/index', 'init');
    }

    public function view($isMobile = false)
    {
        // Init renderable
        $renderable = new store($this->repository, $this->transaction_repository, $isMobile);
        $renderable->set_url($this->get_url());
        $renderable->set_returnurl($this->get_returnto_url('index'));

        // Display content
        $html = $this->renderer->render($renderable);
        if($isMobile) {
            return $html;
        } else {
            $this->output($html);
        }
    }
}
