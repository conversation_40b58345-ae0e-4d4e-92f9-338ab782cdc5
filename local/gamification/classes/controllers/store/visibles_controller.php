<?php

namespace local_gamification\controllers\store;

use \local_gamification\controllers\abstract_controller;
use local_gamification\output\renderables\store\visibles;
use local_gamification\repositories\order_repository;
use local_gamification\repositories\product_repository;
use local_gamification\repositories\transaction_repository;
use local_gamification\services\order_service;
use local_gamification\services\product_service;
use moodle_url;

class visibles_controller extends abstract_controller
{
    const PAGE_SIZE = 10;

    protected product_service $service;

    protected product_repository $repository;

    protected transaction_repository $transaction_repository;

    /**
     * Constructs a new instance of the class.
     *
     * @param order_service $service The setting service instance.
     * @param order_repository $repository The setting repository instance.
     */
    public function __construct(product_service $service, product_repository $repository, transaction_repository $transaction_repository)
    {
        parent::__construct();

        $this->service = $service;
        $this->repository = $repository;
        $this->transaction_repository = $transaction_repository;

        $this->update_repository_params();
    }

    protected function init_common_params()
    {
        // Pagination and sorting
        $this->params['returnurl'] = optional_param('returnurl', NULL, PARAM_URL);

        $this->params['page'] = optional_param('page', 0, PARAM_INT);
        $this->params['pagesize'] = optional_param('pagesize', self::PAGE_SIZE, PARAM_INT);
    }

    protected function common_security_verifications()
    {
        require_login();
    }

    protected function apply_common_page_configuration()
    {
        // Title
        $this->page->set_title(get_string('store:visibles:title', 'local_gamification'));

        // Breadcrumbs
        $this->add_breadcrumb_item();
        $this->add_breadcrumb_item('store:title', $this->get_returnto_url('store_index'));
        $this->add_current_breadcrumb_item('store:visibles:title');

        $this->page->requires->css('/local/gamification/styles.css');

        $this->page->requires->js_call_amd('local_gamification/store/visibles', 'init');
    }

    public function update_repository_params()
    {
        $this->repository->set_page($this->params['page']);
        $this->repository->set_pagesize($this->params['pagesize']);
    }

    public function view($isMobile = false)
    {
        $returnurl = $this->params['returnurl'] ? new moodle_url($this->params['returnurl']) : $this->get_returnto_url('store_index');

        $data = $this->repository->get_visibles_paginated();
        $total = $this->repository->get_visibles_total();

        // Init renderable
        $renderable = new visibles($this->repository, $this->transaction_repository, $isMobile);
        $renderable->set_url($this->get_url());
        $renderable->set_data($data, $total, $this->params['pagesize']);
        $renderable->set_pagination($this->params['page']);
        $renderable->set_returnurl($returnurl);

        // Display content
        $html = $this->renderer->render($renderable);
        if($isMobile) {
            return $html;
        } else {
            $this->output($html);
        }
    }
}
