<?php

namespace local_gamification\controllers\store;

use \local_gamification\controllers\abstract_controller;
use local_gamification\output\renderables\store\transactions;
use local_gamification\output\renderables\store\transactions_table;
use local_gamification\repositories\order_repository;
use local_gamification\repositories\setting_repository;
use local_gamification\repositories\transaction_repository;

class transactions_controller extends abstract_controller
{
    const PAGE_SIZE = 10;
    const SORT = 'timecreated';

    protected transaction_repository $repository;
    protected setting_repository $setting_repository;

    /**
     * Constructs a new instance of the transactions_controller class.
     *
     * @param transaction_repository $repository The transaction repository instance.
     * @param setting_repository $setting_repository The setting repository instance.
     */
    public function __construct(transaction_repository $repository, setting_repository $setting_repository)
    {
        parent::__construct();

        $this->repository = $repository;

        $this->setting_repository = $setting_repository;

        $this->update_repository_params();
    }

    protected function init_common_params()
    {
        // Pagination and sorting
        $this->params['page'] = optional_param('page', 0, PARAM_INT);
        $this->params['pagesize'] = optional_param('pagesize', self::PAGE_SIZE, PARAM_INT);
        $this->params['sort'] = optional_param('tsort', self::SORT, PARAM_TEXT);
        $this->params['order'] = optional_param('tdir', SORT_DESC, PARAM_INT);
    }

    protected function init_common_params_mobile($params){
        // Pagination and sorting - mobile
        $this->params['page'] = $params['paginationPage'] ?? 0;
        $this->params['pagesize'] = $params['pagesize'] ?? 15;
        $this->params['sort'] = $params['tsort'] ?? '';
        $this->params['order'] = $params['tdir'] ?? 0;
        $this->update_repository_params();
    }

    protected function common_security_verifications()
    {
        require_login();
    }

    protected function apply_common_page_configuration()
    {
        // Title
        $this->page->set_title(get_string('store:transactions:title', 'local_gamification'));

        // Breadcrumbs
        $this->add_breadcrumb_item();
        // $this->add_breadcrumb_item('store:title', $this->get_returnto_url('store_index'));
        $this->add_current_breadcrumb_item('store:transactions:title');

        $this->page->requires->css('/local/gamification/styles.css');

        $this->page->requires->js_call_amd('local_gamification/store/transactions', 'init');
    }

    public function update_repository_params()
    {
        $this->repository->set_page($this->params['page']);
        $this->repository->set_pagesize($this->params['pagesize']);
        $this->repository->set_sort($this->params['sort'] ?? 'timecreated');
        $this->repository->set_order($this->params['order']);
    }

    public function view($isMobile = false, $params = [])
    {

        if($isMobile) $this->init_common_params_mobile($params);

        $data = $this->repository->get_paginted_data($this->user->id);
        $total = $this->repository->get_paginted_total($this->user->id);

        $table = transactions_table::create();
        $table->set_url($this->get_url());
        $table->set_mobile_platform($isMobile);
        $table->set_setting_repository($this->setting_repository);
        $table->set_data($data, $total, $this->params['pagesize']);
        $table->set_pagination($this->params['page']);

        // Init renderable
        $renderable = new transactions($this->repository, $isMobile);
        $renderable->set_url($this->get_url());
        $renderable->set_table($table);
        $renderable->set_returnurl($this->get_returnto_url('index'));

        // Display content
        $html = $this->renderer->render($renderable);
        if($isMobile) {
            return $html;
        } else {
            $this->output($html);
        }
    }
}
