<?php

namespace local_gamification\controllers;

use \local_gamification\controllers\abstract_controller;
use local_gamification\models\setting;
use local_gamification\models\transaction;
use local_gamification\output\renderables\index;
use local_gamification\output\renderables\user_goals;
use local_gamification\repositories\goal_repository;
use local_gamification\repositories\rank_repository;
use local_gamification\repositories\setting_repository;
use local_gamification\repositories\transaction_repository;

class index_controller extends abstract_controller
{
    const PAGE_SIZE = 10;
    const SORT = 'timecreated';

    protected goal_repository $goal_repository;
    protected transaction_repository $transaction_repository;
    protected rank_repository $rank_repository;
    protected setting_repository $setting_repository;

    /**
     * Constructs a new instance of the class.
     *
     * @param goal_repository $goal_repository The setting repository instance.
     */
    public function __construct(goal_repository $goal_repository, transaction_repository $transaction_repository, rank_repository $rank_repository, setting_repository $setting_repository)
    {
        parent::__construct();

        $this->goal_repository = $goal_repository;
        $this->transaction_repository = $transaction_repository;
        $this->rank_repository = $rank_repository;
        $this->setting_repository = $setting_repository;

        $this->update_repository_params();
    }

    protected function init_common_params()
    {
        $this->params['level'] = optional_param('level', NULL, PARAM_INT);
    }

    protected function common_security_verifications()
    {
        require_login();
    }

    protected function apply_common_page_configuration()
    {
        // Title
        $this->page->set_title(get_string('pluginname', 'local_gamification'));

        // Breadcrumbs
        $this->add_breadcrumb_item();

        $this->page->requires->css('/local/gamification/styles.css');

        $this->page->requires->js_call_amd('local_gamification/index', 'init');
    }

    public function update_repository_params()
    {
        // $this->repository->set_page($this->params['page']);
        // $this->repository->set_pagesize($this->params['pagesize']);
    }

    public function view($isMobile = false)
    {
        global $USER;

        // $data = $this->repository->get_user_paginted_data($this->user->id);
        // $total = $this->repository->get_user_paginted_total($this->user->id);

        // Init renderable
        $renderable = new index($this->goal_repository, $this->transaction_repository, $this->rank_repository, $this->setting_repository, $isMobile);
        $renderable->set_url($this->get_url());
        $renderable->set_user($USER);
        $renderable->set_level($this->params['level']);
        // $renderable->set_data($data, $total, $this->params['pagesize']);

        // Display content
        $html = $this->renderer->render($renderable);
        if($isMobile) {
            return $html;
        } else {
            $this->output($html);
        }
    }
}
