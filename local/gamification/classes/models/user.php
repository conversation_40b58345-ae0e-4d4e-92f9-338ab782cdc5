<?php

namespace local_gamification\models;

use \tool_lfxp\models\persistence\entity;
use user_picture;

class user extends entity
{
    const TABLE = 'user';

    protected static function track_dirty_fields(): bool
    {
        return false;
    }

    /**
     * Defines the properties of the class.
     *
     * @return array An array containing the properties of the class.
     */
    protected static function define_properties()
    {
        return [
            'username' => [
                'type' => PARAM_TEXT,
            ],
            'firstname' => [
                'type' => PARAM_TEXT,
            ],
            'lastname' => [
                'type' => PARAM_TEXT,
            ],
            'email' => [
                'type' => PARAM_EMAIL,
                'default' => 0,
            ],
            'phone1' => [
                'type' => PARAM_TEXT,
                'default' => '',
            ],
            'phone2' => [
                'type' => PARAM_TEXT,
                'default' => '',
            ],
            'picture' => [
                'type' => PARAM_INT,
                'default' => 0,
            ],
            'firstnamephonetic' => [
                'type' => PARAM_TEXT,
                'default' => '',
            ],
            'lastnamephonetic' => [
                'type' => PARAM_TEXT,
                'default' => '',
            ],
            'middlename' => [
                'type' => PARAM_TEXT,
                'default' => '',
            ],
            'alternatename' => [
                'type' => PARAM_TEXT,
                'default' => '',
            ],
            'imagealt' => [
                'type' => PARAM_TEXT,
                'default' => '',
            ],
        ];
    }

    /**
     * Return the full name of the user.
     *
     * @return string The full name of the user.
     */
    public function get_fullname()
    {
        return $this->raw_get('firstname') . ' ' . $this->raw_get('lastname');
    }

    /**
     * Returns the first non-empty phone number (phone2, phone1).
     *
     * @return string The phone number.
     */
    public function get_some_phone()
    {
        return $this->raw_get('phone2') ?: $this->raw_get('phone1');
    }

    /**
     * Return the user picture with the specified size
     *
     * @param int $size Size of the user picture
     *
     * @return string The user picture
     */
    public function get_user_picture($size = 35)
    {
        global $OUTPUT;

        return $OUTPUT->user_picture($this->to_record(), array('class' => 'userpicture', 'popup' => true, 'size' => $size));
    }

    /**
     * Return the user picture for mobile app
     */
    public function get_user_picture_mobile_app($size = 35)
    {
        /* return '<pre>'.print_r($this->to_record(), true).'</pre>';
        global $OUTPUT, $PAGE, $CFG;
        $CFG->forceloginforprofileimage = false;
        $user = (object) ['id' => $this->raw_get('id')];
        $userpicture = new user_picture($user);
        return '<img src="'.$userpicture->get_url($PAGE).'">'; */

        global $CFG;

        $userid = $this->raw_get('id');
        $context = \context_user::instance($userid);
        $contextid = $context->id;
        if ($this->raw_get('picture') == 0 && empty($CFG->enablegravatar)) {
            return \html_writer::tag('span', mb_substr($this->raw_get('firstname'), 0, 1) . mb_substr($this->raw_get('lastname'), 0, 1), ['class' => 'userinitials size-' . $size]);
        } else {
            return '<a href="'.$CFG->wwwroot.'/user/profile.php?id='.$userid.'" class="d-inline-block aabtn" id="userpicture"><img src="'.$CFG->wwwroot.'/local/gamification/imageprofile.php?id='.$userid.'" class="userpicture" width="'.$size.'" height="'.$size.'" alt=""></a>';
        }
    }
}
