<?php

namespace local_gamification\output;

require(__DIR__ . '/../../../../config.php');
use local_gamification\app\bootstrap;
use local_gamification\controllers\index_controller as IndexController;
use local_gamification\controllers\store\index_controller as StoreIndexController;
use local_gamification\controllers\store\cheapest_controller;
use local_gamification\controllers\user_goals_controller;
use local_gamification\controllers\store\checkout_controller;
use local_gamification\controllers\store\my_purchases_controller;
use local_gamification\controllers\store\popular_controller;
use local_gamification\controllers\store\transactions_controller;
use local_gamification\controllers\store\visibles_controller;

class mobile {

    public static function configChanges(){
        global $CFG;
        $CFG->dirroot = explode('/', dirname(__DIR__));
        $arrayKeyLocalGamification = array_search('local', $CFG->dirroot);
        if($arrayKeyLocalGamification && $CFG->dirroot[$arrayKeyLocalGamification+1] == 'gamification') {
            $CFG->dirroot = implode('/', array_slice($CFG->dirroot, 0, $arrayKeyLocalGamification));
            $CFG->libdir = $CFG->dirroot.'/lib';
            $CFG->localcachedir = $CFG->dataroot.'/localcache';
            $CFG->tempdir = $CFG->dataroot.'/temp';
        } else {
            unset($CFG->dirroot);
        }
        $CFG->forced_plugin_settings = array();
    }

    public static function mobile_view_local_gamification($args) {
        global $CFG;
        self::configChanges();

        // Parameters
        $productid = $args['productid'] ?: 0;
        $page_name = $args['page'] ?: 'index';
        if(isset($args['paginationPage'])){
            if($args['paginationPage'] > 0) {
                $paginationPage = $args['paginationPage']-1;
            } else {
                $paginationPage = 0;
            }
        }
        $page   = self:: url_container_maker($page_name);
        $params = compact('page_name', 'paginationPage', 'productid');

        $url = new \moodle_url($page['url'], []);
        $container = bootstrap::get_container();

        try {
            $controller = $container->make($page['container_make']);
            $controller->set_url($url);
            $html = $controller->view(true,$params);
        } catch (\Throwable $th) {
            return ['templates' => [['id' => 'main-local_gamification','html' => '<pre>'.print_r($th, true).' </br></br>'.print_r($CFG, true).'</pre>']]];
            //return ['templates' => [['id' => 'main-local_gamification','html' => '<p>Problemas no Carregamento do Conteúdo...</p>']]];
        }

        $idFromName = explode('/', $page_name);
        $idFromName = implode('-', $idFromName);

        $css = $page_name == 'index' ? file_get_contents($CFG->wwwroot . '/local/gamification/styles-mobile.css') : '';
        $js = self::get_mobile_javascripts($page_name);
        $otherdata = [
            'component' => 'Gamification',
            'master_id_component' => 'page-local-gamification-'.$idFromName,
            'params' => []
        ];

        return [
            'templates' => [
                [
                    'id' => 'main-local_gamification',
                    'html' => '<style>'.$css.'</style><div id="page-local-gamification-'.$idFromName.'" class="gamification-pages">'.$html.'</div>',
                ],
            ],
            'javascript' => $js,
            'otherdata' => json_encode($otherdata)
        ];
    }

    public static function get_mobile_javascripts($page = 'index') {
        global $CFG;
        $jsMobilePath = $CFG->dirroot . '/local/gamification/js_mobile/';
        $jsNameFiles = [
            ['url' => $jsMobilePath.'loader.js', 'title' => 'mobile-loader', 'page' => 'all'],
            ['url' => $jsMobilePath.'jquery-3.7.1.min.js', 'title' => 'mobile-jquery', 'page' => 'all'],
            ['url' => $jsMobilePath.'index.js', 'title' => 'mobile-index', 'call' => 'init', 'page' => 'index'],
            ['url' => $jsMobilePath.'glide-lazy.js', 'title' => 'mobile-glide', 'page' => 'store/index'],
            ['url' => $jsMobilePath.'glide-lazy.js', 'title' => 'mobile-glide', 'page' => 'store/my_purchases'],
            ['url' => $jsMobilePath.'store/store_index.js', 'title' => 'mobile-store-index', 'call' => 'init', 'page' => 'store/index'],
            ['url' => $jsMobilePath.'store/store_cheapest.js', 'title' => 'mobile-store-cheapest', 'call' => 'init', 'page' => 'store/cheapest'],
            ['url' => $jsMobilePath.'store/store_popular.js', 'title' => 'mobile-store-popular', 'call' => 'init', 'page' => 'store/popular'],
            ['url' => $jsMobilePath.'store/store_visibles.js', 'title' => 'mobile-store-visibles', 'call' => 'init', 'page' => 'store/visibles'],
            ['url' => $jsMobilePath.'imask-lazy.js', 'title' => 'mobile-imask', 'page' => 'store/checkout'],
            ['url' => $jsMobilePath.'store/store_repository.js', 'title' => 'mobile-store-repository', 'page' => 'store/checkout'],
            ['url' => $jsMobilePath.'store/store_checkout.js', 'title' => 'mobile-store-checkout', 'call' => 'init', 'page' => 'store/checkout'],
            ['url' => $jsMobilePath.'store/store_my_purchases.js', 'title' => 'mobile-store-my-purchases', 'call' => 'init', 'page' => 'store/my_purchases'],
        ];
        $jsContent = "";
        $runFinalCalls = "";
        foreach ($jsNameFiles as $jsFile) {
           if($jsFile['page'] == $page || $jsFile['page'] == 'all'){
               if (file_exists($jsFile['url'])) {
                   $jsContent .= file_get_contents($jsFile['url']) . "\n";
                   if (isset($jsFile['call'])) {
                       $jsFileName = explode('/', $jsFile['url']);
                       $jsFileName = $jsFileName[count($jsFileName) - 1];
                       $jsFileName = explode('.', $jsFileName);
                       $jsFileName = $jsFileName[0] . 'NS';
                       $callInitFunction = $jsFile['call'];
                       $runFinalCalls .= <<<JS
                       {$jsFileName}.{$callInitFunction}();
                       JS;
                   }
               } else {
                   $titleFileErro = $jsFile['title'];
                   $titleBlockErro = 'Gamification';
                   $jsContent .= <<<JS
                   (async function (t) {
                       const alert = await t.AlertController.create({
                       header: '{$titleFileErro}',
                       subHeader: 'For: {$titleBlockErro}',
                       message: 'Módulo do bloco não foi carregado!',
                       buttons: ['OK'],
                       });
                       await alert.present();
                   })(this);
                   JS;
               }
           }
        }
        $jsContent .= $runFinalCalls;
        $jsContent = str_replace(["\r", "\n", "\t"], '', $jsContent);
        return $jsContent;
    }

    public static function url_container_maker($page) {
        $pages = [
            'index' => [
                'url' => '/local/gamification/index.php',
                'container_make' => IndexController::class,
            ],
            'store/index' => [
                'url' => '/local/gamification/store/index.php',
                'container_make' => StoreIndexController::class,
            ],
            'goals' => [
                'url' => '/local/gamification/goals.php',
                'container_make' => user_goals_controller::class,
            ],
            'store/cheapest' => [
                'url' => '/local/gamification/store/cheapest.php',
                'container_make' => cheapest_controller::class,
            ],
            'store/checkout' => [
                'url' => '/local/gamification/store/checkout.php',
                'container_make' => checkout_controller::class,
            ],
            'store/my_purchases' => [
                'url' => '/local/gamification/store/my_purchases.php',
                'container_make' => my_purchases_controller::class,
            ],
            'store/popular' => [
                'url' => '/local/gamification/store/popular.php',
                'container_make' => popular_controller::class,
            ],
            'store/transactions' => [
                'url' => '/local/gamification/store/transactions.php',
                'container_make' => transactions_controller::class,
            ],
            'store/visibles' => [
                'url' => '/local/gamification/store/visibles.php',
                'container_make' => visibles_controller::class,
            ],
        ];
        return $pages[$page] ?? $pages['index'];
    }
}