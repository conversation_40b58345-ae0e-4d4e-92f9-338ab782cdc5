<?php

namespace local_gamification\output;

use local_gamification\output\renderables\index;
use local_gamification\output\renderables\manager\goals;
use local_gamification\output\renderables\manager\goals_table;
use local_gamification\output\renderables\manager\orders;
use local_gamification\output\renderables\manager\orders_table;
use local_gamification\output\renderables\manager\products;
use local_gamification\output\renderables\manager\settings;
use local_gamification\output\renderables\store\cheapest;
use local_gamification\output\renderables\store\checkout;
use local_gamification\output\renderables\store\my_purchases;
use local_gamification\output\renderables\store\my_purchases_table;
use local_gamification\output\renderables\store\popular;
use local_gamification\output\renderables\store\store;
use local_gamification\output\renderables\store\transactions;
use local_gamification\output\renderables\store\visibles;
use local_gamification\output\renderables\user_goals;
use paging_bar;
use \renderer_base;

class renderer extends renderer_base
{
    /**
     * Render the settings for the gamification manager.
     *
     * @param settings $renderable The settings to render.
     * @return The rendered output for the settings.
     */
    protected function render_settings(settings $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/manager/settings';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/manager/settings';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_products(products $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/manager/products';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/manager/products';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_orders(orders $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/manager/orders';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/manager/orders';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_orders_table(orders_table $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/manager/orders_table';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/manager/orders_table';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_goals(goals $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/manager/goals';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/manager/goals';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_goals_table(goals_table $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/manager/goals_table';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/manager/goals_table';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_paging_bar(paging_bar $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        return $OUTPUT->render_from_template('local_gamification/pagination', $data);
    }

    protected function render_store(store $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/store/index';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/store/index';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_popular(popular $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/store/popular';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/store/popular';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_visibles(visibles $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/store/visibles';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/store/visibles';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_cheapest(cheapest $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/store/cheapest';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/store/cheapest';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_checkout(checkout $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/store/checkout';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/store/checkout';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_my_purchases(my_purchases $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/store/my_purchases';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/store/my_purchases';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_my_purchases_table(my_purchases_table $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/store/my_purchases_table';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/store/my_purchases_table';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_transactions(transactions $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/store/transactions';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/store/transactions';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_user_goals(user_goals $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/user_goals';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/user_goals';
        return $OUTPUT->render_from_template($templateName, $data);
    }

    protected function render_index(index $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        $templateName = 'local_gamification/index';
        if($renderable->get_isMobile_instance()) $templateName = 'local_gamification/mobile/index';
        return $OUTPUT->render_from_template($templateName, $data);
    }
}
