<?php

namespace local_gamification\output\renderables;

use theme_smart\output\spark_paging_bar;
defined('MOODLE_INTERNAL') || die();

use local_gamification\output\renderables\base;
use local_gamification\repositories\user_goal_repository;
use local_gamification\utils\helper;
use moodle_url;
use paging_bar;
use \renderer_base;

global $CFG;

class user_goals extends base
{
    protected int $page = 0;
    protected int $total = 0;
    protected int $pagesize = 10;

    protected array $data = [];

    protected ?paging_bar $pagination = null;

    protected bool $isMobileInstance = false;

    public function __construct(bool $isMobileInstance = false) {
        $this->$isMobileInstance = $isMobileInstance;
        parent::__construct($this->$isMobileInstance);
    }

    /**
     * Export the data for the template.
     *
     * @param renderer_base $output The renderer instance.
     * @return array The exported data.
     */
    public function export_for_template(renderer_base $output)
    {
        global $USER;

        $data = parent::export_for_template($output);

        $data['goals'] = $this->get_data();

        $from = $this->total === 0 ? 0 : ($this->pagesize * $this->page) + 1;
        $to = ($this->pagesize * $this->page) + count($this->data);

        $data['has_goals'] = count($this->data) > 0;

        $data['pagesize'] = [
            'action' => $this->get_url(),
            'total' => $this->total,
            'showing' => get_string('goal:pagesize:info', 'local_gamification', ['from' => $from, 'to' => $to, 'total' => $this->total]),
            'options' => $this->get_pagesize_options(),
        ];

        if ($this->pagination && $this->isMobileInstance == false) {
            $data['pagination'] = $output->render($this->pagination);
        } else {
            $data['pagination'] =  self::mobile_pagination($this->total, $this->pagesize, $this->page, $this->get_url(), 'goals');
        }

        return $data;
    }

    /**
     * Return the data as an array of records.
     *
     * @param int $balancecoins The user's current balance of coins.
     * @return array The data as an array of records.
     */
    protected function get_data(): array
    {
        return $this->collection_to_records($this->data);
    }

    /**
     * Set the data for the component.
     *
     * @param array $data The goals to be displayed.
     * @param int $total The total number of goals.
     * @param int $pagesize The number of goals per page. Defaults to 5.
     */
    public function set_data(array $data, $total, $pagesize = 5): void
    {
        $this->data = $data;
        $this->total = $total;
        $this->pagesize = $pagesize;
    }

    /**
     * Set the user for the component.
     *
     * @param object $user The user object to be set.
     */
    public function set_user($user)
    {
        $this->user = $user;
    }

    /**
     * Convert a collection of goal to a record array.
     *
     * @param array $collection
     * @return array
     */
    protected function collection_to_records($collection): array
    {
        return array_map(function ($goal) {

            $cover = $this->get_cover_image($goal);
            $progress = $this->calculate_progress($goal);
            $is_completed = $this->is_goal_completed($goal);

            $goal = $goal->to_record();

            $goal->cover = $cover;
            $goal->progress = $progress;
            $goal->is_completed = $is_completed;

            return $goal;
        }, $collection);
    }

    /**
     * Get the cover image URL for a given goal.
     *
     * @param goal $goal The goal for which to get the cover image URL.
     * @return string The URL of the cover image, or a generated image URL if no image is set.
     */
    protected function get_cover_image($goal)
    {
        global $OUTPUT;

        $cover = helper::get_file_url($goal->get('id'), 'goal');

        return $cover ? $cover->out() : $OUTPUT->get_generated_image_for_id($goal->get('id'));
    }

    /**
     * Calculates the progress of a user on a given goal.
     *
     * @param goal $goal The goal to calculate the progress for.
     * @return string A string in the format 'X de Y' where X is the number of criteria completed and Y is the total number of criteria.
     */
    protected function calculate_progress($goal): string
    {
        $user_goal = $goal->get_user_goal($this->user->id);
        $from = $user_goal ? min($user_goal->get('total'), $goal->get('criteria_count')) : 0;
        $to = $goal->get('criteria_count');

        return "{$from} de {$to}";
    }

    /**
     * Check if the user has completed the given goal.
     *
     * @param goal $goal The goal to check.
     * @return bool True if the user has completed the goal, false otherwise.
     */
    protected function is_goal_completed($goal): bool
    {
        $user_goal = $goal->get_user_goal($this->user->id);
        return ($user_goal ? $user_goal->get('total') : 0) >= $goal->get('criteria_count');
    }

    /**
     * Sets the current page and creates a pagination bar.
     *
     * @param int $page The current page.
     */
    public function set_pagination($page)
    {
        $this->page = $page;
        $this->pagination = new spark_paging_bar($this->total, $this->page, $this->pagesize, $this->get_url());
    }

    /**
     * Returns an array of options for the pagesize select element.
     *
     * The options are determined based on the total number of rows in the table.
     * The available options are 5, 10, 25, 50, 100, and the total number of rows.
     *
     * @return array An array of pagesize options
     */
    protected function get_pagesize_options()
    {
        $options = [];
        $options[5] = ['value' => 5, 'text' => 5, 'selected' => $this->pagesize === 5];

        $availableoptions = [10, 25, 50, 100];

        foreach ($availableoptions as $option) {
            if ($this->total >= $option || $option === $this->pagesize) {
                $options[$option] = ['value' => $option, 'text' => $option, 'selected' => $this->pagesize === $option];
            }
        }

        sort($options);

        return $options;
    }
}
