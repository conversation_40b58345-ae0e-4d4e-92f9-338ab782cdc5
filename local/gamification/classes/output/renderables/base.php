<?php

namespace local_gamification\output\renderables;

defined('MOODLE_INTERNAL') || die();

use \renderable;
use \templatable;
use \context;
use \renderer_base;
use \moodle_url;

abstract class base implements renderable, templatable
{
    protected ?moodle_url $url = null;
    protected ?moodle_url $returnurl = null;
    protected int $pagesize = 8;

    protected object $user;

    protected array $extra_data = [];

    protected ?context $context;

    protected bool $isMobileInstance = false;

    protected function __construct(bool $isMobileInstance = false) {
        $this->isMobileInstance = $isMobileInstance;
    }

    /**
     * Creates a new instance instance
     *
     * @return static
     */
    public static function create(): static
    {
        $instance = new static();

        return $instance;
    }

    public function export_for_template(renderer_base $output)
    {
        $data = [];

        $data['returnurl'] = $this->get_returnurl();

        // Exporting header template data
        // $header = header::create_from_base_renderable($this);
        // $data['header'] = $header->export_for_template($output);

        return $data;
    }

    public function set_url(moodle_url $url)
    {
        $this->url = $url;
    }

    public function get_url(): ?moodle_url
    {
        return $this->url;
    }

    public function get_isMobile_instance(): bool
    {
        return $this->isMobileInstance;
    }

    public function set_context(context $context)
    {
        $this->context = $context;
    }

    public function get_context(): ?context
    {
        global $PAGE;

        if (empty($this->context) && $PAGE->context) {
            $this->set_context($PAGE->context);
        }

        return $this->context;
    }

    public function set_returnurl(moodle_url $url)
    {
        $this->returnurl = $url;
    }

    public function get_returnurl(): ?moodle_url
    {
        return $this->returnurl;
    }

    public function set_pagesize(int $pagesize = 8)
    {
        $this->pagesize = $pagesize;
    }

    public function get_pagesize(): int
    {
        return $this->pagesize;
    }

    public function mobile_pagination($totalitems, $perpage, $page, $baseurl, $pageName){
        global $OUTPUT;

        // Calcule as informações da paginação
        $totalpages = ceil($totalitems / $perpage);
        $haspages = $totalpages > 1;
        $pageFromOne = $page+1;

        // Inicialize as variáveis de paginação
        $pagination = array();
        $pagination['haspages'] = $haspages;
        $pagination['pagesize'] = $perpage;
        $pagination['label'] = 'Page';
        $pagination['pages'] = null;

        //previous
        if($pageFromOne <= 1 || $totalpages <= 3) {
            $pagination['previous'] = null;
        } else {
            $pagination['previous'] = [
                'page' => $pageFromOne - 1,
                'pageName' => $pageName
            ];
        }

        //next
        if($pageFromOne >= $totalpages || $totalpages <= 3) {
            $pagination['next'] = null;
        } else {
            $pagination['next'] = [
                'page' => $pageFromOne + 1,
                'pageName' => $pageName
            ];
        }

        //pages
        if($totalpages <= 3) {
            $pagination['pages'] = null;
            for ($pgs=1; $pgs <= $totalpages; $pgs++) {
                $activeSet = false;
                if($pageFromOne == $pgs) $activeSet = true;
                $pagination['pages'][] = [
                    "page" => $pgs,
                    "active" => $activeSet,
                    'pageName' => $pageName
                ];
            }
        } else {

            //first
            if($pageFromOne < 2 || $totalpages <= 3) {
                $pagination['first'] = null;
            } else if($pageFromOne == 3) {
                $pagination['pages'][] = [
                    "page" => 1,
                    "active" => false,
                    'pageName' => $pageName
                ];
            }  else if($pageFromOne > 3) {
                $pagination['first'] = [
                    'page' => 1,
                    'pageName' => $pageName
                ];
            }

            $minus = $pageFromOne > 1 ? $pageFromOne-1 : 1;
            $maxi = $pageFromOne < $totalpages ? $pageFromOne+1 : $totalpages;
            for ($pgs = $minus; $pgs <= $maxi; $pgs++) {
                $activeSet = false;
                if($pageFromOne == $pgs) $activeSet = true;
                $pagination['pages'][] = [
                    "page" => $pgs,
                    "active" => $activeSet,
                    'pageName' => $pageName
                ];
            }

            //last
            if($pageFromOne > ($totalpages - 2) || $totalpages <= 3) {
                $pagination['last'] = null;
            } else if($pageFromOne == ($totalpages - 2)) {
                $pagination['pages'][] = [
                    "page" => $totalpages,
                    "active" => false,
                    'pageName' => $pageName
                ];
            } else {
                $pagination['last'] = [
                    'page' => $totalpages,
                    'pageName' => $pageName
                ];
            }
        }

        return $OUTPUT->render_from_template('local_gamification/mobile/pagination', $pagination);

    }
}
