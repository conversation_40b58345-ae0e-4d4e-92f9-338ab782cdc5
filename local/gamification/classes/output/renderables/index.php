<?php

namespace local_gamification\output\renderables;

defined('MOODLE_INTERNAL') || die();

use local_gamification\models\rank;
use local_gamification\models\setting;
use local_gamification\models\transaction;
use local_gamification\models\user;
use local_gamification\output\renderables\base;
use local_gamification\repositories\goal_repository;
use local_gamification\repositories\rank_repository;
use local_gamification\repositories\setting_repository;
use local_gamification\repositories\transaction_repository;
use local_gamification\repositories\user_goal_repository;
use local_gamification\repositories\user_repository;
use local_gamification\utils\helper;
use moodle_url;
use paging_bar;
use \renderer_base;
use stdClass;

global $CFG;

class index extends base
{
    protected string $level;

    protected array $levels;

    protected setting $setting;

    protected bool $isMobileInstance = false;

    protected goal_repository $goal_repository;
    protected transaction_repository $transaction_repository;
    protected rank_repository $rank_repository;
    protected setting_repository $setting_repository;

    public function __construct(goal_repository $goal_repository, transaction_repository $transaction_repository, rank_repository $rank_repository, setting_repository $setting_repository, bool $isMobileInstance = false)
    {
        $this->goal_repository = $goal_repository;
        $this->rank_repository = $rank_repository;
        $this->setting_repository = $setting_repository;
        $this->transaction_repository = $transaction_repository;
        $this->isMobileInstance = $isMobileInstance;

        parent::__construct($this->isMobileInstance);

        $this->set_setting();
        $this->set_levels();
    }

    /**
     * Export the data for the template.
     *
     * @param renderer_base $output The renderer instance.
     * @return array The exported data.
     */
    public function export_for_template(renderer_base $output)
    {
        $data = parent::export_for_template($output);

        if (has_capability('local/gamification:managesettings', \context_system::instance())) {
            $data['manager_url'] = new moodle_url('/local/gamification/manager/settings.php');
        }

        $data['block_rank'] = $this->get_block_rank();
        $data['block_goals'] = $this->get_block_goals();

        $data['enabledstore'] = $this->setting->get('enablestore');
        $data['points_short_name'] = $this->setting->get('pointsshortname');
        $data['points_name'] = $this->setting->get('pointsname');
        $data['progress'] = $this->get_user_progress();
        $data['user'] = [
            'name' => $this->user->get_fullname(),
            'picture' => $this->isMobileInstance ? $this->user->get_user_picture_mobile_app(50) : $this->user->get_user_picture(50)
        ];

        $data['widgets'] = $this->get_widgets($data['progress']);

        return $data;
    }

    /**
     * Return the data for the widgets in the dashboard.
     *
     * @param array $progress The progress of the user.
     * @return array The data for the widgets.
     */
    protected function get_widgets($progress): array
    {
        $hours_wached = (new user_repository)->get_seconds_wached($this->user->get('id'));

        $balances = $this->transaction_repository->get_received_in_last_days($this->user->get('id'), 7);

        $enabledstore = $this->setting->get('enablestore');

        $coins = $enabledstore ? ['total' => $balances->get('coins')] : false;
        $hours = ['total' => round($hours_wached / HOURSECS)];
        $level = $progress['level'];
        $position = $progress['position'];
        $completions = ['percent' => $this->get_completions()];

        return compact('coins', 'hours', 'level', 'position', 'completions');
    }

    /**
     * Calculate the user's completion percentage based on course and trail completions.
     *
     * Retrieves the total number of course and trail enrollments and completions
     * for the user. It calculates the percentage of completions by dividing the
     * total completions by the total enrollments, then multiplies by 100.
     *
     * @return int The completion percentage as an integer.
     */
    public function get_completions(): int
    {
        $complete_course = $this->setting->get('reward_completedcourse_points');
        $complete_trail = $this->setting->get('reward_completedtrail_points');

        $total_enrollments = 0;
        $total_completions = 0;

        if ($complete_course) {
            $total_enrollments += local_gamification_get_total_course_enrollments($this->user->get('id'));
            $total_completions += local_gamification_get_total_course_completions($this->user->get('id'));
        }

        if ($complete_trail) {
            $total_enrollments += local_gamification_get_total_trail_enrollments($this->user->get('id'));
            $total_completions += local_gamification_get_total_trail_completions($this->user->get('id'));
        }

        if (!$total_enrollments) {
            return 0;
        }

        return round(($total_completions * 100) / $total_enrollments);
    }
    /**
     * Return the progress of the user.
     *
     * @return array The progress data:
     *      - balances: The current balance of the user.
     *      - position: The position of the user in the rank.
     *      - level: The level of the user.
     *      - progress_percent: The percent of progress of the user in the level.
     *      - points_for_next_level: The points needed to reach the next level.
     */
    protected function get_user_progress()
    {
        $balances = $this->transaction_repository->get_balance($this->user->get('id'));

        if (!$this->setting->get('enablestore')) {
            unset($balances['coins']);
        }

        $level = $this->rank_repository->get_level_of_user($this->user->get('id'));

        $progress_percent = $this->rank_repository->get_progress_percent($this->user->get('id'));

        $points_for_next_level = $this->rank_repository->get_points_for_next_level($this->user->get('id'));

        $position = $this->rank_repository->get_rank_position($this->user->get('id'));

        return compact('balances', 'position', 'level', 'progress_percent', 'points_for_next_level');
    }

    /**
     * Set the setting property of the class instance.
     *
     * The setting is retrieved from the setting repository and contains the
     * current configuration of the plugin.
     */
    protected function set_setting(): void
    {
        $this->setting = $this->setting_repository->get_instance();
    }

    /**
     * Sets the levels property of the class instance.
     *
     * The levels are retrieved from the rank repository and contain all the
     * levels available in the plugin.
     */
    protected function set_levels(): void
    {
        $this->levels = $this->rank_repository->get_levels();
    }

    /**
     * Retrieve the block rank data for display.
     *
     * This function fetches the range of levels and retrieves a collection of rank items
     * within that range, limited to 10. The rank items are processed into a data array
     * that includes information such as title, action URL, rank items, whether items exist,
     * levels, current level, and whether the max level is reached.
     *
     * @return array An associative array containing the block rank data.
     */
    protected function get_block_rank(): array
    {
        [$min, $max] = $this->rank_repository->get_range_of_level($this->level);
        $items = $this->rank_repository->get_rank($min, $max, 10);

        $data = [];
        $data['title'] = get_string('goal:goals', 'local_gamification');
        $data['action'] = $this->get_url();
        $data['items'] = $this->rank_collection_to_records($items, $min, $max);
        $data['has_items'] = count($data['items']) > 0;
        $data['levels'] = array_reverse($this->levels);
        $data['level'] = $this->level;
        $data['max_level'] = is_null($max) ? true : false;

        return $data;
    }

    /**
     * Maps a rank collection to a record array.
     *
     * This function takes a rank collection and maps it to an array of records.
     * The records are modified to include the user's id, fullname, and level percent.
     * The level percent is calculated using the min and max parameters.
     *
     * @param $collection array The rank collection.
     * @param $min int The min level.
     * @param $max int The max level.
     * @return array The rank records.
     */
    protected function rank_collection_to_records($collection, $min, $max): array
    {
        $position = 1;

        return array_map(function ($rank) use (&$position, $min, $max) {
            $user = $rank->get_user();
            $level_percent = $rank->get_level_percent($min, $max);
            $rank = $rank->to_record();

            $rank->position = $position++;
            $rank->userid = $user->get('id');
            $rank->user_picture = $this->isMobileInstance ?  $user->get_user_picture_mobile_app() : $user->get_user_picture();
            $rank->user_fullname = $user->get_fullname();
            $rank->level_percent = $level_percent;

            return $rank;
        }, $collection);
    }

    /**
     * Return the block goals data.
     *
     * This function returns the data for the goals block. The data includes the title of the block,
     * the page URL, the goals, and a boolean indicating if the user has goals.
     *
     * @return array The block goals data.
     */
    protected function get_block_goals(): array
    {
        $goals = $this->goal_repository->get_user_paginted_data($this->user->get('id'));

        $data = [];
        $data['title'] = get_string('goal:goals', 'local_gamification');
        $data['page_url'] = new moodle_url('/local/gamification/goals.php');
        $data['goals'] = $this->goal_collection_to_records($goals);
        $data['has_goals'] = count($data['goals']) > 0;

        return $data;
    }

    /**
     * Set the user for the component.
     *
     * @param object $user The user object to be set.
     */
    public function set_user(stdClass $user): void
    {
        $this->user = new user($user);
    }

    /**
     * Set the level for the current instance.
     *
     * If the provided level is empty, it defaults to the first key in the levels array.
     * Marks the specified level as selected within the levels array.
     *
     * @param null|int $level The level to set as active.
     */
    public function set_level(?int $level): void
    {
        if (is_null($level)) {
            $user_level = $this->rank_repository->get_level_of_user($this->user->get('id'));

            if (!is_null($user_level)) {
                $level = $user_level->key;
            } else {
                $level = array_key_last($this->levels);
            }
        }

        $this->level = $level;
        $this->levels[$level]->selected = true;
    }


    /**
     * Convert a collection of goal to a record array.
     *
     * @param array $collection
     * @return array
     */
    protected function goal_collection_to_records($collection): array
    {
        return array_map(function ($goal) {

            $cover = $this->get_goal_cover_image($goal);
            $progress = $this->calculate_goal_progress($goal);
            $is_completed = $this->is_goal_completed($goal);

            $goal = $goal->to_record();

            $goal->cover = $cover;
            $goal->progress = $progress;
            $goal->is_completed = $is_completed;

            return $goal;
        }, $collection);
    }

    /**
     * Get the cover image URL for a given goal.
     *
     * @param goal $goal The goal for which to get the cover image URL.
     * @return string The URL of the cover image, or a generated image URL if no image is set.
     */
    protected function get_goal_cover_image($goal)
    {
        global $OUTPUT;

        $cover = helper::get_file_url($goal->get('id'), 'goal');

        return $cover ? $cover->out() : $OUTPUT->get_generated_image_for_id($goal->get('id'));
    }

    /**
     * Calculates the progress of a user on a given goal.
     *
     * @param goal $goal The goal to calculate the progress for.
     * @return string A string in the format 'X de Y' where X is the number of criteria completed and Y is the total number of criteria.
     */
    protected function calculate_goal_progress($goal): string
    {
        $user_goal = $goal->get_user_goal($this->user->get('id'));
        $from = $user_goal ? min($user_goal->get('total'), $goal->get('criteria_count')) : 0;
        $to = $goal->get('criteria_count');

        return "{$from} de {$to}";
    }

    /**
     * Check if the user has completed the given goal.
     *
     * @param goal $goal The goal to check.
     * @return bool True if the user has completed the goal, false otherwise.
     */
    protected function is_goal_completed($goal): bool
    {
        $user_goal = $goal->get_user_goal($this->user->get('id'));
        return ($user_goal ? $user_goal->get('total') : 0) >= $goal->get('criteria_count');
    }
}
