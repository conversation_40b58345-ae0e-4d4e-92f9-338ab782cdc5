<?php

namespace local_gamification\output\renderables\manager;
use theme_smart\output\spark_paging_bar;

defined('MOODLE_INTERNAL') || die();

use html_writer;
use local_gamification\models\goal;
use local_gamification\output\renderables\base;
use local_gamification\utils\helper;
use moodle_url;
use paging_bar;
use \renderer_base;

global $CFG;

class goals_table extends base
{
    protected array $headers = [];
    protected array $sortables = [];

    protected $sort;
    protected $order;

    protected string $search = '';
    protected int $status;
    protected int $startdate;
    protected int $enddate;

    protected array $items = [];

    protected int $page = 0;
    protected int $total = 0;
    protected int $pagesize = 5;

    protected ?paging_bar $pagination = null;

    /**
     * Export the data for the template.
     *
     * @param renderer_base $output The renderer instance.
     * @return array The exported data.
     */
    public function export_for_template(renderer_base $output)
    {
        $data = [];

        $this->set_sortables(['name', 'visible']);
        $this->set_headers([
            'image' => get_string('goal:table:image', 'local_gamification'),
            'name' => get_string('goal:table:name', 'local_gamification'),
            'visible' => get_string('goal:table:status', 'local_gamification'),
            'actions' => get_string('goal:table:actions', 'local_gamification'),
        ]);

        $data['headers'] = $this->headers;

        $data['items'] = $this->items;

        $from = $this->total === 0 ? 0 : ($this->pagesize * $this->page) + 1;
        $to = ($this->pagesize * $this->page) + count($this->items);

        $data['pagesize'] = [
            'action' => $this->get_url(),
            'total' => $this->total,
            'showing' => get_string('goal:pagesize:info', 'local_gamification', ['from' => $from, 'to' => $to, 'total' => $this->total]),
            'options' => $this->get_pagesize_options(),
        ];

        if ($this->pagination) {
            $data['pagination'] = $output->render($this->pagination);
        }

        return $data;
    }

    public function set_filters(string $search, $status, $startdate, $enddate)
    {
        $this->search = $search;
        $this->status = $status;
        $this->startdate = $startdate;
        $this->enddate = $enddate;
    }

    public function set_data(array $items, $total, $pagesize = 5)
    {
        $this->set_items($items);
        $this->total = $total;
        $this->pagesize = $pagesize;
    }

    public function set_items(array $items)
    {
        foreach ($items as $item) {
            $this->items[] = $this->make_item($item);
        }
    }

    /**
     * Make a table item array from a goal model.
     *
     * @param goal $goal
     * @return array
     */
    public function make_item(goal $goal)
    {
        $description = strlen($goal->get('description')) > 150
            ? substr($goal->get('description'), 0, 150) . '...'
            : $goal->get('description');

        $timeslabel = $goal->get('criteria_count') > 1
            ? get_string('goal:times', 'local_gamification')
            : get_string('goal:time', 'local_gamification');

        $plugin = local_gamification_get_criteria_instance($goal->get('criteria'));
        $criteria = "{$plugin->get_title()} - {$goal->get('criteria_count')} $timeslabel";

        return [
            'uniqueId' => uniqid('table_'),
            'id' => $goal->get('id'),
            'cover' => $this->get_cover_tag($goal),
            'name' => $goal->get('name'),
            'description' => $description,
            'fullDescription' => $goal->get('description'),
            'status' => $this->get_status_tag($goal),
            'criteria' => $criteria,
            'deleteAction' => $this->get_delete_action($goal),
        ];
    }

    /**
     * Sets the headers for the table.
     *
     * @param array $headers The headers for the table.
     * @return void
     */
    public function set_headers(array $headers)
    {
        $index = 1;

        foreach ($headers as $key => $header) {
            $item = [
                'index' => $index,
                'key' => $key,
                'name' => $header
            ];

            if (in_array($key, $this->sortables)) {

                $url = new moodle_url($this->get_url());
                $url->params(['sort' => $key, 'order' => $this->order === SORT_ASC ? SORT_DESC : SORT_ASC]);

                $item['url'] = $url;
                $item['is_sortable'] = true;
                $item['is_sort_asc'] = $this->sort === $key && $this->order === SORT_ASC;
                $item['is_sort_desc'] = $this->sort === $key && $this->order === SORT_DESC;
            }

            $this->headers[] = $item;

            $index++;
        }
    }

    public function set_sort($sort, $order)
    {
        $this->sort = $sort;
        $this->order = $order;
    }

    public function set_sortables($headers)
    {
        $this->sortables = $headers;
    }

    /**
     * Sets the current page and creates a pagination bar.
     *
     * @param int $page The current page.
     */
    public function set_pagination($page)
    {
        $this->page = $page;
        $this->pagination = new spark_paging_bar($this->total, $this->page, $this->pagesize, $this->get_url());
    }

    /**
     * Returns a HTML span tag representing the status of the goal.
     * The color of the tag is based on the status of the goal.
     *
     * @param goal $goal
     * @return string
     */
    public function get_status_tag(goal $goal): string
    {
        switch ($goal->get('visible')) {
            case true:
                $statusclass = 'badge badge-primary';
                break;
            case false:
            default:
                $statusclass = 'badge badge-danger';
        }

        $status = $goal->get_visible_name();

        return html_writer::span($status, $statusclass);
    }

    public function get_cover_tag(goal $goal): string
    {
        global $OUTPUT;

        $cover = helper::get_file_url($goal->get('id'), 'goal');

        if ($cover) {
            $cover->out();
        } else {
            $cover = $OUTPUT->get_generated_image_for_id($goal->get('id'));
        }

        return html_writer::img($cover, $goal->get('name'), array('class' => 'goal-cover'));
    }

    /**
     * Returns an array of options for the pagesize select element.
     *
     * The options are determined based on the total number of rows in the table.
     * The available options are 5, 10, 25, 50, 100, and the total number of rows.
     *
     * @return array An array of pagesize options
     */
    protected function get_pagesize_options()
    {
        $options = [];
        $options[5] = ['value' => 5, 'text' => 5, 'selected' => $this->pagesize === 5];

        $availableoptions = [10, 25, 50, 100];

        foreach ($availableoptions as $option) {
            if ($this->total >= $option || $option === $this->pagesize) {
                $options[$option] = ['value' => $option, 'text' => $option, 'selected' => $this->pagesize === $option];
            }
        }

        sort($options);

        return $options;
    }


    /**
     * Gets the delete action for the given goal.
     *
     * @param goal $goal
     * @return string The HTML for the delete action
     */
    protected function get_delete_action(goal $goal)
    {
        global $OUTPUT;

        $url = new \moodle_url($this->get_url());
        $url->params(['sesskey' => sesskey(), 'goalid' => $goal->get('id'), 'action' => 'delete']);

        $action = new \confirm_action(get_string('goal:delete_message', 'local_gamification'));

        return $OUTPUT->action_link($url, get_string('goal:delete', 'local_gamification'), $action, [
            'class' => 'dropdown-item',
            'onclick' => 'event.preventDefault(); event.stopPropagation();'
        ]);
    }
}
