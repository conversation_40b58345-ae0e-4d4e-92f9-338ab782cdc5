<?php

namespace local_gamification\output\renderables\manager;
use theme_smart\output\spark_paging_bar;

defined('MOODLE_INTERNAL') || die();

use html_writer;
use local_gamification\models\order;
use local_gamification\models\order_status;
use local_gamification\models\product;
use local_gamification\output\renderables\base;
use local_gamification\utils\helper;
use moodle_url;
use paging_bar;
use \renderer_base;

global $CFG;

class orders_table extends base
{
    protected array $headers = [];

    protected string $search = '';
    protected int $status;
    protected int $startdate;
    protected int $enddate;

    protected array $items = [];

    protected int $page = 0;
    protected int $total = 0;
    protected int $pagesize = 5;

    protected ?paging_bar $pagination = null;

    /**
     * Export the data for the template.
     *
     * @param renderer_base $output The renderer instance.
     * @return array The exported data.
     */
    public function export_for_template(renderer_base $output)
    {
        $data = [];

        $this->set_headers([
            get_string('order:table:code', 'local_gamification'),
            get_string('order:table:timecreated', 'local_gamification'),
            get_string('order:table:user', 'local_gamification'),
            get_string('order:table:coins', 'local_gamification'),
            get_string('order:table:quantity', 'local_gamification'),
            get_string('order:table:status', 'local_gamification'),
        ]);

        $data['headers'] = $this->headers;

        $data['items'] = $this->items;

        $from = $this->total === 0 ? 0 : ($this->pagesize * $this->page) + 1;
        $to = ($this->pagesize * $this->page) + count($this->items);

        $data['pagesize'] = [
            'action' => $this->get_url(),
            'total' => $this->total,
            'showing' => get_string('order:pagesize:info', 'local_gamification', ['from' => $from, 'to' => $to, 'total' => $this->total]),
            'options' => $this->get_pagesize_options(),
        ];

        if ($this->pagination) {
            $data['pagination'] = $output->render($this->pagination);
        }

        return $data;
    }

    public function set_filters(string $search, $status, $startdate, $enddate)
    {
        $this->search = $search;
        $this->status = $status;
        $this->startdate = $startdate;
        $this->enddate = $enddate;
    }

    public function set_data(array $items, $total, $pagesize = 5)
    {
        $this->set_items($items);
        $this->total = $total;
        $this->pagesize = $pagesize;
    }

    public function set_items(array $items)
    {
        foreach ($items as $item) {
            $this->items[] = $this->make_item($item);
        }
    }

    public function make_item(order $order)
    {
        $items = [];

        $address_is_required = false;

        foreach ($order->get_items() as $item) {
            $product = $item->get_product();

            if ($product->get('delivery_method') === product::DELIVERY_METHOD_PHYSICAL) {
                $address_is_required = true;
            }

            $items[] = [
                'product' => $product->get('name'),
                'productcover' => $this->get_product_cover_tag($product),
                'type' => $this->get_product_type_tag($product),
                'quantity' => 'x' . $item->get('quantity'),
                'price' => $item->get('price'),
                'total' => $item->get('total'),
            ];
        }

        $address = $order->get_user_address();

        $user = $order->get_user();

        $userinfo = [
            'name' => $address->get('name'),
            'email' => $address->get('email'),
            'phone' => helper::format_phone($address->get('phone')),
        ];

        // Unable to update order status if order is completed
        if ($order->get('status') !== order_status::STATUS_COMPLETED) {
            $updatestatus = [
                'action' => $this->get_url(),
                'options' => $this->get_updatestatus_options($order),
            ];
        }

        return [
            'uniqid' => uniqid('table_'),
            'id' => $order->get('id'),
            'code' => html_writer::tag('b', '#' . $order->get('code')),
            'timecreated' => $order->get_timecreated_formatted(),
            'userpicture' => $user->get_user_picture() . $user->get_fullname(),
            'total' => $order->get('total'),
            'quantity' => $order->get_quantity(),
            'status' => $this->get_order_status_tag($order),
            'items' => $items,
            'address' => $address_is_required ? $address->to_record() : null,
            'user' => $userinfo,
            'updatestatus' => $updatestatus ?? null,
        ];
    }

    public function set_headers(array $headers)
    {
        foreach ($headers as $header) {
            $this->headers[] = ['name' => $header];
        }
    }

    public function set_pagination($page)
    {
        $this->page = $page;
        $this->pagination = new spark_paging_bar($this->total, $this->page, $this->pagesize, $this->get_url());
    }

    /**
     * Returns a HTML span tag representing the status of the order.
     * The color of the tag is based on the status of the order.
     *
     * @param order $order
     * @return string
     */
    public function get_order_status_tag(order $order): string
    {
        switch ($order->get('status')) {
            case order_status::STATUS_COMPLETED:
                $statusclass = 'badge badge-success';
                break;
            case order_status::STATUS_PROCESSING:
                $statusclass = 'badge badge-info';
                break;
            case order_status::STATUS_PENDING:
            default:
                $statusclass = 'badge badge-warning';
        }

        $status = $order->get_status_name();

        return html_writer::span($status, $statusclass);
    }

    public function get_product_cover_tag(product $product): string
    {
        global $OUTPUT;

        $cover = helper::get_product_cover_url($product->get('id'));

        if ($cover) {
            $cover->out();
        } else {
            $cover = $OUTPUT->get_generated_image_for_id($product->get('id'));
        }

        $cover = html_writer::img($cover, $product->get('name'), array('class' => 'product-cover'));

        return html_writer::link(new moodle_url($product->get('url')), $cover, array('target' => '_blank'));
    }

    /**
     * Returns a HTML string representing the type of a given product.
     * The returned string is a Bootstrap badge with a color depending on the type.
     *
     * @param product $product The product to get the type for.
     * @return string The HTML representation of the type.
     */
    public function get_product_type_tag(product $product): string
    {
        switch ($product->get('type')) {
            case product::TYPE_DIGITAL;
                $typeclass = 'badge badge-primary';
                break;
            case product::TYPE_PHYSICAL:
            default:
                $typeclass = 'badge badge-danger';
        }

        $type = $product->get_type_name();

        return html_writer::span($type, $typeclass);
    }

    /**
     * Returns an array of options for the pagesize select element.
     *
     * The options are determined based on the total number of rows in the table.
     * The available options are 5, 10, 25, 50, 100, and the total number of rows.
     *
     * @return array An array of pagesize options
     */
    protected function get_pagesize_options()
    {
        $options = [];
        $options[5] = ['value' => 5, 'text' => 5, 'selected' => $this->pagesize === 5];

        $availableoptions = [10, 25, 50, 100];

        foreach ($availableoptions as $option) {
            if ($this->total >= $option || $option === $this->pagesize) {
                $options[$option] = ['value' => $option, 'text' => $option, 'selected' => $this->pagesize === $option];
            }
        }

        sort($options);

        return $options;
    }

    /**
     * Returns an array of options for the status select element.
     *
     * The available options are the statuses defined in the order model.
     * The selected option is the current status of the given order.
     *
     * @param order $order The order to get the status options for.
     *
     * @return array An array of status options
     */
    protected function get_updatestatus_options(order $order)
    {
        $options = order_status::STATUSES;

        foreach ($options as $key => $value) {
            $options[$key] = ['value' => $key, 'text' => $order->get_status_name($value), 'selected' => $order->get('status') === $key];
        }

        return array_values($options);
    }
}
