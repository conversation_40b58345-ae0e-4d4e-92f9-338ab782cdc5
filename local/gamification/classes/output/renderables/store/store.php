<?php

namespace local_gamification\output\renderables\store;

defined('MOODLE_INTERNAL') || die();

use local_gamification\output\renderables\base;
use local_gamification\repositories\order_repository;
use local_gamification\repositories\product_repository;
use local_gamification\repositories\transaction_repository;
use local_gamification\utils\helper;
use moodle_url;
use \renderer_base;

global $CFG;

class store extends base
{
    protected product_repository $repository;
    protected transaction_repository $transaction_repository;

    protected bool $isMobileInstance = false;

    public function __construct(product_repository $repository, transaction_repository $transaction_repository, bool $isMobileInstance = false)
    {
        $this->repository = $repository;
        $this->transaction_repository = $transaction_repository;
        $this->$isMobileInstance = $isMobileInstance;
        parent::__construct($this->$isMobileInstance);
    }

    /**
     * Export the data for the template.
     *
     * @param renderer_base $output The renderer instance.
     * @return array The exported data.
     */
    public function export_for_template(renderer_base $output)
    {
        global $USER;

        $data = parent::export_for_template($output);

        $data['balances'] = $this->transaction_repository->get_balance($USER->id);

        $data['blocks'] = $this->get_blocks($data['balances']['coins']);

        return $data;
    }

    protected function get_blocks($balancecoins): array
    {
        $blocks = [];

        $popular   = $this->repository->get_popular_paginated();
        $visibles  = $this->repository->get_visibles_paginated();
        $cheapest  = $this->repository->get_cheapest_paginated();

        $blocks[] = [
            'id'       => "glide-popular",
            'title'    => get_string('store:popular:title', 'local_gamification'),
            'products' => $this->collection_to_records($popular, $balancecoins),
            'has_products' => count($popular) > 0,
            'page_url' => new moodle_url('/local/gamification/store/popular.php'),
            'app_url' => 'store/popular'
        ];

        $blocks[] = [
            'id'       => "glide-visibles",
            'title'    => get_string('store:visibles:title', 'local_gamification'),
            'products' => $this->collection_to_records($visibles, $balancecoins),
            'has_products' => count($visibles) > 0,
            'page_url' => new moodle_url('/local/gamification/store/visibles.php'),
            'app_url' => 'store/visibles'
        ];

        $blocks[] = [
            'id'       => "glide-cheapest",
            'title'    => get_string('store:cheapest:title', 'local_gamification'),
            'products' => $this->collection_to_records($cheapest, $balancecoins),
            'has_products' => count($cheapest) > 0,
            'page_url' => new moodle_url('/local/gamification/store/cheapest.php'),
            'app_url' => 'store/cheapest'
        ];

        return $blocks;
    }

    /**
     * Convert a collection of products to a record array.
     *
     * @param array $collection
     * @param int $balancecoins
     * @return array
     */
    protected function collection_to_records($collection, $balancecoins): array
    {
        return array_map(function ($product) use ($balancecoins) {
            global $OUTPUT;

            $cover = helper::get_product_cover_url($product->get('id'));

            if ($cover) {
                $cover->out();
            } else {
                $cover = $OUTPUT->get_generated_image_for_id($product->get('id'));
            }

            $checkout_url = new moodle_url('/local/gamification/store/checkout.php', ['productid' => $product->get('id')]);

            $productIdApp = $product->get('id');

            $canbuy = $balancecoins >= $product->get('price');

            $product = $product->to_record();

            $product->cover = $cover;

            $product->canbuy = $canbuy;

            $product->checkout_url = $checkout_url;

            $product->app_checkout_url = 'store/checkout';
            $product->app_productid = $productIdApp;

            return $product;
        }, $collection);
    }
}
