<?php

namespace local_gamification\output\renderables\store;
use theme_smart\output\spark_paging_bar;

defined('MOODLE_INTERNAL') || die();

use local_gamification\output\renderables\base;
use local_gamification\repositories\product_repository;
use local_gamification\repositories\transaction_repository;
use local_gamification\utils\helper;
use moodle_url;
use paging_bar;
use \renderer_base;

global $CFG;

class popular extends base
{
    protected int $page = 0;
    protected int $total = 0;
    protected int $pagesize = 10;

    protected array $data = [];

    protected ?paging_bar $pagination = null;

    protected product_repository $repository;
    protected transaction_repository $transaction_repository;

    protected bool $isMobileInstance = false;

    public function __construct(product_repository $repository, transaction_repository $transaction_repository, bool $isMobileInstance = false)
    {
        $this->repository = $repository;
        $this->transaction_repository = $transaction_repository;
        $this->$isMobileInstance = $isMobileInstance;
        parent::__construct($this->$isMobileInstance);
    }

    /**
     * Export the data for the template.
     *
     * @param renderer_base $output The renderer instance.
     * @return array The exported data.
     */
    public function export_for_template(renderer_base $output)
    {
        global $USER;

        $data = parent::export_for_template($output);

        $data['balances'] = $this->transaction_repository->get_balance($USER->id);

        $data['products'] = $this->get_data($data['balances']['coins']);

        $from = $this->total === 0 ? 0 : ($this->pagesize * $this->page) + 1;
        $to = ($this->pagesize * $this->page) + count($this->data);

        $data['has_products'] = count($this->data) > 0;

        $data['pagesize'] = [
            'action' => $this->get_url(),
            'total' => $this->total,
            'showing' => get_string('order:pagesize:info', 'local_gamification', ['from' => $from, 'to' => $to, 'total' => $this->total]),
            'options' => $this->get_pagesize_options(),
        ];

        if ($this->pagination) {
            $data['pagination'] = $output->render($this->pagination);
        }

        return $data;
    }

    /**
     * Return the data as an array of records.
     *
     * @param int $balancecoins The user's current balance of coins.
     * @return array The data as an array of records.
     */
    protected function get_data($balancecoins): array
    {
        return $this->collection_to_records($this->data, $balancecoins);
    }

    /**
     * Set the data for the component.
     *
     * @param array $data The products to be displayed.
     * @param int $total The total number of products.
     * @param int $pagesize The number of products per page. Defaults to 5.
     */
    public function set_data(array $data, $total, $pagesize = 5): void
    {
        $this->data = $data;
        $this->total = $total;
        $this->pagesize = $pagesize;
    }

    /**
     * Convert a collection of products to a record array.
     *
     * @param array $collection
     * @param int $balancecoins
     * @return array
     */
    protected function collection_to_records($collection, $balancecoins): array
    {
        return array_map(function ($product) use ($balancecoins) {
            global $OUTPUT;

            $cover = helper::get_product_cover_url($product->get('id'));

            if ($cover) {
                $cover->out();
            } else {
                $cover = $OUTPUT->get_generated_image_for_id($product->get('id'));
            }

            $checkout_url = new moodle_url('/local/gamification/store/checkout.php', ['productid' => $product->get('id')]);

            $productIdApp = $product->get('id');

            $canbuy = $balancecoins >= $product->get('price');

            $product = $product->to_record();

            $product->cover = $cover;

            $product->canbuy = $canbuy;

            $product->checkout_url = $checkout_url;

            $product->app_checkout_url = 'store/checkout';
            $product->app_productid = $productIdApp;

            return $product;
        }, $collection);
    }

    /**
     * Sets the current page and creates a pagination bar.
     *
     * @param int $page The current page.
     */
    public function set_pagination($page)
    {
        $this->page = $page;
        $this->pagination = new spark_paging_bar($this->total, $this->page, $this->pagesize, $this->get_url());
    }

    /**
     * Returns an array of options for the pagesize select element.
     *
     * The options are determined based on the total number of rows in the table.
     * The available options are 5, 10, 25, 50, 100, and the total number of rows.
     *
     * @return array An array of pagesize options
     */
    protected function get_pagesize_options()
    {
        $options = [];
        $options[5] = ['value' => 5, 'text' => 5, 'selected' => $this->pagesize === 5];

        $availableoptions = [10, 25, 50, 100];

        foreach ($availableoptions as $option) {
            if ($this->total >= $option || $option === $this->pagesize) {
                $options[$option] = ['value' => $option, 'text' => $option, 'selected' => $this->pagesize === $option];
            }
        }

        sort($options);

        return $options;
    }
}
