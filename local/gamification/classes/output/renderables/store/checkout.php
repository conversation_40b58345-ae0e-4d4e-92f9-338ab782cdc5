<?php

namespace local_gamification\output\renderables\store;

defined('MOODLE_INTERNAL') || die();

use local_gamification\output\renderables\base;
use local_gamification\repositories\product_repository;
use local_gamification\repositories\transaction_repository;
use local_gamification\utils\helper;
use \renderer_base;
use tool_lfxp\core\form\customform;

global $CFG;

class checkout extends base
{
    protected ?int $productid = null;

    protected customform $_form;

    protected product_repository $repository;
    protected transaction_repository $transaction_repository;

    protected bool $isMobileInstance = false;

    public function __construct(product_repository $repository, transaction_repository $transaction_repository, bool $isMobileInstance = false)
    {
        $this->repository = $repository;
        $this->transaction_repository = $transaction_repository;
        $this->$isMobileInstance = $isMobileInstance;
        parent::__construct($this->$isMobileInstance);
    }

    /**
     * Export the data for the template.
     *
     * @param renderer_base $output The renderer instance.
     * @return array The exported data.
     */
    public function export_for_template(renderer_base $output)
    {
        global $USER;

        $data = parent::export_for_template($output);
        $data = array_merge($data, $this->form()->export_template_data());

        $data['product'] = $this->get_product();

        $data['balances'] = $this->transaction_repository->get_balance($USER->id);

        $data['subtotal'] = $data['product']->price;
        $data['coins_forecast'] = $data['balances']['coins'] - $data['product']->price;

        return $data;
    }

    protected function get_product(): object
    {
        global $OUTPUT;

        $product = $this->repository->find($this->get_productid());

        $cover = helper::get_product_cover_url($product->get('id'));

        if ($cover) {
            $cover->out();
        } else {
            $cover = $OUTPUT->get_generated_image_for_id($product->get('id'));
        }

        $deliverymethodname = $product->get_delivery_method_name();

        $product = $product->to_record();

        $product->cover = $cover;
        $product->deliverymethodname = $deliverymethodname;

        return $product;
    }

    /**
     * Sets the product id.
     *
     * @param int $productid the product id
     *
     * @return void
     */
    public function set_productid($productid)
    {
        $this->productid = $productid;
    }

    /**
     * Returns the product id.
     *
     * @return int the product id
     */
    public function get_productid()
    {
        return $this->productid;
    }

    public function set_form(customform &$form)
    {
        $this->_form = $form;
    }

    public function &form(): customform
    {
        return $this->_form;
    }
}
