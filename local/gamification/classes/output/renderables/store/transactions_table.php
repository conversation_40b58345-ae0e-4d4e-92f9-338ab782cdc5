<?php

namespace local_gamification\output\renderables\store;
use theme_smart\output\spark_paging_bar;

defined('MOODLE_INTERNAL') || die();

use html_writer;
use local_gamification\models\order;
use local_gamification\models\order_status;
use local_gamification\models\product;
use local_gamification\models\transaction;
use local_gamification\output\renderables\base;
use local_gamification\repositories\setting_repository;
use local_gamification\utils\helper;
use moodle_url;
use paging_bar;
use \renderer_base;

global $CFG;

class transactions_table extends base
{
    protected array $headers = [];

    protected array $items = [];

    protected int $page = 0;
    protected int $total = 0;
    protected int $pagesize = 15;

    protected ?paging_bar $pagination = null;

    protected setting_repository $setting_repository;

    protected bool $isMobileInstance = false;

    /**
     * Export the data for the template.
     *
     * @param renderer_base $output The renderer instance.
     * @return array The exported data.
     */
    public function export_for_template(renderer_base $output): array
    {
        $data = [];

        $instance = $this->setting_repository->get_instance();

        $this->set_headers(["Data", "Referente", $instance->get('pointsname'), "Moedas"]);

        $data['headers'] = $this->headers;

        $data['items'] = $this->items;

        $data['has_items'] = count($this->items) > 0;

        $from = $this->total === 0 ? 0 : ($this->pagesize * $this->page) + 1;
        $to = ($this->pagesize * $this->page) + count($this->items);

        $data['pagesize'] = [
            'action' => $this->get_url(),
            'total' => $this->total,
            'showing' => get_string('order:pagesize:info', 'local_gamification', ['from' => $from, 'to' => $to, 'total' => $this->total]),
            'options' => $this->get_pagesize_options(),
        ];

        if ($this->pagination && $this->isMobileInstance == false) {
            $data['pagination'] = $output->render($this->pagination);
        } else {
            $data['pagination'] =  self::mobile_pagination($this->total, $this->pagesize, $this->page, $this->get_url(), 'store/transactions');
        }

        return $data;
    }

    /**
     * Sets the data for the table.
     *
     * Sets the items to be displayed in the table and the total number of items.
     * Also sets the page size for the pagination.
     *
     * @param array $items An array of orders to be displayed in the table.
     * @param int $total The total number of orders.
     * @param int $pagesize The number of orders per page. Defaults to 5.
     */
    public function set_data(array $items, $total, $pagesize = 5): void
    {
        $this->set_items($items);
        $this->total = $total;
        $this->pagesize = $pagesize;
    }

    /**
     * Sets the items to be displayed in the table.
     *
     * Iterates the given array of orders and calls make_item() on each of them.
     * The resulting arrays are stored in the items property.
     *
     * @param array $items An array of orders to be set.
     */
    public function set_items(array $items): void
    {
        foreach ($items as $item) {
            $this->items[] = $this->make_item($item);
        }
    }

    /**
     * Converts an transaction into an array with the relevant data for the template.
     *
     * @param transaction $transaction The order to convert.
     * @return array The converted order.
     */
    public function make_item(transaction $transaction): array
    {
        $coins = $transaction->get('coins');
        $points = $transaction->get('points');

        return [
            'uniqid' => uniqid('table_'),
            'description' => $transaction->get('description'),
            'coins' => $this->get_coins_tag($coins),
            'points' => $this->get_points_tag($points),
            'timecreated' => $transaction->get_timecreated_formatted('d/m')
        ];
    }

    /**
     * Generates a string that displays the given number of coins in a human readable format, with a positive or negative color.
     *
     * @param int $coins The number of coins.
     * @return string A string that displays the given number of coins in a human readable format, with a positive or negative color.
     */
    protected function get_points_tag(int $points): string
    {
        $points_string = $points > 0 ? "+$points" : $points;
        $class_name = $points > 0 ? 'text-primary font-weight-bold' : 'text-danger font-weight-bold';

        $instance = $this->setting_repository->get_instance();

        $data_string = [
            'points' => $points_string,
            'pointsshortname' => $instance->get('pointsshortname')
        ];

        return "<span class='$class_name'>" . get_string('store:user_points', 'local_gamification', $data_string) . "</span>";
    }

    protected function get_coins_tag(int $coins): string
    {
        $coins_string = $coins > 0 ? "+$coins" : $coins;
        $class_name = $coins > 0 ? 'text-primary font-weight-bold' : 'text-danger font-weight-bold';

        return "<span class='$class_name'>" . get_string('store:user_coins', 'local_gamification', $coins_string) . "</span>";
    }

    /**
     * Sets the headers for the table.
     *
     * @param array $headers The headers for the table.
     * @return void
     */
    public function set_headers(array $headers)
    {
        foreach ($headers as $key => $header) {
            $this->headers[] = ['index' => $key + 1, 'name' => $header];
        }
    }

    /**
     * Sets the current page and creates a pagination bar.
     *
     * @param int $page The current page.
     */
    public function set_pagination($page)
    {
        $this->page = $page;
        $this->pagination = new spark_paging_bar($this->total, $this->page, $this->pagesize, $this->get_url());
    }

    public function set_mobile_platform($isMobile){
        $this->isMobileInstance = $isMobile;
    }

    /**
     * Returns a HTML span tag representing the status of the order.
     * The color of the tag is based on the status of the order.
     *
     * @param order $order
     * @return string
     */
    public function get_order_status_tag(order $order): string
    {
        switch ($order->get('status')) {
            case order_status::STATUS_COMPLETED:
                $statusclass = 'badge badge-success';
                break;
            case order_status::STATUS_PROCESSING:
                $statusclass = 'badge badge-info';
                break;
            case order_status::STATUS_PENDING:
            default:
                $statusclass = 'badge badge-warning';
        }

        $status = $order->get_status_name();

        return html_writer::span($status, $statusclass);
    }

    public function get_product_cover_tag(product $product): string
    {
        global $OUTPUT;

        $cover = helper::get_product_cover_url($product->get('id'));

        if ($cover) {
            $cover->out();
        } else {
            $cover = $OUTPUT->get_generated_image_for_id($product->get('id'));
        }

        $cover = html_writer::img($cover, $product->get('name'), array('class' => 'product-cover'));

        return html_writer::link(new moodle_url($product->get('url')), $cover, array('target' => '_blank'));
    }

    /**
     * Returns a HTML string representing the type of a given product.
     * The returned string is a Bootstrap badge with a color depending on the type.
     *
     * @param product $product The product to get the type for.
     * @return string The HTML representation of the type.
     */
    public function get_product_type_tag(product $product): string
    {
        switch ($product->get('type')) {
            case product::TYPE_DIGITAL;
                $typeclass = 'badge badge-primary';
                break;
            case product::TYPE_PHYSICAL:
            default:
                $typeclass = 'badge badge-danger';
        }

        $type = $product->get_type_name();

        return html_writer::span($type, $typeclass);
    }

    /**
     * Returns an array of options for the pagesize select element.
     *
     * The options are determined based on the total number of rows in the table.
     * The available options are 5, 10, 25, 50, 100, and the total number of rows.
     *
     * @return array An array of pagesize options
     */
    protected function get_pagesize_options()
    {
        $options = [];
        $options[5] = ['value' => 5, 'text' => 5, 'selected' => $this->pagesize === 5];

        $availableoptions = [10, 25, 50, 100];

        foreach ($availableoptions as $option) {
            if ($this->total >= $option || $option === $this->pagesize) {
                $options[$option] = ['value' => $option, 'text' => $option, 'selected' => $this->pagesize === $option];
            }
        }

        sort($options);

        return $options;
    }

    /**
     * Returns an array of options for the status select element.
     *
     * The available options are the statuses defined in the order model.
     * The selected option is the current status of the given order.
     *
     * @param order $order The order to get the status options for.
     *
     * @return array An array of status options
     */
    protected function get_updatestatus_options(order $order)
    {
        $options = order_status::STATUSES;

        foreach ($options as $key => $value) {
            $options[$key] = ['value' => $key, 'text' => $order->get_status_name($value), 'selected' => $order->get('status') === $key];
        }

        return array_values($options);
    }

    /**
     * Sets the setting repository.
     *
     * @param setting_repository $setting_repository The setting repository.
     */
    public function set_setting_repository(setting_repository $setting_repository): void
    {
        $this->setting_repository = $setting_repository;
    }
}
