<?php

namespace local_gamification\output\renderables\store;

defined('MOODLE_INTERNAL') || die();

use local_gamification\output\renderables\base;
use local_gamification\repositories\transaction_repository;
use \renderer_base;

global $CFG;

class transactions extends base
{
    protected transaction_repository $repository;

    protected transactions_table $table;

    protected bool $isMobileInstance = false;

    public function __construct(transaction_repository $repository, bool $isMobileInstance = false)
    {
        $this->repository = $repository;
        $this->isMobileInstance = $isMobileInstance;
        parent::__construct($this->isMobileInstance);
    }

    /**
     * Export the data for the template.
     *
     * @param renderer_base $output The renderer instance.
     * @return array The exported data.
     */
    public function export_for_template(renderer_base $output)
    {
        global $USER;

        $data = parent::export_for_template($output);
        $this->table()->set_mobile_platform($this->isMobileInstance);
        $data = array_merge($data, $this->table()->export_for_template($output));

        $data['balances'] = $this->repository->get_balance($USER->id);

        return $data;
    }

    public function set_table(transactions_table &$table)
    {
        $this->table = $table;
    }

    public function &table(): transactions_table
    {
        return $this->table;
    }
}
