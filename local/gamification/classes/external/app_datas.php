<?php

namespace local_gamification\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_single_structure;
use core_external\external_value;
use local_gamification\utils\helper;
use moodle_exception;
use stdClass;
use local_gamification\app\bootstrap;
use local_gamification\controllers\store\checkout_controller;

use local_gamification\repositories\rank_repository;
use local_gamification\repositories\setting_repository;

require(__DIR__ . '/../../../../config.php');
configChanges();
require_once("$CFG->libdir/filelib.php");


class app_datas extends external_api {
    
    public static function ranking_app_parameters() {
        return new external_function_parameters(['level' => new external_value(PARAM_INT, 'level ranking id', VALUE_REQUIRED)]);
    }

    public static function ranking_app_returns(): external_single_structure {
        return new external_single_structure(
            ['data' => new external_value(PARAM_RAW, 'data table from api', VALUE_REQUIRED)]
        );
    }

    public static function ranking_app($level): stdClass {
        global $CFG, $DB, $USER, $OUTPUT, $PAGE;
        require_once($CFG->dirroot . '/lib/outputrenderers.php');

        $PAGE->set_context(\context_system::instance());
        $PAGE->set_pagelayout('standard');
        $PAGE->set_url('/local/gamification/webservice.php');
        $PAGE->set_title('Ranking');
        $PAGE->set_heading('Ranking');

        $validatedparams = self::validate_parameters(self::ranking_app_parameters(), compact('level'));
        extract($validatedparams);

        $compactParamsRequest = compact('level');

        $position = 1;
        $rank_repository = new rank_repository();
        $levels = $rank_repository->get_levels();
        $levels[$compactParamsRequest['level']]->selected = true;

        [$min, $max] = $rank_repository->get_range_of_level($compactParamsRequest['level']);
        $items = $rank_repository->get_rank($min, $max, 10) ?? [];

        $data = [];
        $data['title'] = get_string('goal:goals', 'local_gamification');
        $data['action'] = '';

        $data['items'] = array_map(function ($rank) use (&$position, $min, $max) {
            $user = $rank->get_user();
            $level_percent = $rank->get_level_percent($min, $max);
            $rank = $rank->to_record();

            $rank->position = $position++;
            $rank->userid = $user->get('id');
            $rank->user_picture = $user->get_user_picture_mobile_app();
            $rank->user_fullname = $user->get_fullname();
            $rank->level_percent = $level_percent;

            return $rank;
        }, $items);

        $data['has_items'] = true;
        if(!is_array($data['items']) || count($data['items']) <= 0) {
            $data['items'] = [];
            $data['has_items'] = false;
        }
        $data['levels'] = array_reverse($levels);
        $data['level'] = $compactParamsRequest['level'];
        $data['max_level'] = is_null($max) ? true : false;

        $setRepository = new setting_repository();
        $setsR = $setRepository->get_instance();

        $data['points_short_name'] = $setsR->get('pointsshortname');
        $data['points_name'] = $setsR->get('pointsname');

        $data['pagination'] = '';
        $data['pagesize'] = false;
        $data['showing'] = '';

        try {
            $renderer = $PAGE->get_renderer('core');
            $tableRet = $renderer->render_from_template('local_gamification/mobile/_block_body_rank', $data);
        } catch (\Throwable $th) {
            $tableRet = print_r($th, true);
        }

        return (object) ['data' => $tableRet];
    }
}

function configChanges(){
    global $CFG;
    $CFG->forceloginforprofileimage = false;
    $CFG->enablegravatar = false;
    $CFG->dirroot = explode('/', dirname(__DIR__));
    $arrayKeyLocalGamification = array_search('local', $CFG->dirroot);
    if($arrayKeyLocalGamification && $CFG->dirroot[$arrayKeyLocalGamification+1] == 'gamification') {
        $CFG->dirroot = implode('/', array_slice($CFG->dirroot, 0, $arrayKeyLocalGamification));
        $CFG->libdir = $CFG->dirroot.'/lib';
        $CFG->localcachedir = $CFG->dataroot.'/localcache';
        $CFG->tempdir = $CFG->dataroot.'/temp';
    } else {
        unset($CFG->dirroot);
    }
    $CFG->forced_plugin_settings = array();
}