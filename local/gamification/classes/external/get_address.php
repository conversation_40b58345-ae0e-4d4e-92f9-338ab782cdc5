<?php

namespace local_gamification\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_single_structure;
use core_external\external_value;
use local_gamification\repositories\Address_repository;
use local_gamification\repositories\user_address_repository;
use local_gamification\utils\helper;
use moodle_exception;
use moodle_url;
use stdClass;

configChanges();
require_once("$CFG->libdir/filelib.php");

/**
 * Web service to returns a single Address by ID
 *
 * @package local_gamification
 */
class get_address extends external_api
{
    /**
     * Describes the parameters.
     *
     * @return external_function_parameters
     */
    public static function api_parameters()
    {
        return new external_function_parameters([
            'id' => new external_value(PARAM_INT, 'ID of address', VALUE_REQUIRED, NULL, NULL_NOT_ALLOWED)
        ]);
    }

    /**
     * Describes the return structure of the service.
     *
     * @return external_single_structure
     */
    public static function api_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                // 'id'           => new external_value(PARAM_INT, 'Address ID.', VALUE_REQUIRED),
                'name'         => new external_value(PARAM_TEXT, 'Address Name.', VALUE_REQUIRED),
                'email'        => new external_value(PARAM_TEXT, 'Address description.', VALUE_REQUIRED),
                'phone'        => new external_value(PARAM_TEXT, 'Address cover.', VALUE_REQUIRED),
                'street'       => new external_value(PARAM_TEXT, 'Address cover url.', VALUE_OPTIONAL),
                'number'       => new external_value(PARAM_TEXT, 'Address price.', VALUE_REQUIRED),
                'complement'   => new external_value(PARAM_TEXT, 'Address amount.', VALUE_REQUIRED),
                'neighborhood' => new external_value(PARAM_TEXT, 'Address type.', VALUE_REQUIRED),
                'city'         => new external_value(PARAM_TEXT, 'Address visible.', VALUE_REQUIRED),
                'state'        => new external_value(PARAM_TEXT, 'Address delivery method.', VALUE_REQUIRED),
                'postalcode'   => new external_value(PARAM_TEXT, 'Address allow multiple redemptions.', VALUE_REQUIRED),
            ]
        );
    }

    /**
     * Executes the service.
     *
     * @return stdClass
     */
    public static function api($id): stdClass
    {
        global $CFG, $DB, $USER;

        if (!isloggedin()) {
            return [];
        }

        $context = \context_system::instance();
        self::validate_context($context);

        $params = compact('id');

        self::validate_parameters(self::api_parameters(), $params);

        $repository = new user_address_repository();
        $address = $repository->find($id);

        if (!$address) {
            throw new moodle_exception('store:address_not_found', 'local_gamification');
        }

        return $address->to_record();
    }
}

function configChanges(){
    global $CFG;
    $CFG->enablegravatar = true;
    $CFG->dirroot = explode('/', dirname(__DIR__));
    $arrayKeyLocalGamification = array_search('local', $CFG->dirroot);
    if($arrayKeyLocalGamification && $CFG->dirroot[$arrayKeyLocalGamification+1] == 'gamification') {
        $CFG->dirroot = implode('/', array_slice($CFG->dirroot, 0, $arrayKeyLocalGamification));
        $CFG->libdir = $CFG->dirroot.'/lib';
        $CFG->localcachedir = $CFG->dataroot.'/localcache';
        $CFG->tempdir = $CFG->dataroot.'/temp';
    } else {
        unset($CFG->dirroot);
    }
    $CFG->forced_plugin_settings = array();
}