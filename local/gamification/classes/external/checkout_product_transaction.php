<?php

namespace local_gamification\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_single_structure;
use core_external\external_value;
use local_gamification\utils\helper;
use moodle_exception;
use stdClass;
use local_gamification\app\bootstrap;
use local_gamification\controllers\store\checkout_controller;

configChanges();
require_once("$CFG->libdir/filelib.php");
require(__DIR__ . '/../../../../config.php');

/**
 * Web service to returns a single product by ID
 *
 * @package local_gamification
 */
class checkout_product_transaction extends external_api
{
    /**
     * Describes the parameters.
     *
     * @return external_function_parameters
     */
    public static function api_parameters()
    {
        return new external_function_parameters([
            'productid' => new external_value(PARAM_INT, 'product id', VALUE_REQUIRED),
            'name' => new external_value(PARAM_TEXT, 'customer name', VALUE_REQUIRED),
            'email' => new external_value(PARAM_TEXT, 'email', VALUE_REQUIRED),
            'phone' => new external_value(PARAM_TEXT, 'phone number', VALUE_REQUIRED),
            'address' => new external_value(PARAM_INT, 'use saved address'),
            'postalcode' => new external_value(PARAM_TEXT, 'postalcode'),
            'city' => new external_value(PARAM_TEXT, 'city'),
            'state' => new external_value(PARAM_TEXT, 'state'),
            'neighborhood' => new external_value(PARAM_TEXT, 'neighborhood'),
            'street' => new external_value(PARAM_TEXT, 'street'),
            'number' => new external_value(PARAM_TEXT, 'number'),
            'complement' => new external_value(PARAM_TEXT, 'complement'),
            'save_address' => new external_value(PARAM_BOOL, 'save_address'),
        ]);
    }

    /**
     * Describes the return structure of the service.
     *
     * @return external_single_structure
     */
    public static function api_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'success' => new external_value(PARAM_BOOL, 'Success or failure', VALUE_REQUIRED),
                'message' => new external_value(PARAM_RAW, 'Message', VALUE_REQUIRED),
                'data' => new external_value(PARAM_RAW, 'data from api', VALUE_REQUIRED),
            ]
        );
    }

    /**
     * Executes the service.
     *
     * @return stdClass
     */
    public static function api(
        $productid,
        $name,
        $email,
        $phone,
        $address = 0,
        $postalcode = '',
        $city = '',
        $state = '',
        $neighborhood = '',
        $street = '',
        $number = '',
        $complement = '',
        $save_address = false
        ): stdClass {
        global $CFG, $DB, $USER;

        $validatedparams = self::validate_parameters(
            self::api_parameters(),
            compact(
                'productid',
                'name',
                'email',
                'phone',
                'address',
                'postalcode',
                'city',
                'state',
                'neighborhood',
                'street',
                'number',
                'complement',
                'save_address'
            )
        );
        extract($validatedparams);

        $compactParamsRequest = compact('productid','name','email','phone','address','postalcode','city','state','neighborhood','street','number','complement','save_address');

        $url = new \moodle_url('/local/gamification/store/checkout.php', []);
        $container = bootstrap::get_container();
        $controller = $container->make(checkout_controller::class);
        $controller->set_url($url);
        $result = (object) $controller->process_page_form_submit_from_mobile($compactParamsRequest);

        if(!isset($result->success) || !$result->success){
            if(!isset($result->success)) return (object) ['message' => 'Erro no retorno da API...', 'success' => $result->success ?? false, 'data' => 'sem retorno'];
            return (object) ['message' => $result->message ?? '', 'success' => $result->success ?? false, 'data' => json_encode($result->data) ?? (json_encode($result) ?? '')];
        } else {
            return (object) ['message' => $result->message ?? '', 'success' => $result->success ?? false, 'data' => json_encode($result->data) ?? ''];
        }
    }
}

function configChanges(){
    global $CFG;
    $CFG->enablegravatar = true;
    $CFG->dirroot = explode('/', dirname(__DIR__));
    $arrayKeyLocalGamification = array_search('local', $CFG->dirroot);
    if($arrayKeyLocalGamification && $CFG->dirroot[$arrayKeyLocalGamification+1] == 'gamification') {
        $CFG->dirroot = implode('/', array_slice($CFG->dirroot, 0, $arrayKeyLocalGamification));
        $CFG->libdir = $CFG->dirroot.'/lib';
        $CFG->localcachedir = $CFG->dataroot.'/localcache';
        $CFG->tempdir = $CFG->dataroot.'/temp';
    } else {
        unset($CFG->dirroot);
    }
    $CFG->forced_plugin_settings = array();
}