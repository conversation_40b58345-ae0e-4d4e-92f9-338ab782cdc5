{"name": "local_gamification", "version": "1.0.0", "description": "", "main": "", "directories": {"test": "tests"}, "scripts": {"test": "cd ../../ && php ./vendor/bin/phpunit --testsuite local_gamification_testsuite --exclude skip --testdox-html local/gamification/tests/tests.html", "amd": "cd amd && grunt amd --force", "testcurrent": "cd ../../ && php ./vendor/bin/phpunit --testsuite local_gamification_testsuite --group current", "initphpunit": "cd ../../ && php admin/tool/phpunit/cli/init.php", "upgrade": "cd ../../admin/cli/ && php upgrade.php --non-interactive", "scss": "sass --style=compressed --no-source-map --watch scss/styles.scss styles.css", "mobile:scss": "sass --style=compressed --no-source-map --watch scss/mobile/styles.scss styles-mobile.css"}, "author": "", "license": "", "devDependencies": {"sass": "^1.51.0"}}