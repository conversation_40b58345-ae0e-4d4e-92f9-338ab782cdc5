<?php
// Permite acesso sem login:
define('NO_MOODLE_COOKIES', true); 
require(__DIR__ . '/../../config.php');

$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$is_mobile_app = strpos($user_agent, 'MoodleMobile') !== false;
if (!$is_mobile_app) {
    header('Location: '.$CFG->wwwroot.'/pix/u/f1.png');
    exit;
}

$userid = optional_param('id', 0, PARAM_INT);

if (empty($userid)) {
    http_response_code(400);
    echo "Missing user ID.";
    exit;
}

$user = $DB->get_record('user', ['id' => $userid, 'deleted' => 0], '*', IGNORE_MISSING);

if (!$user) {
    http_response_code(404);
    header('Location: '.$CFG->wwwroot.'/pix/u/f1.png');
    exit;
}

$fs = get_file_storage();
$context = context_user::instance($user->id);

$files = $fs->get_area_files($context->id, 'user', 'icon', 0, "itemid, filepath, filename", false);

if (empty($files)) {
    // Sem imagem personalizada? Redireciona para uma imagem padrão.
    header('Location: '.$CFG->wwwroot.'/pix/u/f1.png');
    exit;
}

$file = reset($files);

// Setar header manualmente:
header('Content-Type: '.$file->get_mimetype());
header('Content-Length: '.$file->get_filesize());

// Enviar conteúdo da imagem:
echo $file->get_content();
exit;
