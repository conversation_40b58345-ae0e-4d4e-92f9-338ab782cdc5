<div class="body">
    {{#has_items}}
        <div class="no-overflow">
            <table class="table">
                <thead>
                    <tr>
                        <th class="c1">{{#str}} home:ranking:position, local_gamification {{/str}}</th>
                        <th class="c2">{{#str}} home:ranking:name, local_gamification {{/str}}</th>
                        <th class="c3">{{ points_name }}</th>
                        <th class="c4">{{#str}} home:ranking:progress, local_gamification {{/str}}</th>
                    </tr>
                </thead>
                <tbody>
                    {{#items}}
                        <tr>
                            <td class="c1">{{ position }}</td>
                            <td class="c2">{{{ user_picture }}} {{ user_fullname }}</td>
                            <td class="c3">{{ points }} <sup>{{ points_short_name }}</sup></td>
                            <td class="c4">
                                <div class="text-right">
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: {{ level_percent }}%" aria-valuenow="{{ level_percent }}" aria-valuemin="0" aria-valuemax="100"></div>
                                        <span>{{ level_percent }}%</span>
                                    </div>
                                    {{#max_level}}
                                        <span class="full-progress">{{#str}} home:ranking:maxlevel, local_gamification {{/str}}</span>
                                    {{/max_level }}
                                </div>
                            </td>
                        </tr>
                    {{/items}}
                </tbody>
            </table>
        </div>

        <div class="table-controls">
            <div class="per-page">
                {{#pagesize}}
                    <small>({{ showing }})</small>
                {{/pagesize}}
            </div>
            <div>
                {{#pagination}} {{{ pagination }}} {{/pagination}}
            </div>
        </div>
    {{/has_items}}

    {{^has_items}}
        <div class="empty">
            {{#pix}} empty-list, local_gamification {{/pix}}
        </div>
    {{/has_items}}

</div>