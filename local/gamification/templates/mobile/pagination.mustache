{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core/paging_bar

    This template renders the horizontal bar with page links, e.g.
        | « | 1 | 2 | 3 | » |

    Example context (json):
    {
        "previous": null,
        "next": {
            "page": 2,
            "url": "./page.php?p=1"
        },
        "first": null,
        "last": {
            "page": 100,
            "url": "./page.php?p=99"
        },
        "label": "Page",
        "pages": [
            {
                "page": 1,
                "active": true,
                "url": null
            },
            {
                "page": 2,
                "active": false,
                "url": "./page.php?p=1"
            }
        ],
        "haspages": true,
        "pagesize": 10
    }
}}
{{#haspages}}
    <nav aria-label="{{label}}" class="pagination pagination-centered justify-content-center">
        <ul class="mt-1 pagination " data-page-size="{{pagesize}}">
            {{#previous}}
                <li class="page-item previous-page" data-page-number="{{page}}">
                    <a core-site-plugins-new-content class="page-link"
                    component="local_gamification" method="mobile_view_local_gamification"
                    [args]="{page: '{{{pageName}}}', paginationPage: {{{page}}} }" samePage="true"
                    [preSets]="{updateFrequency: 4}">
                        <ion-icon name="chevron-back-outline"></ion-icon>
                        <span class="sr-only">{{#str}}previouspage, moodle{{/str}}</span>
                    </a>
                </li>
            {{/previous}}
            {{#first}}
                <li class="page-item" data-page-number="{{page}}">
                    <a core-site-plugins-new-content class="page-link"
                    component="local_gamification" method="mobile_view_local_gamification"
                    [args]="{page: '{{{pageName}}}', paginationPage: {{{page}}} }" samePage="true"
                    [preSets]="{updateFrequency: 4}">
                        <span aria-hidden="true">{{page}}</span>
                        <span class="sr-only">{{#str}}pagea, moodle, {{page}}{{/str}}</span>
                    </a>
                </li>
                <li class="page-item disabled" data-page-number="{{page}}">
                    <span class="page-link">&hellip;</span>
                </li>
            {{/first}}
            {{#pages}}
                <li class="page-item {{#active}}active{{/active}}" data-page-number="{{page}}">
                    <a core-site-plugins-new-content class="page-link mobile-link-lfx" {{#active}}aria-current="page"{{/active}}
                    component="local_gamification" method="mobile_view_local_gamification"
                    [args]="{page: '{{{pageName}}}', paginationPage: {{{page}}} }" page="{{ pageName }}" samePage="true"
                    {{#active}}[preSets]="{getFromCache: 0, saveToCache: 0, updateFrequency: 4}"{{/active}}
                    {{^active}}[preSets]="{updateFrequency: 4}"{{/active}}>
                        <span aria-hidden="true">{{page}}</span>
                        <span class="sr-only">{{#str}}pagea, moodle, {{page}}{{/str}}</span>
                    </a>
                </li>
            {{/pages}}
            {{#last}}
                <li class="page-item disabled" data-page-number="{{page}}">
                    <span class="page-link">&hellip;</span>
                </li>
                <li class="page-item" data-page-number="{{page}}">
                    <a core-site-plugins-new-content class="page-link"
                    component="local_gamification" method="mobile_view_local_gamification"
                    [args]="{page: '{{{pageName}}}', paginationPage: {{{page}}} }" samePage="true"
                    [preSets]="{updateFrequency: 4}">
                        <span aria-hidden="true">{{page}}</span>
                        <span class="sr-only">{{#str}}pagea, moodle, {{page}}{{/str}}</span>
                    </a>
                </li>
            {{/last}}
            {{#next}}
                <li class="page-item next-page" data-page-number="{{page}}">
                    <a core-site-plugins-new-content class="page-link"
                    component="local_gamification" method="mobile_view_local_gamification"
                    [args]="{page: '{{{pageName}}}', paginationPage: {{{page}}} }" samePage="true"
                    [preSets]="{updateFrequency: 4}">
                        <ion-icon name="chevron-forward-outline"></ion-icon>
                        <span class="sr-only">{{#str}}nextpage, moodle{{/str}}</span>
                    </a>
                </li>
            {{/next}}
        </ul>
    </nav>
{{/haspages}}
