{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_rewardsprogram/index

    TODO describe template index

    Example context (json):
    {
    }
}}

<div class="header-content">
</div>

<div class="alert alert-primary" role="alert">
    {{#str}}home:alertinfo, local_gamification, {{ points_short_name }}{{/str}}
</div>

<section class="box mb-5" id="section-info">
    <div class="user-info">

        {{#user}} {{{picture}}} {{/user}}

        <div class="d-flex flex-column">
            {{#user}}
                <h5>{{ name }}</h5>
            {{/user}}

            {{#progress}}
                <span>
                    {{#str}} home:ranking_position, local_gamification {{/str}}
                    <b>
                        {{#position}} 
                            {{ position }} {{#str}} home:position, local_gamification {{/str}}
                        {{/position}} 
                        
                        {{^position}} -- {{/position}}
                    </b>
                </span>
                
                <span>
                    Nível: 
                    <b>
                        {{#level}} {{name}} {{/level}}
                        
                        {{^level}} -- {{/level}}
                    </b>
                </span>
                
            {{/progress}}
        </div>
    </div>

    <div class="progress-info">
        {{#progress}}
            {{#balances.coins}}
                <div class="user-coins">
                    {{#pix}} icon_coin, local_gamification {{/pix}} {{#str}} store:user_coins, local_gamification, {{.}} {{/str}}
                </div>
            {{/balances.coins}}

            <div class="level">
                {{#balances.points}}
                    <span>
                        {{.}} <sup>{{ points_short_name }}</sup>
                    </span>
                {{/balances.points}}

                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: {{ progress_percent }}%;" aria-valuenow="{{ progress_percent }}" aria-valuemin="0" aria-valuemax="100"></div>
                    <span>{{ progress_percent }}%</span>
                </div>
                {{#points_for_next_level}}
                    <span>{{ points_for_next_level }}<sup>{{ points_short_name }}</sup> <small>{{#str}} home:for_next_level, local_gamification {{/str}}</small></span>
                {{/points_for_next_level}}
            </div>
        {{/progress}}
    </div>
</section>

<section class="row mb-5" id="section-widgets">
    {{#enabledstore}}
        <div class="col-12 col-xl-3">
            <div class="banner">
                <div>
                    <h3>{{#str}} home:banner:title, local_gamification {{/str}}</h3>
                    <p>{{#str}} home:banner:description, local_gamification {{/str}}</p>
                </div>
                <a core-site-plugins-new-content style="color: white !important; display: flex;"
                    title="{{#str}} store:title, local_gamification {{/str}}" component="local_gamification"
                    method="mobile_view_local_gamification" [args]="{page: 'store/index'}"
                    [preSets]="{updateFrequency: 4}">
                    {{#str}} home:banner:button, local_gamification {{/str}} <ion-icon style="margin-left: 5px" name="arrow-forward-outline"></ion-icon>
                </a>
            </div>
        </div>
    {{/enabledstore}}
    <div class="col-12 {{#enabledstore}} col-xl-6 {{/enabledstore}} {{^enabledstore}} col-xl-12 {{/enabledstore}}">
        <div class="box">
            <h4>{{#str}} home:track_progress, local_gamification {{/str}}</h4>
            <div class="row indicators">
                {{#widgets}}
                    {{#completions}}
                        <div class="col-12 col-md-4 d-flex">
                            <div class="progress-bar" style="--percent:{{ percent }}%;">
                                <h2>{{ percent }}%</h2>
                                <div>{{#str}} home:watched_content, local_gamification {{/str}}</div>
                            </div>
                        </div>
                    {{/completions}}
                {{/widgets}}

                {{#widgets}}
                    <div class="col-12 col-sm-6 col-md-4 widgets">
                        {{#coins}}
                            <div class="widget">
                                <div class="col1 d-flex flex-column">
                                    <span class="description">{{#str}} home:coins_received, local_gamification {{/str}}</span>
                                    <span class="value">{{ total }}</span>
                                </div>
                                <div class="col2">
                                    {{#pix}} icon_coin, local_gamification {{/pix}}
                                </div>
                            </div>
                        {{/coins}}

                        {{#hours}}
                            <div class="widget">
                                <div class="col1 d-flex flex-column">
                                    <span class="description">{{#str}} home:watched_hours, local_gamification {{/str}}</span>
                                    <span class="value">{{ total }}</span>
                                </div>
                                <div class="col2">
                                    <ion-icon name="fas-stopwatch" class="fa-solid"></ion-icon>
                                </div>
                            </div>
                        {{/hours}}
                    </div>

                    <div class="col-12 col-sm-6 col-md-4 widgets">
                        
                        <div class="widget">
                            <div class="col1 d-flex flex-column">
                                <span class="description">{{#str}} home:ranking_level, local_gamification {{/str}}</span>
                                <span class="value">
                                    {{#level}}
                                        {{ name }}
                                    {{/level}}

                                    {{^level}} -- {{/level}}
                                </span>
                            </div>
                            <div class="col2">
                                <ion-icon name="fas-trophy"></ion-icon>
                            </div>
                        </div>
                        
                        <div class="widget">
                            <div class="col1 d-flex flex-column">
                                <span class="description">{{#str}} home:ranking_position, local_gamification {{/str}}</span>
                                <span class="value">
                                    {{#position}}
                                        {{ position }}
                                    {{/position}}

                                    {{^position}} -- {{/position}}
                                </span>
                            </div>
                            <div class="col2">
                                <ion-icon name="fas-ranking-star" class="fa-solid"></ion-icon>
                            </div>
                        </div>
                       
                    </div>
                {{/widgets}}

            </div>
        </div>
    </div>
    {{#enabledstore}}
        <div class="col-12 col-xl-3">
            <div class="pages">
                <div class="box">
                    <a core-site-plugins-new-content class="btn"
                        title="{{#str}} store:title, local_gamification {{/str}}" component="local_gamification"
                        method="mobile_view_local_gamification" [args]="{page: 'store/index'}"
                        [preSets]="{updateFrequency: 4}">
                        <ion-icon name="fas-shop" class="fa-solid"></ion-icon> {{#str}} home:page:store, local_gamification {{/str}}
                    </a>
                </div>
                <div class="box">
                    <a core-site-plugins-new-content class="btn"
                        title="{{#str}} home:page:my_purchases, local_gamification {{/str}}" component="local_gamification"
                        method="mobile_view_local_gamification" [args]="{page: 'store/my_purchases'}"
                        [preSets]="{updateFrequency: 4}">
                        <ion-icon name="fas-truck-fast" class="fa-solid"></ion-icon> {{#str}} home:page:my_purchases, local_gamification {{/str}}
                    </a>
                </div>
                <div class="box">
                    <a core-site-plugins-new-content class="btn"
                        title="{{#str}} home:page:transactions, local_gamification {{/str}}" component="local_gamification"
                        method="mobile_view_local_gamification" [args]="{page: 'store/transactions'}"
                        [preSets]="{updateFrequency: 4}">
                        <ion-icon name="fas-coins" class="fa-solid"></ion-icon> {{#str}} home:page:transactions, local_gamification {{/str}}
                    </a>
                </div>
            </div>
        </div>
    {{/enabledstore}}
</section>

{{#block_rank}}
    <section class="mb-5" id="section-rank">
        {{> local_gamification/mobile/_block_rank }}
    </section>
{{/block_rank}}

{{#block_goals}}
    <section class="mb-5" id="section-goal">
        {{> local_gamification/mobile/_block_goals }}
    </section>
{{/block_goals}}