{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_gamification/store/checkout

    TODO describe template header

    Example context (json):
    {
    }
}}

<div style="margin-top: 15px;"></div>

<form id="checkout-form" {{#form}}{{{attributes}}}{{/form}}>
    {{#hidden}}
        <input type="hidden" name="{{name}}" value="{{value}}"/>
    {{/hidden}}

    <div class="row">

        <div class="col-12 col-md-6">
            <div class="delivery">
                <h5>{{#str}} store:checkout:delivery_information, local_gamification {{/str}}</h5>

                <div class="row">

                    {{#address}}
                        <div class="col-12 mb-3">
                            <div class="row">
                                <label for="{{id}}" class="col-sm-3 col-form-label">
                                    {{label}}
                                    {{#required}}
                                        <ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>
                                    {{/required}}
                                    {{{help_button}}}
                                </label>
                                <div class="col-sm-9">
                                    <select id="{{id}}" class="form-control {{#error}}is-invalid{{/error}}" name="{{name}}">
                                        {{#options}}
                                            <option value="{{value}}" {{#selected}}selected{{/selected}}>{{text}}</option>
                                        {{/options}}
                                    </select>
                                </div>
                            </div>
                        </div>
                    {{/address}}

                    {{#name}}
                        <div class="col-12 mb-3">
                            <label for="{{id}}" class="form-label">
                                {{label}}
                                {{#required}}<ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>{{/required}}
                                {{{help_button}}}
                            </label>
                            <input class="form-control {{#error}}is-invalid{{/error}}" {{#required}}required{{/required}} {{{attributes}}}>
                            {{#error}}
                                <div class="invalid-feedback">
                                    {{{error}}}
                                </div>
                            {{/error}}
                        </div>
                    {{/name}}

                    {{#email}}
                        <div class="col-12 mb-3">
                            <label for="{{id}}" class="form-label">
                                {{label}}
                                {{#required}}<ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>{{/required}}
                                {{{help_button}}}
                            </label>
                            <input class="form-control {{#error}}is-invalid{{/error}}" {{#required}}required{{/required}} {{{attributes}}}>
                            {{#error}}
                                <div class="invalid-feedback">
                                    {{{error}}}
                                </div>
                            {{/error}}
                        </div>
                    {{/email}}

                    {{#phone}}
                        <div class="col-12 mb-3">
                            <label for="{{id}}" class="form-label">
                                {{label}}
                                {{#required}}<ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>{{/required}}
                                {{{help_button}}}
                            </label>
                            <input class="form-control {{#error}}is-invalid{{/error}}" {{#required}}required{{/required}} {{{attributes}}}>
                            {{#error}}
                                <div class="invalid-feedback">
                                    {{{error}}}
                                </div>
                            {{/error}}
                        </div>
                    {{/phone}}

                    {{#postalcode}}
                        <div class="col-6 mb-3">
                            <label for="{{id}}" class="form-label">
                                {{label}}
                                {{#required}}<ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>{{/required}}
                                {{{help_button}}}
                            </label>
                            <input class="form-control {{#error}}is-invalid{{/error}}" {{#required}}required{{/required}} {{{attributes}}}>
                            {{#error}}
                                <div class="invalid-feedback">
                                    {{{error}}}
                                </div>
                            {{/error}}
                        </div>
                    {{/postalcode}}

                    {{#city}}
                        <div class="col-6 mb-3">
                            <label for="{{id}}" class="form-label">
                                {{label}}
                                {{#required}}<ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>{{/required}}
                                {{{help_button}}}
                            </label>
                            <input class="form-control {{#error}}is-invalid{{/error}}" {{#required}}required{{/required}} {{{attributes}}}>
                            {{#error}}
                                <div class="invalid-feedback">
                                    {{{error}}}
                                </div>
                            {{/error}}
                        </div>
                    {{/city}}

                    {{#state}}
                        <div class="col-6 mb-3">
                            <label for="{{id}}" class="form-label">
                                {{label}}
                                {{#required}}<ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>{{/required}}
                                {{{help_button}}}
                            </label>
                            <input class="form-control {{#error}}is-invalid{{/error}}" {{#required}}required{{/required}} {{{attributes}}}>
                            {{#error}}
                                <div class="invalid-feedback">
                                    {{{error}}}
                                </div>
                            {{/error}}
                        </div>
                    {{/state}}

                    {{#neighborhood}}
                        <div class="col-6 mb-3">
                            <label for="{{id}}" class="form-label">
                                {{label}}
                                {{#required}}<ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>{{/required}}
                                {{{help_button}}}
                            </label>
                            <input class="form-control {{#error}}is-invalid{{/error}}" {{#required}}required{{/required}} {{{attributes}}}>
                            {{#error}}
                                <div class="invalid-feedback">
                                    {{{error}}}
                                </div>
                            {{/error}}
                        </div>
                    {{/neighborhood}}

                    {{#street}}
                        <div class="col-12 mb-3">
                            <label for="{{id}}" class="form-label">
                                {{label}}
                                {{#required}}<ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>{{/required}}
                                {{{help_button}}}
                            </label>
                            <input class="form-control {{#error}}is-invalid{{/error}}" {{#required}}required{{/required}} {{{attributes}}}>
                            {{#error}}
                                <div class="invalid-feedback">
                                    {{{error}}}
                                </div>
                            {{/error}}
                        </div>
                    {{/street}}

                    {{#number}}
                        <div class="col-6 mb-3">
                            <label for="{{id}}" class="form-label">
                                {{label}}
                                {{#required}}<ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>{{/required}}
                                {{{help_button}}}
                            </label>
                            <input class="form-control {{#error}}is-invalid{{/error}}" {{#required}}required{{/required}} {{{attributes}}}>
                            {{#error}}
                                <div class="invalid-feedback">
                                    {{{error}}}
                                </div>
                            {{/error}}
                        </div>
                    {{/number}}

                    {{#complement}}
                        <div class="col-6 mb-3">
                            <label for="{{id}}" class="form-label">
                                {{label}}
                                {{#required}}<ion-icon name="alert-circle-sharp" class="text-danger ml-1" title="{{#str}} requiredelement, core_form {{/str}}"></ion-icon>{{/required}}
                                {{{help_button}}}
                            </label>
                            <input class="form-control {{#error}}is-invalid{{/error}}" {{#required}}required{{/required}} {{{attributes}}}>
                            {{#error}}
                                <div class="invalid-feedback">
                                    {{{error}}}
                                </div>
                            {{/error}}
                        </div>
                    {{/complement}}

                    {{#save_address}}
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input type="hidden" name="{{name}}" value="0">
                                <input type="checkbox" class="form-check-input {{#error}}is-invalid{{/error}}" {{{attributes}}}>
                                <label class="form-check-label" for="{{id}}">
                                    {{label}}
                                    {{#required}}
                                        <ion-icon name="alert-circle-sharp" class="text-danger ml-1"></ion-icon>
                                    {{/required}}
                                    {{{help_button}}}
                                </label>
                            </div>
                        </div>
                    {{/save_address}}  

                </div>
            </div>
        </div>

        <div class="col-12 col-md-6">
        
            <div class="detail">
                <h5>{{#str}} store:checkout:purchase_details, local_gamification {{/str}}</h5>

                {{#product}}
                    <div class="product">
                        <img src="{{cover}}" alt="{{name}}">
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <h4 class="name">{{name}}</h4>
                                <div class="price">
                                    {{#pix}} icon_coin, local_gamification {{/pix}} {{#str}} store:checkout:product_price, local_gamification, {{price}} {{/str}}
                                </div>
                            </div>

                            <span class="amount">{{#str}} store:checkout:purchase_amount, local_gamification, 1 {{/str}}</span>
                        </div>
                    </div>
                {{/product}}

                <hr>

                <div class="info">
                    <div class="d-flex justify-content-between">
                        <span>{{#str}} store:checkout:subtotal, local_gamification {{/str}}</span>
                        <span>{{#str}} store:checkout:subtotal_coins, local_gamification, {{subtotal}} {{/str}}</span>
                    </div>

                    {{#balances}}
                        <div class="d-flex justify-content-between">
                            <span>{{#str}} store:checkout:your_coins, local_gamification {{/str}}</span>
                            <span>{{coins}}</span>
                        </div>
                    {{/balances}}

                    <div class="d-flex justify-content-between">
                        <span>{{#str}} store:checkout:coins_forecast, local_gamification {{/str}}</span>
                        <span>{{coins_forecast}}</span>
                    </div>
                    
                    {{#product}}
                        <div class="d-flex justify-content-between">
                            <span>{{#str}} store:checkout:delivery_method, local_gamification {{/str}}</span>
                            <span>{{deliverymethodname}}</span>
                        </div>
                    {{/product}}
                </div>

                <div class="actions">        
                    {{#cancel}}
                    {{/cancel}}

                    {{#submitbutton}}
                        <button (click)="sendFormCheckoutDelivery()" value="{{value}}" id="{{id}}" data-initial-value="{{value}}" 
                         class="btn btn-primary {{class}} d-flex justify-content-center align-items-center">
                            <ion-icon name="checkmark-circle-outline" style="margin-right: 5px;"></ion-icon> {{value}}
                        </button>
                    {{/submitbutton}}
                </div>
            </div>
        </div>
        
    </div>
</form>
<br>