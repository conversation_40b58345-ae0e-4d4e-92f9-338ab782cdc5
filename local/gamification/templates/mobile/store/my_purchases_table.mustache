{{#has_items}}
    <div class="no-overflow">
        <table class="table">
            <tbody>
                {{#items}}
                    <tr>
                        <td class="c1">{{{ productcover }}}</td>
                        <td class="c2">
                            <div class="d-flex flex-column">
                                <span class="order-timecreated">{{ timecreated }}</span>
                                <div class="d-flex align-items-center">
                                    <span class="product-name mr-2">{{ name }}</span> {{{ status }}}
                                </div>
                                <span class="product-description" title="{{ fulldescription }}">{{ description }}</span>
                            </div>
                        </td>
                        <td class="c3">
                            <div class="">
                                <div class="text-right">
                                    <span class="order-quantity">{{#str}} store:checkout:purchase_amount, local_gamification, {{ quantity }} {{/str}}</span>
                                    <span class="order-price">{{#str}} store:checkout:subtotal_coins, local_gamification, {{ price }} {{/str}}</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                {{/items}}
            </tbody>
        </table>
    </div>

    <div class="table-controls">
        <div class="per-page">
            {{#pagesize}}
                <small>({{ showing }})</small>
            {{/pagesize}}
        </div>
        <div>
            {{#pagination}} {{{ pagination }}} {{/pagination}}
        </div>
    </div>
{{/has_items}}

{{^has_items}}
    <div class="empty-list">
        {{#pix}} empty-list, local_gamification {{/pix}}
    </div>
{{/has_items}}