{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_gamification/card

    TODO describe template card

    Example context (json):
    {
    }
}}
<div class="card">
    <div class="card-img">
        <img src="{{cover}}" alt="{{name}}">
    </div>
    <div class="card-body">    
        <div class="name">{{name}}</div>
        <div class="description">{{description}}</div>
        <div class="coins">{{#str}} store:product_price, local_gamification, {{price}} {{/str}}</div>
    </div>
    <div class="card-footer">
        <div class="amount">{{#str}} store:product_amount, local_gamification, {{amount}} {{/str}}</div>
        {{#amount}}
            <a aria-label="Buy" class="buy"
                {{#canbuy}} 
                    href="#" core-site-plugins-new-content title="{{#str}} store:checkout:title, local_gamification {{/str}}" 
                    component="local_gamification" method="mobile_view_local_gamification" 
                    [args]="{page: '{{{app_checkout_url}}}', productid: {{{app_productid}}}}"
                    [preSets]="{getFromCache: 0, saveToCache: 0, omitExpires: 1}"
                {{/canbuy}} {{^canbuy}} 
                    href="#" disabled
                {{/canbuy}}>
                {{#str}} store:product_buy, local_gamification {{/str}} <ion-icon name="arrow-forward-outline"></ion-icon>
            </a>
        {{/amount}}
        {{^amount}}
            <span class="buy">
                {{#str}} store:product_unavailable, local_gamification {{/str}}
            </span>
        {{/amount}}
    </div>
</div>