<div class="glide" id="{{ id }}">
    <div class="glide-header">
        <div class="d-inline-flex align-items-center">
            <h4 class="blocktitle mb-0 mr-3" aria-label="Os mais pedidos">{{title}}</h4>
        
            <a core-site-plugins-new-content class="d-inline-flex align-items-center mb-0" aria-label="Ver todos"
                title="{{title}}" component="local_gamification"
                method="mobile_view_local_gamification" [args]="{page: '{{{app_url}}}'}"
                [preSets]="{updateFrequency: 4}">
                <h6 class="show-all">Ver todos</h6>
                <ion-icon class="icon" aria-hidden="true" name="chevron-forward-outline"></ion-icon>
            </a>

        </div>

        <div class="glide__arrows" data-glide-el="controls">
            <button class="glide__arrow glide__arrow--left btn btn-dark" aria-label="Item anterior" data-glide-dir="<"><ion-icon name="chevron-back-outline"></ion-icon></button>
            <button class="glide__arrow glide__arrow--right btn btn-dark" aria-label="Próximo item" data-glide-dir=">"><ion-icon name="chevron-forward-outline"></ion-icon></button>
        </div>
    </div>

    <div class="glide__track" data-glide-el="track">
        
        <ul class="glide__slides">
            {{#products}}
                <li class="glide__slide">
                    {{> local_gamification/mobile/store/product_card }}
                </li>
            {{/products}}
        </ul>
        
        {{#has_products}}
            <div class="skeleton">
                {{> local_gamification/mobile/store/product_card_skeleton }}
                {{> local_gamification/mobile/store/product_card_skeleton }}
                {{> local_gamification/mobile/store/product_card_skeleton }}
                {{> local_gamification/mobile/store/product_card_skeleton }}
                {{> local_gamification/mobile/store/product_card_skeleton }}
            </div>
        {{/has_products}}
    </div>
    
    {{^has_products}}
        <div class="empty">
            {{#pix}} empty-list, local_gamification {{/pix}}
        </div>
    {{/has_products}}
</div>