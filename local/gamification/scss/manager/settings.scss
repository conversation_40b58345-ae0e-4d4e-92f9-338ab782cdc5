#page-local-gamification-manager-settings {
  form {
    .form-control {
      &.is-invalid {
        border-color: #ca3120 !important;
      }
    }
  }

  // .alert-primary {
  //   color: #ffffff !important;
  //   background-color: #0d6ef7 !important;
  // }

  .btn-remove-line {
    height: 25px;
    width: 25px;
    margin-top: 5px;
    padding: 0px 7px !important;
  }

  #levels {
    .row {
      @media (max-width: 576px) {
        background-color: #343a406b;
        margin-bottom: 1rem !important;
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
      }
    }
  }

  #course-points {
    table {
      background-color: transparent;

      thead {
        color: var(--primary);
        border-bottom: 3px solid var(--primary);
      }

      tbody {
        tr {
          cursor: pointer;
          background-color: rgba(0, 0, 0, 0) !important;

          &[aria-expanded="true"] {
            td > i.fa-chevron-right {
              transform: rotate(90deg);
            }
          }

          td {
            i {
              transition: all 0.35s;
            }
          }
        }
      }

      table {
        background-color: #343a40;

        tbody tr {
          cursor: auto;

          td {
            vertical-align: middle;

            .actions {
              float: right;
              max-width: 150px;
            }

            .btn-link {
              color: var(--primary) !important;

              &:hover {
                color: var(--primary) !important;
              }
            }
          }
        }
      }

      .hiddenRow {
        padding: 0 !important;
      }
    }
  }
}
