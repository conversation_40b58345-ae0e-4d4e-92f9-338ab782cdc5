#page-local-gamification-manager-products {
  @import "./../components/table.scss";

  .modal {
    .upload {
      .area {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #8a8a8a;
        aspect-ratio: 1 / 1;
        max-width: 180px;
        margin: 0 auto;
        border-radius: 10px;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;

        &.is-invalid {
          border: 1px solid #ca3120 !important;
        }

        img.icon {
          width: 50px;
          height: auto;
          filter: unset;
          cursor: pointer;
          margin: 0;
          background-color: #0000003d;
          border-radius: 50%;
          padding: 10px;
        }
      }

      .info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        overflow: hidden;

        label {
          margin-top: 12px;
          margin-bottom: 0;
        }

        span {
          font-size: 13px;
        }
      }
    }

    .modal-lg {
      @media (min-width: 992px) {
        max-width: 850px;
      }
    }
  }

  form#form_product {
    input::placeholder,
    textarea::placeholder {
      color: #9b9b9b;
    }
  }

  .simplesearchform {
    padding: 0;
  }

  .no-overflow:has(> table) {
    overflow-x: auto;
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;

    padding-top: 30px;

    @media (max-width: 576px) {
      // overflow-x: auto;
      padding-top: 45px;
      padding-bottom: 15px;
    }
  }

  table {
    thead {
      tr {
        th {
          &.c4 {
            text-align: right;
          }
        }
      }
    }

    tbody {
      tr {
        td {
          &.c0 {
            width: 180px;
          }

          &.c1 {
            overflow: hidden;
            text-overflow: ellipsis;
          }

          &.c4 {
            min-width: 275px;

            @media (max-width: 576px) {
              min-width: 260px;
            }
          }

          .product-cover {
            width: 150px;
            height: 80px;
            object-fit: cover;
            border-radius: 20px;

            @media (max-width: 576px) {
              width: 80px;
              height: 50px;
              border-radius: 10px;
            }
          }

          .product-name,
          .product-amount {
            font-size: 16px;
            font-weight: bold;

            @media (max-width: 576px) {
              font-size: 14px;
            }
          }

          .product-amount {
            @media (max-width: 576px) {
              font-size: 13px;
            }
          }

          .product-description,
          .product-price {
            color: #a2a4a6;

            @media (max-width: 576px) {
              font-size: 13px;
            }
          }

          .badge {
            font-size: 13px;
            border-radius: 0.2rem !important;

            @media (max-width: 576px) {
              font-size: 10px;
            }
          }

          .actions {
            display: flex;
            gap: 20px;
            justify-content: end;
            align-items: center;

            .dropdown-menu {
              .dropdown-item {
                color: #fff !important;
              }
            }
          }
        }
      }
    }
  }

  @import "../components/pagination.scss";
}
