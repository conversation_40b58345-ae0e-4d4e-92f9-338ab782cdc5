::ng-deep #page-local-gamification-index {

  padding: 0 16px 20px 16px;
  @import "components/table.scss";

  .header-content {
    align-items: center;
    gap: 15px;
  }

  .userinitials.size-35 {
    width: 35px;
    height: 35px;
  }
  .userinitials {
      background-color: #e9ecef;
      vertical-align: middle;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: #343a40;
      font-weight: 400;
      margin-right: .25rem;
  }

  .form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0 .75rem;
    font-weight: 400;
    line-height: 1.5;
    border: 1px solid #8f959e;
    border-radius: .5rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    margin-left: 5px !important;
    color: #dee2e6;
    background-color: #212529;
    border-color: #495057;
  }

  select {
    width: auto;
    word-wrap: normal;
    text-transform: none;
  }

  .progress-bar{
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    transition: width 0.6s ease;
  }


  .box {
    background-color: #24292d;
    padding: 20px 16px;
    border: 1px solid #343a40;
    border-radius: 15px;
  }

  #section-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;

    @media (max-width: 425px) {
      flex-direction: column;
    }

    .user-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;

      h5 {
        font-size: 17px;
        font-weight: bold;
      }

      span {
        font-size: 14px;
      }

      img.userpicture {
        width: 70px;
        height: 70px;
        border-radius: 50%;

        @media (max-width: 425px) {
          width: 40px;
          height: 40px;
        }
      }

      .userinitials {
        display: flex;
        justify-content: center;
        align-items: center;
        min-width: 50px !important;
        min-height: 50px !important;
        width: 50px !important;
        height: 50px !important;
        font-size: 22px;
        border-radius: 35px;
        border: 1px solid #343a40;
        padding: 10px;
      }
    }

    .progress-info {
      text-align: center;

      .user-coins {
        font-size: 18px;

        img {
          filter: unset;
          margin: 0;
          width: 25px;
          height: 25px;
        }
      }

      .level {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 3px;
        margin-top: 5px;

        > span {
          font-size: 12px;
          sup, small {
            font-size: 10px;
          }

          sup{
            margin-right: 5px;
          }
        }

        .progress {
          display: flex;
          position: relative;
          width: 170px;
          height: 1rem;
          overflow: hidden;
          line-height: 0;
          font-size: 0.703125rem;
          border-radius: 0.5rem;
          background-color: #343a40;

          > span {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 11px;
          }

          .progress-bar {
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            background-color: #dc3545;
            transition: width 0.6s ease;
          }
        }
      }
    }
  }

  #section-widgets {
    @media (max-width: 1199px) {
      gap: 2rem;
    }

    .banner {
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      height: 100%;
      background: rgb(183, 29, 70);
      background: linear-gradient(
        144deg,
        rgba(183, 29, 70, 1) 0%,
        rgba(161, 42, 116, 1) 0%,
        rgba(75, 57, 178, 1) 100%
      );
      border-radius: 15px;
      padding: 25px;

      > a {
        color: #fff !important;
      }
    }

    .indicators {
      margin-top: 30px;

      @media (max-width: 575px) {
        gap: 20px;
      }

      .progress-bar {
        color: white;
        width: 170px;
        height: 170px;
        margin: auto;
        border-radius: 50%;
        background: radial-gradient(
            closest-side,
            #1a1e22 85%,
            transparent 95% 100%
          ),
          conic-gradient(#1a58b6 var(--percent), #1a1e22 0);

        @media (min-width: 576px) and (max-width: 767px) {
          margin-bottom: 20px;
        }

        > div {
          font-size: 11px;
          font-weight: 900;
          padding: 0 20px;
          text-wrap: wrap;
        }
      }

      .widgets {
        display: grid;
        gap: 15px;
        align-items: center;
        padding-right: 2px;
        padding-left: 2px;

        @media (max-width: 575px) {
          gap: 20px;
        }

        .widget {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 10px;
          padding: 15px;
          background-color: #1a1e22;
          border-radius: 16px;
          height: 93px;

          .col1 {
            .description {
              font-size: 11px;
              font-weight: bold;

              @media (min-width: 1200px) and (max-width: 1405px) {
                font-size: 9px;
              }
            }

            .value {
              font-weight: bold;
              font-size: 20px;
            }
          }

          .col2 {
            background-color: #0377f2;
            border-radius: 10px;
            padding: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 47px !important;
            height: 47px !important;
            min-width: 47px !important;
            min-height: 47px !important;

            ion-icon {
              font-size: 24px !important;
            }

            img.icon {
              margin: 0;
              width: 28px;
              height: 28px;
              filter: unset;
            }
          }
        }
      }
    }

    .pages {
      display: flex;
      flex-direction: column;
      gap: 2rem;

      .box {
        display: flex;
        align-items: center;
        height: 80px;

        a {
          width: 100%;
          text-align: left;

          ion-icon {
            font-size: 24px !important;
            margin-right: 0.5rem;
          }

          img {
            filter: unset;
            width: 25px;
            height: auto;
          }
        }
      }
    }
  }

  .rank {
    > .header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;

      @media (max-width: 575px) {
        flex-direction: column;
      }

      > .filter {
        width: 300px;
        padding-left: 15px;
        padding-right: 15px;
      }
    }

    table {
      .c1,
      .c3 {
        text-align: center;
      }

      .c2 {
        text-align: left;
      }

      .c4 {
        text-align: right;
      }

      .c1 {
        max-width: 60px;
      }

      .progress {
        display: flex;
        position: relative;
        height: 1rem;
        overflow: hidden;
        line-height: 0;
        font-size: 0.703125rem;
        border-radius: 0.5rem;

        > span {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .progress-bar {
          display: flex;
          flex-direction: column;
          justify-content: center;
          overflow: hidden;
          color: #fff;
          text-align: center;
          white-space: nowrap;
          background-color: #dc3545;
          transition: width 0.6s ease;
        }
      }
      .full-progress {
        color: #a2a4a6;
        font-size: 13px;
      }
    }
  }

  .goals {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(5, 1fr);

    // .card
    @import "components/goals-card.scss";

    @media (max-width: 575px) {
      & {
        grid-template-columns: repeat(1, 1fr);
      }
    }

    @media (min-width: 576px) and (max-width: 991px) {
      & {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (min-width: 992px) and (max-width: 1199px) {
      & {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    @media (min-width: 1200px) {
      & {
        grid-template-columns: repeat(4, 1fr);
      }
    }

    @media (min-width: 1600px) {
      & {
        grid-template-columns: repeat(5, 1fr);
      }
    }
  }

  .empty {
    margin-top:5px;
    display: flex;
    justify-content: center;

    img {
      width: 175px;
      height: auto;
      filter: url();
    }
  }
}
