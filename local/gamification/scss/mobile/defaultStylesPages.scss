::ng-deep .gamification-pages{

    .userpicture {
        border-radius: 50%;
    }
    img {
        max-width: none !important;
        vertical-align: middle;
        border-style: none;
    }

    .badge {
        display: inline-block;
        padding: .25em .4em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: .25rem;
    }

    .badge-success {
        color: #fff;
        background-color: #28a745;
    }

    .badge-primary {
        color: #fff;
        background-color: #007bff;
    }

    .badge-secondary {
        color: #fff;
        background-color: #6c757d;
    }

    .badge-danger {
        color: #fff;
        background-color: #dc3545;
    }

    .badge-warning {
        color: #212529;
        background-color: #ffc107;
    }

    .badge-info {
        color: #fff;
        background-color: #17a2b8;
    }

    .badge-light {
        color: #212529;
        background-color: #f8f9fa;
    }

    .badge-dark {
        color: #fff;
        background-color: #343a40;
    }
        
    .ml-1, .mx-1 {
        margin-left: 0.25rem !important;
    }

    hr {
        margin-top: 1rem;
        margin-bottom: 1rem;
        border: 0;
        border-top: 1px solid #495057;
        box-sizing: content-box;
        height: 0;
        overflow: visible;
    }

    .form-control {
        color: #dee2e6;
        background-color: #212529;
        display: block;
        width: 100%;
        height: calc(1.5em + 0.75rem + 2px);
        padding: 0 .75rem;
        font-weight: 400;
        line-height: 1.5;
        border: 1px solid #495057;
        border-radius: .5rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        margin-left: 0 !important;
    }

    .no-overflow {
        overflow: auto;
    }

    div:has(>table) {
        overflow-x: auto;
        scrollbar-width: thin;
        scrollbar-color: #6a737b #343a40;
    }

    .table {
        width: 100%;
        margin-bottom: 1rem;
        color: #fff;
        thead th {
            border-bottom-color: #454d55;
        }

        th, td {
            border-top: 1px solid #dee2e6;
            padding: .75rem;
        }
    }

    table {
        border-collapse: collapse;
    }

    sup {
        top: -.5em;
    }
    sub, sup {
        position: relative;
        font-size: 75%;
        line-height: 0;
        vertical-align: baseline;
    }

    .card-body {
        flex: 1 1 auto;
        min-height: 1px;
        padding: 1.25rem;
    }

    @media (max-width: 767.98px) {
        .card .card-body {
            padding: .625rem;
        }
    }

    .card-footer {
        padding: .75rem 1.25rem;
    }

    .card-footer:last-child {
        border-radius: 0 0 calc(0.5rem - 1px) calc(0.5rem - 1px);
    }

    .btn-dark {
        color: #fff;
        background-color: #343a40;
        border-color: #343a40;
    }

    .rounded-pill {
        border-radius: 50rem !important;
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        align-items: start;
        gap: 15px;
        margin-bottom: 30px;
    }

    .justify-content-center {
        justify-content: center !important;
    }

    *{
        font-family: "Helvetica Neue", sans-serif;
        font-size: calc(0.90375rem + 0.045vw);
    }

    .mr-2 {
        margin-right: 0.5rem !important;
    }

    .mr-3 {
        margin-right: 1rem !important;
    }

    .mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .mb-5, .my-5 {
        margin-bottom: 2rem !important;
    }

    .modal-overlay {
        content: "";
        position: absolute;
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1050;
        background: #2c3e50;
        opacity: .6;
    }

    .modal-wrapper {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        margin: auto;
        width: fit-content;
        height: fit-content;
        padding: 2rem;
        z-index: 1051;
        transform: none;
    }

    .modal-header {
        border-bottom-color: #495057;
        background: #212529;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #495057;
        border-top-left-radius: calc(0.6rem - 1px);
        border-top-right-radius: calc(0.6rem - 1px);
    }

    .modal-content {
        background-color: #212529;
        border-color: rgba(255, 255, 255, .2);
        background: #212529;
        border-radius: 0;
        border: none;
        padding: 20px;
        text-align: center;
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        outline: 0;
    }

    .modal-footer {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-end;
        padding: 15px;
        border-top: 1px solid #dee2e6;
        border-bottom-right-radius: calc(0.6rem - 1px);
        border-bottom-left-radius: calc(0.6rem - 1px);
        background: #212529;
        border-top-color: #495057;
    }

    .mb-3 {
    margin-bottom: 1rem !important;
    }

    .mb-0 {
    margin-bottom: 0 !important;
    }

    .pt-3 {
        padding-top: 1rem;
    }

    .mb-1 {
    margin-bottom: 0.25rem !important;
    }

    .pr-1 {
    padding-right: 0.25rem !important;
    }

    .mt-2 {
    margin-top: 0.5rem !important;
    }

    .m-1 {
    margin: 0.25rem !important;
    }

    .btn-outline-secondary {
        color: #6c757d !important;
        background-color: transparent !important;
        border: 1px solid #6c757d !important;
    }
    
    .btn-outline-secondary:hover {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 0.375rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .btn-primary {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .btn-primary:hover {
        color: #fff;
        background-color: #0b5ed7;
        border-color: #0a58ca;
    }

    .btn-primary.focus {
        color: #fff;
        background-color: #0b5ed7;
        border-color: #0a58ca;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.5);
    }

    .btn-primary.disabled {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;
        opacity: 0.65;
    }  

    .mt-5 {
        margin-top: 3rem !important;
    }

    .mt-0 {
        margin-top: 0 !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .mt-4 {
        margin-top: 1.5rem !important;
    }

    .w-75 {
        width: 75% !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .mx-2 {
        margin-left: 0.5rem !important;
        margin-right: 0.5rem !important;
    }

    .d-inline-flex {
        display: inline-flex !important;
    }

    .d-flex {
        display: flex !important;
    }

    .d-none {
        display: none !important;
    }

    .flex-wrap {
        flex-wrap: wrap !important;
    }

    .justify-content-left {
        justify-content: flex-start !important;
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .col-lg-12 {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .justify-content-right {
        justify-content: flex-end !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
    }

    .alert-primary {
        color: rgb(129, 180, 254);
        background-color: rgb(3, 22, 51);
        border-color: rgb(4, 31, 71);
    }

    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .col-form-label {
        padding-top: calc(0.375rem + 1px);
        padding-bottom: calc(0.375rem + 1px);
        margin-bottom: 0;
        font-size: inherit;
        line-height: 1.5;
    }
    .col-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    
    .col-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }

    h2, .h2 {
        font-size: 1.875rem;
    }

    h3, .h3 {
        font-size: 1.640625rem;
    }

    h4, .h4 {
        font-size: 1.40625rem;
    }

    @media (max-width: 1200px) {
        h3, .h3 {
            font-size: calc(0.9740625rem + 0.88875vw);
        }

        h4, .h4 {
            font-size: calc(0.950625rem + 0.6075vw);
        }

        .form-control {
            font-size: calc(0.90375rem + 0.045vw);
        }

        h2, .h2 {
            font-size: calc(0.9975rem + 1.17vw);
        }
    }

    h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
        margin-top: 0;
        margin-bottom: .5rem;
        font-weight: 700;
        line-height: 1.2;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    a {
        text-decoration: none;
        background-color: transparent;
    }

    .alert {
        position: relative;
        padding: .75rem 1.25rem;
        margin-bottom: 1rem;
        border: 0 solid transparent;
        border-radius: .5rem;
    }

    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
        display: inline-block;
        font-weight: 400;
        text-align: center;
        vertical-align: middle;
        cursor: pointer;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .rounded-circle {
        border-radius: 50% !important;
    }

    .p-1 {
        padding: 0.25rem !important;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 768px) {  
        .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
    }

    @media (min-width: 992px) {
        .col-lg-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }
    }

    @media (min-width: 1200px) {
        .col-xl-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }
        .col-xl-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
    }

    .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col, .col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm, .col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md, .col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg, .col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl, .col-xl-auto {
        position: relative;
        width: 100%;
        padding-right: 2px;
        padding-left: 2px;
    }

    @media (min-width: 768px) {
        .col-md-4 {
            flex: 0 0 33.33333333% !important;
            max-width: 33.33333333% !important;
        }
    }
    @media (min-width: 576px) and (max-width: 767px) {
        .col-sm-6 {
            flex: 0 0 50% !important;
            max-width: 50% !important;
        }
    }

    .align-self-center {
    align-self: center !important;
    }

    .card-link {
    background-color: #343a40 !important;
    color: #fff !important;
    }

    .justify-content-center {
    justify-content: center !important;
    }

    .align-items-end {
    align-items: flex-end !important;
    }

    .p-0 {
    padding: 0 !important;
    }

    .p-2 {
    padding: 0.5rem !important;
    }

    .text-uppercase {
    text-transform: uppercase !important;
    }

    .text-muted {
    color: #6c757d !important;
    }

    .mt-3 {
    margin-top: 1rem !important;
    }

    ion-icon {
        font-size: 18px !important;
    }

    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid rgba(255, 255, 255, .175);
        border-radius: .5rem;
    }
}