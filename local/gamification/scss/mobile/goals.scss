::ng-deep #page-local-gamification-goals {
  padding: 0 16px 20px 16px;
  .goals {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(5, 1fr);

    // .card
    @import "components/goals-card.scss";

    @media (max-width: 575px) {
      & {
        grid-template-columns: repeat(1, 1fr);
      }
    }

    @media (min-width: 576px) and (max-width: 991px) {
      & {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (min-width: 992px) and (max-width: 1199px) {
      & {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    @media (min-width: 1200px) {
      & {
        grid-template-columns: repeat(4, 1fr);
      }
    }

    @media (min-width: 1600px) {
      & {
        grid-template-columns: repeat(5, 1fr);
      }
    }
  }

  .table-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    gap: 20px;

    select {
      margin: 0 5px;
    }

    @media (max-width: 576px) {
      flex-direction: column;
    }

    @import "components/per-page.scss";

    @import "components/pagination.scss";
  }

  .empty {
    display: flex;
    justify-content: center;

    img {
      width: 175px;
      height: auto;
      filter: url();
    }
  }
}
