::ng-deep .air-datepicker-cell.-year-.-other-decade-,
::ng-deep .air-datepicker-cell.-day-.-other-month- {
  color: var(--adp-color-other-month);
}
::ng-deep .air-datepicker-cell.-year-.-other-decade-:hover,
::ng-deep .air-datepicker-cell.-day-.-other-month-:hover {
  color: var(--adp-color-other-month-hover);
}
::ng-deep .-disabled-.-focus-.air-datepicker-cell.-year-.-other-decade-,
::ng-deep .-disabled-.-focus-.air-datepicker-cell.-day-.-other-month- {
  color: var(--adp-color-other-month);
}
::ng-deep .-selected-.air-datepicker-cell.-year-.-other-decade-,
::ng-deep .-selected-.air-datepicker-cell.-day-.-other-month- {
  color: #fff;
  background: var(--adp-background-color-selected-other-month);
}
::ng-deep .-selected-.-focus-.air-datepicker-cell.-year-.-other-decade-,
::ng-deep .-selected-.-focus-.air-datepicker-cell.-day-.-other-month- {
  background: var(--adp-background-color-selected-other-month-focused);
}
::ng-deep .-in-range-.air-datepicker-cell.-year-.-other-decade-,
::ng-deep .-in-range-.air-datepicker-cell.-day-.-other-month- {
  background-color: var(--adp-background-color-in-range);
  color: var(--adp-color);
}
::ng-deep .-in-range-.-focus-.air-datepicker-cell.-year-.-other-decade-,
::ng-deep .-in-range-.-focus-.air-datepicker-cell.-day-.-other-month- {
  background-color: var(--adp-background-color-in-range-focused);
}
::ng-deep .air-datepicker-cell.-year-.-other-decade-:empty,
::ng-deep .air-datepicker-cell.-day-.-other-month-:empty {
  background: none;
  border: none;
}
::ng-deep .air-datepicker-cell {
  border-radius: var(--adp-cell-border-radius);
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
::ng-deep .air-datepicker-cell.-focus- {
  background: var(--adp-cell-background-color-hover);
}
::ng-deep .air-datepicker-cell.-current- {
  color: var(--adp-color-current-date);
}
::ng-deep .air-datepicker-cell.-current-.-focus- {
  color: var(--adp-color);
}
::ng-deep .air-datepicker-cell.-current-.-in-range- {
  color: var(--adp-color-current-date);
}
::ng-deep .air-datepicker-cell.-disabled- {
  cursor: default;
  color: var(--adp-color-disabled);
}
::ng-deep .air-datepicker-cell.-disabled-.-focus- {
  color: var(--adp-color-disabled);
}
::ng-deep .air-datepicker-cell.-disabled-.-in-range- {
  color: var(--adp-color-disabled-in-range);
}
::ng-deep .air-datepicker-cell.-disabled-.-current-.-focus- {
  color: var(--adp-color-disabled);
}
::ng-deep .air-datepicker-cell.-in-range- {
  background: var(--adp-cell-background-color-in-range);
  border-radius: 0;
}
::ng-deep .air-datepicker-cell.-in-range-:hover,
::ng-deep .air-datepicker-cell.-in-range-.-focus- {
  background: var(--adp-cell-background-color-in-range-hover);
}
::ng-deep .air-datepicker-cell.-range-from- {
  border: 1px solid var(--adp-cell-border-color-in-range);
  background-color: var(--adp-cell-background-color-in-range);
  border-radius: var(--adp-cell-border-radius) 0 0 var(--adp-cell-border-radius);
}
::ng-deep .air-datepicker-cell.-range-to- {
  border: 1px solid var(--adp-cell-border-color-in-range);
  background-color: var(--adp-cell-background-color-in-range);
  border-radius: 0 var(--adp-cell-border-radius) var(--adp-cell-border-radius) 0;
}
::ng-deep .air-datepicker-cell.-range-to-.-range-from- {
  border-radius: var(--adp-cell-border-radius);
}
::ng-deep .air-datepicker-cell.-selected- {
  color: #fff;
  border: none;
  background: var(--adp-cell-background-color-selected);
}
::ng-deep .air-datepicker-cell.-selected-.-current- {
  color: #fff;
  background: var(--adp-cell-background-color-selected);
}
::ng-deep .air-datepicker-cell.-selected-.-focus- {
  background: var(--adp-cell-background-color-selected-hover);
}
::ng-deep .air-datepicker-body {
  transition: all var(--adp-transition-duration) var(--adp-transition-ease);
}
::ng-deep .air-datepicker-body.-hidden- {
  display: none;
}
::ng-deep .air-datepicker-body--day-names {
  display: grid;
  grid-template-columns: repeat(7, var(--adp-day-cell-width));
  margin: 8px 0 3px;
}
::ng-deep .air-datepicker-body--day-name {
  color: var(--adp-day-name-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  text-transform: uppercase;
  font-size: 0.8em;
}
::ng-deep .air-datepicker-body--day-name.-clickable- {
  cursor: pointer;
}
::ng-deep .air-datepicker-body--day-name.-clickable-:hover {
  color: var(--adp-day-name-color-hover);
}
::ng-deep .air-datepicker-body--cells {
  display: grid;
}
::ng-deep .air-datepicker-body--cells.-days- {
  grid-template-columns: repeat(7, var(--adp-day-cell-width));
  grid-auto-rows: var(--adp-day-cell-height);
}
::ng-deep .air-datepicker-body--cells.-months- {
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: var(--adp-month-cell-height);
}
::ng-deep .air-datepicker-body--cells.-years- {
  grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: var(--adp-year-cell-height);
}
::ng-deep .air-datepicker-nav {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--adp-border-color-inner);
  min-height: var(--adp-nav-height);
  padding: var(--adp-padding);
  box-sizing: content-box;
}
::ng-deep .-only-timepicker- .air-datepicker-nav {
  display: none;
}
::ng-deep .air-datepicker-nav--title,
::ng-deep .air-datepicker-nav--action {
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}
::ng-deep .air-datepicker-nav--action {
  width: var(--adp-nav-action-size);
  border-radius: var(--adp-border-radius);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
::ng-deep .air-datepicker-nav--action:hover {
  background: var(--adp-background-color-hover);
}
::ng-deep .air-datepicker-nav--action:active {
  background: var(--adp-background-color-active);
}
::ng-deep .air-datepicker-nav--action.-disabled- {
  visibility: hidden;
}
::ng-deep .air-datepicker-nav--action svg {
  width: 32px;
  height: 32px;
}
::ng-deep .air-datepicker-nav--action path {
  fill: none;
  stroke: var(--adp-nav-arrow-color);
  stroke-width: 2px;
}
::ng-deep .air-datepicker-nav--title {
  border-radius: var(--adp-border-radius);
  padding: 0 8px;
}
::ng-deep .air-datepicker-nav--title i {
  font-style: normal;
  color: var(--adp-nav-color-secondary);
  margin-left: 0.3em;
}
::ng-deep .air-datepicker-nav--title:hover {
  background: var(--adp-background-color-hover);
}
::ng-deep .air-datepicker-nav--title:active {
  background: var(--adp-background-color-active);
}
::ng-deep .air-datepicker-nav--title.-disabled- {
  cursor: default;
  background: none;
}
::ng-deep .air-datepicker-buttons {
  display: grid;
  grid-auto-columns: 1fr;
  grid-auto-flow: column;
}
::ng-deep .air-datepicker-button {
  display: inline-flex;
  color: var(--adp-btn-color);
  border-radius: var(--adp-btn-border-radius);
  cursor: pointer;
  height: var(--adp-btn-height);
  border: none;
  background: rgba(255, 255, 255, 0);
}
::ng-deep .air-datepicker-button:hover {
  color: var(--adp-btn-color-hover);
  background: var(--adp-btn-background-color-hover);
}
::ng-deep .air-datepicker-button:focus {
  color: var(--adp-btn-color-hover);
  background: var(--adp-btn-background-color-hover);
  outline: none;
}
::ng-deep .air-datepicker-button:active {
  background: var(--adp-btn-background-color-active);
}
::ng-deep .air-datepicker-button span {
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
::ng-deep .air-datepicker-time {
  display: grid;
  grid-template-columns: max-content 1fr;
  grid-column-gap: 12px;
  align-items: center;
  position: relative;
  padding: 0 var(--adp-time-padding-inner);
}
::ng-deep .-only-timepicker- .air-datepicker-time {
  border-top: none;
}
::ng-deep .air-datepicker-time--current {
  display: flex;
  align-items: center;
  flex: 1;
  font-size: 14px;
  text-align: center;
}
::ng-deep .air-datepicker-time--current-colon {
  margin: 0 2px 3px;
  line-height: 1;
}
::ng-deep .air-datepicker-time--current-hours,
::ng-deep .air-datepicker-time--current-minutes {
  line-height: 1;
  font-size: 19px;
  font-family: "Century Gothic", CenturyGothic, AppleGothic, sans-serif;
  position: relative;
  z-index: 1;
}
::ng-deep .air-datepicker-time--current-hours:after,
::ng-deep .air-datepicker-time--current-minutes:after {
  content: "";
  background: var(--adp-background-color-hover);
  border-radius: var(--adp-border-radius);
  position: absolute;
  left: -2px;
  top: -3px;
  right: -2px;
  bottom: -2px;
  z-index: -1;
  opacity: 0;
}
::ng-deep .air-datepicker-time--current-hours.-focus-:after,
::ng-deep .air-datepicker-time--current-minutes.-focus-:after {
  opacity: 1;
}
::ng-deep .air-datepicker-time--current-ampm {
  text-transform: uppercase;
  align-self: flex-end;
  color: var(--adp-time-day-period-color);
  margin-left: 6px;
  font-size: 11px;
  margin-bottom: 1px;
}
::ng-deep .air-datepicker-time--row {
  display: flex;
  align-items: center;
  font-size: 11px;
  height: 17px;
  background: linear-gradient(
      to right,
      var(--adp-time-track-color),
      var(--adp-time-track-color)
    )
    left 50%/100% var(--adp-time-track-height) no-repeat;
}
::ng-deep .air-datepicker-time--row:first-child {
  margin-bottom: 4px;
}
::ng-deep .air-datepicker-time--row input[type="range"] {
  background: none;
  cursor: pointer;
  flex: 1;
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  -webkit-appearance: none;
}
::ng-deep .air-datepicker-time--row input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
}
::ng-deep .air-datepicker-time--row input[type="range"]::-ms-tooltip {
  display: none;
}
::ng-deep .air-datepicker-time--row input[type="range"]:hover::-webkit-slider-thumb {
  border-color: var(--adp-time-track-color-hover);
}
::ng-deep .air-datepicker-time--row input[type="range"]:hover::-moz-range-thumb {
  border-color: var(--adp-time-track-color-hover);
}
::ng-deep .air-datepicker-time--row input[type="range"]:hover::-ms-thumb {
  border-color: var(--adp-time-track-color-hover);
}
::ng-deep .air-datepicker-time--row input[type="range"]:focus {
  outline: none;
}
::ng-deep .air-datepicker-time--row input[type="range"]:focus::-webkit-slider-thumb {
  background: var(--adp-cell-background-color-selected);
  border-color: var(--adp-cell-background-color-selected);
}
::ng-deep .air-datepicker-time--row input[type="range"]:focus::-moz-range-thumb {
  background: var(--adp-cell-background-color-selected);
  border-color: var(--adp-cell-background-color-selected);
}
::ng-deep .air-datepicker-time--row input[type="range"]:focus::-ms-thumb {
  background: var(--adp-cell-background-color-selected);
  border-color: var(--adp-cell-background-color-selected);
}
::ng-deep .air-datepicker-time--row input[type="range"]::-webkit-slider-thumb {
  box-sizing: border-box;
  height: 12px;
  width: 12px;
  border-radius: 3px;
  border: 1px solid var(--adp-time-track-color);
  background: #fff;
  cursor: pointer;
  -webkit-transition: background var(--adp-transition-duration);
  transition: background var(--adp-transition-duration);
}
::ng-deep .air-datepicker-time--row input[type="range"]::-moz-range-thumb {
  box-sizing: border-box;
  height: 12px;
  width: 12px;
  border-radius: 3px;
  border: 1px solid var(--adp-time-track-color);
  background: #fff;
  cursor: pointer;
  -moz-transition: background var(--adp-transition-duration);
  transition: background var(--adp-transition-duration);
}
::ng-deep .air-datepicker-time--row input[type="range"]::-ms-thumb {
  box-sizing: border-box;
  height: 12px;
  width: 12px;
  border-radius: 3px;
  border: 1px solid var(--adp-time-track-color);
  background: #fff;
  cursor: pointer;
  -ms-transition: background var(--adp-transition-duration);
  transition: background var(--adp-transition-duration);
}
::ng-deep .air-datepicker-time--row input[type="range"]::-webkit-slider-thumb {
  margin-top: calc(var(--adp-time-thumb-size) / 2 * -1);
}
::ng-deep .air-datepicker-time--row input[type="range"]::-webkit-slider-runnable-track {
  border: none;
  height: var(--adp-time-track-height);
  cursor: pointer;
  color: rgba(0, 0, 0, 0);
  background: rgba(0, 0, 0, 0);
}
::ng-deep .air-datepicker-time--row input[type="range"]::-moz-range-track {
  border: none;
  height: var(--adp-time-track-height);
  cursor: pointer;
  color: rgba(0, 0, 0, 0);
  background: rgba(0, 0, 0, 0);
}
::ng-deep .air-datepicker-time--row input[type="range"]::-ms-track {
  border: none;
  height: var(--adp-time-track-height);
  cursor: pointer;
  color: rgba(0, 0, 0, 0);
  background: rgba(0, 0, 0, 0);
}
::ng-deep .air-datepicker-time--row input[type="range"]::-ms-fill-lower {
  background: rgba(0, 0, 0, 0);
}
::ng-deep .air-datepicker-time--row input[type="range"]::-ms-fill-upper {
  background: rgba(0, 0, 0, 0);
}
::ng-deep .air-datepicker {
  --adp-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol";
  --adp-font-size: 14px;
  --adp-width: 246px;
  --adp-z-index: 100;
  --adp-padding: 4px;
  --adp-grid-areas: "nav" "body" "timepicker" "buttons";
  --adp-transition-duration: 0.3s;
  --adp-transition-ease: ease-out;
  --adp-transition-offset: 8px;
  --adp-background-color: #fff;
  --adp-background-color-hover: #f0f0f0;
  --adp-background-color-active: #eaeaea;
  --adp-background-color-in-range: rgba(92, 196, 239, 0.1);
  --adp-background-color-in-range-focused: rgba(92, 196, 239, 0.2);
  --adp-background-color-selected-other-month-focused: #8ad5f4;
  --adp-background-color-selected-other-month: #a2ddf6;
  --adp-color: #4a4a4a;
  --adp-color-secondary: #9c9c9c;
  --adp-accent-color: #4eb5e6;
  --adp-color-current-date: var(--adp-accent-color);
  --adp-color-other-month: #dedede;
  --adp-color-disabled: #aeaeae;
  --adp-color-disabled-in-range: #939393;
  --adp-color-other-month-hover: #c5c5c5;
  --adp-border-color: #dbdbdb;
  --adp-border-color-inner: #efefef;
  --adp-border-radius: 4px;
  --adp-border-color-inline: #d7d7d7;
  --adp-nav-height: 32px;
  --adp-nav-arrow-color: var(--adp-color-secondary);
  --adp-nav-action-size: 32px;
  --adp-nav-color-secondary: var(--adp-color-secondary);
  --adp-day-name-color: #ff9a19;
  --adp-day-name-color-hover: #8ad5f4;
  --adp-day-cell-width: 1fr;
  --adp-day-cell-height: 32px;
  --adp-month-cell-height: 42px;
  --adp-year-cell-height: 56px;
  --adp-pointer-size: 10px;
  --adp-poiner-border-radius: 2px;
  --adp-pointer-offset: 14px;
  --adp-cell-border-radius: 4px;
  --adp-cell-background-color-hover: var(--adp-background-color-hover);
  --adp-cell-background-color-selected: #5cc4ef;
  --adp-cell-background-color-selected-hover: #45bced;
  --adp-cell-background-color-in-range: rgba(92, 196, 239, 0.1);
  --adp-cell-background-color-in-range-hover: rgba(92, 196, 239, 0.2);
  --adp-cell-border-color-in-range: var(--adp-cell-background-color-selected);
  --adp-btn-height: 32px;
  --adp-btn-color: var(--adp-accent-color);
  --adp-btn-color-hover: var(--adp-color);
  --adp-btn-border-radius: var(--adp-border-radius);
  --adp-btn-background-color-hover: var(--adp-background-color-hover);
  --adp-btn-background-color-active: var(--adp-background-color-active);
  --adp-time-track-height: 1px;
  --adp-time-track-color: #dedede;
  --adp-time-track-color-hover: #b1b1b1;
  --adp-time-thumb-size: 12px;
  --adp-time-padding-inner: 10px;
  --adp-time-day-period-color: var(--adp-color-secondary);
  --adp-mobile-font-size: 16px;
  --adp-mobile-nav-height: 40px;
  --adp-mobile-width: 320px;
  --adp-mobile-day-cell-height: 38px;
  --adp-mobile-month-cell-height: 48px;
  --adp-mobile-year-cell-height: 64px;
}
::ng-deep .air-datepicker-overlay {
  --adp-overlay-background-color: rgba(0, 0, 0, 0.3);
  --adp-overlay-transition-duration: 0.3s;
  --adp-overlay-transition-ease: ease-out;
  --adp-overlay-z-index: 99;
}
::ng-deep .air-datepicker {
  background: var(--adp-background-color);
  border: 1px solid var(--adp-border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: var(--adp-border-radius);
  box-sizing: content-box;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: repeat(4, max-content);
  grid-template-areas: var(--adp-grid-areas);
  font-family: var(--adp-font-family), sans-serif;
  font-size: var(--adp-font-size);
  color: var(--adp-color);
  width: var(--adp-width);
  position: absolute;
  transition: opacity var(--adp-transition-duration) var(--adp-transition-ease),
    transform var(--adp-transition-duration) var(--adp-transition-ease);
  z-index: var(--adp-z-index);
}
::ng-deep .air-datepicker:not(.-custom-position-) {
  opacity: 0;
}
::ng-deep .air-datepicker.-from-top- {
  transform: translateY(calc(var(--adp-transition-offset) * -1));
}
::ng-deep .air-datepicker.-from-right- {
  transform: translateX(var(--adp-transition-offset));
}
::ng-deep .air-datepicker.-from-bottom- {
  transform: translateY(var(--adp-transition-offset));
}
::ng-deep .air-datepicker.-from-left- {
  transform: translateX(calc(var(--adp-transition-offset) * -1));
}
::ng-deep .air-datepicker.-active-:not(.-custom-position-) {
  transform: translate(0, 0);
  opacity: 1;
}
::ng-deep .air-datepicker.-active-.-custom-position- {
  transition: none;
}
::ng-deep .air-datepicker.-inline- {
  border-color: var(--adp-border-color-inline);
  box-shadow: none;
  position: static;
  left: auto;
  right: auto;
  opacity: 1;
  transform: none;
}
::ng-deep .air-datepicker.-inline- .air-datepicker--pointer {
  display: none;
}
::ng-deep .air-datepicker.-is-mobile- {
  --adp-font-size: var(--adp-mobile-font-size);
  --adp-day-cell-height: var(--adp-mobile-day-cell-height);
  --adp-month-cell-height: var(--adp-mobile-month-cell-height);
  --adp-year-cell-height: var(--adp-mobile-year-cell-height);
  --adp-nav-height: var(--adp-mobile-nav-height);
  --adp-nav-action-size: var(--adp-mobile-nav-height);
  position: fixed;
  width: var(--adp-mobile-width);
  border: none;
}
::ng-deep .air-datepicker.-is-mobile- * {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
::ng-deep .air-datepicker.-is-mobile- .air-datepicker--pointer {
  display: none;
}
::ng-deep .air-datepicker.-is-mobile-:not(.-custom-position-) {
  transform: translate(-50%, calc(-50% + var(--adp-transition-offset)));
}
::ng-deep .air-datepicker.-is-mobile-.-active-:not(.-custom-position-) {
  transform: translate(-50%, -50%);
}
::ng-deep .air-datepicker.-custom-position- {
  transition: none;
}
::ng-deep .air-datepicker-global-container {
  position: absolute;
  left: 0;
  top: 0;
}
::ng-deep .air-datepicker--pointer {
  --pointer-half-size: calc(var(--adp-pointer-size) / 2);
  position: absolute;
  width: var(--adp-pointer-size);
  height: var(--adp-pointer-size);
  z-index: -1;
}
::ng-deep .air-datepicker--pointer:after {
  content: "";
  position: absolute;
  background: #fff;
  border-top: 1px solid var(--adp-border-color-inline);
  border-right: 1px solid var(--adp-border-color-inline);
  border-top-right-radius: var(--adp-poiner-border-radius);
  width: var(--adp-pointer-size);
  height: var(--adp-pointer-size);
  box-sizing: border-box;
}
::ng-deep .-top-left- .air-datepicker--pointer,
::ng-deep .-top-center- .air-datepicker--pointer,
::ng-deep .-top-right- .air-datepicker--pointer,
::ng-deep [data-popper-placement^="top"] .air-datepicker--pointer {
  top: calc(100% - var(--pointer-half-size) + 1px);
}
::ng-deep .-top-left- .air-datepicker--pointer:after,
::ng-deep .-top-center- .air-datepicker--pointer:after,
::ng-deep .-top-right- .air-datepicker--pointer:after,
::ng-deep [data-popper-placement^="top"] .air-datepicker--pointer:after {
  transform: rotate(135deg);
}
::ng-deep .-right-top- .air-datepicker--pointer,
::ng-deep .-right-center- .air-datepicker--pointer,
::ng-deep .-right-bottom- .air-datepicker--pointer,
::ng-deep [data-popper-placement^="right"] .air-datepicker--pointer {
  right: calc(100% - var(--pointer-half-size) + 1px);
}
::ng-deep .-right-top- .air-datepicker--pointer:after,
::ng-deep .-right-center- .air-datepicker--pointer:after,
::ng-deep .-right-bottom- .air-datepicker--pointer:after,
::ng-deep [data-popper-placement^="right"] .air-datepicker--pointer:after {
  transform: rotate(225deg);
}
::ng-deep .-bottom-left- .air-datepicker--pointer,
::ng-deep .-bottom-center- .air-datepicker--pointer,
::ng-deep .-bottom-right- .air-datepicker--pointer,
::ng-deep [data-popper-placement^="bottom"] .air-datepicker--pointer {
  bottom: calc(100% - var(--pointer-half-size) + 1px);
}
::ng-deep .-bottom-left- .air-datepicker--pointer:after,
::ng-deep .-bottom-center- .air-datepicker--pointer:after,
::ng-deep .-bottom-right- .air-datepicker--pointer:after,
::ng-deep [data-popper-placement^="bottom"] .air-datepicker--pointer:after {
  transform: rotate(315deg);
}
::ng-deep .-left-top- .air-datepicker--pointer,
::ng-deep .-left-center- .air-datepicker--pointer,
::ng-deep .-left-bottom- .air-datepicker--pointer,
::ng-deep [data-popper-placement^="left"] .air-datepicker--pointer {
  left: calc(100% - var(--pointer-half-size) + 1px);
}
::ng-deep .-left-top- .air-datepicker--pointer:after,
::ng-deep .-left-center- .air-datepicker--pointer:after,
::ng-deep .-left-bottom- .air-datepicker--pointer:after,
::ng-deep [data-popper-placement^="left"] .air-datepicker--pointer:after {
  transform: rotate(45deg);
}
::ng-deep .-top-left- .air-datepicker--pointer,
::ng-deep .-bottom-left- .air-datepicker--pointer {
  left: var(--adp-pointer-offset);
}
::ng-deep .-top-right- .air-datepicker--pointer,
::ng-deep .-bottom-right- .air-datepicker--pointer {
  right: var(--adp-pointer-offset);
}
::ng-deep .-top-center- .air-datepicker--pointer,
::ng-deep .-bottom-center- .air-datepicker--pointer {
  left: calc(50% - var(--adp-pointer-size) / 2);
}
::ng-deep .-left-top- .air-datepicker--pointer,
::ng-deep .-right-top- .air-datepicker--pointer {
  top: var(--adp-pointer-offset);
}
::ng-deep .-left-bottom- .air-datepicker--pointer,
::ng-deep .-right-bottom- .air-datepicker--pointer {
  bottom: var(--adp-pointer-offset);
}
::ng-deep .-left-center- .air-datepicker--pointer,
::ng-deep .-right-center- .air-datepicker--pointer {
  top: calc(50% - var(--adp-pointer-size) / 2);
}
::ng-deep .air-datepicker--navigation {
  grid-area: nav;
}
::ng-deep .air-datepicker--content {
  box-sizing: content-box;
  padding: var(--adp-padding);
  grid-area: body;
}
::ng-deep .-only-timepicker- .air-datepicker--content {
  display: none;
}
::ng-deep .air-datepicker--time {
  grid-area: timepicker;
}
::ng-deep .air-datepicker--buttons {
  grid-area: buttons;
}
::ng-deep .air-datepicker--buttons,
::ng-deep .air-datepicker--time {
  padding: var(--adp-padding);
  border-top: 1px solid var(--adp-border-color-inner);
}
::ng-deep .air-datepicker-overlay {
  position: fixed;
  background: var(--adp-overlay-background-color);
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  opacity: 0;
  transition: opacity var(--adp-overlay-transition-duration)
      var(--adp-overlay-transition-ease),
    left 0s, height 0s, width 0s;
  transition-delay: 0s, var(--adp-overlay-transition-duration),
    var(--adp-overlay-transition-duration),
    var(--adp-overlay-transition-duration);
  z-index: var(--adp-overlay-z-index);
}
::ng-deep .air-datepicker-overlay.-active- {
  opacity: 1;
  width: 100%;
  height: 100%;
  transition: opacity var(--adp-overlay-transition-duration)
      var(--adp-overlay-transition-ease),
    height 0s, width 0s;
}
