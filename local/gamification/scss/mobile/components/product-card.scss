::ng-deep .card {
  width: 100%;
  border-radius: 25px;
  overflow: hidden;
  background-color: #131315;

  &:hover {
    opacity: 1;
    background: radial-gradient(
      circle,
      rgba(0, 0, 0, 0.2) 0%,
      rgb(0 0 0 / 20%) 100%
    );
  }

  .card-img img {
    width: 100%;
    height: 165px;
    object-fit: cover;
  }

  .card-body {
    padding-bottom: 5px;

    .name {
      color: #efefef;
      font-size: 17px;
      font-weight: bold;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
    }

    .description {
      color: #efefef;
      font-size: 13px;
      min-height: 39px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .coins {
      font-size: 19px;
      font-weight: bold;
      margin-top: 10px;
    }
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: transparent;
    border: 0;
    padding-top: 5px;

    .amount {
      font-size: 12px;
    }

    .buy {
      display: flex;
      color: #db3747 !important;
      font-weight: bold;

      ion-icon {
        margin-left: 5px;
      }
    }
  }
}
