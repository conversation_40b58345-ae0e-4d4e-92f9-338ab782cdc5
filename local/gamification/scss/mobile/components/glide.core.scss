::ng-deep .glide {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}
::ng-deep .glide * {
  box-sizing: inherit;
}
::ng-deep .glide__track {
  overflow: hidden;
}
::ng-deep .glide__slides {
  position: relative;
  width: 100%;
  list-style: none;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  touch-action: pan-Y;
  overflow: hidden;
  margin: 0;
  padding: 0;
  white-space: nowrap;
  display: flex;
  flex-wrap: nowrap;
  will-change: transform;
}
::ng-deep .glide__slides--dragging {
  user-select: none;
}
::ng-deep .glide__slide {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  white-space: normal;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}
::ng-deep .glide__slide a {
  user-select: none;
  -webkit-user-drag: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
::ng-deep .glide__arrows {
  -webkit-touch-callout: none;
  user-select: none;
}
::ng-deep .glide__bullets {
  -webkit-touch-callout: none;
  user-select: none;
}
::ng-deep .glide--rtl {
  direction: rtl;
}
