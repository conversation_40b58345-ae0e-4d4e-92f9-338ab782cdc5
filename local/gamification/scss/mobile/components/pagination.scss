::ng-deep nav.pagination {

  ul {
    margin: 0 !important;
    display: flex;
    padding-left: 0;
    border-radius: .5rem;
    list-style: none;
    
    @media (max-width: 767.98px) {
        flex-wrap: wrap;
        justify-content: center;
    }

    li.page-item {
      margin: 2px;

      .page-link {
        position: relative;
        display: block;
        padding: .5rem .75rem;
        margin-left: -1px;
        line-height: 1.25;
        color: #cce0ff;
        background-color: #03163300;
      }

      a {
        border: 0;
        border-radius: 5px;
        font-weight: bold;
        color: #fff !important;

        span {
          vertical-align: middle;
        }
      }

      &.active a {
        background-color: #031633;
        /* background-color: var(--primary) !important; */
      }

      &.disabled {
        cursor: not-allowed;
        span.page-link {
          border: 0;
          background-color: transparent;
        }
      }
    }
  }
}
