::ng-deep table {
  background-color: transparent !important;

  thead {
    border-bottom: 3px solid #0377f2;

    tr {
      th {
        border-top: 0;
        text-wrap: nowrap;

        &,
        a,
        a[data-sortable] {
          color: #0377f2 !important;
        }
      }
    }
  }

  tbody {
    tr {
      &:nth-of-type(odd) {
        background-color: transparent !important;
      }

      &[aria-expanded="true"] {
        td > i.fa-chevron-right {
          transform: rotate(90deg);
        }
      }

      &:first-child,
      &:last-child {
        .dropdown-menu {
          transform: translate3d(-147px, -114px, 0px) !important;
        }
      }

      td {
        text-wrap: nowrap;
        overflow: visible;
        vertical-align: middle;

        i {
          transition: all 0.35s;
        }
      }
    }
  }
}

::ng-deep .table-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  gap: 20px;

  select {
    margin: 0 5px;
  }

  @media (max-width: 576px) {
    flex-direction: column;
  }

  @import "./per-page.scss";

  @import "./pagination.scss";
}
