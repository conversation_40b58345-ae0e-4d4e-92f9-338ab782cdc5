::ng-deep .card {
  width: 100%;
  padding: 15px;
  border-radius: 25px;
  overflow: hidden;
  background-color: #24292d;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  border: 1px solid rgba(255, 255, 255, .125);

  &--completed {
    background-color: rgba(0, 0, 0, 0.1);
  }

  &:hover {
    opacity: 1;
    background: radial-gradient(
      circle,
      rgba(0, 0, 0, 0.2) 0%,
      rgb(0 0 0 / 20%) 100%
    );
  }

  .card-img {
    display: flex;
    justify-content: center;

    img {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      object-fit: cover;

      &.grayscale {
        opacity: 0.8;
        filter: grayscale(100%);
      }
    }
  }

  .card-body {
    text-align: center;
    padding-bottom: 5px;
    margin-top: 5px;

    .name {
      color: #efefef;
      font-size: 17px;
      font-weight: bold;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
    }

    .description {
      color: #efefef;
      font-size: 13px;
      min-height: 39px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    align-items: center;
    background-color: transparent;
    border: 0;
    padding-top: 20px;
    padding-bottom: 0;
  }
}
