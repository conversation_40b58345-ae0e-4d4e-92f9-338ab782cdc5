@import "../components/glide.core.scss";

::ng-deep #page-local-gamification-store-index {
  .block {
    margin-top: 30px;

    .glide {
      .glide-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .show-all {
          color: #fff;
          margin-bottom: 0;
        }

        .glide__arrows {
          display: inline-flex;
          gap: 8px;

          .glide__arrow {
            display: flex;
            height: 27px;
            width: 27px;
            min-width: 35px;
            min-height: 35px;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            padding: 0;

            ion-icon{
              font-size: 20px !important;
            }
          }
        }
      }

      .card {
        display: none;
        border: 1px solid rgba(255, 255, 255, .175);
      }

      @import "../components/product-card.scss";

      .skeleton {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        max-height: 337px;

        .card.is-loading {
          display: block;
          flex: 1 1 calc(100% / 5 - 16px);

          div:not(.card-body):not(.card-footer) {
            background: linear-gradient(
              -110deg,
              #24292d 8%,
              #282d33 18%,
              #24292d 33%
            );
            background-size: 200% 100%;
            animation: shine 1.5s linear infinite;
          }

          .card-img {
            height: 165px;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
          }

          .name {
            height: 22px;
            width: 70%;
          }

          .description {
            height: 36px;
            min-height: 36px;
          }

          .coins {
            height: 25px;
            width: 60%;
          }

          .amount {
            height: 18px;
            width: 48%;
          }

          .buy {
            height: 18px;
            width: 38%;
          }

          @media (max-width: 575px) {
            & {
              flex: 1 1 calc(100% / 1 - 16px);
            }
          }

          @media (min-width: 576px) and (max-width: 991px) {
            & {
              flex: 1 1 calc(100% / 2 - 16px);
            }
          }

          @media (min-width: 992px) and (max-width: 1199px) {
            & {
              flex: 1 1 calc(100% / 3 - 16px);
            }
          }

          @media (min-width: 1200px) {
            & {
              flex: 1 1 calc(100% / 4 - 16px);
            }
          }

          @media (min-width: 1600px) {
            & {
              flex: 1 1 calc(100% / 5 - 16px);
            }
          }
        }

        @keyframes shine {
          to {
            background-position-x: -200%;
          }
        }
      }

      .empty {
        display: flex;
        justify-content: center;

        img {
          width: 175px;
          height: auto;
          filter: url();
        }
      }
    }
  }
}
