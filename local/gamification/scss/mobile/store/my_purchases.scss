@import "../components/glide.core.scss";

::ng-deep #page-local-gamification-store-my_purchases {
  @import "../components/table.scss";

  .badge-warning {
    color: #1d2125;
    background-color: #f0ad4e;
  }

  .badge {
    border-radius: .2rem !important;
    display: inline-block;
    padding: .25em .4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .5rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  @media (max-width: 576px) {
    .badge {
        font-size: 10px;
    }
  }

  .header-content {
    .balances {
      text-align: center;

      .user-points {
        font-size: 12px;
      }

      .user-coins {
        font-size: 17px;

        img {
          filter: unset;
          margin: 0;
          width: 25px;
          height: 25px;
        }
      }
    }
  }

  table {
    background-color: transparent !important;

    span {
      margin-bottom: 4px;
    }

    tr {
      td {
        border-top-color: #454d55;
        text-wrap: nowrap;

        &.c1 {
          width: 180px;
          vertical-align: middle;
        }

        &.c2 {
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: middle;
        }

        &.c3 {
          vertical-align: middle;

          > div {
            display: flex;
            justify-content: end;

            div {
              display: flex;
              flex-direction: column;
              gap: 5px;
            }
          }
        }

        .product-cover {
          min-width: 150px;
          height: 80px;
          object-fit: cover;
          border-radius: 20px;

          @media (max-width: 576px) {
            min-width: 80px;
            height: 50px;
            border-radius: 10px;
          }
        }

        .order-timecreated {
          font-size: 14px;
        }

        .product-name,
        .order-quantity {
          font-size: 16px;
          font-weight: bold;

          @media (max-width: 576px) {
            font-size: 14px;
          }
        }

        .order-quantity {
          @media (max-width: 576px) {
            font-size: 13px;
          }
        }

        .order-price,
        .order-timecreated,
        .product-description {
          color: #a2a4a6;

          @media (max-width: 576px) {
            font-size: 13px;
          }
        }
      }
    }
  }

  .block {
    margin-top: 30px;

    .glide {
      .glide-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .show-all {
          color: #fff;
          margin-bottom: 0;
        }

        .glide__arrows {
          display: inline-flex;
          gap: 8px;

          .glide__arrow {
            display: flex;
            height: 27px;
            width: 27px;
            min-width: 35px;
            min-height: 35px;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            padding: 0;
          }
        }
      }

      .card {
        display: none;
      }

      @import "../components/product-card.scss";

      .skeleton {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        max-height: 337px;

        .card.is-loading {
          display: block;
          flex: 1 1 calc(100% / 5 - 16px);

          div:not(.card-body):not(.card-footer) {
            background: linear-gradient(
              -110deg,
              #24292d 8%,
              #282d33 18%,
              #24292d 33%
            );
            background-size: 200% 100%;
            animation: shine 1.5s linear infinite;
          }

          .card-img {
            height: 165px;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
          }

          .name {
            height: 22px;
            width: 70%;
          }

          .description {
            height: 36px;
            min-height: 36px;
          }

          .coins {
            height: 25px;
            width: 60%;
          }

          .amount {
            height: 18px;
            width: 48%;
          }

          .buy {
            height: 18px;
            width: 38%;
          }

          @media (max-width: 575px) {
            & {
              flex: 1 1 calc(100% / 1 - 16px);
            }
          }

          @media (min-width: 576px) and (max-width: 991px) {
            & {
              flex: 1 1 calc(100% / 2 - 16px);
            }
          }

          @media (min-width: 992px) and (max-width: 1199px) {
            & {
              flex: 1 1 calc(100% / 3 - 16px);
            }
          }

          @media (min-width: 1200px) {
            & {
              flex: 1 1 calc(100% / 4 - 16px);
            }
          }

          @media (min-width: 1600px) {
            & {
              flex: 1 1 calc(100% / 5 - 16px);
            }
          }
        }

        @keyframes shine {
          to {
            background-position-x: -200%;
          }
        }
      }

      .empty {
        display: flex;
        justify-content: center;

        img {
          width: 175px;
          height: auto;
          filter: url();
        }
      }
    }
  }
}
