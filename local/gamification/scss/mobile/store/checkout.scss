::ng-deep #page-local-gamification-store-checkout {
  padding: 0 16px 20px 16px;

  @media (max-width: 1200px) {
      h5, .h5 {
        font-size: calc(0.9271875rem + 0.32625vw);
      }
  }

  label {
    display: inline-block;
    margin-bottom: .5rem;
  }

  .form-label{
    display: flex;
  }

  .delivery {
    // background-color: #212529;
    border: 1px solid #343a40;
    padding: 25px;
    border-radius: 8px;
    background-color: #24292d;
  }

  .detail {
    // background-color: #212529;
    background-color: #24292d;
    border: 1px solid #343a40;
    padding: 25px;
    border-radius: 8px;
    min-height: 620px;
    height: 100%;

    .product {
      margin-top: 20px;

      img {
        width: 100%;
        height: 200px;
        object-fit: cover;
      }

      .price {
        img {
          filter: unset;
          margin: 0;
          width: 20px;
          height: 20px;
        }
      }
    }

    .info {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .actions {
      position: absolute;
      display: flex;
      gap: 20px;
      bottom: 25px;
      right: 40px;

      @media (max-width: 575px) {
        position: unset;
        margin-top: 20px;
        flex-direction: column;
      }
    }

    @media (max-width: 575px) {
      margin-top: 20px;
    }
  }
}
