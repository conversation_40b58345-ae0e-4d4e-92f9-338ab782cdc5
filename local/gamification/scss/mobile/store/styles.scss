@import "store.scss";
@import "popular.scss";
@import "visibles.scss";
@import "cheapest.scss";
@import "checkout.scss";
@import "my_purchases.scss";
@import "transactions.scss";

::ng-deep #page-local-gamification-store-index,
::ng-deep #page-local-gamification-store-popular,
::ng-deep #page-local-gamification-store-visibles,
::ng-deep #page-local-gamification-store-cheapest,
::ng-deep #page-local-gamification-store-my_purchases,
::ng-deep #page-local-gamification-store-transactions {
  padding: 0 16px 20px 16px;
  .balances {
    justify-content: end;
    align-self: end;
    text-align: center;
    margin-bottom: 30px;

    .user-points {
      span {
        font-size: 12px;
        sup, small {
          font-size: 10px;
        }

        sup{
          margin-right: 5px;
        }
      }
    }

    .user-coins {
      font-size: 17px;

      img {
        filter: unset;
        margin: 0;
        width: 25px;
        height: 25px;
      }
    }
  }

  .empty-list {
    display: flex;
    justify-content: center;

    img {
      width: 175px;
      height: auto;
      filter: url();
    }
  }

  .products {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(5, 1fr);

    // .card
    @import "./../components/product-card.scss";

    @media (max-width: 575px) {
      & {
        grid-template-columns: repeat(1, 1fr);
      }
    }

    @media (min-width: 576px) and (max-width: 991px) {
      & {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (min-width: 992px) and (max-width: 1199px) {
      & {
        grid-template-columns: repeat(3, 1fr);
      }
    }

    @media (min-width: 1200px) {
      & {
        grid-template-columns: repeat(4, 1fr);
      }
    }

    @media (min-width: 1600px) {
      & {
        grid-template-columns: repeat(5, 1fr);
      }
    }
  }
}
::ng-deep #page-local-gamification-store-index,
::ng-deep #page-local-gamification-store-my_purchases,
::ng-deep #page-local-gamification-store-transactions {
  .header-content {
    flex-direction: column;
    align-items: start;
    gap: 15px;

    .balances {
      text-align: center;
      margin-bottom: 0;

      .user-coins {
        font-size: 17px;
      }
    }
  }
}
