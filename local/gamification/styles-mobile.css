::ng-deep .glide{position:relative;width:100%;box-sizing:border-box}::ng-deep .glide *{box-sizing:inherit}::ng-deep .glide__track{overflow:hidden}::ng-deep .glide__slides{position:relative;width:100%;list-style:none;backface-visibility:hidden;transform-style:preserve-3d;touch-action:pan-Y;overflow:hidden;margin:0;padding:0;white-space:nowrap;display:flex;flex-wrap:nowrap;will-change:transform}::ng-deep .glide__slides--dragging{user-select:none}::ng-deep .glide__slide{width:100%;height:100%;flex-shrink:0;white-space:normal;user-select:none;-webkit-touch-callout:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}::ng-deep .glide__slide a{user-select:none;-webkit-user-drag:none;-moz-user-select:none;-ms-user-select:none}::ng-deep .glide__arrows{-webkit-touch-callout:none;user-select:none}::ng-deep .glide__bullets{-webkit-touch-callout:none;user-select:none}::ng-deep .glide--rtl{direction:rtl}::ng-deep #page-local-gamification-store-index .block{margin-top:30px}::ng-deep #page-local-gamification-store-index .block .glide .glide-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px}::ng-deep #page-local-gamification-store-index .block .glide .glide-header .show-all{color:#fff;margin-bottom:0}::ng-deep #page-local-gamification-store-index .block .glide .glide-header .glide__arrows{display:inline-flex;gap:8px}::ng-deep #page-local-gamification-store-index .block .glide .glide-header .glide__arrows .glide__arrow{display:flex;height:27px;width:27px;min-width:35px;min-height:35px;align-items:center;justify-content:center;border-radius:50%;padding:0}::ng-deep #page-local-gamification-store-index .block .glide .glide-header .glide__arrows .glide__arrow ion-icon{font-size:20px !important}::ng-deep #page-local-gamification-store-index .block .glide .card{display:none;border:1px solid rgba(255,255,255,.175)}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card{width:100%;border-radius:25px;overflow:hidden;background-color:#131315}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card:hover{opacity:1;background:radial-gradient(circle, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%)}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card .card-img img{width:100%;height:165px;object-fit:cover}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card .card-body{padding-bottom:5px}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card .card-body .name{color:#efefef;font-size:17px;font-weight:bold;overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;line-clamp:1;-webkit-box-orient:vertical}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card .card-body .description{color:#efefef;font-size:13px;min-height:39px;overflow:hidden;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card .card-body .coins{font-size:19px;font-weight:bold;margin-top:10px}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card .card-footer{display:flex;justify-content:space-between;align-items:center;background-color:rgba(0,0,0,0);border:0;padding-top:5px}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card .card-footer .amount{font-size:12px}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card .card-footer .buy{display:flex;color:#db3747 !important;font-weight:bold}::ng-deep #page-local-gamification-store-index .block .glide ::ng-deep .card .card-footer .buy ion-icon{margin-left:5px}::ng-deep #page-local-gamification-store-index .block .glide .skeleton{display:flex;flex-wrap:wrap;gap:20px;max-height:337px}::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading{display:block;flex:1 1 calc(20% - 16px)}::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading div:not(.card-body):not(.card-footer){background:linear-gradient(-110deg, #24292d 8%, #282d33 18%, #24292d 33%);background-size:200% 100%;animation:shine 1.5s linear infinite}::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading .card-img{height:165px;border-bottom-left-radius:0;border-bottom-right-radius:0}::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading .name{height:22px;width:70%}::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading .description{height:36px;min-height:36px}::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading .coins{height:25px;width:60%}::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading .amount{height:18px;width:48%}::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading .buy{height:18px;width:38%}@media(max-width: 575px){::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading{flex:1 1 calc(100% - 16px)}}@media(min-width: 576px)and (max-width: 991px){::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading{flex:1 1 calc(50% - 16px)}}@media(min-width: 992px)and (max-width: 1199px){::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading{flex:1 1 calc(33.3333333333% - 16px)}}@media(min-width: 1200px){::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading{flex:1 1 calc(25% - 16px)}}@media(min-width: 1600px){::ng-deep #page-local-gamification-store-index .block .glide .skeleton .card.is-loading{flex:1 1 calc(20% - 16px)}}@keyframes shine{to{background-position-x:-200%}}::ng-deep #page-local-gamification-store-index .block .glide .empty{display:flex;justify-content:center}::ng-deep #page-local-gamification-store-index .block .glide .empty img{width:175px;height:auto;filter:url()}::ng-deep #page-local-gamification-store-popular .table-controls{display:flex;align-items:center;justify-content:space-between;margin-top:20px;gap:20px}::ng-deep #page-local-gamification-store-popular .table-controls select{margin:0 5px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-popular .table-controls{flex-direction:column}}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep .per-page{display:flex;align-items:center;gap:8px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep .per-page{flex-direction:column}}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep .per-page>div{display:flex;text-wrap:nowrap;align-items:center}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep .per-page select{width:auto}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep nav.pagination ul{margin:0 !important;display:flex;padding-left:0;border-radius:.5rem;list-style:none}@media(max-width: 767.98px){::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep nav.pagination ul{flex-wrap:wrap;justify-content:center}}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep nav.pagination ul li.page-item{margin:2px}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep nav.pagination ul li.page-item .page-link{position:relative;display:block;padding:.5rem .75rem;margin-left:-1px;line-height:1.25;color:#cce0ff;background-color:rgba(3,22,51,0)}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep nav.pagination ul li.page-item a{border:0;border-radius:5px;font-weight:bold;color:#fff !important}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep nav.pagination ul li.page-item a span{vertical-align:middle}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep nav.pagination ul li.page-item.active a{background-color:#031633}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep nav.pagination ul li.page-item.disabled{cursor:not-allowed}::ng-deep #page-local-gamification-store-popular .table-controls ::ng-deep nav.pagination ul li.page-item.disabled span.page-link{border:0;background-color:rgba(0,0,0,0)}::ng-deep #page-local-gamification-store-popular .empty{display:flex;justify-content:center}::ng-deep #page-local-gamification-store-popular .empty img{width:175px;height:auto;filter:url()}::ng-deep #page-local-gamification-store-visibles .table-controls{display:flex;align-items:center;justify-content:space-between;margin-top:20px;gap:20px}::ng-deep #page-local-gamification-store-visibles .table-controls select{margin:0 5px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-visibles .table-controls{flex-direction:column}}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep .per-page{display:flex;align-items:center;gap:8px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep .per-page{flex-direction:column}}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep .per-page>div{display:flex;text-wrap:nowrap;align-items:center}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep .per-page select{width:auto}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep nav.pagination ul{margin:0 !important;display:flex;padding-left:0;border-radius:.5rem;list-style:none}@media(max-width: 767.98px){::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep nav.pagination ul{flex-wrap:wrap;justify-content:center}}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep nav.pagination ul li.page-item{margin:2px}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep nav.pagination ul li.page-item .page-link{position:relative;display:block;padding:.5rem .75rem;margin-left:-1px;line-height:1.25;color:#cce0ff;background-color:rgba(3,22,51,0)}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep nav.pagination ul li.page-item a{border:0;border-radius:5px;font-weight:bold;color:#fff !important}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep nav.pagination ul li.page-item a span{vertical-align:middle}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep nav.pagination ul li.page-item.active a{background-color:#031633}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep nav.pagination ul li.page-item.disabled{cursor:not-allowed}::ng-deep #page-local-gamification-store-visibles .table-controls ::ng-deep nav.pagination ul li.page-item.disabled span.page-link{border:0;background-color:rgba(0,0,0,0)}::ng-deep #page-local-gamification-store-visibles .empty{display:flex;justify-content:center}::ng-deep #page-local-gamification-store-visibles .empty img{width:175px;height:auto;filter:url()}::ng-deep #page-local-gamification-store-cheapest .table-controls{display:flex;align-items:center;justify-content:space-between;margin-top:20px;gap:20px}::ng-deep #page-local-gamification-store-cheapest .table-controls select{margin:0 5px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-cheapest .table-controls{flex-direction:column}}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep .per-page{display:flex;align-items:center;gap:8px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep .per-page{flex-direction:column}}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep .per-page>div{display:flex;text-wrap:nowrap;align-items:center}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep .per-page select{width:auto}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep nav.pagination ul{margin:0 !important;display:flex;padding-left:0;border-radius:.5rem;list-style:none}@media(max-width: 767.98px){::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep nav.pagination ul{flex-wrap:wrap;justify-content:center}}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep nav.pagination ul li.page-item{margin:2px}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep nav.pagination ul li.page-item .page-link{position:relative;display:block;padding:.5rem .75rem;margin-left:-1px;line-height:1.25;color:#cce0ff;background-color:rgba(3,22,51,0)}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep nav.pagination ul li.page-item a{border:0;border-radius:5px;font-weight:bold;color:#fff !important}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep nav.pagination ul li.page-item a span{vertical-align:middle}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep nav.pagination ul li.page-item.active a{background-color:#031633}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep nav.pagination ul li.page-item.disabled{cursor:not-allowed}::ng-deep #page-local-gamification-store-cheapest .table-controls ::ng-deep nav.pagination ul li.page-item.disabled span.page-link{border:0;background-color:rgba(0,0,0,0)}::ng-deep #page-local-gamification-store-cheapest .empty{display:flex;justify-content:center}::ng-deep #page-local-gamification-store-cheapest .empty img{width:175px;height:auto;filter:url()}::ng-deep #page-local-gamification-store-checkout{padding:0 16px 20px 16px}@media(max-width: 1200px){::ng-deep #page-local-gamification-store-checkout h5,::ng-deep #page-local-gamification-store-checkout .h5{font-size:calc(.9271875rem + .32625vw)}}::ng-deep #page-local-gamification-store-checkout label{display:inline-block;margin-bottom:.5rem}::ng-deep #page-local-gamification-store-checkout .form-label{display:flex}::ng-deep #page-local-gamification-store-checkout .delivery{border:1px solid #343a40;padding:25px;border-radius:8px;background-color:#24292d}::ng-deep #page-local-gamification-store-checkout .detail{background-color:#24292d;border:1px solid #343a40;padding:25px;border-radius:8px;min-height:620px;height:100%}::ng-deep #page-local-gamification-store-checkout .detail .product{margin-top:20px}::ng-deep #page-local-gamification-store-checkout .detail .product img{width:100%;height:200px;object-fit:cover}::ng-deep #page-local-gamification-store-checkout .detail .product .price img{filter:unset;margin:0;width:20px;height:20px}::ng-deep #page-local-gamification-store-checkout .detail .info{display:flex;flex-direction:column;gap:20px}::ng-deep #page-local-gamification-store-checkout .detail .actions{position:absolute;display:flex;gap:20px;bottom:25px;right:40px}@media(max-width: 575px){::ng-deep #page-local-gamification-store-checkout .detail .actions{position:unset;margin-top:20px;flex-direction:column}}@media(max-width: 575px){::ng-deep #page-local-gamification-store-checkout .detail{margin-top:20px}}::ng-deep .glide{position:relative;width:100%;box-sizing:border-box}::ng-deep .glide *{box-sizing:inherit}::ng-deep .glide__track{overflow:hidden}::ng-deep .glide__slides{position:relative;width:100%;list-style:none;backface-visibility:hidden;transform-style:preserve-3d;touch-action:pan-Y;overflow:hidden;margin:0;padding:0;white-space:nowrap;display:flex;flex-wrap:nowrap;will-change:transform}::ng-deep .glide__slides--dragging{user-select:none}::ng-deep .glide__slide{width:100%;height:100%;flex-shrink:0;white-space:normal;user-select:none;-webkit-touch-callout:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}::ng-deep .glide__slide a{user-select:none;-webkit-user-drag:none;-moz-user-select:none;-ms-user-select:none}::ng-deep .glide__arrows{-webkit-touch-callout:none;user-select:none}::ng-deep .glide__bullets{-webkit-touch-callout:none;user-select:none}::ng-deep .glide--rtl{direction:rtl}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table{background-color:rgba(0,0,0,0) !important}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table thead{border-bottom:3px solid #0377f2}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table thead tr th{border-top:0;text-wrap:nowrap}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table thead tr th,::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table thead tr th a,::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table thead tr th a[data-sortable]{color:#0377f2 !important}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table tbody tr:nth-of-type(odd){background-color:rgba(0,0,0,0) !important}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table tbody tr[aria-expanded=true] td>i.fa-chevron-right{transform:rotate(90deg)}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table tbody tr:first-child .dropdown-menu,::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table tbody tr:last-child .dropdown-menu{transform:translate3d(-147px, -114px, 0px) !important}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table tbody tr td{text-wrap:nowrap;overflow:visible;vertical-align:middle}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep table tbody tr td i{transition:all .35s}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls{display:flex;align-items:center;justify-content:space-between;margin-top:20px;gap:20px}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls select{margin:0 5px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls{flex-direction:column}}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep .per-page{display:flex;align-items:center;gap:8px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep .per-page{flex-direction:column}}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep .per-page>div{display:flex;text-wrap:nowrap;align-items:center}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep .per-page select{width:auto}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep nav.pagination ul{margin:0 !important;display:flex;padding-left:0;border-radius:.5rem;list-style:none}@media(max-width: 767.98px){::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep nav.pagination ul{flex-wrap:wrap;justify-content:center}}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item{margin:2px}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item .page-link{position:relative;display:block;padding:.5rem .75rem;margin-left:-1px;line-height:1.25;color:#cce0ff;background-color:rgba(3,22,51,0)}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item a{border:0;border-radius:5px;font-weight:bold;color:#fff !important}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item a span{vertical-align:middle}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item.active a{background-color:#031633}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item.disabled{cursor:not-allowed}::ng-deep #page-local-gamification-store-my_purchases ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item.disabled span.page-link{border:0;background-color:rgba(0,0,0,0)}::ng-deep #page-local-gamification-store-my_purchases .badge-warning{color:#1d2125;background-color:#f0ad4e}::ng-deep #page-local-gamification-store-my_purchases .badge{border-radius:.2rem !important;display:inline-block;padding:.25em .4em;font-size:75%;font-weight:700;line-height:1;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.5rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out}@media(max-width: 576px){::ng-deep #page-local-gamification-store-my_purchases .badge{font-size:10px}}::ng-deep #page-local-gamification-store-my_purchases .header-content .balances{text-align:center}::ng-deep #page-local-gamification-store-my_purchases .header-content .balances .user-points{font-size:12px}::ng-deep #page-local-gamification-store-my_purchases .header-content .balances .user-coins{font-size:17px}::ng-deep #page-local-gamification-store-my_purchases .header-content .balances .user-coins img{filter:unset;margin:0;width:25px;height:25px}::ng-deep #page-local-gamification-store-my_purchases table{background-color:rgba(0,0,0,0) !important}::ng-deep #page-local-gamification-store-my_purchases table span{margin-bottom:4px}::ng-deep #page-local-gamification-store-my_purchases table tr td{border-top-color:#454d55;text-wrap:nowrap}::ng-deep #page-local-gamification-store-my_purchases table tr td.c1{width:180px;vertical-align:middle}::ng-deep #page-local-gamification-store-my_purchases table tr td.c2{overflow:hidden;text-overflow:ellipsis;vertical-align:middle}::ng-deep #page-local-gamification-store-my_purchases table tr td.c3{vertical-align:middle}::ng-deep #page-local-gamification-store-my_purchases table tr td.c3>div{display:flex;justify-content:end}::ng-deep #page-local-gamification-store-my_purchases table tr td.c3>div div{display:flex;flex-direction:column;gap:5px}::ng-deep #page-local-gamification-store-my_purchases table tr td .product-cover{min-width:150px;height:80px;object-fit:cover;border-radius:20px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-my_purchases table tr td .product-cover{min-width:80px;height:50px;border-radius:10px}}::ng-deep #page-local-gamification-store-my_purchases table tr td .order-timecreated{font-size:14px}::ng-deep #page-local-gamification-store-my_purchases table tr td .product-name,::ng-deep #page-local-gamification-store-my_purchases table tr td .order-quantity{font-size:16px;font-weight:bold}@media(max-width: 576px){::ng-deep #page-local-gamification-store-my_purchases table tr td .product-name,::ng-deep #page-local-gamification-store-my_purchases table tr td .order-quantity{font-size:14px}}@media(max-width: 576px){::ng-deep #page-local-gamification-store-my_purchases table tr td .order-quantity{font-size:13px}}::ng-deep #page-local-gamification-store-my_purchases table tr td .order-price,::ng-deep #page-local-gamification-store-my_purchases table tr td .order-timecreated,::ng-deep #page-local-gamification-store-my_purchases table tr td .product-description{color:#a2a4a6}@media(max-width: 576px){::ng-deep #page-local-gamification-store-my_purchases table tr td .order-price,::ng-deep #page-local-gamification-store-my_purchases table tr td .order-timecreated,::ng-deep #page-local-gamification-store-my_purchases table tr td .product-description{font-size:13px}}::ng-deep #page-local-gamification-store-my_purchases .block{margin-top:30px}::ng-deep #page-local-gamification-store-my_purchases .block .glide .glide-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px}::ng-deep #page-local-gamification-store-my_purchases .block .glide .glide-header .show-all{color:#fff;margin-bottom:0}::ng-deep #page-local-gamification-store-my_purchases .block .glide .glide-header .glide__arrows{display:inline-flex;gap:8px}::ng-deep #page-local-gamification-store-my_purchases .block .glide .glide-header .glide__arrows .glide__arrow{display:flex;height:27px;width:27px;min-width:35px;min-height:35px;align-items:center;justify-content:center;border-radius:50%;padding:0}::ng-deep #page-local-gamification-store-my_purchases .block .glide .card{display:none}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card{width:100%;border-radius:25px;overflow:hidden;background-color:#131315}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card:hover{opacity:1;background:radial-gradient(circle, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%)}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card .card-img img{width:100%;height:165px;object-fit:cover}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card .card-body{padding-bottom:5px}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card .card-body .name{color:#efefef;font-size:17px;font-weight:bold;overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;line-clamp:1;-webkit-box-orient:vertical}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card .card-body .description{color:#efefef;font-size:13px;min-height:39px;overflow:hidden;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card .card-body .coins{font-size:19px;font-weight:bold;margin-top:10px}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card .card-footer{display:flex;justify-content:space-between;align-items:center;background-color:rgba(0,0,0,0);border:0;padding-top:5px}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card .card-footer .amount{font-size:12px}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card .card-footer .buy{display:flex;color:#db3747 !important;font-weight:bold}::ng-deep #page-local-gamification-store-my_purchases .block .glide ::ng-deep .card .card-footer .buy ion-icon{margin-left:5px}::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton{display:flex;flex-wrap:wrap;gap:20px;max-height:337px}::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading{display:block;flex:1 1 calc(20% - 16px)}::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading div:not(.card-body):not(.card-footer){background:linear-gradient(-110deg, #24292d 8%, #282d33 18%, #24292d 33%);background-size:200% 100%;animation:shine 1.5s linear infinite}::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading .card-img{height:165px;border-bottom-left-radius:0;border-bottom-right-radius:0}::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading .name{height:22px;width:70%}::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading .description{height:36px;min-height:36px}::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading .coins{height:25px;width:60%}::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading .amount{height:18px;width:48%}::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading .buy{height:18px;width:38%}@media(max-width: 575px){::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading{flex:1 1 calc(100% - 16px)}}@media(min-width: 576px)and (max-width: 991px){::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading{flex:1 1 calc(50% - 16px)}}@media(min-width: 992px)and (max-width: 1199px){::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading{flex:1 1 calc(33.3333333333% - 16px)}}@media(min-width: 1200px){::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading{flex:1 1 calc(25% - 16px)}}@media(min-width: 1600px){::ng-deep #page-local-gamification-store-my_purchases .block .glide .skeleton .card.is-loading{flex:1 1 calc(20% - 16px)}}@keyframes shine{to{background-position-x:-200%}}::ng-deep #page-local-gamification-store-my_purchases .block .glide .empty{display:flex;justify-content:center}::ng-deep #page-local-gamification-store-my_purchases .block .glide .empty img{width:175px;height:auto;filter:url()}::ng-deep #page-local-gamification-store-transactions ::ng-deep table{background-color:rgba(0,0,0,0) !important}::ng-deep #page-local-gamification-store-transactions ::ng-deep table thead{border-bottom:3px solid #0377f2}::ng-deep #page-local-gamification-store-transactions ::ng-deep table thead tr th{border-top:0;text-wrap:nowrap}::ng-deep #page-local-gamification-store-transactions ::ng-deep table thead tr th,::ng-deep #page-local-gamification-store-transactions ::ng-deep table thead tr th a,::ng-deep #page-local-gamification-store-transactions ::ng-deep table thead tr th a[data-sortable]{color:#0377f2 !important}::ng-deep #page-local-gamification-store-transactions ::ng-deep table tbody tr:nth-of-type(odd){background-color:rgba(0,0,0,0) !important}::ng-deep #page-local-gamification-store-transactions ::ng-deep table tbody tr[aria-expanded=true] td>i.fa-chevron-right{transform:rotate(90deg)}::ng-deep #page-local-gamification-store-transactions ::ng-deep table tbody tr:first-child .dropdown-menu,::ng-deep #page-local-gamification-store-transactions ::ng-deep table tbody tr:last-child .dropdown-menu{transform:translate3d(-147px, -114px, 0px) !important}::ng-deep #page-local-gamification-store-transactions ::ng-deep table tbody tr td{text-wrap:nowrap;overflow:visible;vertical-align:middle}::ng-deep #page-local-gamification-store-transactions ::ng-deep table tbody tr td i{transition:all .35s}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls{display:flex;align-items:center;justify-content:space-between;margin-top:20px;gap:20px}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls select{margin:0 5px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls{flex-direction:column}}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep .per-page{display:flex;align-items:center;gap:8px}@media(max-width: 576px){::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep .per-page{flex-direction:column}}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep .per-page>div{display:flex;text-wrap:nowrap;align-items:center}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep .per-page select{width:auto}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep nav.pagination ul{margin:0 !important;display:flex;padding-left:0;border-radius:.5rem;list-style:none}@media(max-width: 767.98px){::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep nav.pagination ul{flex-wrap:wrap;justify-content:center}}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item{margin:2px}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item .page-link{position:relative;display:block;padding:.5rem .75rem;margin-left:-1px;line-height:1.25;color:#cce0ff;background-color:rgba(3,22,51,0)}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item a{border:0;border-radius:5px;font-weight:bold;color:#fff !important}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item a span{vertical-align:middle}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item.active a{background-color:#031633}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item.disabled{cursor:not-allowed}::ng-deep #page-local-gamification-store-transactions ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item.disabled span.page-link{border:0;background-color:rgba(0,0,0,0)}::ng-deep #page-local-gamification-store-transactions table thead tr .c1,::ng-deep #page-local-gamification-store-transactions table thead tr .c3,::ng-deep #page-local-gamification-store-transactions table thead tr .c4{text-align:center}::ng-deep #page-local-gamification-store-transactions table tbody tr .c1,::ng-deep #page-local-gamification-store-transactions table tbody tr .c3,::ng-deep #page-local-gamification-store-transactions table tbody tr .c4{text-align:center}::ng-deep #page-local-gamification-store-transactions table tbody tr .c1 .text-primary,::ng-deep #page-local-gamification-store-transactions table tbody tr .c3 .text-primary,::ng-deep #page-local-gamification-store-transactions table tbody tr .c4 .text-primary{color:#007bff !important}::ng-deep #page-local-gamification-store-index,::ng-deep #page-local-gamification-store-popular,::ng-deep #page-local-gamification-store-visibles,::ng-deep #page-local-gamification-store-cheapest,::ng-deep #page-local-gamification-store-my_purchases,::ng-deep #page-local-gamification-store-transactions{padding:0 16px 20px 16px}::ng-deep #page-local-gamification-store-index .balances,::ng-deep #page-local-gamification-store-popular .balances,::ng-deep #page-local-gamification-store-visibles .balances,::ng-deep #page-local-gamification-store-cheapest .balances,::ng-deep #page-local-gamification-store-my_purchases .balances,::ng-deep #page-local-gamification-store-transactions .balances{justify-content:end;align-self:end;text-align:center;margin-bottom:30px}::ng-deep #page-local-gamification-store-index .balances .user-points span,::ng-deep #page-local-gamification-store-popular .balances .user-points span,::ng-deep #page-local-gamification-store-visibles .balances .user-points span,::ng-deep #page-local-gamification-store-cheapest .balances .user-points span,::ng-deep #page-local-gamification-store-my_purchases .balances .user-points span,::ng-deep #page-local-gamification-store-transactions .balances .user-points span{font-size:12px}::ng-deep #page-local-gamification-store-index .balances .user-points span sup,::ng-deep #page-local-gamification-store-index .balances .user-points span small,::ng-deep #page-local-gamification-store-popular .balances .user-points span sup,::ng-deep #page-local-gamification-store-popular .balances .user-points span small,::ng-deep #page-local-gamification-store-visibles .balances .user-points span sup,::ng-deep #page-local-gamification-store-visibles .balances .user-points span small,::ng-deep #page-local-gamification-store-cheapest .balances .user-points span sup,::ng-deep #page-local-gamification-store-cheapest .balances .user-points span small,::ng-deep #page-local-gamification-store-my_purchases .balances .user-points span sup,::ng-deep #page-local-gamification-store-my_purchases .balances .user-points span small,::ng-deep #page-local-gamification-store-transactions .balances .user-points span sup,::ng-deep #page-local-gamification-store-transactions .balances .user-points span small{font-size:10px}::ng-deep #page-local-gamification-store-index .balances .user-points span sup,::ng-deep #page-local-gamification-store-popular .balances .user-points span sup,::ng-deep #page-local-gamification-store-visibles .balances .user-points span sup,::ng-deep #page-local-gamification-store-cheapest .balances .user-points span sup,::ng-deep #page-local-gamification-store-my_purchases .balances .user-points span sup,::ng-deep #page-local-gamification-store-transactions .balances .user-points span sup{margin-right:5px}::ng-deep #page-local-gamification-store-index .balances .user-coins,::ng-deep #page-local-gamification-store-popular .balances .user-coins,::ng-deep #page-local-gamification-store-visibles .balances .user-coins,::ng-deep #page-local-gamification-store-cheapest .balances .user-coins,::ng-deep #page-local-gamification-store-my_purchases .balances .user-coins,::ng-deep #page-local-gamification-store-transactions .balances .user-coins{font-size:17px}::ng-deep #page-local-gamification-store-index .balances .user-coins img,::ng-deep #page-local-gamification-store-popular .balances .user-coins img,::ng-deep #page-local-gamification-store-visibles .balances .user-coins img,::ng-deep #page-local-gamification-store-cheapest .balances .user-coins img,::ng-deep #page-local-gamification-store-my_purchases .balances .user-coins img,::ng-deep #page-local-gamification-store-transactions .balances .user-coins img{filter:unset;margin:0;width:25px;height:25px}::ng-deep #page-local-gamification-store-index .empty-list,::ng-deep #page-local-gamification-store-popular .empty-list,::ng-deep #page-local-gamification-store-visibles .empty-list,::ng-deep #page-local-gamification-store-cheapest .empty-list,::ng-deep #page-local-gamification-store-my_purchases .empty-list,::ng-deep #page-local-gamification-store-transactions .empty-list{display:flex;justify-content:center}::ng-deep #page-local-gamification-store-index .empty-list img,::ng-deep #page-local-gamification-store-popular .empty-list img,::ng-deep #page-local-gamification-store-visibles .empty-list img,::ng-deep #page-local-gamification-store-cheapest .empty-list img,::ng-deep #page-local-gamification-store-my_purchases .empty-list img,::ng-deep #page-local-gamification-store-transactions .empty-list img{width:175px;height:auto;filter:url()}::ng-deep #page-local-gamification-store-index .products,::ng-deep #page-local-gamification-store-popular .products,::ng-deep #page-local-gamification-store-visibles .products,::ng-deep #page-local-gamification-store-cheapest .products,::ng-deep #page-local-gamification-store-my_purchases .products,::ng-deep #page-local-gamification-store-transactions .products{display:grid;gap:20px;grid-template-columns:repeat(5, 1fr)}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card{width:100%;border-radius:25px;overflow:hidden;background-color:#131315}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card:hover,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card:hover,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card:hover,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card:hover,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card:hover,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card:hover{opacity:1;background:radial-gradient(circle, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%)}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card .card-img img,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card .card-img img,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card .card-img img,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card .card-img img,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card .card-img img,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card .card-img img{width:100%;height:165px;object-fit:cover}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card .card-body,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card .card-body,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card .card-body,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card .card-body,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card .card-body,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card .card-body{padding-bottom:5px}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card .card-body .name,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card .card-body .name,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card .card-body .name,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card .card-body .name,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card .card-body .name,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card .card-body .name{color:#efefef;font-size:17px;font-weight:bold;overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;line-clamp:1;-webkit-box-orient:vertical}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card .card-body .description,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card .card-body .description,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card .card-body .description,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card .card-body .description,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card .card-body .description,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card .card-body .description{color:#efefef;font-size:13px;min-height:39px;overflow:hidden;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card .card-body .coins,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card .card-body .coins,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card .card-body .coins,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card .card-body .coins,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card .card-body .coins,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card .card-body .coins{font-size:19px;font-weight:bold;margin-top:10px}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card .card-footer,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card .card-footer,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card .card-footer,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card .card-footer,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card .card-footer,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card .card-footer{display:flex;justify-content:space-between;align-items:center;background-color:rgba(0,0,0,0);border:0;padding-top:5px}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card .card-footer .amount,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card .card-footer .amount,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card .card-footer .amount,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card .card-footer .amount,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card .card-footer .amount,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card .card-footer .amount{font-size:12px}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card .card-footer .buy,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card .card-footer .buy,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card .card-footer .buy,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card .card-footer .buy,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card .card-footer .buy,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card .card-footer .buy{display:flex;color:#db3747 !important;font-weight:bold}::ng-deep #page-local-gamification-store-index .products ::ng-deep .card .card-footer .buy ion-icon,::ng-deep #page-local-gamification-store-popular .products ::ng-deep .card .card-footer .buy ion-icon,::ng-deep #page-local-gamification-store-visibles .products ::ng-deep .card .card-footer .buy ion-icon,::ng-deep #page-local-gamification-store-cheapest .products ::ng-deep .card .card-footer .buy ion-icon,::ng-deep #page-local-gamification-store-my_purchases .products ::ng-deep .card .card-footer .buy ion-icon,::ng-deep #page-local-gamification-store-transactions .products ::ng-deep .card .card-footer .buy ion-icon{margin-left:5px}@media(max-width: 575px){::ng-deep #page-local-gamification-store-index .products,::ng-deep #page-local-gamification-store-popular .products,::ng-deep #page-local-gamification-store-visibles .products,::ng-deep #page-local-gamification-store-cheapest .products,::ng-deep #page-local-gamification-store-my_purchases .products,::ng-deep #page-local-gamification-store-transactions .products{grid-template-columns:repeat(1, 1fr)}}@media(min-width: 576px)and (max-width: 991px){::ng-deep #page-local-gamification-store-index .products,::ng-deep #page-local-gamification-store-popular .products,::ng-deep #page-local-gamification-store-visibles .products,::ng-deep #page-local-gamification-store-cheapest .products,::ng-deep #page-local-gamification-store-my_purchases .products,::ng-deep #page-local-gamification-store-transactions .products{grid-template-columns:repeat(2, 1fr)}}@media(min-width: 992px)and (max-width: 1199px){::ng-deep #page-local-gamification-store-index .products,::ng-deep #page-local-gamification-store-popular .products,::ng-deep #page-local-gamification-store-visibles .products,::ng-deep #page-local-gamification-store-cheapest .products,::ng-deep #page-local-gamification-store-my_purchases .products,::ng-deep #page-local-gamification-store-transactions .products{grid-template-columns:repeat(3, 1fr)}}@media(min-width: 1200px){::ng-deep #page-local-gamification-store-index .products,::ng-deep #page-local-gamification-store-popular .products,::ng-deep #page-local-gamification-store-visibles .products,::ng-deep #page-local-gamification-store-cheapest .products,::ng-deep #page-local-gamification-store-my_purchases .products,::ng-deep #page-local-gamification-store-transactions .products{grid-template-columns:repeat(4, 1fr)}}@media(min-width: 1600px){::ng-deep #page-local-gamification-store-index .products,::ng-deep #page-local-gamification-store-popular .products,::ng-deep #page-local-gamification-store-visibles .products,::ng-deep #page-local-gamification-store-cheapest .products,::ng-deep #page-local-gamification-store-my_purchases .products,::ng-deep #page-local-gamification-store-transactions .products{grid-template-columns:repeat(5, 1fr)}}::ng-deep #page-local-gamification-store-index .header-content,::ng-deep #page-local-gamification-store-my_purchases .header-content,::ng-deep #page-local-gamification-store-transactions .header-content{flex-direction:column;align-items:start;gap:15px}::ng-deep #page-local-gamification-store-index .header-content .balances,::ng-deep #page-local-gamification-store-my_purchases .header-content .balances,::ng-deep #page-local-gamification-store-transactions .header-content .balances{text-align:center;margin-bottom:0}::ng-deep #page-local-gamification-store-index .header-content .balances .user-coins,::ng-deep #page-local-gamification-store-my_purchases .header-content .balances .user-coins,::ng-deep #page-local-gamification-store-transactions .header-content .balances .user-coins{font-size:17px}::ng-deep #page-local-gamification-goals{padding:0 16px 20px 16px}::ng-deep #page-local-gamification-goals .goals{display:grid;gap:20px;grid-template-columns:repeat(5, 1fr)}::ng-deep #page-local-gamification-goals .goals ::ng-deep .card{width:100%;padding:15px;border-radius:25px;overflow:hidden;background-color:#24292d;position:relative;display:flex;flex-direction:column;min-width:0;word-wrap:break-word;border:1px solid rgba(255,255,255,.125)}::ng-deep #page-local-gamification-goals .goals ::ng-deep .card--completed{background-color:rgba(0,0,0,.1)}::ng-deep #page-local-gamification-goals .goals ::ng-deep .card:hover{opacity:1;background:radial-gradient(circle, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%)}::ng-deep #page-local-gamification-goals .goals ::ng-deep .card .card-img{display:flex;justify-content:center}::ng-deep #page-local-gamification-goals .goals ::ng-deep .card .card-img img{width:150px;height:150px;border-radius:50%;object-fit:cover}::ng-deep #page-local-gamification-goals .goals ::ng-deep .card .card-img img.grayscale{opacity:.8;filter:grayscale(100%)}::ng-deep #page-local-gamification-goals .goals ::ng-deep .card .card-body{text-align:center;padding-bottom:5px;margin-top:5px}::ng-deep #page-local-gamification-goals .goals ::ng-deep .card .card-body .name{color:#efefef;font-size:17px;font-weight:bold;overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;line-clamp:1;-webkit-box-orient:vertical}::ng-deep #page-local-gamification-goals .goals ::ng-deep .card .card-body .description{color:#efefef;font-size:13px;min-height:39px;overflow:hidden;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}::ng-deep #page-local-gamification-goals .goals ::ng-deep .card .card-footer{display:flex;justify-content:space-between;flex-direction:column;align-items:center;background-color:rgba(0,0,0,0);border:0;padding-top:20px;padding-bottom:0}@media(max-width: 575px){::ng-deep #page-local-gamification-goals .goals{grid-template-columns:repeat(1, 1fr)}}@media(min-width: 576px)and (max-width: 991px){::ng-deep #page-local-gamification-goals .goals{grid-template-columns:repeat(2, 1fr)}}@media(min-width: 992px)and (max-width: 1199px){::ng-deep #page-local-gamification-goals .goals{grid-template-columns:repeat(3, 1fr)}}@media(min-width: 1200px){::ng-deep #page-local-gamification-goals .goals{grid-template-columns:repeat(4, 1fr)}}@media(min-width: 1600px){::ng-deep #page-local-gamification-goals .goals{grid-template-columns:repeat(5, 1fr)}}::ng-deep #page-local-gamification-goals .table-controls{display:flex;align-items:center;justify-content:center;margin-top:20px;gap:20px}::ng-deep #page-local-gamification-goals .table-controls select{margin:0 5px}@media(max-width: 576px){::ng-deep #page-local-gamification-goals .table-controls{flex-direction:column}}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep .per-page{display:flex;align-items:center;gap:8px}@media(max-width: 576px){::ng-deep #page-local-gamification-goals .table-controls ::ng-deep .per-page{flex-direction:column}}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep .per-page>div{display:flex;text-wrap:nowrap;align-items:center}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep .per-page select{width:auto}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep nav.pagination ul{margin:0 !important;display:flex;padding-left:0;border-radius:.5rem;list-style:none}@media(max-width: 767.98px){::ng-deep #page-local-gamification-goals .table-controls ::ng-deep nav.pagination ul{flex-wrap:wrap;justify-content:center}}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep nav.pagination ul li.page-item{margin:2px}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep nav.pagination ul li.page-item .page-link{position:relative;display:block;padding:.5rem .75rem;margin-left:-1px;line-height:1.25;color:#cce0ff;background-color:rgba(3,22,51,0)}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep nav.pagination ul li.page-item a{border:0;border-radius:5px;font-weight:bold;color:#fff !important}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep nav.pagination ul li.page-item a span{vertical-align:middle}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep nav.pagination ul li.page-item.active a{background-color:#031633}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep nav.pagination ul li.page-item.disabled{cursor:not-allowed}::ng-deep #page-local-gamification-goals .table-controls ::ng-deep nav.pagination ul li.page-item.disabled span.page-link{border:0;background-color:rgba(0,0,0,0)}::ng-deep #page-local-gamification-goals .empty{display:flex;justify-content:center}::ng-deep #page-local-gamification-goals .empty img{width:175px;height:auto;filter:url()}::ng-deep .gamification-pages .userpicture{border-radius:50%}::ng-deep .gamification-pages img{max-width:none !important;vertical-align:middle;border-style:none}::ng-deep .gamification-pages .badge{display:inline-block;padding:.25em .4em;font-size:75%;font-weight:700;line-height:1;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25rem}::ng-deep .gamification-pages .badge-success{color:#fff;background-color:#28a745}::ng-deep .gamification-pages .badge-primary{color:#fff;background-color:#007bff}::ng-deep .gamification-pages .badge-secondary{color:#fff;background-color:#6c757d}::ng-deep .gamification-pages .badge-danger{color:#fff;background-color:#dc3545}::ng-deep .gamification-pages .badge-warning{color:#212529;background-color:#ffc107}::ng-deep .gamification-pages .badge-info{color:#fff;background-color:#17a2b8}::ng-deep .gamification-pages .badge-light{color:#212529;background-color:#f8f9fa}::ng-deep .gamification-pages .badge-dark{color:#fff;background-color:#343a40}::ng-deep .gamification-pages .ml-1,::ng-deep .gamification-pages .mx-1{margin-left:.25rem !important}::ng-deep .gamification-pages hr{margin-top:1rem;margin-bottom:1rem;border:0;border-top:1px solid #495057;box-sizing:content-box;height:0;overflow:visible}::ng-deep .gamification-pages .form-control{color:#dee2e6;background-color:#212529;display:block;width:100%;height:calc(1.5em + .75rem + 2px);padding:0 .75rem;font-weight:400;line-height:1.5;border:1px solid #495057;border-radius:.5rem;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;margin-left:0 !important}::ng-deep .gamification-pages .no-overflow{overflow:auto}::ng-deep .gamification-pages div:has(>table){overflow-x:auto;scrollbar-width:thin;scrollbar-color:#6a737b #343a40}::ng-deep .gamification-pages .table{width:100%;margin-bottom:1rem;color:#fff}::ng-deep .gamification-pages .table thead th{border-bottom-color:#454d55}::ng-deep .gamification-pages .table th,::ng-deep .gamification-pages .table td{border-top:1px solid #dee2e6;padding:.75rem}::ng-deep .gamification-pages table{border-collapse:collapse}::ng-deep .gamification-pages sup{top:-0.5em}::ng-deep .gamification-pages sub,::ng-deep .gamification-pages sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}::ng-deep .gamification-pages .card-body{flex:1 1 auto;min-height:1px;padding:1.25rem}@media(max-width: 767.98px){::ng-deep .gamification-pages .card .card-body{padding:.625rem}}::ng-deep .gamification-pages .card-footer{padding:.75rem 1.25rem}::ng-deep .gamification-pages .card-footer:last-child{border-radius:0 0 calc(.5rem - 1px) calc(.5rem - 1px)}::ng-deep .gamification-pages .btn-dark{color:#fff;background-color:#343a40;border-color:#343a40}::ng-deep .gamification-pages .rounded-pill{border-radius:50rem !important}::ng-deep .gamification-pages .header-content{display:flex;justify-content:space-between;flex-direction:column;align-items:start;gap:15px;margin-bottom:30px}::ng-deep .gamification-pages .justify-content-center{justify-content:center !important}::ng-deep .gamification-pages *{font-family:"Helvetica Neue",sans-serif;font-size:calc(.90375rem + .045vw)}::ng-deep .gamification-pages .mr-2{margin-right:.5rem !important}::ng-deep .gamification-pages .mr-3{margin-right:1rem !important}::ng-deep .gamification-pages .mb-2{margin-bottom:.5rem !important}::ng-deep .gamification-pages .mb-5,::ng-deep .gamification-pages .my-5{margin-bottom:2rem !important}::ng-deep .gamification-pages .modal-overlay{content:"";position:absolute;position:fixed;top:0;right:0;bottom:0;left:0;z-index:1050;background:#2c3e50;opacity:.6}::ng-deep .gamification-pages .modal-wrapper{position:fixed;top:0;right:0;bottom:0;left:0;margin:auto;width:fit-content;height:fit-content;padding:2rem;z-index:1051;transform:none}::ng-deep .gamification-pages .modal-header{border-bottom-color:#495057;background:#212529;padding:15px 20px;display:flex;justify-content:space-between;align-items:center;border-bottom:1px solid #495057;border-top-left-radius:calc(.6rem - 1px);border-top-right-radius:calc(.6rem - 1px)}::ng-deep .gamification-pages .modal-content{background-color:#212529;border-color:rgba(255,255,255,.2);background:#212529;border-radius:0;border:none;padding:20px;text-align:center;position:relative;display:flex;flex-direction:column;width:100%;pointer-events:auto;outline:0}::ng-deep .gamification-pages .modal-footer{display:flex;flex-wrap:wrap;align-items:center;justify-content:flex-end;padding:15px;border-top:1px solid #dee2e6;border-bottom-right-radius:calc(.6rem - 1px);border-bottom-left-radius:calc(.6rem - 1px);background:#212529;border-top-color:#495057}::ng-deep .gamification-pages .mb-3{margin-bottom:1rem !important}::ng-deep .gamification-pages .mb-0{margin-bottom:0 !important}::ng-deep .gamification-pages .pt-3{padding-top:1rem}::ng-deep .gamification-pages .mb-1{margin-bottom:.25rem !important}::ng-deep .gamification-pages .pr-1{padding-right:.25rem !important}::ng-deep .gamification-pages .mt-2{margin-top:.5rem !important}::ng-deep .gamification-pages .m-1{margin:.25rem !important}::ng-deep .gamification-pages .btn-outline-secondary{color:#6c757d !important;background-color:rgba(0,0,0,0) !important;border:1px solid #6c757d !important}::ng-deep .gamification-pages .btn-outline-secondary:hover{color:#fff;background-color:#6c757d;border-color:#6c757d}::ng-deep .gamification-pages .btn{display:inline-block;font-weight:400;text-align:center;white-space:nowrap;vertical-align:middle;user-select:none;border:1px solid rgba(0,0,0,0);padding:.375rem .75rem;font-size:1rem;line-height:1.5;border-radius:.375rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out}::ng-deep .gamification-pages .btn-primary{color:#fff;background-color:#0d6efd;border-color:#0d6efd}::ng-deep .gamification-pages .btn-primary:hover{color:#fff;background-color:#0b5ed7;border-color:#0a58ca}::ng-deep .gamification-pages .btn-primary.focus{color:#fff;background-color:#0b5ed7;border-color:#0a58ca;box-shadow:0 0 0 .25rem rgba(13,110,253,.5)}::ng-deep .gamification-pages .btn-primary.disabled{color:#fff;background-color:#0d6efd;border-color:#0d6efd;opacity:.65}::ng-deep .gamification-pages .mt-5{margin-top:3rem !important}::ng-deep .gamification-pages .mt-0{margin-top:0 !important}::ng-deep .gamification-pages .mb-4{margin-bottom:1.5rem !important}::ng-deep .gamification-pages .mt-4{margin-top:1.5rem !important}::ng-deep .gamification-pages .w-75{width:75% !important}::ng-deep .gamification-pages .w-100{width:100% !important}::ng-deep .gamification-pages .mx-2{margin-left:.5rem !important;margin-right:.5rem !important}::ng-deep .gamification-pages .d-inline-flex{display:inline-flex !important}::ng-deep .gamification-pages .d-flex{display:flex !important}::ng-deep .gamification-pages .d-none{display:none !important}::ng-deep .gamification-pages .flex-wrap{flex-wrap:wrap !important}::ng-deep .gamification-pages .justify-content-left{justify-content:flex-start !important}::ng-deep .gamification-pages .justify-content-center{justify-content:center !important}::ng-deep .gamification-pages .col-lg-12{width:100%;padding-right:15px;padding-left:15px}::ng-deep .gamification-pages .justify-content-right{justify-content:flex-end !important}::ng-deep .gamification-pages .justify-content-between{justify-content:space-between !important}::ng-deep .gamification-pages .align-items-center{align-items:center !important}::ng-deep .gamification-pages .row{display:flex;flex-wrap:wrap}::ng-deep .gamification-pages .alert-primary{color:#81b4fe;background-color:#031633;border-color:#041f47}::ng-deep .gamification-pages .alert-danger{color:#721c24;background-color:#f8d7da;border-color:#f5c6cb}::ng-deep .gamification-pages .col-form-label{padding-top:calc(.375rem + 1px);padding-bottom:calc(.375rem + 1px);margin-bottom:0;font-size:inherit;line-height:1.5}::ng-deep .gamification-pages .col-3{flex:0 0 25%;max-width:25%}::ng-deep .gamification-pages .col-9{flex:0 0 75%;max-width:75%}::ng-deep .gamification-pages h2,::ng-deep .gamification-pages .h2{font-size:1.875rem}::ng-deep .gamification-pages h3,::ng-deep .gamification-pages .h3{font-size:1.640625rem}::ng-deep .gamification-pages h4,::ng-deep .gamification-pages .h4{font-size:1.40625rem}@media(max-width: 1200px){::ng-deep .gamification-pages h3,::ng-deep .gamification-pages .h3{font-size:calc(.9740625rem + .88875vw)}::ng-deep .gamification-pages h4,::ng-deep .gamification-pages .h4{font-size:calc(.950625rem + .6075vw)}::ng-deep .gamification-pages .form-control{font-size:calc(.90375rem + .045vw)}::ng-deep .gamification-pages h2,::ng-deep .gamification-pages .h2{font-size:calc(.9975rem + 1.17vw)}}::ng-deep .gamification-pages h1,::ng-deep .gamification-pages h2,::ng-deep .gamification-pages h3,::ng-deep .gamification-pages h4,::ng-deep .gamification-pages h5,::ng-deep .gamification-pages h6,::ng-deep .gamification-pages .h1,::ng-deep .gamification-pages .h2,::ng-deep .gamification-pages .h3,::ng-deep .gamification-pages .h4,::ng-deep .gamification-pages .h5,::ng-deep .gamification-pages .h6{margin-top:0;margin-bottom:.5rem;font-weight:700;line-height:1.2}::ng-deep .gamification-pages p{margin-top:0;margin-bottom:1rem}::ng-deep .gamification-pages a{text-decoration:none;background-color:rgba(0,0,0,0)}::ng-deep .gamification-pages .alert{position:relative;padding:.75rem 1.25rem;margin-bottom:1rem;border:0 solid rgba(0,0,0,0);border-radius:.5rem}::ng-deep .gamification-pages .btn-secondary{color:#fff;background-color:#6c757d;border-color:#6c757d;display:inline-block;font-weight:400;text-align:center;vertical-align:middle;cursor:pointer;border:1px solid rgba(0,0,0,0);padding:.375rem .75rem;font-size:1rem;line-height:1.5;border-radius:.25rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out}::ng-deep .gamification-pages .rounded-circle{border-radius:50% !important}::ng-deep .gamification-pages .p-1{padding:.25rem !important}::ng-deep .gamification-pages .col-12{flex:0 0 100%;max-width:100%}@media(min-width: 768px){::ng-deep .gamification-pages .col-md-6{flex:0 0 50%;max-width:50%}}@media(min-width: 992px){::ng-deep .gamification-pages .col-lg-4{flex:0 0 33.333333%;max-width:33.333333%}}@media(min-width: 1200px){::ng-deep .gamification-pages .col-xl-3{flex:0 0 25%;max-width:25%}::ng-deep .gamification-pages .col-xl-6{flex:0 0 50%;max-width:50%}}::ng-deep .gamification-pages .col-1,::ng-deep .gamification-pages .col-2,::ng-deep .gamification-pages .col-3,::ng-deep .gamification-pages .col-4,::ng-deep .gamification-pages .col-5,::ng-deep .gamification-pages .col-6,::ng-deep .gamification-pages .col-7,::ng-deep .gamification-pages .col-8,::ng-deep .gamification-pages .col-9,::ng-deep .gamification-pages .col-10,::ng-deep .gamification-pages .col-11,::ng-deep .gamification-pages .col-12,::ng-deep .gamification-pages .col,::ng-deep .gamification-pages .col-auto,::ng-deep .gamification-pages .col-sm-1,::ng-deep .gamification-pages .col-sm-2,::ng-deep .gamification-pages .col-sm-3,::ng-deep .gamification-pages .col-sm-4,::ng-deep .gamification-pages .col-sm-5,::ng-deep .gamification-pages .col-sm-6,::ng-deep .gamification-pages .col-sm-7,::ng-deep .gamification-pages .col-sm-8,::ng-deep .gamification-pages .col-sm-9,::ng-deep .gamification-pages .col-sm-10,::ng-deep .gamification-pages .col-sm-11,::ng-deep .gamification-pages .col-sm-12,::ng-deep .gamification-pages .col-sm,::ng-deep .gamification-pages .col-sm-auto,::ng-deep .gamification-pages .col-md-1,::ng-deep .gamification-pages .col-md-2,::ng-deep .gamification-pages .col-md-3,::ng-deep .gamification-pages .col-md-4,::ng-deep .gamification-pages .col-md-5,::ng-deep .gamification-pages .col-md-6,::ng-deep .gamification-pages .col-md-7,::ng-deep .gamification-pages .col-md-8,::ng-deep .gamification-pages .col-md-9,::ng-deep .gamification-pages .col-md-10,::ng-deep .gamification-pages .col-md-11,::ng-deep .gamification-pages .col-md-12,::ng-deep .gamification-pages .col-md,::ng-deep .gamification-pages .col-md-auto,::ng-deep .gamification-pages .col-lg-1,::ng-deep .gamification-pages .col-lg-2,::ng-deep .gamification-pages .col-lg-3,::ng-deep .gamification-pages .col-lg-4,::ng-deep .gamification-pages .col-lg-5,::ng-deep .gamification-pages .col-lg-6,::ng-deep .gamification-pages .col-lg-7,::ng-deep .gamification-pages .col-lg-8,::ng-deep .gamification-pages .col-lg-9,::ng-deep .gamification-pages .col-lg-10,::ng-deep .gamification-pages .col-lg-11,::ng-deep .gamification-pages .col-lg-12,::ng-deep .gamification-pages .col-lg,::ng-deep .gamification-pages .col-lg-auto,::ng-deep .gamification-pages .col-xl-1,::ng-deep .gamification-pages .col-xl-2,::ng-deep .gamification-pages .col-xl-3,::ng-deep .gamification-pages .col-xl-4,::ng-deep .gamification-pages .col-xl-5,::ng-deep .gamification-pages .col-xl-6,::ng-deep .gamification-pages .col-xl-7,::ng-deep .gamification-pages .col-xl-8,::ng-deep .gamification-pages .col-xl-9,::ng-deep .gamification-pages .col-xl-10,::ng-deep .gamification-pages .col-xl-11,::ng-deep .gamification-pages .col-xl-12,::ng-deep .gamification-pages .col-xl,::ng-deep .gamification-pages .col-xl-auto{position:relative;width:100%;padding-right:2px;padding-left:2px}@media(min-width: 768px){::ng-deep .gamification-pages .col-md-4{flex:0 0 33.33333333% !important;max-width:33.33333333% !important}}@media(min-width: 576px)and (max-width: 767px){::ng-deep .gamification-pages .col-sm-6{flex:0 0 50% !important;max-width:50% !important}}::ng-deep .gamification-pages .align-self-center{align-self:center !important}::ng-deep .gamification-pages .card-link{background-color:#343a40 !important;color:#fff !important}::ng-deep .gamification-pages .justify-content-center{justify-content:center !important}::ng-deep .gamification-pages .align-items-end{align-items:flex-end !important}::ng-deep .gamification-pages .p-0{padding:0 !important}::ng-deep .gamification-pages .p-2{padding:.5rem !important}::ng-deep .gamification-pages .text-uppercase{text-transform:uppercase !important}::ng-deep .gamification-pages .text-muted{color:#6c757d !important}::ng-deep .gamification-pages .mt-3{margin-top:1rem !important}::ng-deep .gamification-pages ion-icon{font-size:18px !important}::ng-deep .gamification-pages .card{position:relative;display:flex;flex-direction:column;min-width:0;word-wrap:break-word;background-color:#fff;background-clip:border-box;border:1px solid rgba(255,255,255,.175);border-radius:.5rem}::ng-deep #page-local-gamification-index{padding:0 16px 20px 16px}::ng-deep #page-local-gamification-index ::ng-deep table{background-color:rgba(0,0,0,0) !important}::ng-deep #page-local-gamification-index ::ng-deep table thead{border-bottom:3px solid #0377f2}::ng-deep #page-local-gamification-index ::ng-deep table thead tr th{border-top:0;text-wrap:nowrap}::ng-deep #page-local-gamification-index ::ng-deep table thead tr th,::ng-deep #page-local-gamification-index ::ng-deep table thead tr th a,::ng-deep #page-local-gamification-index ::ng-deep table thead tr th a[data-sortable]{color:#0377f2 !important}::ng-deep #page-local-gamification-index ::ng-deep table tbody tr:nth-of-type(odd){background-color:rgba(0,0,0,0) !important}::ng-deep #page-local-gamification-index ::ng-deep table tbody tr[aria-expanded=true] td>i.fa-chevron-right{transform:rotate(90deg)}::ng-deep #page-local-gamification-index ::ng-deep table tbody tr:first-child .dropdown-menu,::ng-deep #page-local-gamification-index ::ng-deep table tbody tr:last-child .dropdown-menu{transform:translate3d(-147px, -114px, 0px) !important}::ng-deep #page-local-gamification-index ::ng-deep table tbody tr td{text-wrap:nowrap;overflow:visible;vertical-align:middle}::ng-deep #page-local-gamification-index ::ng-deep table tbody tr td i{transition:all .35s}::ng-deep #page-local-gamification-index ::ng-deep .table-controls{display:flex;align-items:center;justify-content:space-between;margin-top:20px;gap:20px}::ng-deep #page-local-gamification-index ::ng-deep .table-controls select{margin:0 5px}@media(max-width: 576px){::ng-deep #page-local-gamification-index ::ng-deep .table-controls{flex-direction:column}}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep .per-page{display:flex;align-items:center;gap:8px}@media(max-width: 576px){::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep .per-page{flex-direction:column}}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep .per-page>div{display:flex;text-wrap:nowrap;align-items:center}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep .per-page select{width:auto}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep nav.pagination ul{margin:0 !important;display:flex;padding-left:0;border-radius:.5rem;list-style:none}@media(max-width: 767.98px){::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep nav.pagination ul{flex-wrap:wrap;justify-content:center}}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item{margin:2px}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item .page-link{position:relative;display:block;padding:.5rem .75rem;margin-left:-1px;line-height:1.25;color:#cce0ff;background-color:rgba(3,22,51,0)}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item a{border:0;border-radius:5px;font-weight:bold;color:#fff !important}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item a span{vertical-align:middle}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item.active a{background-color:#031633}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item.disabled{cursor:not-allowed}::ng-deep #page-local-gamification-index ::ng-deep .table-controls ::ng-deep nav.pagination ul li.page-item.disabled span.page-link{border:0;background-color:rgba(0,0,0,0)}::ng-deep #page-local-gamification-index .header-content{align-items:center;gap:15px}::ng-deep #page-local-gamification-index .userinitials.size-35{width:35px;height:35px}::ng-deep #page-local-gamification-index .userinitials{background-color:#e9ecef;vertical-align:middle;display:inline-flex;align-items:center;justify-content:center;border-radius:50%;color:#343a40;font-weight:400;margin-right:.25rem}::ng-deep #page-local-gamification-index .form-control{display:block;width:100%;height:calc(1.5em + .75rem + 2px);padding:0 .75rem;font-weight:400;line-height:1.5;border:1px solid #8f959e;border-radius:.5rem;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;margin-left:5px !important;color:#dee2e6;background-color:#212529;border-color:#495057}::ng-deep #page-local-gamification-index select{width:auto;word-wrap:normal;text-transform:none}::ng-deep #page-local-gamification-index .progress-bar{display:flex;flex-direction:column;justify-content:center;overflow:hidden;text-align:center;white-space:nowrap;transition:width .6s ease}::ng-deep #page-local-gamification-index .box{background-color:#24292d;padding:20px 16px;border:1px solid #343a40;border-radius:15px}::ng-deep #page-local-gamification-index #section-info{display:flex;justify-content:space-between;align-items:center;gap:20px}@media(max-width: 425px){::ng-deep #page-local-gamification-index #section-info{flex-direction:column}}::ng-deep #page-local-gamification-index #section-info .user-info{display:flex;justify-content:space-between;align-items:center;gap:10px}::ng-deep #page-local-gamification-index #section-info .user-info h5{font-size:17px;font-weight:bold}::ng-deep #page-local-gamification-index #section-info .user-info span{font-size:14px}::ng-deep #page-local-gamification-index #section-info .user-info img.userpicture{width:70px;height:70px;border-radius:50%}@media(max-width: 425px){::ng-deep #page-local-gamification-index #section-info .user-info img.userpicture{width:40px;height:40px}}::ng-deep #page-local-gamification-index #section-info .user-info .userinitials{display:flex;justify-content:center;align-items:center;min-width:50px !important;min-height:50px !important;width:50px !important;height:50px !important;font-size:22px;border-radius:35px;border:1px solid #343a40;padding:10px}::ng-deep #page-local-gamification-index #section-info .progress-info{text-align:center}::ng-deep #page-local-gamification-index #section-info .progress-info .user-coins{font-size:18px}::ng-deep #page-local-gamification-index #section-info .progress-info .user-coins img{filter:unset;margin:0;width:25px;height:25px}::ng-deep #page-local-gamification-index #section-info .progress-info .level{display:flex;flex-direction:column;align-items:center;gap:3px;margin-top:5px}::ng-deep #page-local-gamification-index #section-info .progress-info .level>span{font-size:12px}::ng-deep #page-local-gamification-index #section-info .progress-info .level>span sup,::ng-deep #page-local-gamification-index #section-info .progress-info .level>span small{font-size:10px}::ng-deep #page-local-gamification-index #section-info .progress-info .level>span sup{margin-right:5px}::ng-deep #page-local-gamification-index #section-info .progress-info .level .progress{display:flex;position:relative;width:170px;height:1rem;overflow:hidden;line-height:0;font-size:.703125rem;border-radius:.5rem;background-color:#343a40}::ng-deep #page-local-gamification-index #section-info .progress-info .level .progress>span{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);font-size:11px}::ng-deep #page-local-gamification-index #section-info .progress-info .level .progress .progress-bar{display:flex;flex-direction:column;justify-content:center;overflow:hidden;color:#fff;text-align:center;white-space:nowrap;background-color:#dc3545;transition:width .6s ease}@media(max-width: 1199px){::ng-deep #page-local-gamification-index #section-widgets{gap:2rem}}::ng-deep #page-local-gamification-index #section-widgets .banner{display:flex;justify-content:space-between;flex-direction:column;height:100%;background:#b71d46;background:linear-gradient(144deg, rgb(183, 29, 70) 0%, rgb(161, 42, 116) 0%, rgb(75, 57, 178) 100%);border-radius:15px;padding:25px}::ng-deep #page-local-gamification-index #section-widgets .banner>a{color:#fff !important}::ng-deep #page-local-gamification-index #section-widgets .indicators{margin-top:30px}@media(max-width: 575px){::ng-deep #page-local-gamification-index #section-widgets .indicators{gap:20px}}::ng-deep #page-local-gamification-index #section-widgets .indicators .progress-bar{color:#fff;width:170px;height:170px;margin:auto;border-radius:50%;background:radial-gradient(closest-side, #1a1e22 85%, transparent 95% 100%),conic-gradient(#1a58b6 var(--percent), #1a1e22 0)}@media(min-width: 576px)and (max-width: 767px){::ng-deep #page-local-gamification-index #section-widgets .indicators .progress-bar{margin-bottom:20px}}::ng-deep #page-local-gamification-index #section-widgets .indicators .progress-bar>div{font-size:11px;font-weight:900;padding:0 20px;text-wrap:wrap}::ng-deep #page-local-gamification-index #section-widgets .indicators .widgets{display:grid;gap:15px;align-items:center;padding-right:2px;padding-left:2px}@media(max-width: 575px){::ng-deep #page-local-gamification-index #section-widgets .indicators .widgets{gap:20px}}::ng-deep #page-local-gamification-index #section-widgets .indicators .widgets .widget{display:flex;justify-content:space-between;align-items:center;gap:10px;padding:15px;background-color:#1a1e22;border-radius:16px;height:93px}::ng-deep #page-local-gamification-index #section-widgets .indicators .widgets .widget .col1 .description{font-size:11px;font-weight:bold}@media(min-width: 1200px)and (max-width: 1405px){::ng-deep #page-local-gamification-index #section-widgets .indicators .widgets .widget .col1 .description{font-size:9px}}::ng-deep #page-local-gamification-index #section-widgets .indicators .widgets .widget .col1 .value{font-weight:bold;font-size:20px}::ng-deep #page-local-gamification-index #section-widgets .indicators .widgets .widget .col2{background-color:#0377f2;border-radius:10px;padding:10px;display:flex;justify-content:center;align-items:center;width:47px !important;height:47px !important;min-width:47px !important;min-height:47px !important}::ng-deep #page-local-gamification-index #section-widgets .indicators .widgets .widget .col2 ion-icon{font-size:24px !important}::ng-deep #page-local-gamification-index #section-widgets .indicators .widgets .widget .col2 img.icon{margin:0;width:28px;height:28px;filter:unset}::ng-deep #page-local-gamification-index #section-widgets .pages{display:flex;flex-direction:column;gap:2rem}::ng-deep #page-local-gamification-index #section-widgets .pages .box{display:flex;align-items:center;height:80px}::ng-deep #page-local-gamification-index #section-widgets .pages .box a{width:100%;text-align:left}::ng-deep #page-local-gamification-index #section-widgets .pages .box a ion-icon{font-size:24px !important;margin-right:.5rem}::ng-deep #page-local-gamification-index #section-widgets .pages .box a img{filter:unset;width:25px;height:auto}::ng-deep #page-local-gamification-index .rank>.header{display:flex;justify-content:space-between;margin-bottom:10px}@media(max-width: 575px){::ng-deep #page-local-gamification-index .rank>.header{flex-direction:column}}::ng-deep #page-local-gamification-index .rank>.header>.filter{width:300px;padding-left:15px;padding-right:15px}::ng-deep #page-local-gamification-index .rank table .c1,::ng-deep #page-local-gamification-index .rank table .c3{text-align:center}::ng-deep #page-local-gamification-index .rank table .c2{text-align:left}::ng-deep #page-local-gamification-index .rank table .c4{text-align:right}::ng-deep #page-local-gamification-index .rank table .c1{max-width:60px}::ng-deep #page-local-gamification-index .rank table .progress{display:flex;position:relative;height:1rem;overflow:hidden;line-height:0;font-size:.703125rem;border-radius:.5rem}::ng-deep #page-local-gamification-index .rank table .progress>span{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}::ng-deep #page-local-gamification-index .rank table .progress .progress-bar{display:flex;flex-direction:column;justify-content:center;overflow:hidden;color:#fff;text-align:center;white-space:nowrap;background-color:#dc3545;transition:width .6s ease}::ng-deep #page-local-gamification-index .rank table .full-progress{color:#a2a4a6;font-size:13px}::ng-deep #page-local-gamification-index .goals{display:grid;gap:20px;grid-template-columns:repeat(5, 1fr)}::ng-deep #page-local-gamification-index .goals ::ng-deep .card{width:100%;padding:15px;border-radius:25px;overflow:hidden;background-color:#24292d;position:relative;display:flex;flex-direction:column;min-width:0;word-wrap:break-word;border:1px solid rgba(255,255,255,.125)}::ng-deep #page-local-gamification-index .goals ::ng-deep .card--completed{background-color:rgba(0,0,0,.1)}::ng-deep #page-local-gamification-index .goals ::ng-deep .card:hover{opacity:1;background:radial-gradient(circle, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%)}::ng-deep #page-local-gamification-index .goals ::ng-deep .card .card-img{display:flex;justify-content:center}::ng-deep #page-local-gamification-index .goals ::ng-deep .card .card-img img{width:150px;height:150px;border-radius:50%;object-fit:cover}::ng-deep #page-local-gamification-index .goals ::ng-deep .card .card-img img.grayscale{opacity:.8;filter:grayscale(100%)}::ng-deep #page-local-gamification-index .goals ::ng-deep .card .card-body{text-align:center;padding-bottom:5px;margin-top:5px}::ng-deep #page-local-gamification-index .goals ::ng-deep .card .card-body .name{color:#efefef;font-size:17px;font-weight:bold;overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;line-clamp:1;-webkit-box-orient:vertical}::ng-deep #page-local-gamification-index .goals ::ng-deep .card .card-body .description{color:#efefef;font-size:13px;min-height:39px;overflow:hidden;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}::ng-deep #page-local-gamification-index .goals ::ng-deep .card .card-footer{display:flex;justify-content:space-between;flex-direction:column;align-items:center;background-color:rgba(0,0,0,0);border:0;padding-top:20px;padding-bottom:0}@media(max-width: 575px){::ng-deep #page-local-gamification-index .goals{grid-template-columns:repeat(1, 1fr)}}@media(min-width: 576px)and (max-width: 991px){::ng-deep #page-local-gamification-index .goals{grid-template-columns:repeat(2, 1fr)}}@media(min-width: 992px)and (max-width: 1199px){::ng-deep #page-local-gamification-index .goals{grid-template-columns:repeat(3, 1fr)}}@media(min-width: 1200px){::ng-deep #page-local-gamification-index .goals{grid-template-columns:repeat(4, 1fr)}}@media(min-width: 1600px){::ng-deep #page-local-gamification-index .goals{grid-template-columns:repeat(5, 1fr)}}::ng-deep #page-local-gamification-index .empty{margin-top:5px;display:flex;justify-content:center}::ng-deep #page-local-gamification-index .empty img{width:175px;height:auto;filter:url()}::ng-deep .path-local-gamification #region-main-box{overflow:hidden}::ng-deep .path-local-gamification label .btn-link{margin-top:-1px}::ng-deep .path-local-gamification .gap-10{gap:10px}::ng-deep .path-local-gamification .gap-20{gap:20px}::ng-deep .path-local-gamification .gap-y-20{gap:20px 0}::ng-deep .path-local-gamification .badge{font-size:13px;border-radius:.2rem !important}@media(max-width: 576px){::ng-deep .path-local-gamification .badge{font-size:10px}}
