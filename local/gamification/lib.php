<?php

use local_gamification\app\bootstrap;
use local_gamification\repositories\setting_repository;

/**
 * Adds a menubar icon for the Gamification plugin.
 *
 * @return object|false The menubar icon object or false if the plugin is disabled or the user is a guest.
 */
function local_gamification_add_menubar_icon()
{
    global $PAGE;

    $container = bootstrap::get_container();
    $repository = $container->make(setting_repository::class);
    $instance = $repository->get_instance();

    if (!$instance) {
        return false;
    }

    if ((!$instance->is_enabled() && !has_capability('local/gamification:managesettings', \context_system::instance())) || isguestuser()) {
        return false;
    }

    return (object)[
        "name" => get_string('pluginname', 'local_gamification'),
        "icon" => 'icon-trophy',
        "url" => new \moodle_url("/local/gamification/index.php"),
        "active" => $PAGE->pagetype == "local-gamification-index" ? 'active' : '',
        "order" => 7
    ];
}

/**
 * Serve the requested file for the local_user plugin.
 *
 * @param stdClass $course the course object
 * @param stdClass $cm the course module object
 * @param stdClass $context the context
 * @param string $filearea the name of the file area
 * @param array $args extra arguments (itemid, path)
 * @param bool $forcedownload whether or not force download
 * @param array $options additional options affecting the file serving
 * @return bool false if the file not found, just send the file otherwise and do not return anything
 */
function local_gamification_pluginfile(
    $course,
    $cm,
    $context,
    string $filearea,
    array $args,
    bool $forcedownload,
    array $options
): bool {
    // Extracting more information
    $itemid = array_shift($args);
    $filename = array_pop($args);
    if (empty($args)) {
        // $args is empty => the path is '/'.
        $filepath = '/';
    } else {
        // $args contains the remaining elements of the filepath.
        $filepath = '/' . implode('/', $args) . '/';
    }

    // Retrieve the file from the Files API.
    $fs = get_file_storage();
    $file = $fs->get_file($context->id, 'local_gamification', $filearea, $itemid, $filepath, $filename);

    if (!$file) {
        return false;
    }

    send_stored_file($file, DAYSECS, 0, $forcedownload, $options);
};