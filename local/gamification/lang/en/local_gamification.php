<?php

$string['pluginname'] = 'Gamification';

$string['back'] = 'Back';
$string['manage'] = 'Manage';
$string['gamification:managesettings'] = 'Manage gamification settings';
$string['gamification:manageproducts'] = 'Manage gamification store items';
$string['gamification:manageorders'] = 'Manage gamification orders';
$string['gamification:managegoals'] = 'Manage gamification achievements';

$string['home:alertinfo'] = 'Each 01 {$a} earned equals 01 coin.';

$string['setting:alertinfo'] = 'If the reward store is enabled, each experience point earned will be converted into 1 coin, which can be used to redeem prizes in the store.';
$string['setting:alertform'] = "If the option '<b>I want to choose which courses will score individually</b>' is enabled:
<ul>
    <li>You will be able to change the experience point value individually;</li>
    <li>For courses that do not have their value changed, the experience points defined in 'Complete course' will remain;</li>
    <li>Newly added courses will have 0XP and will need to be adjusted individually.</li>
</ul>";

$string['setting:enabled'] = 'Enable Plugin?';
$string['setting:enabled_desc'] = 'Enable access and icon in the sidebar';

$string['setting:enablestore'] = 'Enable Gamification Store?';
$string['setting:enablestore_desc'] = 'Enables the store in the Gamification Plugin';

$string['setting:pointsname'] = 'Points name';
$string['setting:pointsname_default'] = 'Experience Points';
$string['setting:pointsname_help'] = 'Enter a name for the experience points that users will earn. If desired, you can enter a name that references your company or field.';

$string['setting:pointsshortname'] = 'Abbreviation (up to 3 letters)';
$string['setting:pointsshortname_default'] = 'Exp';
$string['setting:pointsshortname_help'] = 'Enter an abbreviation of up to 3 letters to represent the name of the experience points.';

$string['setting:enable'] = 'Enable gamification?';
$string['setting:enable_help'] = 'Enabling Gamification will activate:
<ul>
    <li>The point calculation (XPs)</li>
    <li>The ranking system</li>
</ul>';

$string['setting:enablestore'] = 'Enable reward store?';
$string['setting:enablestore_help'] = "Enabling the Reward Store will activate:
<ul>
    <li>Point conversion ('XPs')</li>
    <li>Coin monetization</li>
    <li>The ability to redeem rewards with coins</li>
</ul>";

$string['setting:reward'] = 'What behavior do you want to encourage with gamification?';
$string['setting:reward_help'] = "The number of points set for <b>'Complete Course'</b> and <b>'Complete Trail'</b> will be accumulated by the user every time they complete a course and/or trail.";
$string['setting:reward_completedcourse_points'] = 'Complete Course';
$string['setting:reward_completedtrail_points'] = 'Complete Trail';
$string['setting:customreward'] = 'I want to choose which courses will score individually';

$string['setting:coursescore'] = 'Score per course';
$string['setting:table:categories'] = 'Categories and courses';
$string['setting:table:qtdcourses'] = 'Number of courses';

$string['product:cover'] = 'Reward photo';
$string['product:cover_placeholder'] = 'Recommended size<br>356 x 260';
$string['product:name'] = 'Reward title';
$string['product:name_placeholder'] = 'Enter the reward name';
$string['product:description'] = 'Brief reward description';
$string['product:description_placeholder'] = 'Briefly describe the benefit or how the reward will be delivered';
$string['product:price'] = 'Coins required for redemption';
$string['product:price_placeholder'] = 'Enter the number of points';
$string['product:price_coins'] = 'Value: {$a} coins';
$string['product:amount'] = 'Stock';
$string['product:amount_placeholder'] = 'Reward stock quantity';
$string['product:amount_units_stock'] = '{$a} units in stock';
$string['product:type'] = 'Product type';
$string['product:type_help'] = 'Physical items require physical delivery to the user. Digital items can be delivered digitally via the user\'s email.';
$string['product:type:physical'] = 'Physical';
$string['product:type:digital'] = 'Digital';
$string['product:status'] = 'Status';
$string['product:status:visible'] = 'Visible';
$string['product:status:hidden'] = 'Hidden';
$string['product:status:toggle'] = 'Show/Hide';
$string['product:status:visible_toggle'] = 'Hide';
$string['product:status:hidden_toggle'] = 'Show';
$string['product:status:visible_toggle_message'] = 'Are you sure you want to hide this reward?';
$string['product:status:hidden_toggle_message'] = 'Are you sure you want to show this reward?';
$string['product:status:hidden_successfully'] = 'Reward successfully hidden';
$string['product:status:shown_successfully'] = 'Reward successfully shown';
$string['product:delivery_method'] = 'Delivery method';
$string['product:delivery_method:physical'] = 'Physical';
$string['product:delivery_method:digital'] = 'Digital';
$string['product:delivery_method_help'] = 'This field defines how the user will receive their reward. For physical and digital products, the user must provide their full residential address and contact details, such as email and phone.';
$string['product:allow_multiple_redemptions'] = 'Can the user redeem multiple times?';
$string['product:visible'] = 'Reward visibility';
$string['product:visible:si'] = 'Visible';
$string['product:visible:no'] = 'Hidden';
$string['product:delete'] = 'Delete reward';
$string['product:delete_message'] = 'Are you sure you want to delete this reward?';

$string['product:add_new'] = 'Add new';
$string['product:add_new_redemption'] = 'Add new reward';
$string['product:edit_redemption'] = 'Edit reward';
$string['product:added_successfully'] = 'Reward added successfully!';
$string['product:edited_successfully'] = 'Reward updated successfully!';
$string['product:deleted_successfully'] = 'Reward deleted successfully!';
$string['product:delete_failed_pending_redemptions'] = 'Cannot delete this item as there are pending redemptions.';
$string['product:not_found'] = 'Reward not found.';
$string['product_deleted_suffix'] = ' (deleted)';

$string['product:table:cover'] = 'Image';
$string['product:table:name'] = 'Name';
$string['product:table:type'] = 'Product Type';
$string['product:table:status'] = 'Status';
$string['product:table:actions'] = 'Actions';

$string['product:validation:name_required'] = 'The reward title is required.';
$string['product:validation:name_minlength'] = 'The reward title must be at least 3 characters.';
$string['product:validation:name_maxlength'] = 'The reward title must not exceed 50 characters.';
$string['product:validation:name_invalid'] = 'The reward title must contain only plain text.';

$string['product:validation:description_required'] = 'The reward description is required.';
$string['product:validation:description_minlength'] = 'The reward description must be at least 10 characters.';
$string['product:validation:description_maxlength'] = 'The reward description must not exceed 500 characters.';
$string['product:validation:description_invalid'] = 'The reward description must contain only plain text.';

$string['product:validation:price_required'] = 'The reward price is required.';
$string['product:validation:price_invalid'] = 'The price must be a positive numeric value.';

$string['product:validation:amount_required'] = 'The reward quantity is required.';
$string['product:validation:amount_invalid'] = 'The quantity must be a positive numeric value.';

$string['order:widget:totalcoins'] = 'Coins spent by users';
$string['order:widget:countcompleted'] = 'Items delivered';
$string['order:widget:countpending'] = 'Pending items';
$string['order:widget:avgtime'] = 'Average waiting time';

$string['order:filter:date_placeholder'] = 'Filter by date';

$string['order:status:all'] = 'All orders';
$string['order:status:pending'] = 'Pending';
$string['order:status:processing'] = 'Processing';
$string['order:status:completed'] = 'Completed';

$string['order:table:code'] = 'Order ID';
$string['order:table:timecreated'] = 'Creation Date';
$string['order:table:user'] = 'User';
$string['order:table:coins'] = 'Coins';
$string['order:table:quantity'] = 'Number of Items';
$string['order:table:status'] = 'Status';
$string['order:table:product'] = 'Item';
$string['order:table:type'] = 'Type';
$string['order:table:total'] = 'Total';
$string['order:table:delivery_info'] = 'Delivery Information';
$string['order:table:address'] = 'Address';
$string['order:table:city'] = 'City';
$string['order:table:save_status'] = 'Save';

$string['order:pagesize:info'] = 'Showing {$a->from} to {$a->to} of {$a->total} records.';

$string['order:update_status'] = 'Status Update';
$string['order:confirm:update_status'] = 'Are you sure you want to complete the order? This action cannot be undone.';

$string['order:status:updated_successfully'] = 'Status updated successfully!';

$string['order:notification:subject'] = 'Status update for your order #{$a->code}';
$string['order:notification:message'] = '<p>Hello {$a->fullname},<br><br>
We would like to inform you that the status of your order #{$a->code} has been updated.<br><br>
New order status: {$a->status}</p>';

$string['goal:add_new'] = 'Add new';
$string['goal:add_goal'] = 'Add new achievement';
$string['goal:edit_goal'] = 'Edit achievement';
$string['goal:criteria_required'] = 'Criteria for earning the badge';

$string['goal:table:image'] = 'Image';
$string['goal:table:name'] = 'Name';
$string['goal:table:status'] = 'Status';
$string['goal:table:actions'] = 'Actions';

$string['goal:cover'] = 'Achievement image';
$string['goal:cover_placeholder'] = 'Recommended Size<br>96 x 96';
$string['goal:name'] = 'Achievement title';
$string['goal:name_placeholder'] = 'Enter the achievement name';
$string['goal:description'] = 'Description';
$string['goal:description_placeholder'] = 'Achievement description';
$string['goal:criteria'] = 'Criteria';
$string['goal:criteria_placeholder'] = 'Select a criterion';
$string['goal:criteria_help'] = 'Select a criterion.';
$string['goal:criteria_count'] = 'Number of times the criterion must be met';
$string['goal:criteria_count_help'] = 'Number of times the criterion must be met';
$string['goal:visible'] = 'Achievement visibility';
$string['goal:visible:si'] = 'Visible';
$string['goal:visible:no'] = 'Hidden';

$string['goal:validation:name_required'] = 'The achievement title is required.';
$string['goal:validation:name_minlength'] = 'The achievement title must be at least 3 characters.';
$string['goal:validation:name_maxlength'] = 'The achievement title must not exceed 50 characters.';
$string['goal:validation:name_invalid'] = 'The achievement title must contain only plain text.';

$string['goal:validation:description_required'] = 'The achievement description is required.';
$string['goal:validation:description_minlength'] = 'The achievement description must be at least 10 characters.';
$string['goal:validation:description_maxlength'] = 'The achievement description must not exceed 300 characters.';
$string['goal:validation:description_invalid'] = 'The achievement description must contain plain text only.';

$string['goal:validation:criteria_duplicate'] = 'The selected criterion, combined with the defined completion count, has already been used.';

$string['goal:added_successfully'] = 'Achievement added successfully!';
$string['goal:edited_successfully'] = 'Achievement updated successfully!';
$string['goal:deleted_successfully'] = 'Achievement deleted successfully!';

$string['goal:delete'] = 'Delete achievement';
$string['goal:delete_message'] = "Are you sure you want to delete the achievement? Once deleted, the achievement will be removed from the 'Gamification Settings' and the users' 'Achievements' page.";

$string['goal:pagesize:info'] = 'Showing from {$a->from} to {$a->to} of {$a->total} records';
$string['goal:time'] = 'time';
$string['goal:times'] = 'times';

$string['goal:goals'] = 'Achievements';
$string['goal:title'] = 'All achievements';

$string['goal:alertinfo'] = "The 'Criterion' and 'Completion count' can only be defined when adding the achievement. These settings cannot be changed later. To define new criteria, add a new achievement.";

$string['task:process_user_goals'] = 'Process user achievements';

$string['title'] = 'Gamification';
$string['help'] = 'Help';
$string['rewards'] = 'Rewards';
$string['history'] = 'Points History';
$string['orders'] = 'Orders';

$string['itemsperpage'] = 'items per page';

$string['ranking:checktheranking'] = 'Check the ranking';

$string['achievements'] = 'Achievements';

$string['manager:title'] = 'Gamification Settings';

$string['manager:tab'] = 'Management';
$string['manager:tab:settings'] = 'Settings';
$string['manager:tab:products'] = 'Store items';
$string['manager:tab:orders'] = 'Orders';
$string['manager:tab:goals'] = 'Achievements';

$string['tab_settings:title'] = 'General settings';
$string['tab_products:title'] = 'Configure store items';
$string['tab_orders:title'] = 'Completed orders';
$string['tab_goals:title'] = 'Configure achievements';

$string['search'] = 'Search...';
$string['search_by_code'] = 'Search by ID';

$string['messageprovider:order_status_updated'] = 'Order status updated';

$string['store:title'] = 'Rewards';
$string['store:popular:title'] = 'Most requested';
$string['store:visibles:title'] = 'Claim now';
$string['store:cheapest:title'] = 'Lowest prices';
$string['store:checkout:title'] = 'Claim your reward';
$string['store:checkout:onDoneAction_app'] = 'This reward had just take. (Action done)';
$string['store:alertinfo'] = 'All your progress on the platform is saved! Use coins to redeem amazing rewards! Coins are earned by completing courses and learning paths.';
$string['store:user_coins'] = '{$a} coins';
$string['store:user_points'] = '{$a->points} {$a->pointsshortname}';
$string['store:product_price'] = '{$a} coins';
$string['store:product_amount'] = '{$a} units remaining';
$string['store:product_buy'] = 'Redeem';
$string['store:product_unavailable'] = 'Unavailable';
$string['store:checkout:saved_address'] = 'Saved addresses';
$string['store:checkout:name'] = 'Name';
$string['store:checkout:name_placeholder'] = 'Enter your name';
$string['store:checkout:email'] = 'Email';
$string['store:checkout:email_placeholder'] = 'Enter your email';
$string['store:checkout:phone'] = 'Phone with area code';
$string['store:checkout:phone_placeholder'] = 'Enter your phone with area code';
$string['store:checkout:postalcode'] = 'Postal code';
$string['store:checkout:postalcode_placeholder'] = 'Enter the postal code';
$string['store:checkout:city'] = 'City';
$string['store:checkout:city_placeholder'] = 'Enter the city';
$string['store:checkout:state'] = 'State';
$string['store:checkout:state_placeholder'] = 'Enter the state';
$string['store:checkout:neighborhood'] = 'Neighborhood';
$string['store:checkout:neighborhood_placeholder'] = 'Enter the neighborhood';
$string['store:checkout:street'] = 'Street';
$string['store:checkout:street_placeholder'] = 'Enter the street';
$string['store:checkout:number'] = 'Number';
$string['store:checkout:number_placeholder'] = 'Enter the number';
$string['store:checkout:complement'] = 'Complement';
$string['store:checkout:complement_placeholder'] = 'Enter the complement';
$string['store:checkout:save_address'] = 'Save information for a future purchase';
$string['store:checkout:save'] = 'Save reward';

$string['store:checkout:address_not_found'] = 'Address not found';
$string['store:checkout:delivery_information'] = 'Delivery information';
$string['store:checkout:purchase_details'] = 'Reward details';
$string['store:checkout:purchase_amount'] = '{$a} unit';
$string['store:checkout:subtotal_coins'] = '{$a} coins';
$string['store:checkout:subtotal'] = 'Subtotal';
$string['store:checkout:your_coins'] = 'Your coins';
$string['store:checkout:coins_forecast'] = 'Remaining coins';
$string['store:checkout:delivery_method'] = 'Delivery method';
$string['store:checkout:product_price'] = '{$a} coins';
$string['store:not_enough_coins'] = 'You do not have enough coins to redeem this reward';
$string['store:checkout:missing_product'] = 'No product selected';
$string['store:checkout:creation_message'] = '<p>Order #{$a->code} has been placed! <br> 
Track your request in the <a href="{$a->link}">my redemptions</a> area.</p>';
$string['store:checkout:creation_message_app'] = '<p>Order #{$a->code} has been placed! <br> 
Track your request in the my redemptions area.</p>';
$string['store:checkout:product_already_purchased'] = 'This reward does not allow multiple redemptions.';

$string['order:notification:creation_subject'] = 'Order #{$a->code}: Placed';
$string['order:notification:creation_message'] = '<p>Hello {$a->user},<br><br>
Order #{$a->code} has been placed! <br><br> 
Track your request in the <a href="{$a->link}">my redemptions</a> area.</p>';

$string['transaction:earned'] = 'Earned {$a}';
$string['transaction:spent'] = 'Redeemed {$a}';
$string['transaction:exception:cannot_use_method_create'] = 'Please use register_earned or register_spent methods.';

$string['validation:store:checkout:invalid_fields'] = 'Invalid Fields.';
$string['validation:name_required'] = 'Name is required.';
$string['validation:name_invalid'] = 'The name should contain only letters, dots, and spaces.';
$string['validation:name_incomplete'] = 'Please enter the full name (first and last name).';
$string['validation:phone_invalid'] = 'The phone number must contain between 10 and 15 digits and only numbers.';
$string['validation:postalcode_required'] = 'Postal code is required.';
$string['validation:postalcode_invalid'] = 'The postal code must contain exactly 8 digits and only numbers.';
$string['validation:city_required'] = 'The city field is required.';
$string['validation:city_minlength'] = 'The city field must have at least 4 characters.';
$string['validation:city_invalid'] = 'The city must contain only letters, including accents, and spaces.';
$string['validation:state_required'] = 'The state field is required.';
$string['validation:state_minlength'] = 'The state field must have at least 4 characters.';
$string['validation:state_invalid'] = 'The state must contain only letters, including accents, and spaces.';
$string['validation:neighborhood_required'] = 'The neighborhood field is required.';
$string['validation:neighborhood_minlength'] = 'The neighborhood field must have at least 3 characters.';
$string['validation:street_required'] = 'The street field is required.';
$string['validation:street_minlength'] = 'The street field must have at least 5 characters.';
$string['validation:number_required'] = 'The residence number is required.';
$string['validation:number_invalid'] = 'The residence number must be numeric or alphanumeric (e.g., 123, 123B).';
$string['validation:complement_maxlength'] = 'The complement must have a maximum of 50 characters.';

$string['transaction:course_completed'] = 'Completed {$a}';
$string['transaction:trail_completed'] = 'Completed {$a}';

$string['store:my_purchases:title'] = 'My Redemptions';
$string['store:transactions:title'] = 'Coin History';

$string['subplugintype_criteria'] = 'Achievement Criterion';
$string['subplugintype_criteria_plural'] = 'Achievement Criteria';

$string['home:page:store'] = 'Rewards Store';
$string['home:page:my_purchases'] = 'My Redemptions';
$string['home:page:transactions'] = 'Coin History';

$string['home:ranking_position'] = 'Ranking Position';
$string['home:ranking_level'] = 'Ranking Level';
$string['home:watched_hours'] = 'Watched Hours';
$string['home:coins_received'] = 'Coins Received in the Last 7 Days';
$string['home:watched_content'] = 'Watched Content';
$string['home:track_progress'] = 'Track Your Progress';
$string['home:banner:title'] = 'Redeem Your Coins';
$string['home:banner:description'] = 'Take advantage of our rewards system and redeem your coins for amazing prizes.';
$string['home:banner:button'] = 'Click and Check Out';
$string['home:for_next_level'] = 'to the next level';
$string['home:position'] = 'th place';
$string['home:checktheranking'] = 'Check the Ranking';
$string['home:level'] = 'Level: ';
$string['home:ranking:position'] = 'Position';
$string['home:ranking:name'] = 'Name';
$string['home:ranking:progress'] = 'Progress';
$string['home:ranking:maxlevel'] = 'Max Level ';
