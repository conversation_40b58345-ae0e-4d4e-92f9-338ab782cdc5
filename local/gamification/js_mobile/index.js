
const indexNS = {
  init: () => {
    ref.registerEvents = async (v) => {
        let valor = v.detail?.value;
        let ldrRank = await ref.LoadingController.ctrl.create({});
        ldrRank.present();
        let request = {
          methodname: "local_gamification_ranking_app",
          args: {'level': valor},
        };
      
        try {
          let ress = await ref.CoreSitesProvider.currentSite.read(request.methodname, request.args, {});
          console.log(ress);
          $('.box.rank.app-block-rank .body').replaceWith(ress.data);
          ldrRank.dismiss();
        } catch (error) {
          let alert = await ref.AlertController.create({header: 'Erro ao atualizar Ranking',message: 'Ocorreu algum problema ao tentar atualizar o ranking com o nível selecionado.'});
          await alert.present();
          ldrRank.dismiss();
          console.log(error);
        }
    };
    $( document ).ready(function() {
      ref.registerEvents();
    });
  }
};