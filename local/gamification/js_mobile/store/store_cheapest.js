class CheapestPage {
  constructor() {
    this.buyLinkDisabled = document.querySelectorAll(".card .buy[disabled]");

    this.registerEvents();
  }

  showInsufficientBalanceMessage = (e) => {
    e.preventDefault();
    ref.CoreToastsService.show({ message: ref.CoreLangProvider.sitePluginsStrings[ref.CoreLangProvider.currentLanguage]['plugin.local_gamification.store:not_enough_coins'].value, color: 'danger', position: 'top', translucent: true, animated: true});
  };

  registerEvents = () => {
    this.buyLinkDisabled.forEach((link) => {
      link.addEventListener("click", this.showInsufficientBalanceMessage);
    });
  };
};

const store_cheapestNS = {
  init: () => {
    new CheapestPage();
  },
};