class MyPurchasesPage {
  constructor() {
    this.skeletons = document.querySelectorAll(".glide .skeleton");
    this.cards = document.querySelectorAll(".glide .card");
    this.buyLinkDisabled = document.querySelectorAll(".card .buy[disabled]");

    this.registerEvents();
    this.initializeGlider();
  }

  initializeGlider = () => {

    console.log("Initializing Glider...");

    let options = {
      type: "slider",
      bound: true,
      perView: 5,
      gap: 20,
      breakpoints: {
        1600: {
          perView: 4,
        },
        1200: {
          perView: 3,
        },
        992: {
          perView: 2,
        },
        768: {
          perView: 2,
        },
        576: {
          perView: 1,
        },
      },
    };

    let glidePurhasePage = new glideNS.init("#glide-visibles-my-purchases", options);
    glidePurhasePage.mount();

    this.skeletons.forEach((skeleton) => {
      skeleton.style.display = "none";
    });

    this.cards.forEach((loader) => {
      loader.style.display = "block";
    });

    document.addEventListener('focusout', () => {
      glidePurhasePage?.update();
    });   
  };

  showInsufficientBalanceMessage = (e) => {
    e.preventDefault();

    ref.CoreToastsService.show({ message: ref.CoreLangProvider.sitePluginsStrings[ref.CoreLangProvider.currentLanguage]['plugin.local_gamification.store:not_enough_coins'].value, color: 'danger', position: 'top', translucent: true, animated: true});
  };

  registerEvents = () => {
    this.buyLinkDisabled.forEach((link) => {
      link.addEventListener("click", this.showInsufficientBalanceMessage);
    });
  };
}

const store_my_purchasesNS = {
  init: () => {
    $( document ).ready(function() {
      new MyPurchasesPage();
    });
  },
};
