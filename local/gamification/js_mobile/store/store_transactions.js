/**
 * Manages my-transactions-page operations and UI interactions.
 */
class TransactionsPage {
  /**
   * Creates an instance of OrderPage.
   */
  constructor() {
    this.pageSize = document.querySelector("#pagesize");

    this.registerEvents();
  }

  changePageSize = () => {
    const form = this.pageSize.closest("form");

    form.submit();
  };

  /**
   * Registers event listeners for various UI elements.
   */
  registerEvents = () => {
    if (this.pageSize) {
      this.pageSize.addEventListener("change", this.changePageSize);
    }
  };
}

/**
 * Initializes the ProductPage.
 */
export const init = () => {
  new TransactionsPage();
};
