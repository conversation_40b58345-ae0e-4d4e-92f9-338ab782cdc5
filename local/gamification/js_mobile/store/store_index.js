class StorePage {
  constructor() {
    this.skeletons = document.querySelectorAll(".glide .skeleton");
    this.cards = document.querySelectorAll(".glide .card");
    this.buyLinkDisabled = document.querySelectorAll(".card .buy[disabled]");

    this.registerEvents();
    this.initializeGlider();
  }

  initializeGlider = () => {

    console.log("Initializing Glider...");

    let options = {
      type: "slider",
      bound: true,
      perView: 5,
      gap: 20,
      breakpoints: {
        1600: {
          perView: 4,
        },
        1200: {
          perView: 3,
        },
        992: {
          perView: 2,
        },
        768: {
          perView: 2,
        },
        576: {
          perView: 1,
        },
      },
    };

    let popularGlide = new glideNS.init("#glide-popular", options);
    let visiblesGlide = new glideNS.init("#glide-visibles", options);
    let cheapestGlide = new glideNS.init("#glide-cheapest", options);

    ref.local_gamification = {
      store: {
        glides: [popularGlide, visiblesGlide, cheapestGlide]
      }
    };

    ref.local_gamification.store.glides.forEach((gls) => {
      gls?.mount();
    });

    this.skeletons.forEach((skeleton) => {
      skeleton.style.display = "none";
    });

    this.cards.forEach((loader) => {
      loader.style.display = "block";
    });

    document.addEventListener('focusout', () => {
      ref.local_gamification.store.glides.forEach((gls) => {
        gls?.update();
      });
    });    
  };

  showInsufficientBalanceMessage = (e) => {
    e.preventDefault();
    ref.CoreToastsService.show({ message: ref.CoreLangProvider.sitePluginsStrings[ref.CoreLangProvider.currentLanguage]['plugin.local_gamification.store:not_enough_coins'].value, color: 'danger', position: 'top', translucent: true, animated: true});
  };

  registerEvents = () => {
    this.buyLinkDisabled.forEach((link) => {
      link.addEventListener("click", this.showInsufficientBalanceMessage);
    });
  };
};

const store_indexNS = {
  init: () => {
    $( document ).ready(function() {
      new StorePage();
    });
  },
};