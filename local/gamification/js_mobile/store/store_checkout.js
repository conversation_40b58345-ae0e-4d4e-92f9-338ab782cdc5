class CheckoutPage {
  constructor() {
    this.formAddress = document.querySelector("#page-local-gamification-store-checkout #form_delivery");
    this.addressSelect = document.querySelector("#page-local-gamification-store-checkout #id_address");
    this.phoneInput = document.querySelector("#page-local-gamification-store-checkout #id_phone");
    this.postalcodeInput = document.querySelector("#page-local-gamification-store-checkout #id_postalcode");

    console.log(this.phoneInput);
    console.log('INIT CheckoutPage');

    this.registerEvents();
  }

  registerEvents = () => {
    this.initializeIMask();

    if (this.addressSelect !== null) {
      this.addressSelect.addEventListener("change", (event) =>
        this.getAddress(event.target.value)
      );
    }

    if (this.postalcodeInput) {
      this.postalcodeInput.addEventListener(
        "change",
        this.findAddressByPostCode
      );
    }
  };

  initializeIMask = () => {
    console.log('INIT initializeIMask');
    if (this.phoneInput) {
      imaskNS.init(this.phoneInput, {
        mask: [
          {
            mask: "(00) 0000-0000",
          },
          {
            mask: "(00) 90000-0000",
          },
        ],
      });
    }

    if (this.postalcodeInput) {
      imaskNS.init(this.postalcodeInput, {
        mask: "00000-000",
      });
    }
  };

  findAddressByPostCode = async (event) => {
    console.log('INIT canche CEP');
    let postalcode = event.target.value;
    let address = await store_repositoryNS.getAddressByPostalCode(postalcode);

    if (!address) {
      let errorMessage = ref.CoreLangProvider.sitePluginsStrings[ref.CoreLangProvider.currentLanguage]['plugin.local_gamification.store:checkout:address_not_found'].value;

      ref.CoreToastsService.show({ message: errorMessage, color: 'danger', translucent: true, animated: true});
      return;
    }

    let preserveFields = ["name", "email", "phone"];

    preserveFields.forEach((field) => {
      address[field] = this.formAddress.querySelector(`[name=${field}]`).value;
    });

    this.populateForm(address);
  };

  populateForm = (address) => {
    let fields = [
      {name: "id", value: address.id},
      {name: "name", value: address.name ?? ""},
      {name: "email", value: address.email ?? ""},
      {name: "phone", value: address.phone ?? ""},
      {name: "postalcode", value: address.postalcode ?? ""},
      {name: "city", value: address.city ?? ""},
      {name: "state", value: address.state ?? ""},
      {name: "neighborhood", value: address.neighborhood ?? ""},
      {name: "street", value: address.street ?? ""},
      {name: "number", value: address.number ?? ""},
      {name: "complement", value: address.complement ?? ""},
    ];

    fields.forEach((field) => {
      let element = this.formAddress.querySelector(`[name=${field.name}]`);
      if (element) {
        element.value = field.value;
      }
    });
  };

  resetFormData = () => {
    this.populateForm({
      id: 0,
      name: "",
      email: "",
      phone: "",
      postalcode: "",
      city: "",
      state: "",
      neighborhood: "",
      street: "",
      number: "",
      complement: "",
    });
  };

  getAddress = async (id) => {
    try {
      id = parseInt(id);

      if (id === 0) {
        this.resetFormData();
        return;
      }

      let address = await store_repositoryNS.getAddress(id);

      this.populateForm(address);
    } catch (e) {
      alert(e);
    }
  };
}

const store_checkoutNS = {
  init: () => {
    ref.backActionOnceJs = function(){
      ref.CoreNavigatorService.back();
    };
    ref.sendingFormCheckoutDeliveryStatus = false;
    ref.sendFormCheckoutDelivery = async function () {
      if(ref.sendingFormCheckoutDeliveryStatus == true) {
        let loadingRequest = await ref.ToastController.ctrl.create({
          message: '<ion-spinner name="crescent"></ion-spinner>',
          spinner: 'crescent',
          duration: 2000
        });
        await loadingRequest.present();
      } else {
        ref.sendingFormCheckoutDeliveryStatus = true;
        let form = document.querySelector("#page-local-gamification-store-checkout #form_delivery");
        let dados = {
            'productid': form.querySelector('[name="productid"]').value,
            'name': form.querySelector('[name="name"]').value,
            'email': form.querySelector('[name="email"]').value,
            'phone': form.querySelector('[name="phone"]').value,
            'address': form.querySelector('[name="address"]')?.value || 0,
            'postalcode': form.querySelector('[name="postalcode"]')?.value || '',
            'city': form.querySelector('[name="city"]')?.value || '',
            'state': form.querySelector('[name="state"]')?.value || '',
            'neighborhood': form.querySelector('[name="neighborhood"]')?.value || '',
            'street': form.querySelector('[name="street"]')?.value || '',
            'number': form.querySelector('[name="number"]')?.value || '',
            'complement': form.querySelector('[name="complement"]')?.value || '',
            'save_address': form.querySelector('[name="save_address"]')?.checked || false
        };
        
        let result = await store_repositoryNS.sendFormCheckoutDelivery(dados);
        document.querySelectorAll(".error-message-form-validation").forEach(span => span.remove());
        if(result.erro) {
          ref.CoreToastsService.show({ message: 'WS | '+result.message.message, color: 'danger', translucent: true, animated: true});
          ref.sendingFormCheckoutDeliveryStatus = false;
        } else {
          if(result.data.success){
            ref.backActionOnceJs();
            let dtdata = JSON.parse(result.data.data);
            let alert = await ref.AlertController.create({
              header: dtdata.title,
              subHeader: 'pedido #'+dtdata.code,
              message: result.data.message,
              buttons: [{
                text: dtdata.toGo.btnText,
                handler: () => {
                  ref.openContent(dtdata.toGo.btnText, {page: 'store/my_purchases', nCheckout: dados.productid+"-|-"+dados.email}, "local_gamification", "mobile_view_local_gamification", null, {}, true);
                }
              }, {
                text: 'OK'
              }],
              });
            await alert.present();
            ref.sendingFormCheckoutDeliveryStatus = false;
          } else {
            let dtdata = JSON.parse(result.data.data);
            if(dtdata.invalid){
              for (let chave in dtdata.invalid) {
                let field = document.querySelector(`#page-local-gamification-store-checkout [name="${chave}"]`).parentElement;
                let spanerror = document.createElement("span");
                spanerror.classList.add("error-message-form-validation");
                spanerror.style.color = "red";
                spanerror.style.fontSize = "12px";
                spanerror.style.fontWeight = "bold";
                spanerror.style.marginTop = "5px";
                let t = document.createTextNode(dtdata.invalid[chave]);
                spanerror.appendChild(t);
                field.appendChild(spanerror);
              }
            }
            ref.sendingFormCheckoutDeliveryStatus = false;
            ref.CoreToastsService.show({ message: result.data.message, color: 'danger', translucent: true, animated: true});
          }
        }
      }
    };
    $( document ).ready(function() {
      new CheckoutPage();
    });
  }
};