const store_repositoryNS = {
  getAddress: (id) => {
    let request = {
      methodname: "local_gamification_get_address_app",
      args: {
        id,
      },
    };
  
    console.log(ref.CoreSitesProvider);
    console.log(ref.CoreSitesProvider.currentSite.read(request.methodname, request.args, {}));
    return ref.CoreSitesProvider.currentSite.read(request.methodname, request.args, {});
    /* return fetchMany([request])[0]; */
  },

  sendFormCheckoutDelivery: async (dados) => {
    let request = {
      methodname: "local_gamification_checkout_product_transaction",
      args: dados,
    };
  
    try {
      let ress = await ref.CoreSitesProvider.currentSite.write(request.methodname, request.args, {});
      return {
        'erro': false,
        'data': ress
      };
    } catch (error) {
      return {
        'erro': true,
        'message': error
      };
    }
  },

  getAddressByPostalCode: async (postalcode) => {
    let response = await fetch(`https://viacep.com.br/ws/${postalcode}/json`);
    let data = await response.json();
  
    if (data.erro) {
      return null;
    }
  
    let address = {
      id: 0,
      postalcode: postalcode,
      street: data.logradouro,
      neighborhood: data.bairro,
      city: data.localidade,
      state: data.estado,
      complement: data.complemento,
    };
  
    return address;
  }

};