.catalog-page .catalog-header {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
}

.catalog-page .catalog-header .actions {
  display: flex;
  gap: 10px;
}

.catalog-page .catalog-header .actions .back-button {
  display: flex;
  align-items: center;
  height: 34px;
}

#category-list,
#course-list > .card-courses {
  gap: 25px 0;
}

#category-list .card {
  cursor: pointer;
  border: 0;
  overflow: hidden;
  height: var(--card-height);
}

#category-list .card .card-img {
  width: 100%;
  height: var(--img-height);
  max-height: 350px;
  object-fit: cover;
}

#category-list .card .card-img-overlay {
  /* opacity: 0.8;
  background: radial-gradient(
    circle,
    rgb(0 0 0 / 5%) 0%,
    rgb(0 0 0 / 75%) 100%
  ); */

  -webkit-transition: opacity 0.8s ease-in-out;
  -moz-transition: opacity 0.8s ease-in-out;
  -o-transition: opacity 0.8s ease-in-out;
  transition: opacity 0.8s ease-in-out;
}

#category-list .card .card-img-overlay:hover {
  zoom: 1;
  filter: alpha(opacity=100);
  opacity: 1;

  background: radial-gradient(
    circle,
    rgba(0, 0, 0, 0.2) 0%,
    rgb(0 0 0 / 20%) 100%
  );
}

#category-list .card .card-img-overlay .card-title {
  position: absolute;
  width: 80%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  text-align: center;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .catalog-page .catalog-header {
    gap: 20px;
    flex-direction: column-reverse;
  }

  .catalog-page #action_bar .navitem {
    width: 100%;
    margin-right: 0;
  }

  .catalog-page #action_bar .navitem form .custom-select {
    width: 100%;
  }

  .catalog-page #action_bar .navitem .simplesearchform form {
    padding-right: 0;
  }
}

.path-local-catalog .dimmed,
.path-local-catalog .card-img-top:not(.visible),
.path-local-catalog .card-body:not(.visible) {
  opacity: 0.5;
}

.path-local-catalog .modal-dialog .row {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.path-local-catalog .modal-dialog label {
  margin-right: 0;
}

.path-local-catalog .field-parent-container .felement {
  display: flex;
  flex-direction: column-reverse;
}

.path-local-catalog .modal-dialog .form-inline,
.path-local-catalog .modal-dialog input[type="text"],
.path-local-catalog .modal-dialog select {
  width: 100% !important;
}

.path-local-catalog .form-filetypes-descriptions ul {
  display: flex;
}

.path-local-catalog .form-filetypes-descriptions ul li:not(:last-child):after {
  content: ", ";
}

.path-local-catalog .form-filetypes-descriptions ul li {
  padding-right: 0.5rem;
}

.path-local-catalog .btn-view.active {
  pointer-events: none;
}

.path-local-catalog .card:focus-within,
.path-local-catalog .list-item:focus-within {
  box-shadow: 0 0 0 0.2rem var(--primary) !important;
}
