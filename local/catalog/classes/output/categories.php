<?php

namespace local_catalog\output;

use paging_bar;
use renderer_base;
use moodle_url;
use \theme_smart\output\spark_paging_bar;

require_once($CFG->dirroot . '/local/catalog/locallib.php');

class categories extends page
{
    /** @var context $context */
    protected $context;

    /** @var stdClass $category */
    protected $category;

    /** @var string $search */
    protected $search;

    /** @var \paging_bar|null $pagination */
    protected ?paging_bar $pagination = null;

    /**
     * Construtor da classe categories.
     *
     * @param int $page O número da página.
     * @param int $categoryid O ID da categoria.
     * @param string $search A string de pesquisa.
     */
    public function __construct($page, $categoryid = 0, $search = '')
    {
        parent::__construct($page, $categoryid, $search);

        $this->require_resources();
    }

    /**
     * Exporta os dados do template para renderização.
     *
     * @param renderer_base $output O objeto renderer.
     * @return array Os dados exportados do template.
     */
    public function export_for_template(renderer_base $output): array
    {
        global $OUTPUT, $USER, $PAGE;

        $data = parent::export_for_template($output);

        $is_client = theme_smart_is_client($USER->id);

        // URL para a paginação.
        $url = new moodle_url('/local/catalog/index.php', [
            'categoryid' => $this->category->id,
            'search' => $this->search,
        ]);

        // Obtém as categorias com paginação.
        [$categories, $total] = local_catalog_get_categories($this->category->id, $this->page);

        $data['is_admin'] = $is_client || is_siteadmin();
        $data['categories'] = array_values($categories);
        $data['emptyurl'] = $OUTPUT->image_url('empty-list', 'local_catalog');
        $data['has_categories'] = !empty($categories);

        // Configura a paginação.
        $this->pagination = new spark_paging_bar($total, $this->page, LOCAL_CATALOG_PAGESIZE, $url);

        if ($this->pagination && $data['has_categories']) {
            $data['pagination'] = $output->render($this->pagination);
        }

        return $data;
    }

    /**
     * Requer os recursos necessários para a página atual.
     *
     * Esta função inicializa os recursos JavaScript necessários para a página atual.
     * Ela utiliza o objeto PAGE do Moodle para carregar os arquivos JavaScript necessários.
     *
     * @return void
     */
    public function require_resources()
    {
        global $PAGE;

        $PAGE->requires->js_call_amd(
            'local_catalog/category-card',
            'init',
            []
        );
    }
}
