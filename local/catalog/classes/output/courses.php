<?php

namespace local_catalog\output;

use core_completion\progress;
use local_courseblockapi\traits\course_trait;
use moodle_url;
use paging_bar;
use \theme_smart\output\spark_paging_bar;
use renderer_base;
use stdClass;

require_once($CFG->dirroot . '/local/catalog/locallib.php');

class courses extends page
{
    use course_trait;

    /** @var context $context */
    protected $context;

    /** @var stdClass $category */
    protected $category;

    /** @var string $search */
    protected $search;

    protected ?paging_bar $pagination = null;
    protected ?paging_bar $paginationCategories = null;


    /**
     * Constructor for the courses class.
     *
     * @param int $page The page number.
     * @param int $categoryid The ID of the category.
     * @param string $search The search query.
     * @return void
     */
    public function __construct($page, $categoryid = 0, $search = '')
    {
        parent::__construct($page, $categoryid, $search);

        $this->require_resources();
    }

    /**
     * Export the template data for the courses page.
     *
     * @param renderer_base $output The renderer object.
     * @return array The template data.
     */
    public function export_for_template(renderer_base $output): array
{
    global $USER, $PAGE, $OUTPUT;

    $is_client = theme_smart_is_client($USER->id);
    $data = parent::export_for_template($output);

    $url = new moodle_url($PAGE->url, ['categoryid' => $this->category->id]);

    // Separar controle de páginas para cursos e categorias
    $coursePage = optional_param('coursepage', 0, PARAM_INT);
    $categoryPage = optional_param('categorypage', 0, PARAM_INT);

    // Obter cursos e total de cursos com base na página de cursos
    [$courses, $totalCourses] = local_catalog_get_courses($coursePage, $this->search, $this->category->id);

    foreach ($courses as $course) {
        $course->courseid            = $course->id;
        $course->total_sections      = self::get_course_total_sections($course->id, null);
        $course->progress_percentage = round(progress::get_course_progress_percentage($course, $USER->id) ?? 0);
        $course->url                 = self::get_course_url_with_entry($course->id, $url);
        $course->cover               = self::get_course_image($course);
        $course->is_enrolled         = self::get_course_user_is_enrolled($course->id, $USER->id);
        $course->is_favourite        = self::get_course_is_favourite($course->id, $USER->id);
        $course->gamification        = self::get_course_gamification($course->id);
    }

    // Obter categorias e total de categorias com base na página de categorias
    [$categories, $totalCategories] = local_catalog_get_categories($this->category->id, $categoryPage);

    $data['emptyurl'] = !count($courses) ? $OUTPUT->image_url('empty-list', 'local_catalog')->out() : '';
    $data['is_admin'] = $is_client || is_siteadmin() ? true : false;
    $data['has_categories'] = !empty($categories);
    $data['courses'] = $courses;

    if (!empty($this->search)) {
        $data['page']['description'] = get_string('search_results', 'local_catalog') . ': ' . count($courses);
    } else {
        $data['categories'] = $categories;
    }

    $coursePageUrl = new moodle_url($url, ['search' => $this->search]);
    $categoryPageUrl = new moodle_url($url, ['search' => $this->search]);

    $coursePageUrl->param('categorypage', $categoryPage);
    $categoryPageUrl->param('coursepage', $coursePage);

    // Paginação separada para cursos e categorias
    $coursePagination = new spark_paging_bar($totalCourses, $coursePage, LOCAL_CATALOG_PAGESIZE, $coursePageUrl, 'coursepage');
    $categoryPagination = new spark_paging_bar($totalCategories, $categoryPage, LOCAL_CATALOG_PAGESIZE, $categoryPageUrl, 'categorypage');

    if ($coursePagination) {
        $data['pagination'] = $output->render($coursePagination);
    }
    if ($categoryPagination && empty($this->search)) {
        $data['paginationCategories'] = $output->render($categoryPagination);
    }

    return $data;
}

    /**
     * Requires the necessary resources for the current page.
     *
     * This function initializes the JavaScript resources required for the current page.
     * It uses the Moodle PAGE object to load the necessary JavaScript files.
     *
     * @throws None
     * @return void
     */
    public function require_resources()
    {
        global $PAGE;

        $PAGE->requires->js_call_amd(
            'local_catalog/category-card',
            'init',
            []
        );

        $PAGE->requires->js_call_amd(
            'local_courseblockapi/favourite',
            'init',
            ['local_courseblockapi']
        );

        $PAGE->requires->js_call_amd(
            'local_courseblockapi/block',
            'init',
            ['courses']
        );
    }

    /**
     * Generates a URL for a course with a parameter for the entry page.
     *
     * The generated URL includes the course ID, and the current URL as a parameter
     * for the entry page. This is used to open the course from the catalog page.
     *
     * @param int $courseid The ID of the course.
     * @param moodle_url $currenturl The current URL.
     * @return string The generated URL.
     */
    protected function get_course_url_with_entry($courseid, $currenturl)
    {
        $params = [
            'entryname' => 'local_catalog',
            'entryurl' => $currenturl
        ];

        $url = self::get_course_url($courseid);

        return (new moodle_url($url, $params))->out(false);
    }
}
