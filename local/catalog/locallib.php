<?php

use tool_lfxp\core\category\category_image;

require_once($CFG->dirroot . "/admin/tool/coursemanagement/lib.php");
//require_once($CFG->libdir . '/coursecatlib.php');

define('LOCAL_CATALOG_PAGESIZE', 8);

/**
 * Retrieves an array of categories and their information.
 *
 * @param int $categoryid The ID of the parent category. Defaults to 0.
 * @param int $page The page number for pagination. Defaults to 0.
 * @return array An array of category information and total count.
 */
function local_catalog_get_categories($categoryid = 0, $page = 0): array
{
    global $OUTPUT, $USER;

    $is_client = theme_smart_is_client($USER->id);

    $data = core_course_category::get($categoryid)->get_children();
    $total = count($data);
    
    // Garantir que $page seja um inteiro
    $page = (int) $page;
    
    $skip = $page * LOCAL_CATALOG_PAGESIZE;
    $paged_data = array_slice($data, $skip, LOCAL_CATALOG_PAGESIZE);

    $categories = [];

    foreach ($paged_data as $category) {

        $coursecount = $category->get_courses();
        $category = convert_to_array($category);
        $has_learningflix = category_has_course_learningflix($category['id']);
        $cover = category_image::get_url($category['id']);

        $category['hascover'] = $cover ? true : false;
        $category['cover'] = $cover ?? $OUTPUT->image_url("cover-default", "local_catalog");
        $category['url'] = new moodle_url('/local/catalog/index.php?categoryid=' . $category['id']);
        $category['canedit'] = (!$has_learningflix && $is_client) || is_siteadmin() ? true : false;
        $category['coursecount'] = count($coursecount);

        $categories[] = $category;
    }

    return [$categories, $total];
}

/**
 * Retrieves an array of courses and their information.
 *
 * @param int $page The page number of courses to retrieve.
 * @param string $search The string to search courses by.
 * @param int $categoryid The category ID to filter courses by. Defaults to 0.
 * @return array An array of course information, with the total number of results.
 */
function local_catalog_get_courses($page, $search, $categoryid = 0): array
{
    global $DB, $USER;

    $is_admin = theme_smart_is_client($USER->id) || is_siteadmin();

    $courses = [];
    $where = "1=1 ";
    $params = [];

    if (!empty($search)) {
        $course_query = ' (' . $DB->sql_like('mc.fullname', ':coursename1', false, false)
            . ' OR ' . $DB->sql_like('mc.shortname', ':coursename2', false, false)
            . ' OR ' . $DB->sql_like('mc.summary', ':coursename3', false, false)
            . ' ) ';

        $params['coursename1'] = '%' . trim($search) . '%';
        $params['coursename2'] = '%' . trim($search) . '%';
        $params['coursename3'] = '%' . trim($search) . '%';

        // Search in modules
        $sqlmodules = "SELECT m.name FROM {modules} m";
        $modules = $DB->get_recordset_sql($sqlmodules);

        $search_modules = [];

        foreach ($modules as $module) {
            $search_modules[] = "
                mc.id IN (
                    SELECT course FROM {{$module->name}} WHERE LOWER(CONCAT_WS(' ', name, intro)) LIKE LOWER(:search_{$module->name})
                )";

            $params["search_{$module->name}"] = '%' . $search . '%';
        }

        $search_query = join(' OR ', $search_modules);

        $where .= " AND ({$course_query} OR {$search_query})";
    }

    if ($categoryid > 0) {
        $where .= " AND mc.category = :categoryid ";
        $params['categoryid'] = $categoryid;
    }

    $where .= !$is_admin ? " AND mc.visible = 1 " : "";

    $sql = "SELECT 
                mc.id, mc.fullname AS course, mc.visible, mcc.name AS category, UPPER(mlcfc.modality) AS modality
            FROM {course} mc
                JOIN {course_categories} mcc ON (mcc.id = mc.category)
                JOIN {local_custom_fields_course} mlcfc ON (mlcfc.courseid = mc.id)
            WHERE $where
            ORDER BY mc.sortorder";

    $skip = $page * LOCAL_CATALOG_PAGESIZE;

    $recordset = $DB->get_recordset_sql($sql, $params, $skip, LOCAL_CATALOG_PAGESIZE);

    $total = $DB->count_records_sql("SELECT COUNT(1) FROM ($sql) counted", $params);

    foreach ($recordset as $record) {
        $courses[] = $record;
    }

    $recordset->close();


    return [$courses, $total];
}
