<?php

defined('MOODLE_INTERNAL') || die();

if ($hassiteconfig) {
    $ADMIN->add('accounts', new admin_externalpage('local_hierarchy',
        get_string('pluginname', 'local_hierarchy'),
        new moodle_url('/local/hierarchy/index.php/positions')));

    // removido na v0.2
    // // Main config page
    // $settings = new admin_settingpage(
    //     'manage_hierarchy_orgchart',
    //     new lang_string('settings:orgchart_title', 'local_hierarchy'),
    //     'local/hierarchy:manage'
    // );

    // $ADMIN->add("localplugins", $settings);

    // if ($ADMIN->fulltree) {
    //     require_once(__DIR__ . '/lib.php');

    //     $settings->add(new admin_setting_heading(
    //         'local_hierarchy/settingsheading',
    //         new lang_string('settings:orgchart_manager_main_heading_title', 'local_hierarchy'),
    //         new lang_string('settings:orgchart_manager_main_heading_desc', 'local_hierarchy')
    //     ));

        
    //     // $options = local_hierarchy_get_manager_positions_array();

    //     $settings->add(new admin_setting_configselect(
    //         'local_hierarchy/orgchart_main_manager',
    //         new lang_string('settings:orgchart_manager_main', 'local_hierarchy'),
    //         new lang_string('settings:orgchart_manager_main_desc', 'local_hierarchy'),
    //         0,
    //         $options
    //     ));

    // }
    // removido na v0.2
}
