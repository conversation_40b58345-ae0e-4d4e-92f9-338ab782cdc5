<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_hierarchy\external;

use local_hierarchy\output\index;
use local_hierarchy\model\positions;
use core_external\external_function_parameters;
use core_external\external_multiple_structure;
use core_external\external_single_structure;
use core_external\external_api;
use core_external\external_value;
use context_system;
use moodle_exception;

/**
 * Class positions_upload
 *
 * @package    local_hierarchy
 * @copyright  2024 2024 REVVO
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class positions_upload extends external_api
{

    /**
     * Verifica se o nome do cargo já existe.
     */
    public static function validate_name_exists($row): bool
    {
        return positions::record_exists_select('LOWER(name) = LOWER(:name) AND id != :id AND deleted = 0', [
            'name' => $row['cargo'],
            'id' => $row['id_cargo']
        ]);
    }

    /**
     * Verifica se o ID do cargo existe.
     */
    public static function validate_id_exists($row): bool
    {
        return positions::record_exists_select('id = :id AND deleted = 0', ['id' => $row['id_cargo']]);
    }

    /**
     * Verifica se o campo "manage" foi alterado.
     */
    public static function validate_manage_update($row): bool
    {
        if (!self::validate_id_exists($row)) {
            return false;
        }

        $position = new positions((int)$row['id_cargo']);
        $current_manage = $position->get('manage');
        // Modificado para aceitar "SIM" em qualquer formato de capitalização
        $new_manage = (strtolower(trim($row['gestor'])) === 'sim') ? 1 : 0;

        return $current_manage != $new_manage;
    }

    /**
     * Obtém o status da posição.
     */
    // Adicionar este novo método após os métodos de validação existentes
    protected static function validate_duplicate_row($current_row, $processed_rows): bool
    {
        foreach ($processed_rows as $row) {
            if (strcasecmp($row['cargo'], $current_row['cargo']) === 0) {
                return true;
            }
        }
        return false;
    }

    public static function get_status($row, $processed_rows = []): array
    {
        $status = [];
        $status_color = '';

        // Validação de linha duplicada no arquivo
        if (self::validate_duplicate_row($row, $processed_rows)) {
            $status[] = 'Cargo duplicado no arquivo';
            $status_color = 'danger';
            // Retorna imediatamente se encontrar um erro crítico
            $status_message = implode('; ', $status);
            return [$status_message, $status_color];
        }

        if (self::validate_name_exists($row)) {
            $status[] = get_string('validate_name_exists', 'local_hierarchy');
            $status_color = 'danger';
            // Retorna imediatamente se encontrar um erro crítico
            $status_message = implode('; ', $status);
            return [$status_message, $status_color];
        }
        
        if (!self::validate_id_exists($row) && !empty($row['id_cargo'])) {
            $status[] = get_string('validate_id_exists', 'local_hierarchy');
            $status_color = 'danger';
            // Retorna imediatamente se encontrar um erro crítico
            $status_message = implode('; ', $status);
            return [$status_message, $status_color];
        }
        
        if (self::validate_id_exists($row) && $status_color !== 'danger') {
            $status[] = get_string('validate_update', 'local_hierarchy');
            $status_color = 'success';
        }
        
        // Verifica se a posição está ativa antes de aplicar a validação do campo "manage".
        if (self::validate_id_exists($row)) {
            $position = new positions((int)$row['id_cargo']);
            if ($position->fetch_status() === 1 && self::validate_manage_update($row)) {
                $status[] = get_string('validate_manage_update', 'local_hierarchy');
                $status_color = 'warning';
            }
        }
        
        if (empty($status)) {
            $status[] = get_string('validate_create', 'local_hierarchy');
            $status_color = 'success';
        }

        $status_message = implode('; ', $status);
        return [$status_message, $status_color];
    }

    /**
     * Método para salvar ou atualizar uma posição usando o persistent do Moodle.
     */
    protected static function save_position(array $data)
    {
        global $DB; // Necessário para o contexto do Moodle.

        // Tentar carregar a posição com o ID fornecido.
        if (!empty($data['id_cargo'])) {
            $position = new positions((int)$data['id_cargo']);

            // Verificar se a posição existe.
            if (!$position->get('id')) {
                throw new moodle_exception('Cargo com o ID ' . $data['id_cargo'] . ' não encontrado.');
            }
        } else {
            // Se não existe um ID, criar uma nova posição.
            $position = new positions();
        }

        // Atribuir os valores ao persistent.
        $position->set('name', $data['cargo']);
        if (!empty($data['gestor'])) {
            // Modificado para aceitar "SIM" em qualquer formato de capitalização
            $manage = (strtolower(trim($data['gestor'])) === 'sim') ? 1 : 0;
            // Verificar se o cargo está em uso, se estivar, não pode atualizar $manage.
            if ($position->fetch_status() === 0 || !self::validate_manage_update($data)) {
                $position->set('manage', $manage);
            }
        }

        // Restaura o cargo se estiver excluído.
        if ((bool)$position->get('deleted')) {
            $position->set('deleted', 0);
            $position->set('userdeleted', 0);
        }

        // Valida e salva a posição.
        try {
            $position->validate();
            $position->save();
        } catch (Exception $e) {
            throw new moodle_exception('Erro ao salvar a posição: ' . $e->getMessage());
        }

        return $position;
    }

    /**
     * Describes the parameters for local_hierarchy_preview_upload
     *
     * @return external_function_parameters
     */
    public static function preview_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'file' => new external_value(PARAM_FILE, 'CSV File'),
                'delimiter' => new external_value(PARAM_RAW, 'CSV delimiter'),
                'encoding' => new external_value(PARAM_TEXT, 'File encoding'),
                'lines' => new external_value(PARAM_INT, 'Number of preview lines')
            ]
        );
    }

    /**
     * Implementation of web service local_hierarchy_preview_upload
     *
     * @param string $file
     * @param string $delimiter
     * @param string $encoding
     * @param int $lines
     * @return array
     */
    public static function preview($file, $delimiter, $encoding, $lines)
    {
        $params = self::validate_parameters(
            self::preview_parameters(),
            [
                'file' => $file,
                'delimiter' => $delimiter,
                'encoding' => $encoding,
                'lines' => $lines
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);
        require_capability('local/hierarchy:manage', $context);

        // Decodificar o arquivo Base64
        $decoded_file = base64_decode($params['file']);

        // Remove o BOM (se existir)
        if (substr($decoded_file, 0, 3) === "\xEF\xBB\xBF") {
            $decoded_file = substr($decoded_file, 3);
        }

        // Forçar o conteúdo como UTF-8
        $utf8_content = mb_convert_encoding($decoded_file, 'UTF-8', $params['encoding']);

        // Usar php://temp para criar um ponteiro temporário
        $file_handle = fopen('php://temp', 'r+');
        fwrite($file_handle, $utf8_content);
        rewind($file_handle);

        $positions = [];
        $processed_rows = []; // Adicionar esta linha
        $line_count = 0;

        $headers = array_map('strtolower', fgetcsv($file_handle, 0, $delimiter));

        // Verifique se a $headers tem pelo menos 2 colunas
        if (count($headers) < 2) {
            fclose($file_handle);
            return [
                'items' => [],
                'headers' => [],
                'params' => $params,
                'error' => 'Delimitador definido é incompatível com o formato do arquivo ou faltam colunas. Verifique o delimitador e as colunas definidas e tente novamente.'
            ];
        }

        // Processar o CSV linha por linha até alcançar o limite de pré-visualização
        while (($data = fgetcsv($file_handle, 0, $delimiter)) !== false && $line_count < $lines) {
            if (count($data) === count($headers)) {
                $row = array_combine($headers, $data);
                $row['linha_csv'] = ($line_count + 2);

                list($status, $status_color) = self::get_status($row, $processed_rows); // Modificar esta linha

                $row['status'] = $status;
                $row['status_color'] = $status_color;

                $positions[] = $row;
                $processed_rows[] = $row; // Adicionar esta linha
            }
            $line_count++;
        }

        fclose($file_handle);

        array_unshift($headers, 'Linha do CSV');
        array_push($headers, 'Status');

        // Retornar os dados processados para o frontend
        return [
            'items' => $positions,
            'headers' => $headers,
            'params' => $params
        ];
    }

    /**
     * Describes the return structure for local_hierarchy_preview_upload
     *
     * @return external_single_structure
     */
    public static function preview_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'items' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'linha_csv' => new external_value(PARAM_INT, 'Número da linha no CSV'),
                            'id_cargo' => new external_value(PARAM_RAW, 'ID do cargo'),
                            'cargo' => new external_value(PARAM_RAW, 'Nome do cargo'),
                            'gestor' => new external_value(PARAM_RAW, 'Nome do gestor'),
                            'status' => new external_value(PARAM_RAW, 'Status do cargo'),
                            'status_color' => new external_value(PARAM_RAW, 'Cor do status do cargo'),
                        ]
                    )
                ),
                'headers' => new external_multiple_structure(
                    new external_value(PARAM_RAW, 'CSV headers')
                ),
                'params' => new external_single_structure(
                    [
                        'file' => new external_value(PARAM_FILE, 'CSV File'),
                        'delimiter' => new external_value(PARAM_RAW, 'CSV delimiter'),
                        'encoding' => new external_value(PARAM_TEXT, 'File encoding'),
                        'lines' => new external_value(PARAM_INT, 'Number of preview lines')
                    ]
                ),
                'error' => new external_value(PARAM_RAW, 'Error message', null, false),
            ]
        );
    }

    /**
     * Describes the parameters for local_hierarchy_process_upload
     *
     * @return external_function_parameters
     */
    public static function process_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'file' => new external_value(PARAM_FILE, 'CSV File'),
                'delimiter' => new external_value(PARAM_RAW, 'CSV delimiter'),
                'encoding' => new external_value(PARAM_TEXT, 'File encoding'),
                'lines' => new external_value(PARAM_INT, 'Number of preview lines'),
                'action' => new external_value(PARAM_INT, 'Transmission action: 1 = Add new only, 2 = Add and update, 3 = Update only'),
            ]
        );
    }

    /**
     * Implementation of web service local_hierarchy_process_upload
     *
     * @param string $file
     * @param string $delimiter
     * @param string $encoding
     * @param int $lines
     * @return array
     */
    public static function process($file, $delimiter, $encoding, $lines, $action)
    {
        $params = self::validate_parameters(
            self::process_parameters(),
            [
                'file' => $file,
                'delimiter' => $delimiter,
                'encoding' => $encoding,
                'lines' => $lines,
                'action' => $action
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);
        require_capability('local/hierarchy:manage', $context);

        // Decodificar o arquivo Base64
        $decoded_file = base64_decode($params['file']);

        // Remove o BOM (se existir)
        if (substr($decoded_file, 0, 3) === "\xEF\xBB\xBF") {
            $decoded_file = substr($decoded_file, 3);
        }

        // Forçar o conteúdo como UTF-8
        $utf8_content = mb_convert_encoding($decoded_file, 'UTF-8', $params['encoding']);

        // Usar php://temp para criar um ponteiro temporário
        $file_handle = fopen('php://temp', 'r+');
        fwrite($file_handle, $utf8_content);
        rewind($file_handle);

        $positions = [];
        $line_count = 0;

        $headers = array_map('strtolower', fgetcsv($file_handle, 0, $delimiter));

        // Processar o CSV linha por linha até alcançar o limite de pré-visualização
        while (($data = fgetcsv($file_handle, 0, $delimiter)) !== false) {
            if (count($data) === count($headers)) {
                $row = array_combine($headers, $data);
                $row['linha_csv'] = ($line_count + 2);

                list($status, $status_color) = self::get_status($row);
                $row['status'] = $status;
                $row['status_color'] = $status_color;

                // Lógica para salvar com base na ação selecionada
                switch ($action) {
                    case 1: // Adicionar somente novos cargos
                        if ($status_color === 'success' && !$row['id_cargo']) {
                            try {
                                $saved_position = self::save_position($row);
                                $row['id_cargo'] = $saved_position->get('id');
                                $row['status'] = 'Cargo cadastrado com sucesso';
                                $row['status_color'] = 'success';
                            } catch (Exception $e) {
                                $row['status'] = 'Erro ao salvar cargo: ' . $e->getMessage();
                                $row['status_color'] = 'danger';
                            }
                        } else {
                            $row['status'] = 'Nada foi alterado no cargo.';
                            $row['status_color'] = 'warning';
                        }
                        break;

                    case 2: // Atualizar cargos
                        if ($row['id_cargo']) {
                            try {
                                $saved_position = self::save_position($row);
                                $row['id_cargo'] = $saved_position->get('id');
                                if (self::validate_manage_update($row)) {
                                    $row['status'] = 'Cargo atualizado com sucesso.Exceto a coluna GESTOR';
                                    $row['status_color'] = 'warning';
                                } else {
                                    $row['status'] = 'Cargo atualizado com sucesso.';
                                    $row['status_color'] = 'success';
                                }
                                $row['gestor'] = $saved_position->get('manage') ? 'Sim' : 'Não';
                            } catch (Exception $e) {
                                $row['status'] = 'Erro ao salvar cargo: ' . $e->getMessage();
                                $row['status_color'] = 'danger';
                            }
                        } else {
                            $row['status'] = 'Não foi criado o cargo.';
                            $row['status_color'] = 'warning';
                        }
                        break;
                }

                $positions[] = $row;
            }

            $line_count++;
        }

        fclose($file_handle);

        array_unshift($headers, 'Linha do CSV');
        array_push($headers, 'Status');


        // Retornar os dados processados para o frontend
        return [
            'items' => $positions,
            'headers' => $headers,
            'params' => $params
        ];
    }

    /**
     * Describes the return structure for local_hierarchy_process_upload
     *
     * @return external_single_structure
     */
    public static function process_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'items' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'linha_csv' => new external_value(PARAM_INT, 'Número da linha no CSV'),
                            'id_cargo' => new external_value(PARAM_RAW, 'ID do cargo'),
                            'cargo' => new external_value(PARAM_RAW, 'Nome do cargo'),
                            'gestor' => new external_value(PARAM_RAW, 'Nome do gestor'),
                            'status' => new external_value(PARAM_RAW, 'Status do cargo'),
                            'status_color' => new external_value(PARAM_RAW, 'Cor do status do cargo'),
                        ]
                    )
                ),
                'headers' => new external_multiple_structure(
                    new external_value(PARAM_RAW, 'CSV headers')
                ),
                'params' => new external_single_structure(
                    [
                        'file' => new external_value(PARAM_FILE, 'CSV File'),
                        'delimiter' => new external_value(PARAM_RAW, 'CSV delimiter'),
                        'encoding' => new external_value(PARAM_TEXT, 'File encoding'),
                        'lines' => new external_value(PARAM_INT, 'Number of preview lines')
                    ]
                )
            ]
        );
    }

    /**
     * Describes the parameters for local_hierarchy_get_encodings_options
     *
     * @return external_function_parameters
     */
    public static function get_encodings_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            []
        );
    }

    /**
     * Implementation of web service local_hierarchy_get_encoding_options
     *
     * @param string $search
     */
    public static function get_encodings()
    {
        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        $encodings = mb_list_encodings();

        return array_map(
            function ($key, $value) {
                return [
                    'id' => $key,
                    'name' => $value
                ];
            },
            array_keys($encodings),
            array_values($encodings)
        );
    }

    /**
     * Describe the return structure for local_hierarchy_get_encoding_options
     *
     * @return external_multiple_structure
     */
    public static function get_encodings_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure(
                [
                    'id' => new external_value(PARAM_INT, 'Encoding id'),
                    'name' => new external_value(PARAM_TEXT, 'Encoding name'),
                ]
            )
        );
    }
}
