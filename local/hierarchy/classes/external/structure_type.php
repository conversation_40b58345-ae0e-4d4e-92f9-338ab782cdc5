<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_hierarchy\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_multiple_structure;
use core_external\external_single_structure;
use core_external\external_value;
use \local_hierarchy\model\structure_type as model;

/**
 * Class structure_type
 *
 * @package    local_hierarchy
 * @copyright  2025 Revvo | Dev <PERSON> Oliveira
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class structure_type extends external_api
{

  /**
   * Retorna um tipo de estrutura pelo id.
   * 
   * @param int $id
   * @return structure_type|null
   */
  public static function get_by_id($id)
  {
    return model::get_by_id($id);
  }

  /**
   * Define os parametros de entrada para o método `fetch`.
   * 
   * @return external_function_parameters
   */
  public static function fetch_parameters()
  {
    return new external_function_parameters([]);
  }

  /**
   * Retorna todos os tipos de estrutura.
   * 
   * @return structure_type[]
   */
  public static function fetch()
  {
    $data = array_map(function ($type) {
      return (array) $type;
    }, model::get_all());

    return ['types' => $data];
  }

  /**
   * Define a estrutura de saída para o método `fetch`.
   *
   * @return external_single_structure
   */
  public static function fetch_returns()
  {
    return new external_single_structure([
      'types' => new external_multiple_structure(
        new external_single_structure([
          'id' => new external_value(PARAM_INT, 'Id do tipo de estrutura'),
          'name' => new external_value(PARAM_TEXT, 'Nome do tipo de estrutura'),
          'timecreated' => new external_value(PARAM_INT, 'Data de criação'),
          'usercreated' => new external_value(PARAM_INT, 'Id do criador'),
          'timemodified' => new external_value(PARAM_INT, 'Data de atualização'),
          'usermodified' => new external_value(PARAM_INT, 'Id do atualizador'),
          'timedeleted' => new external_value(PARAM_INT, 'Data de exclusão', VALUE_OPTIONAL),
          'uses' => new external_value(PARAM_INT, 'Quantidade de usos')
        ])
      ),
    ]);
  }

  /**
   * Define os parâmetros para salvar um tipo de estrutura.
   * 
   * @param int|null $id
   * @param string $name
   * @return external_function_parameters
   */
  public static function save_parameters()
  {
    return new external_function_parameters([
      'id' => new external_value(PARAM_INT, 'ID of the structure type', VALUE_DEFAULT, null),
      'name' => new external_value(PARAM_TEXT, 'Name of the structure type', VALUE_DEFAULT, '')
    ]);
  }

  /**
   * Salva um tipo de estrutura.
   * 
   * @param int|null $id
   * @param string $name
   * @return int
   */
  public static function save($id = null, $name = '')
  {
    global $USER;

    // Validação de entrada
    $params = self::validate_parameters(self::save_parameters(), ['id' => $id, 'name' => $name]);

    $type = new model($params['id'] ?: null);
    $type->set('name', $params['name']);
    $type->set('timecreated', $params['id'] ? $type->get('timecreated') : time());
    $type->set('usercreated', $params['id'] ? $type->get('usercreated') : $USER->id);
    $type->set('timemodified', time());
    $type->set('usermodified', $USER->id);
    $type->set('timedeleted', 0);
    $type->set('userdeleted', 0);
    $type->set('deleted', 0);

    $type->save();

    return [
      'id' => $type->get('id'),
      'message' => get_string($params['id'] ? 'structures_type_update_successfully' : 'structures_type_create_successfully', 'local_hierarchy', $params['name'], 'local_hierarchy')
    ];
  }

  /**
   * Define os parâmetros para salvar um tipo de estrutura.
   * 
   * @return external_value
   */
  public static function save_returns()
  {
    return new external_single_structure([
      'id' => new external_value(PARAM_INT, 'Id do tipo de estrutura'),
      'message' => new external_value(PARAM_TEXT, 'Mensagem de sucesso')
    ]);
  }


  /**
   * Define os parâmetros para deletar um tipo de estrutura.
   * 
   * @param int $id
   * @return external_function_parameters
   */
  public static function delete_parameters()
  {
    return new external_function_parameters([
      'id' => new external_value(PARAM_INT, 'Id do tipo de estrutura'),
    ]);
  }

  /**
   * Deleta um tipo de estrutura.
   * 
   * @param int $id
   * @return void
   */
  public static function delete($id)
  {
    global $USER;
    // Validação de entrada
    $params = self::validate_parameters(self::save_parameters(), ['id' => $id]);

    $type = model::get_by_id($params['id']);
    $type->set('timedeleted', time());
    $type->set('userdeleted', $USER->id);
    $type->set('deleted', 1);

    return $type->save();
  }

  /**
   * Define os parâmetros para deletar um tipo de estrutura.
   * 
   * @return external_value
   */
  public static function delete_returns()
  {
    return new external_value(PARAM_INT, 'Id do tipo de estrutura');
  }
}
