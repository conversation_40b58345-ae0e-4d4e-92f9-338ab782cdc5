<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_hierarchy\external;

use local_hierarchy\output\index;
use local_hierarchy\model\structures as model;
use core_external\external_function_parameters;
use core_external\external_multiple_structure;
use core_external\external_single_structure;
use core_external\external_api;
use core_external\external_value;
use context_system;

/**
 * Class structures
 *
 * @package    local_hierarchy
 * @copyright  2025 Revvo | <PERSON>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class structures extends external_api
{
    /**
     * Define os parametros de entrada para o método `fetch`.
     * 
     * @return external_function_parameters
     */
    public static function fetch_parameters()
    {
        return new external_function_parameters(
            [
                'search' => new external_value(PARAM_RAW, 'Search term or JSON encoded array of search terms', VALUE_DEFAULT, ''),
                'status' => new external_value(PARAM_INT, 'status', VALUE_DEFAULT, 0),
                'page' => new external_value(PARAM_INT, 'page', VALUE_DEFAULT, 1),
                'perpage' => new external_value(PARAM_INT, 'perpage', VALUE_DEFAULT, index::PER_PAGE),
                'sort' => new external_multiple_structure(
                    new external_single_structure([
                        'field' => new external_value(PARAM_TEXT, 'Field name'),
                        'direction' => new external_value(PARAM_TEXT, 'Sort direction')
                    ]),
                    'sortorder',
                    VALUE_DEFAULT,
                    []
                )
            ]
        );
    }

    /**
     * Fetches structures based on the given parameters.
     *
     * @param string $search
     * @param int|null $status
     * @param int $page
     * @param int $perpage
     * @param array $sort
     * @return structures[]
     */
    public static function fetch($search = '', int $status = 0, int $page = 1, int $perpage = index::PER_PAGE, array $sort = [])
    {
        $params = self::validate_parameters(
            self::fetch_parameters(),
            [
                'search' => $search,
                'status' => $status,
                'page' => $page,
                'perpage' => $perpage,
                'sort' => $sort
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        $params['search'] = json_decode($params['search'], true) ?? $params['search'];

        $structures = array_map(
            function ($structure) {

                $fields = $structure->to_record();

                return [
                    'id' => $fields->id,
                    'structure' => $fields->name,
                    'type' => $fields->type,
                    'subordinate' => $fields->subordinate,
                    'status' => $fields->status,
                ];
            },
            model::get_by_name_or_id(
                $params['search'],
                $params['status'],
                $params['page'] - 1,
                $params['perpage'],
                $params['sort']
            )

        );

        $total_structures = model::count_fetched($params['search'], $params['status']);
        $total_pages = (int) ceil($total_structures / $params['perpage']);
        $total_pages = $total_pages ? $total_pages : 1;

        $headers = self::get_headers_table();

        return [
            'total_pages' => $total_pages,
            'page' => $params['page'],
            'perpage' => $params['perpage'],
            'total_items' => $total_structures,
            'sort' => $params['sort'],
            'items' => $structures,
            'headers' => $headers
        ];
    }

    /**
     * Describe the return structure for fetch_structures
     *
     * @return external_single_structure
     */
    public static function fetch_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'total_pages' => new external_value(PARAM_INT, 'number of pages'),
                'page' => new external_value(PARAM_INT, 'current page'),
                'perpage' => new external_value(PARAM_INT, 'current perpage'),
                'total_items' => new external_value(PARAM_INT, 'Total number of structures fetched, not considering the pagination'),
                'sort' => new external_multiple_structure(
                    new external_single_structure([
                        'field' => new external_value(PARAM_TEXT, 'Field name'),
                        'direction' => new external_value(PARAM_TEXT, 'Sort direction')
                    ]),
                    'sortorder',
                    VALUE_DEFAULT,
                    []
                ),
                'items' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'id' => new external_value(PARAM_INT, 'Structure id'),
                            'structure' => new external_value(PARAM_TEXT, 'Structure name'),
                            'type' => new external_value(PARAM_TEXT, 'Structure type'),
                            'subordinate' => new external_value(PARAM_TEXT, 'Structure parent name'),
                            'status' => new external_value(PARAM_INT, 'Structure status'),
                        ]
                    )
                ),
                'headers' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'key' => new external_value(PARAM_TEXT, 'Header key'),
                            'label' => new external_value(PARAM_TEXT, 'Header label')
                        ]
                    )
                )
            ]
        );
    }

    /**
     * Describes the parameters for local_hierarchy_get_structure
     *
     * @return external_function_parameters
     */
    public static function get_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'id' => new external_value(PARAM_INT, 'Structure id', VALUE_REQUIRED)
            ]
        );
    }

    /**
     * Implementation of web service local_hierarchy_get_structure
     *
     * @param string $id
     * @return array
     */
    public static function get($id)
    {
        $params = self::validate_parameters(
            self::get_parameters(),
            [
                'id' => $id
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        $structure = model::get_record(['id' => $params['id']]);

        if (!$structure) {
            throw new moodle_exception('structuredoesntexist', 'local_hierarchy');
        }

        return [
            'item' => [
                'id' => $structure->get('id'),
                'structure' => $structure->get('name'),
                'type' => $structure->fetch_type(),
                'subordinate' => $structure->fetch_subordinate(),
                'positions' => array_values($structure->fetch_positions()),
            ]
        ];
    }

    /**
     * Describe the return structure for local_hierarchy_get_structure
     *
     * @return external_single_structure
     */
    public static function get_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'item' => new external_single_structure(
                    [
                        'id' => new external_value(PARAM_INT, 'Structure id'),
                        'structure' => new external_value(PARAM_TEXT, 'Structure name'),
                        'type' => new external_multiple_structure(
                            new external_single_structure([
                                'id' => new external_value(PARAM_INT, 'Type id'),
                                'name' => new external_value(PARAM_TEXT, 'Type name'),
                            ])
                        ),
                        'subordinate' => new external_multiple_structure(
                            new external_single_structure([
                                'id' => new external_value(PARAM_INT, 'Parent id'),
                                'structure' => new external_value(PARAM_TEXT, 'Parent name'),
                            ])
                        ),
                        'positions' => new external_multiple_structure(
                            new external_single_structure([
                                'id' => new external_value(PARAM_INT, 'Position id'),
                                'name' => new external_value(PARAM_TEXT, 'Position name'),
                                'is_manager' => new external_value(PARAM_BOOL, 'If the position is a management position'),
                            ])
                        )
                    ]
                )
            ]
        );
    }

    /**
     * Describes the parameters for local_hierarchy_get_status_options
     *
     * @return external_function_parameters
     */
    public static function get_headers_table_parameters(): external_function_parameters
    {
        return new external_function_parameters([]);
    }

    /**
     * Implementation of web service local_hierarchy_get_headers_table
     */
    public static function get_headers_table()
    {
        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        $returns = self::fetch_returns();
        $structures = $returns->keys["items"];
        $keys = array_keys($structures->content->keys);
        // Header para serem ocultados
        $keys = array_diff($keys, model::get_hidden_headers());

        $result = array_map(
            function ($key) {
                return [
                    'key' => $key,
                    'label' => get_string($key, 'local_hierarchy')
                ];
            },
            $keys
        );

        return $result;
    }

    /**
     * Define os parâmetros para salvar uma estrutura.
     * 
     * @return external_function_parameters
     */
    public static function save_structure_parameters()
    {
        return new external_function_parameters([
            'data' => new external_single_structure(
                [
                    'id' => new external_value(PARAM_INT, 'ID da estrutura', VALUE_DEFAULT, null),
                    'name' => new external_value(PARAM_TEXT, 'Nome da estrutura', VALUE_DEFAULT, ''),
                    'typeid' => new external_value(PARAM_INT, 'ID do tipo de estrutura', VALUE_DEFAULT, null),
                    'parentid' => new external_value(PARAM_INT, 'ID da estrutura subordinada', VALUE_DEFAULT, null),
                    'positions' => new external_multiple_structure(
                        new external_single_structure([
                            'id' => new external_value(PARAM_INT, 'ID da posição', VALUE_DEFAULT, null),
                            'name' => new external_value(PARAM_TEXT, 'Nome da posição', VALUE_DEFAULT, ''),
                        ]),
                        'positions',
                        VALUE_DEFAULT,
                        []
                    ),
                ]
            ),
        ]);
    }

    /**
     * Salva uma estrutura.
     * 
     * @param array $data
     * @return array
     */
    public static function save_structure(array $data)
    {
        global $DB;

        $params = self::validate_parameters(
            self::save_structure_parameters(),
            [
                'data' => $data
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);
        require_capability('local/hierarchy:manage', $context);

        $data = $params['data'];
        $transaction = $DB->start_delegated_transaction();

        try {
            $structure = new model($data['id'] ?: null);
            $errors = [];

            // Validação 1: Nome
            $validation = $structure->validate_name_field($data['id'], $data['name']);
            if ($validation !== true) {
                // Retorna a mensagem de erro no formato desejado
                $errors[] = [
                    'field' => 'structure',
                    'message' => $validation->out()
                ];
            }

            // Se houver erros, retorne-os
            if (!empty($errors)) {
                return [
                    'id' => $structure->get('id'),
                    'success' => false,
                    'errors' => $errors
                ];
            }

            $structure->set('name', $data['name']);
            $structure->set('typeid', $data['typeid']);
            $structure->set('parentid', $data['parentid'] ?: 0);

            $structure->save();

            $structure->add_positions($data['positions']);

            $transaction->allow_commit();

            return [
                'id' => $structure->get('id'),
                'success' => true,
                'message' => get_string($data['id'] ? 'structures_update_successfully' : 'structures_create_successfully', 'local_hierarchy'),
                'errors' => []
            ];
        } catch (\Exception $e) {
            $transaction->rollback($e);
            throw new \moodle_exception(
                'error_creating_structure',
                'local_hierarchy',
                '',
                null,
                $e->getMessage()
            );
        }
    }

    /**
     * Define os parâmetros para salvar uma estrutura.
     * 
     * @return external_value
     */
    public static function save_structure_returns()
    {
        return new external_single_structure(
            [
                'id' => new external_value(PARAM_INT, 'Position id', VALUE_DEFAULT, null),
                'message' => new external_value(PARAM_TEXT, 'Message'),
                'success' => new external_value(PARAM_BOOL, 'Success'),
                'message' => new external_value(PARAM_TEXT, 'Message', VALUE_DEFAULT, ''),
                'errors' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'field' => new external_value(PARAM_TEXT, 'Field name'),
                            'message' => new external_value(PARAM_TEXT, 'Message')
                        ],
                        'error',
                        VALUE_DEFAULT,
                        []
                    )
                )
            ]
        );
    }

    /**
     * Define os parâmetros para deletar uma estrutura.
     * 
     * @param int $id
     * @return external_function_parameters
     */
    public static function delete_structure_parameters()
    {
        return new external_function_parameters([
            'id' => new external_value(PARAM_INT, 'Id da estrutura'),
        ]);
    }

    /**
     * Deleta uma estrutura.
     * 
     * @param int $id
     * @return void
     */
    public static function delete_structure($id): bool
    {
        $params = self::validate_parameters(self::delete_structure_parameters(), ['id' => $id]);

        $structure = model::get_record(
            [
                'id' => $params['id'],
                'deleted' => 0
            ]
        );

        if (!$structure) {
            throw new moodle_exception('structuredoesntexist', 'local_hierarchy');
        }

        $structure->delete_structure();

        return (bool) $structure->get('deleted');
    }

    /**
     * Define os parâmetros para deletar um tipo de estrutura.
     * 
     * @return external_value
     */
    public static function delete_structure_returns()
    {
        return new external_value(PARAM_BOOL, 'Success');
    }

    /**
     * Descreve os parâmetros para a função de exportação
     *
     * @return external_function_parameters
     */
    public static function export_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'search' => new external_value(PARAM_RAW, 'Search term or JSON encoded array of search terms', VALUE_DEFAULT, ''),
                'status' => new external_value(PARAM_INT, 'status', VALUE_DEFAULT, 0),
                'format' => new external_value(PARAM_TEXT, 'Export format', VALUE_DEFAULT, '')
            ]
        );
    }

    /**
     * Implementação da função de exportação
     *
     * @param string $search
     * @param int $status
     * @param bool $onlymanagers
     * @param string $format
     * @return string
     */
    public static function export($search = '', int $status = 0, string $format = ''): array
    {
        $params = self::validate_parameters(
            self::export_parameters(),
            [
                'search' => $search,
                'status' => $status,
                'format' => $format
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        // Decodifica o termo de busca se necessário
        $params['search'] = json_decode($params['search'], true) ?? $params['search'];

        $structures = array_map(
            function ($structure) {

                $fields = $structure->to_record();

                return [
                    'id' => $fields->id,
                    'structure' => $fields->name,
                    'type' => $fields->type,
                    'subordinate' => $fields->subordinate,
                    'status' => $fields->status == 1 ? 'Ativo' : 'Inativo',
                ];
            },
            model::get_all_structures(
                $params['search'],
                $params['status'],
            )
        );

        return ['export' => model::export_structures($structures, $params['format'])];
    }

    /**
     * Descreve a estrutura de retorno para a função de exportação
     *
     * @return external_single_structure
     */
    public static function export_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'export' => new external_value(PARAM_RAW, 'CSV')
            ]
        );
    }
}
