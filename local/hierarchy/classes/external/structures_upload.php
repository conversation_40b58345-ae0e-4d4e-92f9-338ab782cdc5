<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_hierarchy\external;

use local_hierarchy\output\index;
use local_hierarchy\model\structures;
use core_external\external_function_parameters;
use core_external\external_multiple_structure;
use core_external\external_single_structure;
use core_external\external_api;
use core_external\external_value;
use context_system;
use moodle_exception;

/**
 * Class structures_upload
 *
 * @package    local_hierarchy
 * @copyright  2024 2024 REVVO
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class structures_upload extends external_api
{

    public static function validate_name_exists($row)
    {
        return structures::record_exists_select('LOWER(name) = LOWER(:name) AND id != :id AND deleted = 0', ['name' => $row['estrutura'], 'id' => $row['id_estrutura']]);
    }

    public static function validate_id_exists($row)
    {
        return structures::record_exists_select('id = :id AND deleted = 0', ['id' => $row['id_estrutura']]);
    }

    /**
     * Valida e retorna o typeid correspondente ao tipo de estrutura.
     *
     * @param string $type Nome do tipo de estrutura.
     * @return int|null Retorna o typeid ou null se o tipo for inválido.
     */
    protected static function validate_structure_type($type)
    {
        global $DB;

        $typeid = $DB->get_field('local_hierarchy_struc_type', 'id', ['name' => $type]);

        return $typeid ? (int)$typeid : null;
    }

    /**
     * Obtém o status da posição.
     */
    // Adicionar após os métodos de validação existentes
    protected static function validate_duplicate_row($current_row, $processed_rows): bool
    {
        foreach ($processed_rows as $row) {
            if (strcasecmp($row['estrutura'], $current_row['estrutura']) === 0) {
                return true;
            }
        }
        return false;
    }

    public static function get_status($row, $processed_rows = []): array
    {
        $status = [];
        $status_color = '';

        // Validação de linha duplicada no arquivo
        if (self::validate_duplicate_row($row, $processed_rows)) {
            $status[] = 'Estrutura duplicada no arquivo';
            $status_color = 'danger';
        }

        // Validação do tamanho do nome da estrutura
        if (strlen($row['estrutura']) > 40) {
            $status[] = 'O nome da estrutura é muito longo! Máximo de 40 caracteres.';
            $status_color = 'danger';
        }

        if (self::validate_name_exists($row)) {
            $status[] = get_string('validate_name_exists_structure', 'local_hierarchy');
            $status_color = 'danger';
        }
        if (!self::validate_id_exists($row) && !empty($row['id_estrutura'])) {
            $status[] = get_string('validate_id_exists_structure', 'local_hierarchy');
            $status_color = 'danger';
        }

        // Validação do tipo de estrutura.
        if (!empty($row['tipo'])) {
            $typeid = self::validate_structure_type($row['tipo']);
            if (!$typeid) {
                $status[] = get_string('validate_invalid_type', 'local_hierarchy', $row['tipo']);
                $status_color = 'danger';
            }
        }
        if (self::validate_id_exists($row) && $status_color !== 'danger') {
            $status[] = get_string('validate_update', 'local_hierarchy');
            $status_color = 'success';
        }

        if (empty($status)) {
            $status[] = get_string('validate_create', 'local_hierarchy');
            $status_color = 'success';
        }

        $status_message = implode('; ', $status);
        return [$status_message, $status_color];
    }

    /**
     * Método para salvar ou atualizar uma estrutura usando o persistent do Moodle.
     */
    protected static function save_structure(array $data)
    {
        global $DB;

        // Tentar carregar a estrutura com o ID fornecido.
        if (!empty($data['id_estrutura'])) {
            $structure = new structures((int)$data['id_estrutura']);

            // Verificar se a estrutura existe.
            if (!$structure->get('id')) {
                throw new moodle_exception('Estrutura com o ID ' . $data['id_estrutura'] . ' não encontrado.');
            }
        } else {
            // Se não existe um ID, criar uma nova estrutura.
            $structure = new structures();
        }

        // Atribuir os valores ao persistent.
        $structure->set('name', $data['estrutura']);
        // Validar e mapear o tipo de estrutura.
        if (!empty($data['tipo'])) {
            $typeid = self::validate_structure_type($data['tipo']);
            if (!$typeid) {
                throw new moodle_exception('Tipo de estrutura inválido: ' . $data['tipo']);
            }
            $structure->set('typeid', $typeid);
        }

        // Verificar se o cargo foi excluído e restaura.
        if ((bool)$structure->get('deleted')) {
            $structure->set('deleted', 0);
            $structure->set('userdeleted', 0);
        }

        // Validação e salvamento do persistent.
        try {
            $structure->validate(); // Validação interna do persistent.
            $structure->save(); // Salva a posição (inserção ou atualização).

        } catch (Exception $e) {
            throw new moodle_exception('Erro ao salvar a estrutura: ' . $e->getMessage());
        }

        return $structure;
    }

    /**
     * Describes the parameters for local_hierarchy_preview_upload
     *
     * @return external_function_parameters
     */
    public static function preview_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'file' => new external_value(PARAM_FILE, 'CSV File'),
                'delimiter' => new external_value(PARAM_RAW, 'CSV delimiter'),
                'encoding' => new external_value(PARAM_TEXT, 'File encoding'),
                'lines' => new external_value(PARAM_INT, 'Number of preview lines')
            ]
        );
    }

    /**
     * Implementation of web service local_hierarchy_preview_upload
     *
     * @param string $file
     * @param string $delimiter
     * @param string $encoding
     * @param int $lines
     * @return array
     */
    public static function preview($file, $delimiter, $encoding, $lines)
    {
        $params = self::validate_parameters(
            self::preview_parameters(),
            [
                'file' => $file,
                'delimiter' => $delimiter,
                'encoding' => $encoding,
                'lines' => $lines
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);
        require_capability('local/hierarchy:manage', $context);

        // Decodificar o arquivo Base64
        $decoded_file = base64_decode($params['file']);

        // Remove o BOM (se existir)
        if (substr($decoded_file, 0, 3) === "\xEF\xBB\xBF") {
            $decoded_file = substr($decoded_file, 3);
        }

        // Forçar o conteúdo como UTF-8
        $utf8_content = mb_convert_encoding($decoded_file, 'UTF-8', $params['encoding']);

        // Usar php://temp para criar um ponteiro temporário
        $file_handle = fopen('php://temp', 'r+');
        fwrite($file_handle, $utf8_content);
        rewind($file_handle);

        $structures = [];
        $processed_rows = [];
        $line_count = 0;

        $headers = array_map('strtolower', fgetcsv($file_handle, 0, $delimiter));

        // Verifique se a $headers tem pelo menos 2 colunas
        if (count($headers) < 2) {
            fclose($file_handle);
            return [
                'items' => [],
                'headers' => [],
                'params' => $params,
                'error' => 'Delimitador definido é incompatível com o formato do arquivo ou faltam colunas. Verifique o delimitador e as colunas definidas e tente novamente.'
            ];
        }

        // Processar o CSV linha por linha até alcançar o limite de pré-visualização
        while (($data = fgetcsv($file_handle, 0, $delimiter)) !== false && $line_count < $lines) {
            if (count($data) === count($headers)) {
                $row = array_combine($headers, $data);
                $row['linha_csv'] = ($line_count + 2);

                list($status, $status_color) = self::get_status($row, $processed_rows);

                $row['status'] = $status;
                $row['status_color'] = $status_color;

                $structures[] = $row;
                $processed_rows[] = $row;
            }
            $line_count++;
        }

        fclose($file_handle);

        array_unshift($headers, 'Linha do CSV');
        array_push($headers, 'Status');

        // Retornar os dados processados para o frontend
        return [
            'items' => $structures,
            'headers' => $headers,
            'params' => $params
        ];
    }

    /**
     * Describes the return structure for local_hierarchy_preview_upload
     *
     * @return external_single_structure
     */
    public static function preview_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'items' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'linha_csv' => new external_value(PARAM_INT, 'Número da linha no CSV'),
                            'id_estrutura' => new external_value(PARAM_RAW, 'ID da estrutura'),
                            'estrutura' => new external_value(PARAM_RAW, 'Nome da estrutura'),
                            'tipo' => new external_value(PARAM_RAW, 'Tipo da estrutura'),
                            'status' => new external_value(PARAM_RAW, 'Status do estrutura'),
                            'status_color' => new external_value(PARAM_RAW, 'Cor do status da estrutura'),
                        ]
                    )
                ),
                'headers' => new external_multiple_structure(
                    new external_value(PARAM_RAW, 'CSV headers')
                ),
                'params' => new external_single_structure(
                    [
                        'file' => new external_value(PARAM_FILE, 'CSV File'),
                        'delimiter' => new external_value(PARAM_RAW, 'CSV delimiter'),
                        'encoding' => new external_value(PARAM_TEXT, 'File encoding'),
                        'lines' => new external_value(PARAM_INT, 'Number of preview lines')
                    ]
                ),
                'error' => new external_value(PARAM_RAW, 'Error message', null, false),
            ]
        );
    }

    /**
     * Describes the parameters for local_hierarchy_process_upload
     *
     * @return external_function_parameters
     */
    public static function process_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'file' => new external_value(PARAM_FILE, 'CSV File'),
                'delimiter' => new external_value(PARAM_RAW, 'CSV delimiter'),
                'encoding' => new external_value(PARAM_TEXT, 'File encoding'),
                'lines' => new external_value(PARAM_INT, 'Number of preview lines'),
                'action' => new external_value(PARAM_INT, 'Transmission action: 1 = Add new only, 2 = Add and update, 3 = Update only'),
            ]
        );
    }

    /**
     * Implementation of web service local_hierarchy_process_upload
     *
     * @param string $file
     * @param string $delimiter
     * @param string $encoding
     * @param int $lines
     * @return array
     */
    public static function process($file, $delimiter, $encoding, $lines, $action)
    {
        $params = self::validate_parameters(
            self::process_parameters(),
            [
                'file' => $file,
                'delimiter' => $delimiter,
                'encoding' => $encoding,
                'lines' => $lines,
                'action' => $action
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);
        require_capability('local/hierarchy:manage', $context);

        // Decodificar o arquivo Base64
        $decoded_file = base64_decode($params['file']);

        // Remove o BOM (se existir)
        if (substr($decoded_file, 0, 3) === "\xEF\xBB\xBF") {
            $decoded_file = substr($decoded_file, 3);
        }

        // Forçar o conteúdo como UTF-8
        $utf8_content = mb_convert_encoding($decoded_file, 'UTF-8', $params['encoding']);

        // Usar php://temp para criar um ponteiro temporário
        $file_handle = fopen('php://temp', 'r+');
        fwrite($file_handle, $utf8_content);
        rewind($file_handle);

        $structures = [];
        $line_count = 0;

        $headers = array_map('strtolower', fgetcsv($file_handle, 0, $delimiter));

        // Processar o CSV linha por linha até alcançar o limite de pré-visualização
        while (($data = fgetcsv($file_handle, 0, $delimiter)) !== false) {
            if (count($data) === count($headers)) {
                $row = array_combine($headers, $data);
                $row['linha_csv'] = ($line_count + 2);

                list($status, $status_color) = self::get_status($row);
                $row['status'] = $status;
                $row['status_color'] = $status_color;

                // Lógica para salvar com base na ação selecionada
                switch ($action) {
                    case 1: // Adicionar somente novas estruturas
                        if ($status_color === 'success' && !$row['id_estrutura']) {
                            try {
                                $saved_structure = self::save_structure($row);
                                $row['id_estrutura'] = $saved_structure->get('id');
                                $row['status'] = 'Estrutura cadastrada com sucesso';
                                $row['status_color'] = 'success';
                            } catch (Exception $e) {
                                $row['status'] = 'Erro ao salvar estrutura: ' . $e->getMessage();
                                $row['status_color'] = 'danger';
                            }
                        } else {
                            $row['status'] = 'Nada foi alterado na estrutura.';
                            $row['status_color'] = 'warning';
                        }
                        break;

                    case 2: // Atualizar cargos
                        if ($row['id_estrutura']) {
                            try {
                                $saved_structure = self::save_structure($row);
                                $row['id_estrutura'] = $saved_structure->get('id');
                                $row['status'] = 'Estrutura atualizada com sucesso.';
                                $row['status_color'] = 'success';
                            } catch (Exception $e) {
                                $row['status'] = 'Erro ao salvar estrutura: ' . $e->getMessage();
                                $row['status_color'] = 'danger';
                            }
                        } else {
                            $row['status'] = 'Não foi criada a estrutura.';
                            $row['status_color'] = 'warning';
                        }
                        break;
                }

                $structures[] = $row;
            }

            $line_count++;
        }

        fclose($file_handle);

        array_unshift($headers, 'Linha do CSV');
        array_push($headers, 'Status');


        // Retornar os dados processados para o frontend
        return [
            'items' => $structures,
            'headers' => $headers,
            'params' => $params
        ];
    }

    /**
     * Describes the return structure for local_hierarchy_process_upload
     *
     * @return external_single_structure
     */
    public static function process_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'items' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'linha_csv' => new external_value(PARAM_INT, 'Número da linha no CSV'),
                            'id_estrutura' => new external_value(PARAM_RAW, 'ID da estrutura'),
                            'estrutura' => new external_value(PARAM_RAW, 'Nome da estrutura'),
                            'tipo' => new external_value(PARAM_RAW, 'Tipo da estrutura'),
                            'status' => new external_value(PARAM_RAW, 'Status do estrutura'),
                            'status_color' => new external_value(PARAM_RAW, 'Cor do status da estrutura'),
                        ]
                    )
                ),
                'headers' => new external_multiple_structure(
                    new external_value(PARAM_RAW, 'CSV headers')
                ),
                'params' => new external_single_structure(
                    [
                        'file' => new external_value(PARAM_FILE, 'CSV File'),
                        'delimiter' => new external_value(PARAM_RAW, 'CSV delimiter'),
                        'encoding' => new external_value(PARAM_TEXT, 'File encoding'),
                        'lines' => new external_value(PARAM_INT, 'Number of preview lines')
                    ]
                )
            ]
        );
    }
}
