<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_hierarchy\external;

use core_external\external_function_parameters;
use core_external\external_multiple_structure;
use core_external\external_single_structure;
use core_external\external_api;
use core_external\external_value;
use context_system;


/**
 * Class structures_orgchart
 *
 * @package    local_hierarchy
 * @copyright  2024 2024 REVVO
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class structures_orgchart extends external_api
{

    /**
     * Describes the parameters for local_hierarchy_fetch_orgchart_data
     *
     * @return external_function_parameters
     */
    public static function fetch_orgchart_data_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            []
        );
    }

    /**
     * Implementation of web service local_hierarchy_fetch_orgchart_data
     * @param string $file
     * @param string $delimiter
     * @param string $encoding
     * @param int $lines
     * @return array
     */
    public static function fetch_orgchart_data()
    {
        global $DB;

        // Validar parâmetros (neste caso, não há parâmetros)
        self::validate_parameters(self::fetch_orgchart_data_parameters(), []);


        // Validar contexto e permissões
        $context = context_system::instance();
        self::validate_context($context);
        require_capability('local/hierarchy:manage', $context);

        // Query para buscar a hierarquia de estruturas
        $sql = "
            SELECT 
                mlhs.id AS id, 
                mlhs.name AS title, 
                mlhst.name AS type,
                mlhs.parentid AS pid,
                CASE 
                    WHEN EXISTS (
                        SELECT 1 
                        FROM {user_info_data} uid 
                        JOIN {user_info_field} uif ON uid.fieldid = uif.id
                        JOIN {local_hierarchy_pos_struct} lhps ON lhps.structureid = mlhs.id
                        WHERE uid.data = lhps.positionid
                        and uif.datatype = 'position'
                    ) THEN 1 
                    ELSE 0
                END AS status
            FROM 
                {local_hierarchy_structure} mlhs
            JOIN 
                {local_hierarchy_struc_type} mlhst 
            ON 
                mlhs.typeid = mlhst.id
            WHERE 
                mlhs.deleted = 0
            ORDER BY 
                pid ASC;
        ";

        // Executar a query e iterar sobre os resultados
        $recordset = $DB->get_recordset_sql($sql);
        $data = [];

        foreach ($recordset as $record) {
            $data[] = [
                'id' => $record->id,
                'pid' => $record->pid,
                'title' => $record->title,
                'subtitle' => $record->type,
                'tags' => $record->status ? [] : ["inativo"],
            ];
        }

        $recordset->close();

        return [
            'data' => $data,
        ];
    }

    /**
     * Describes the return structure for local_hierarchy_fetch_orgchart_data
     *
     * @return external_single_structure
     */
    public static function fetch_orgchart_data_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'data' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'id' => new external_value(PARAM_INT, 'ID da estrutura'),
                            'pid' => new external_value(PARAM_INT, 'ID do gestor'),
                            'title' => new external_value(PARAM_RAW, 'Nome da estrutura'),
                            'subtitle' => new external_value(PARAM_RAW, 'Tipo da estrutura'),
                            'tags' => new external_multiple_structure(
                                new external_value(PARAM_RAW, 'Tags da estrutura')
                            ),
                        ]
                    )
                ),
            ]
        );
    }
}
