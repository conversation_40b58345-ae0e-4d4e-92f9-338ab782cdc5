<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_hierarchy\external;

use local_hierarchy\output\index;
use local_hierarchy\model\positions as model;
use core_external\external_function_parameters;
use core_external\external_multiple_structure;
use core_external\external_single_structure;
use core_external\external_api;
use core_external\external_value;
use context_system;
use moodle_exception;

/**
 * Class positions
 *
 * @package    local_hierarchy
 * @copyright  2024 2024 REVVO
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class positions extends external_api
{
    private static $max_depth = 3;

    /**
     * Describes the parameters for local_hierarchy_fetch_positions
     *
     * @return external_function_parameters
     */
    public static function fetch_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'search' => new external_value(PARAM_RAW, 'Search term or JSON encoded array of search terms', VALUE_DEFAULT, ''),
                'status' => new external_value(PARAM_INT, 'status', VALUE_DEFAULT, 0),
                'onlymanagers' => new external_value(PARAM_BOOL, 'if want to return only management positions', VALUE_DEFAULT, false),
                'page' => new external_value(PARAM_INT, 'page', VALUE_DEFAULT, 1),
                'perpage' => new external_value(PARAM_INT, 'perpage', VALUE_DEFAULT, index::PER_PAGE),
                'sort' => new external_multiple_structure(
                    new external_single_structure([
                        'field' => new external_value(PARAM_TEXT, 'Field name'),
                        'direction' => new external_value(PARAM_TEXT, 'Sort direction')
                    ]),
                    'sortorder',
                    VALUE_DEFAULT,
                    []
                )
            ]
        );
    }

    /**
     * Implementation of web service local_hierarchy_fetch_positions
     *
     * @param string $search
     */
    public static function fetch($search = '', int $status = 0, bool $onlymanagers = false, int $page = 1, int $perpage = index::PER_PAGE, array $sort = [])
    {
        $params = self::validate_parameters(
            self::fetch_parameters(),
            [
                'search' => $search,
                'status' => $status,
                'onlymanagers' => $onlymanagers,
                'page' => $page,
                'perpage' => $perpage,
                'sort' => $sort
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        $params['search'] = json_decode($params['search'], true) ?? $params['search'];

        $positions = array_map(
            function ($position) {

                $fields = $position->to_record();

                return [
                    'id' => $fields->id,
                    'name' => $fields->name,
                    'structures' => $fields->structures,
                    'is_manager' => $fields->manage,
                    'managers' => $fields->managers,
                    'status' => $fields->status
                ];
            },
            model::get_by_name_or_id(
                $params['search'],
                $params['status'],
                $params['onlymanagers'],
                $params['page'] - 1,
                $params['perpage'],
                $params['sort']
            )
        );

        $total_positions = model::count_fetched($params['search'], $params['status'], $params['onlymanagers']);
        $total_pages =  (int) ceil($total_positions / $params['perpage']);
        $total_pages = $total_pages ? $total_pages : 1;

        $headers = self::get_headers_table();

        return [
            'total_pages' => $total_pages,
            'page' => $params['page'],
            'perpage' => $params['perpage'],
            'total_items' => $total_positions,
            'sort' => $params['sort'],
            'items' => $positions,
            'headers' => $headers
        ];
    }

    /**
     * Describe the return structure for local_hierarchy_fetch_positions
     *
     * @return external_single_structure
     */
    public static function fetch_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'total_pages' => new external_value(PARAM_INT, 'number of pages'),
                'page' => new external_value(PARAM_INT, 'current page'),
                'perpage' => new external_value(PARAM_INT, 'current perpage'),
                'total_items' => new external_value(PARAM_INT, 'Total number of positions fetched, not considering the pagination'),
                'sort' => new external_multiple_structure(
                    new external_single_structure([
                        'field' => new external_value(PARAM_TEXT, 'Field name'),
                        'direction' => new external_value(PARAM_TEXT, 'Sort direction')
                    ]),
                    'sortorder',
                    VALUE_DEFAULT,
                    []
                ),
                'items' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'id' => new external_value(PARAM_INT, 'Position id'),
                            'name' => new external_value(PARAM_TEXT, 'Position name'),
                            'structures' => new external_value(PARAM_TEXT, 'Structures'),
                            'is_manager' => new external_value(PARAM_BOOL, 'If the position is a management position'),
                            'managers' => new external_value(PARAM_TEXT, 'Managers'),
                            'status' => new external_value(PARAM_INT, 'Position status')
                        ]
                    )
                ),
                'headers' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'key' => new external_value(PARAM_TEXT, 'Header key'),
                            'label' => new external_value(PARAM_TEXT, 'Header label')
                        ]
                    )
                )
            ]
        );
    }

    /**
     * Describes the parameters for local_hierarchy_get_position
     *
     * @return external_function_parameters
     */
    public static function get_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'id' => new external_value(PARAM_INT, 'Position id', VALUE_REQUIRED),
                'include_subordinates' => new external_value(PARAM_BOOL, 'Include second level subordinates', VALUE_DEFAULT, false)
            ]
        );
    }

    /**
     * Implementation of web service local_hierarchy_get_position
     *
     * @param string $id
     * @return array
     */
    public static function get($id, $include_subordinates = false)
    {
        $params = self::validate_parameters(
            self::get_parameters(),
            [
                'id' => $id,
                'include_subordinates' => $include_subordinates,
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        $position = model::get_record(['id' => $params['id']]);

        if (!$position) {
            throw new moodle_exception('positiondoesntexist', 'local_hierarchy');
        }

        $subordinates = $params['include_subordinates']
            ? $position->fetch_subordinates_recursive([], 0, self::$max_depth)
            : array_values($position->fetch_subordinates());

        return [
            'item' => [
                'id' => $position->get('id'),
                'name' => $position->get('name'),
                'is_manager' => $position->get('manage'),
                'managers' => array_values($position->fetch_managers()),
                'subordinates' => $subordinates,
                'status' =>  $position->fetch_status()
            ]
        ];
    }

    /**
     * Describe the return structure for local_hierarchy_get_position
     *
     * @return external_single_structure
     */
    public static function get_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'item' => new external_single_structure(
                    [
                        'id' => new external_value(PARAM_INT, 'Position id'),
                        'name' => new external_value(PARAM_TEXT, 'Position name'),
                        'is_manager' => new external_value(PARAM_BOOL, 'If the position is a management position'),
                        'managers' => new external_multiple_structure(
                            new external_single_structure([
                                'id' => new external_value(PARAM_INT, 'Manager id'),
                                'name' => new external_value(PARAM_TEXT, 'Manager name'),
                                'manager_order' => new external_value(PARAM_INT, 'Manager order'),
                                'is_master' => new external_value(PARAM_BOOL, 'If the manager is a master manager')
                            ])
                        ),
                        'subordinates' => new external_multiple_structure(
                            self::subordinate_structure(),
                            'List of subordinates'
                        ),
                        'status' => new external_value(PARAM_INT, 'Position status')
                    ]
                )
            ]
        );
    }

    /**
     * Define a recursive structure for subordinates, with depth control
     *
     * @param int $current_depth - Current depth level
     * @param int $max_depth - Maximum allowed depth
     * @return external_single_structure
     */
    private static function subordinate_structure(int $current_depth = 0): external_single_structure
    {
        //TODO: Tornar isso dinâmico no futuro
        $max_depth = self::$max_depth;
        // Se a profundidade máxima for atingida, não faz mais sentido recursivamente incluir subordinados.
        if ($current_depth >= $max_depth) {
            return new external_single_structure(
                [
                    'id' => new external_value(PARAM_INT, 'Subordinate id'),
                    'name' => new external_value(PARAM_TEXT, 'Subordinate name'),
                    'is_manager' => new external_value(PARAM_BOOL, 'If the position is a management position'),
                    'subordinates' => new external_multiple_structure(
                        // Retorna um array vazio quando a profundidade máxima for atingida
                        new external_value(PARAM_TEXT, 'Empty array, no subordinates'),
                        'List of subordinates',
                        VALUE_OPTIONAL,
                        []
                    ),
                ]
            );
        }

        // Estrutura dos subordinados, incluindo a recursão
        return new external_single_structure(
            [
                'id' => new external_value(PARAM_INT, 'Subordinate id'),
                'name' => new external_value(PARAM_TEXT, 'Subordinate name'),
                'is_manager' => new external_value(PARAM_BOOL, 'If the position is a management position'),
                'subordinates' => new external_multiple_structure(
                    // Chama recursivamente a função, com a profundidade incrementada
                    self::subordinate_structure($current_depth + 1, $max_depth),
                    'List of subordinates',
                    VALUE_OPTIONAL,
                    []
                ),
            ]
        );
    }

    /**
     * Describes the parameters for local_hierarchy_create_position
     *
     * @return external_function_parameters
     */
    public static function create_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'data' => new external_single_structure(
                    [
                        'name' => new external_value(PARAM_TEXT, 'Position name', VALUE_REQUIRED),
                        'manage' => new external_value(PARAM_BOOL, 'If the position is a management position', VALUE_DEFAULT, false),
                        'managers' => new external_multiple_structure(
                            new external_single_structure([
                                'id' => new external_value(PARAM_INT, 'Manager id', VALUE_REQUIRED),
                                'name' => new external_value(PARAM_TEXT, 'Manager name', VALUE_REQUIRED),
                                'manager_order' => new external_value(PARAM_INT, 'Manager order', VALUE_REQUIRED),
                                'is_master' => new external_value(PARAM_BOOL, 'If the manager is a master manager', VALUE_DEFAULT, false)
                            ])
                        )
                    ]
                )
            ]
        );
    }

    /**
     * Implementation of web service local_hierarchy_create_position
     *
     * @param array $data
     * @return array
     */
    public static function create(array $data)
    {
        global $DB;

        $params = self::validate_parameters(
            self::create_parameters(),
            [
                'data' => $data
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);
        require_capability('local/hierarchy:manage', $context);

        $data = $params['data'];
        $transaction = $DB->start_delegated_transaction();


        try {


            $position = new model();
            $errors = [];

            // Validação 1: Nome
            $validation = $position->validate_name_field($data['id'], $data['name']);
            if ($validation !== true) {
                // Retorna a mensagem de erro no formato desejado
                $errors[] = [
                    'field' => 'name',
                    'message' => $validation->out()
                ];
            }

            // Se houver erros, retorne-os
            if (!empty($errors)) {
                return [
                    'id' => $position->get('id'),
                    'success' => false,
                    'errors' => $errors
                ];
            }

            $position->set('name', $data['name']);
            $position->set('manage', $data['manage'] ? 1 : 0);
            $position->save();

            $position->add_managers($data['managers']);

            $transaction->allow_commit();

            return [
                'id' => $position->get('id'),
                'success' => true,
                'message' => get_string('positioncreated', 'local_hierarchy'),
                'errors' => []
            ];
        } catch (\Exception $e) {
            $transaction->rollback($e);
            throw new \moodle_exception(
                'error_creating_position',
                'local_hierarchy',
                '',
                null,
                $e->getMessage()
            );
        }
    }

    /**
     * Describe the return structure for local_hierarchy_create_position
     *
     * @return external_single_structure
     */
    public static function create_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'id' => new external_value(PARAM_INT, 'Position id', VALUE_DEFAULT, null),
                'message' => new external_value(PARAM_TEXT, 'Message'),
                'success' => new external_value(PARAM_BOOL, 'Success'),
                'message' => new external_value(PARAM_TEXT, 'Message', VALUE_DEFAULT, ''),
                'errors' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'field' => new external_value(PARAM_TEXT, 'Field name'),
                            'message' => new external_value(PARAM_TEXT, 'Message')
                        ],
                        'error',
                        VALUE_DEFAULT,
                        []
                    )
                )
            ]
        );
    }


    /**
     * Describes the parameters for local_hierarchy_edit_position
     *
     * @return external_function_parameters
     */
    public static function edit_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'data' => new external_single_structure(
                    [
                        'id' => new external_value(PARAM_INT, 'Position id', VALUE_REQUIRED),
                        'name' => new external_value(PARAM_TEXT, 'Position name', VALUE_REQUIRED),
                        'is_manager' => new external_value(PARAM_BOOL, 'If the position is a management position', VALUE_DEFAULT, false),
                        'managers' => new external_multiple_structure(
                            new external_single_structure([
                                'id' => new external_value(PARAM_INT, 'Manager id', VALUE_REQUIRED),
                                'name' => new external_value(PARAM_TEXT, 'Manager name', VALUE_REQUIRED),
                                'manager_order' => new external_value(PARAM_INT, 'Manager order', VALUE_REQUIRED),
                                'is_master' => new external_value(PARAM_BOOL, 'If the manager is a master manager', VALUE_DEFAULT, false)
                            ]),
                            'managers',
                            VALUE_OPTIONAL,
                            []
                        )
                    ]
                )
            ]
        );
    }

    /**
     * Implementation of web service local_hierarchy_edit_position
     *
     * @param array $data
     * @return array
     */
    public static function edit(array $data)
    {
        global $DB;

        $params = self::validate_parameters(
            self::edit_parameters(),
            [
                'data' => $data
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);
        require_capability('local/hierarchy:manage', $context);

        $data = $params['data'];
        $transaction = $DB->start_delegated_transaction();


        try {

            $position = new model($data['id']);
            $errors = [];

            // Validação 1: Nome
            $validation = $position->validate_name_field($data['id'], $data['name']);
            if ($validation !== true) {
                // Retorna a mensagem de erro no formato desejado
                $errors[] = [
                    'field' => 'name',
                    'message' => $validation->out()
                ];
            }

            // Se houver erros, retorne-os
            if (!empty($errors)) {
                return [
                    'id' => $position->get('id'),
                    'success' => false,
                    'errors' => $errors
                ];
            }

            $position->set('name', $data['name']);
            $position->set('manage', $data['is_manager'] ? 1 : 0);
            $position->save();

            $position->add_managers($data['managers']);

            $transaction->allow_commit();

            return [
                'id' => $position->get('id'),
                'success' => true,
                'message' => get_string('positionupdated', 'local_hierarchy'),
                'errors' => []
            ];
        } catch (\Exception $e) {
            $transaction->rollback($e);
            throw new \moodle_exception(
                'error_creating_position',
                'local_hierarchy',
                '',
                null,
                $e->getMessage()
            );
        }
    }

    /**
     * Describe the return structure for local_hierarchy_edit_position
     *
     * @return external_single_structure
     */
    public static function edit_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'id' => new external_value(PARAM_INT, 'Position id'),
                'success' => new external_value(PARAM_BOOL, 'Success'),
                'message' => new external_value(PARAM_TEXT, 'Message', VALUE_DEFAULT, ''),
                'errors' => new external_multiple_structure(
                    new external_single_structure(
                        [
                            'field' => new external_value(PARAM_TEXT, 'Field name'),
                            'message' => new external_value(PARAM_TEXT, 'Message')
                        ],
                        'error',
                        VALUE_DEFAULT,
                        []
                    )
                )
            ]
        );
    }

    /**
     * Descreve os parâmetros para a função de exportação
     *
     * @return external_function_parameters
     */
    public static function export_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'search' => new external_value(PARAM_RAW, 'Search term or JSON encoded array of search terms', VALUE_DEFAULT, ''),
                'status' => new external_value(PARAM_INT, 'status', VALUE_DEFAULT, 0),
                'onlymanagers' => new external_value(PARAM_BOOL, 'if want to return only management positions', VALUE_DEFAULT, false),
                'format' => new external_value(PARAM_TEXT, 'Export format', VALUE_DEFAULT, '')
            ]
        );
    }

    /**
     * Implementação da função de exportação
     *
     * @param string $search
     * @param int $status
     * @param bool $onlymanagers
     * @param string $format
     * @return string
     */
    public static function export($search = '', int $status = 0, bool $onlymanagers = false, string $format = ''): array
    {
        $params = self::validate_parameters(
            self::export_parameters(),
            [
                'search' => $search,
                'status' => $status,
                'onlymanagers' => $onlymanagers,
                'format' => $format
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        // Decodifica o termo de busca se necessário
        $params['search'] = json_decode($params['search'], true) ?? $params['search'];

        $positions = array_map(
            function ($position) {

                $fields = $position->to_record();

                return [
                    'id' => $fields->id,
                    'name' => $fields->name,
                    'structures' => $fields->structures,
                    'is_manager' => $fields->manage ? 'Sim' : 'Não',
                    'managers' => $fields->managers,
                    'status' => $fields->status == 1 ? 'Ativo' : 'Inativo',
                ];
            },
            model::get_all_positions(
                $params['search'],
                $params['status'],
                $params['onlymanagers'],
            )
        );

        return ['export' => model::export_positions($positions, $params['format'])];
    }

    /**
     * Descreve a estrutura de retorno para a função de exportação
     *
     * @return external_single_structure
     */
    public static function export_returns(): external_single_structure
    {
        return new external_single_structure(
            [
                'export' => new external_value(PARAM_RAW, 'CSV')
            ]
        );
    }

    /**
     * Describes the parameters for local_hierarchy_get_status_options
     *
     * @return external_function_parameters
     */
    public static function get_status_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            []
        );
    }

    /**
     * Implementation of web service local_hierarchy_get_status_options
     *
     * @param string $search
     */
    public static function get_status()
    {
        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        $status = index::get_status();

        return array_map(
            function ($key, $value) {
                return [
                    'id' => $key,
                    'name' => $value
                ];
            },
            array_keys($status),
            array_values($status)
        );
    }

    /**
     * Describe the return structure for local_hierarchy_get_status_options
     *
     * @return external_multiple_structure
     */
    public static function get_status_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure(
                [
                    'id' => new external_value(PARAM_INT, 'Position id'),
                    'name' => new external_value(PARAM_TEXT, 'Position name')
                ]
            )
        );
    }

    /**
     * Describes the parameters for local_hierarchy_get_status_options
     *
     * @return external_function_parameters
     */
    public static function get_headers_table_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            []
        );
    }

    /**
     * Implementation of web service local_hierarchy_get_status_options
     *
     * @param string $search
     */
    public static function get_headers_table()
    {
        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        $returns = self::fetch_returns();

        $positions = $returns->keys["items"];

        $keys = array_keys($positions->content->keys);

        return array_map(
            function ($key) {
                return [
                    'key' => $key,
                    'label' => get_string($key, 'local_hierarchy')
                ];
            },
            $keys
        );
    }

    /**
     * Describe the return structure for local_hierarchy_get_status_options
     *
     * @return external_multiple_structure
     */
    public static function get_headers_table_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure(
                [
                    'key' => new external_value(PARAM_TEXT, 'Header key'),
                    'label' => new external_value(PARAM_TEXT, 'Header label')
                ]
            )
        );
    }

    /**
     * Describes the parameters for local_hierarchy_delete_position
     *
     * @return external_function_parameters
     */
    public static function delete_position_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'id' => new external_value(PARAM_INT, 'Position id')
            ]
        );
    }

    /**
     * Implementation of web service local_hierarchy_delete_position
     *
     * @param string $search
     */
    public static function delete_position(int $id)
    {
        $params = self::validate_parameters(
            self::delete_position_parameters(),
            [
                'id' => $id
            ]
        );

        $context = context_system::instance();
        self::validate_context($context);

        require_capability('local/hierarchy:manage', $context);

        $position = model::get_record(
            [
                'id' => $params['id'],
                'deleted' => 0
            ]
        );

        if (!$position) {
            throw new moodle_exception('positiondoesntexist', 'local_hierarchy');
        }

        $position->delete_position();

        return (bool) $position->get('deleted');
    }

    /**
     * Describe the return structure for local_hierarchy_delete_position
     *
     * @return external_value
     */
    public static function delete_position_returns(): external_value
    {
        return new external_value(PARAM_BOOL, 'if deleted or not');
    }

    /**
     * Implementation of web service local_hierarchy_get_positions_by_structure
     *
     * @param int $structureid
     * @return external_function_parameters
     */
    public static function get_positions_by_structure_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'structureid' => new external_value(PARAM_INT, 'Structure ID')
        ]);
    }

    /**
     * Describe the return structure for local_hierarchy_get_positions_by_structure
     *
     * @return external_single_structure
     */
    public static function get_positions_by_structure($structureid)
    {
        global $DB;

        $params = self::validate_parameters(self::get_positions_by_structure_parameters(), [
            'structureid' => $structureid
        ]);

        // Inicializa o resultado com a opção "Nenhum cargo selecionado"
        $result = [[
            'id' => 0,
            'name' => get_string('nopositionselected', 'profilefield_position')
        ]];

        // Se a estrutura for válida (não zero), busca os cargos associados
        if (!empty($params['structureid'])) {
            $sql = "SELECT p.id, p.name 
                    FROM {local_hierarchy_position} p
                    JOIN {local_hierarchy_pos_struct} ps ON ps.positionid = p.id
                    WHERE ps.structureid = :structureid 
                    AND p.deleted = 0
                    ORDER BY p.name ASC";

            $positions = $DB->get_records_sql($sql, ['structureid' => $params['structureid']]);

            foreach ($positions as $position) {
                $result[] = [
                    'id' => $position->id,
                    'name' => $position->name
                ];
            }
        }

        return ['positions' => $result];
    }

    /**
     * Describe the return structure for local_hierarchy_get_positions_by_structure
     *
     * @return external_single_structure
     */
    public static function get_positions_by_structure_returns(): external_single_structure
    {
        return new external_single_structure([
            'positions' => new external_multiple_structure(
                new external_single_structure([
                    'id' => new external_value(PARAM_INT, 'ID of the position'),
                    'name' => new external_value(PARAM_TEXT, 'Name of the position')
                ]),
                'List of positions'
            )
        ]);
    }
}
