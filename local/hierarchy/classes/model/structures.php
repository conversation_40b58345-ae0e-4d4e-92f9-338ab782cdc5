<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_hierarchy\model;

require_once($CFG->libdir . '/phpspreadsheet/vendor/autoload.php');

use local_hierarchy\event\structure_created;
use local_hierarchy\event\structure_updated;
use local_hierarchy\event\structure_deleted;
use local_hierarchy\output\index;

use core\persistent;
use context_system;
use lang_string;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use stdClass;

/**
 * Class structures
 *
 * @package    local_hierarchy
 * @copyright  2024 REVVO <www.somosrevvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

class structures extends persistent
{
    /**
     * The table name.
     */
    const TABLE = 'local_hierarchy_structure';

    const HIDDEN_HEADERS = ['used', 'positions'];

    public static function get_hidden_headers()
    {
        return self::HIDDEN_HEADERS;
    }

    /**
     * Return the definition of the properties of this model.
     * 
     * @return array
     */
    protected static function define_properties()
    {
        return [
            'name' => [
                'type' => PARAM_TEXT,
                'description' => 'Structure name.',
                'default' => '',
                'null' => NULL_NOT_ALLOWED,
            ],
            'typeid' => [
                'type' => PARAM_INT,
                'description' => 'Structure type id.',
                'default' => '',
                'null' => NULL_NOT_ALLOWED,
            ],
            'type' => [
                'type' => PARAM_TEXT,
                'description' => 'Structure type name.',
                'default' => '',
                'null' => NULL_NOT_ALLOWED,
            ],
            'parentid' => [
                'type' => PARAM_INT,
                'description' => 'Structure parent id',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'status' => [
                'type' => PARAM_BOOL,
                'description' => '1 if active, 0 otherwise.',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'subordinate' => [
                'type' => PARAM_TEXT,
                'description' => 'Structure parent name',
                'default' => '',
                'null' => NULL_ALLOWED,
            ],
            'deleted' => [
                'type' => PARAM_INT,
                'description' => '1 if deleted, 0 otherwise.',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'usercreated' => [
                'type' => PARAM_INT,
                'description' => 'User id of creator',
                'null' => NULL_NOT_ALLOWED,
                'default' => function () {
                    global $USER;

                    return $USER->id;
                }
            ],
            'timedeleted' => [
                'type' => PARAM_INT,
                'description' => 'UNIX Timestamp of deletion',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'userdeleted' => [
                'type' => PARAM_INT,
                'description' => 'User id of deleter',
                'null' => NULL_ALLOWED,
                'default' => null
            ],
        ];
    }

    /**
     * Hook to execute after a create.
     *
     * @return void
     */
    protected function after_create()
    {
        $id = $this->get('id');

        $data = [
            'context' => context_system::instance(),
            'objectid' => $id
        ];

        $event = structure_created::create($data);

        $event->trigger();
    }

    protected function after_update($result)
    {
        if ($result) {
            $data = [
                'context' => context_system::instance(),
                'objectid' => $this->get('id')
            ];

            $event = structure_updated::create($data);

            $event->trigger();

            // TODO: verificar se ao atualizar preciso atualizar as estruturas filhas

        }
    }

    public static function get_positions(int $structureid)
    {
        global $DB;

        $positions = $DB->get_records('local_hierarchy_pos_struct', ['structureid' => $structureid]);

        return $positions;
    }

    public function get_position_by_id($positionid)
    {
        global $DB;

        return $DB->get_record('local_hierarchy_pos_struct', ['structureid' => $this->get('id'), 'positionid' => $positionid]);
    }

    public function add_positions(array $positions)
    {
        global $DB, $USER;

        $structureid = $this->get('id');
        $userid = $USER->id;

        if (!$structureid) {
            throw new \moodle_exception('missing_structure', 'local_hierarchy');
        }

        // Buscar positions já cadastrados no banco para essa estrutura
        $existingPositions = $DB->get_records('local_hierarchy_pos_struct', ['structureid' => $structureid], '', 'positionid');

        // Extrai apenas os positionid dos registros existentes no banco
        $existingPositionIds = array_map(function ($record) {
            return $record->positionid;
        }, $existingPositions);

        // Extrai os novos positionid da lista de positions recebida
        $newPositionIds = array_map(function ($position) {
            return $position['id'];
        }, $positions);

        // Se os arrays de positionid forem iguais, não precisa atualizar nada
        if (empty(array_diff($existingPositionIds, $newPositionIds)) && empty(array_diff($newPositionIds, $existingPositionIds))) {
            return;
        }

        // Identifica posições removidas para atualizar perfis de usuários
        $removedPositionIds = array_diff($existingPositionIds, $newPositionIds);
        if (!empty($removedPositionIds)) {
            $this->update_user_positions($removedPositionIds);
        }

        // Deleta os records antigos
        $DB->delete_records('local_hierarchy_pos_struct', ['structureid' => $structureid]);

        // Insere os novos positions
        foreach ($positions as $position) {
            $record = new stdClass();
            $record->structureid = $structureid;
            $record->positionid = $position['id']; // Aqui estamos usando $position['id'] corretamente
            $record->usercreated = $userid;
            $record->timecreated = time();
            $DB->insert_record('local_hierarchy_pos_struct', $record);
        }
    }

    /**
     * Atualiza os perfis de usuários que têm posições removidas da estrutura
     * 
     * @param array $removedPositionIds IDs das posições removidas da estrutura
     * @return void
     */
    private function update_user_positions(array $removedPositionIds)
    {
        global $DB;

        // Obtém o ID do campo de perfil 'structure'
        $structureFieldId = $DB->get_field('user_info_field', 'id', ['shortname' => 'structure']);
        if (!$structureFieldId) {
            return; // Campo 'structure' não encontrado
        }

        // Obtém o ID do campo de perfil 'position'
        $positionFieldId = $DB->get_field('user_info_field', 'id', ['shortname' => 'position']);
        if (!$positionFieldId) {
            return; // Campo 'position' não encontrado
        }

        // ID da estrutura atual
        $structureId = $this->get('id');

        // Busca usuários que têm as posições removidas e pertencem à estrutura atual
        $sql = "SELECT uid_pos.userid, uid_pos.id as data_id
                FROM {user_info_data} uid_pos
                JOIN {user_info_data} uid_struct ON uid_pos.userid = uid_struct.userid
                WHERE uid_pos.fieldid = :positionfieldid
                AND uid_struct.fieldid = :structurefieldid
                AND uid_struct.data = :structureid
                AND uid_pos.data IN (" . implode(',', $removedPositionIds) . ")";

        $params = [
            'positionfieldid' => $positionFieldId,
            'structurefieldid' => $structureFieldId,
            'structureid' => $structureId
        ];

        $usersToUpdate = $DB->get_records_sql($sql, $params);

        // Atualiza os registros para definir a posição como '0'
        foreach ($usersToUpdate as $user) {
            $DB->set_field('user_info_data', 'data', '0', ['id' => $user->data_id]);
        }
    }

    /**
     * Atualiza os perfis de usuários que usam a estrutura que está sendo deletada
     * 
     * @return void
     */
    private function update_users_structure()
    {
        global $DB;

        // Obtém o ID do campo de perfil 'structure'
        $structureFieldId = $DB->get_field('user_info_field', 'id', ['shortname' => 'structure']);
        if (!$structureFieldId) {
            return; // Campo 'structure' não encontrado
        }

        // ID da estrutura que está sendo deletada
        $structureId = $this->get('id');

        // Busca usuários que têm esta estrutura definida em seu perfil
        $sql = "SELECT uid.id as data_id
                FROM {user_info_data} uid
                WHERE uid.fieldid = :structurefieldid
                AND uid.data = :structureid";

        $params = [
            'structurefieldid' => $structureFieldId,
            'structureid' => $structureId
        ];

        $usersToUpdate = $DB->get_records_sql($sql, $params);

        // Atualiza os registros para definir a estrutura como '0'
        foreach ($usersToUpdate as $user) {
            $DB->set_field('user_info_data', 'data', '0', ['id' => $user->data_id]);
        }
    }

    public function delete_structure(): bool
    {
        global $USER, $DB;

        // Atualiza os usuários que usam esta estrutura
        $this->update_users_structure();

        // Atualizar estruturas filhas
        $DB->set_field('local_hierarchy_structure', 'parentid', 0, ['parentid' => $this->get('id')]);

        $this->set('deleted', 1);
        $this->set('userdeleted', $USER->id);
        $this->save();

        $data = [
            'context' => context_system::instance(),
            'objectid' => $this->get('id')
        ];

        $event = structure_deleted::create($data);

        $event->trigger();

        return $this->get('deleted');
    }

    public static function validate_name_field($structureid, $value)
    {
        $structure = new self($structureid);

        return $structure->validate_name($value);
    }
    /**
     * Validate the name.
     *
     * @param int $value The value.
     * @return true|lang_string
     */
    protected function validate_name($value)
    {
        $id = $this->get('id');
        if (self::record_exists_select('LOWER(name) = LOWER(:name) AND id != :id AND deleted = 0', ['id' => $id, 'name' => $value])) {
            return new lang_string('structurealreadyexistes', 'local_hierarchy');
        }

        return true;
    }

    public static function validate_parentid_field($structureid, $value)
    {
        $structure = new self($structureid);

        return $structure->validate_parentid($value);
    }
    /**
     * Validate the parentid.
     *
     * @param int $value The value.
     * @return true|lang_string
     */
    protected function validate_parentid($value)
    {
        $id = $this->get('id');

        if ($value) {

            if ($value == $id) {
                return new lang_string('samepositionid', 'local_hierarchy');
            }

            if (!self::record_exists_select('id = :id AND deleted = 0', ['id' => $value])) {
                return new lang_string('positiondoesntexist', 'local_hierarchy');
            }

            // if (!self::record_exists_select('id = :id AND manage = 1', ['id' => $value])) {
            //     return new lang_string('notamanagerposition', 'local_hierarchy');
            // }
        }

        return true;
    }

    public static function get_by_name_or_id(
        $search = '',
        $status = null,
        int $page = 0,
        int $perpage = index::PER_PAGE,
        array $sort = []
    ): array {
        list($where, $params, $fields) = self::get_fetch_sql($search, $status);

        $page = ($page * $perpage);

        // Verifica e converte o campo 'structure' para 'name' no array de ordenação.
        $sort = array_map(function ($sortItem) {
            if ($sortItem['field'] === 'structure') {
                $sortItem['field'] = 'name'; // Converte 'structure' para 'name'.
            }
            return $sortItem;
        }, $sort);

        if (empty($sort)) {
            $sort = [['field' => 'name', 'direction' => 'ASC']];
        }

        $sort = implode(', ', array_map(
            function ($sortItem) {
                return $sortItem['field'] . ' ' . strtoupper($sortItem['direction']);
            },
            $sort
        ));

        return self::get_records_select(
            $where,
            $params,
            $sort,
            $fields,
            $page,
            $perpage
        );
    }

    public static function get_all_structures(
        $search = '',
        $status = null,
    ): array {
        list($where, $params, $fields) = self::get_fetch_sql($search, $status);

        return self::get_records_select(
            $where,
            $params,
            '',
            $fields
        );
    }


    public static function export_structures(array $structures, string $format)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'ID');
        $sheet->setCellValue('B1', 'ESTRUTURA');
        $sheet->setCellValue('C1', 'TIPO');
        $sheet->setCellValue('D1', 'SUBORDINADO A');
        $sheet->setCellValue('E1', 'STATUS');

        foreach (range('A', 'E') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        $row = 2;
        foreach ($structures as $structure) {
            $sheet->setCellValue('A' . $row, $structure['id']);
            $sheet->setCellValue('B' . $row, $structure['structure']);
            $sheet->setCellValue('C' . $row, $structure['type']);
            $sheet->setCellValue('D' . $row, $structure['subordinate']);
            $sheet->setCellValue('E' . $row, $structure['status']);
            $row++;
        }

        if ($format == 'csv') {
            $writer = new Csv($spreadsheet);
            $writer->setDelimiter(',');
            $writer->setEnclosure('"');
            $writer->setLineEnding("\r\n");
            $writer->setSheetIndex(0);

            $tempFile = tempnam(sys_get_temp_dir(), 'Relatório de Estruturas Cadastrados') . '.csv';
            $writer->save($tempFile);

            $csvData = file_get_contents($tempFile);
            unlink($tempFile);

            return $csvData;
        }

        if ($format == 'xls') {
            $writer = new Xlsx($spreadsheet);
            $tempFile = tempnam(sys_get_temp_dir(), 'Relatório de Estruturas Cadastrados') . '.xlsx';

            $writer->save($tempFile);

            $xlsData = file_get_contents($tempFile);
            unlink($tempFile);

            return base64_encode($xlsData);
        }
    }

    public static function count_fetched(
        $search,
        $status = null
    ): int {
        list($where, $params) = self::get_fetch_sql($search, $status);

        return self::count_records_select(
            $where,
            $params
        );
    }

    private static function get_fetch_sql(
        $search = '',
        int $status = 0
    ): array {
        global $DB;

        $where = 'deleted = 0';
        $params = [];
        $fields = "{local_hierarchy_structure}.*,
        CASE 
            WHEN EXISTS (
                SELECT 1 
                FROM {user_info_data} uid 
                JOIN {user_info_field} uif ON uid.fieldid = uif.id
                JOIN {local_hierarchy_pos_struct} lhps ON lhps.structureid = {local_hierarchy_structure}.id
                WHERE uid.data = lhps.positionid
                and uif.datatype = 'position'
            ) THEN 1 
            ELSE 0
        END AS status,
        (SELECT name FROM {local_hierarchy_structure} sub 
            WHERE sub.id = {local_hierarchy_structure}.parentid) AS subordinate,
        (SELECT name FROM {local_hierarchy_struc_type} t
            WHERE t.id = {local_hierarchy_structure}.typeid) AS type
        ";

        if (!empty($search)) {
            if (is_array($search)) {
                $search_placeholders = [];
                foreach ($search as $index => $label) {
                    $search_placeholders[] = 'name = :search_name_' . $index;
                    $search_placeholders[] = 'id = :search_id_' . $index;
                    $params['search_name_' . $index] = $label;
                    $params['search_id_' . $index] = $label;
                }
                $where .= ' AND (' . implode(' OR ', $search_placeholders) . ')';
            } else {
                // Usar COLLATE para ignorar acentos em MySQL
                $where .= " AND (
                {$DB->sql_like('id', ':search_id', false, false)} OR 
                {$DB->sql_like('name COLLATE utf8mb4_general_ci', ':search_name', false, false)}
            )";
                $params['search_id'] = "%{$search}%";
                $params['search_name'] = "%{$search}%";
            }
        }

        if ($status > 0) {
            $not = ($status === 2) ? "NOT" : "";

            $where .= " 
                AND {$not} EXISTS (
                    SELECT 1 
                FROM {user_info_data} uid 
                JOIN {user_info_field} uif ON uid.fieldid = uif.id
                JOIN {local_hierarchy_pos_struct} lhps ON lhps.structureid = {local_hierarchy_structure}.id
                WHERE uid.data = lhps.positionid
                and uif.datatype = 'position'
                )
            ";
        }

        return [$where, $params, $fields];
    }

    public static function get_parentid_list()
    {
        global $DB;

        $none_selected = [0 => get_string('nonestructureselected', 'local_hierarchy')];

        $structures = $DB->get_records_menu(
            'local_hierarchy_structure',
            [
                'deleted' => 0,
            ],
            'id ASC',
            'id, name'
        );


        return $none_selected + $structures;
    }

    public static function get_main_structure_list()
    {
        global $DB;

        $none_selected = [0 => get_string('nonestructureselected', 'local_hierarchy')];

        $structures = $DB->get_records_menu(
            'local_hierarchy_structure',
            [
                'deleted' => 0,
                'parentid' => 0
            ],
            'id ASC',
            'id, name'
        );


        return $none_selected + $structures;
    }

    public function fetch_type()
    {
        global $DB;

        if (!$this->get('typeid')) {
            return [];
        }

        $type = $DB->get_record('local_hierarchy_struc_type', ['id' => $this->get('typeid')], '*', IGNORE_MISSING);

        return $type ? [['id' => $type->id, 'name' => $type->name]] : [];
    }

    public function fetch_subordinate()
    {
        global $DB;

        if (!$this->get('parentid')) {
            return [];
        }

        $parent = $DB->get_record('local_hierarchy_structure', ['id' => $this->get('parentid')], 'id, name', IGNORE_MISSING);

        return $parent ? [['id' => $parent->id, 'structure' => $parent->name]] : [];
    }

    public function fetch_positions()
    {
        global $DB;

        $sql = "SELECT p.id, p.name, p.manage as is_manager
            FROM {local_hierarchy_position} p
            JOIN {local_hierarchy_pos_struct} ps ON ps.positionid = p.id
            WHERE p.deleted = 0 
            AND ps.structureid = :structureid";

        return $DB->get_records_sql($sql, ['structureid' => $this->get('id')]);
    }
}
