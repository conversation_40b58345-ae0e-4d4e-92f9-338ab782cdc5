<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_hierarchy\model;

use core\persistent;

defined('MOODLE_INTERNAL') || die;

/**
 * Class structure_type
 *
 * @package    local_hierarchy
 * @copyright  2025 Revvo | Dev Paulo Oliveira
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class structure_type extends persistent
{
    /** @var string O nome da tabela do banco de dados */
    const TABLE = 'local_hierarchy_struc_type';

    /** @var string O nome da coluna que identifica unicamente o registro */
    const PRIMARY_KEY = 'id';

    /** @var string O nome do tipo de estrutura */
    const FIELD_NAME = 'name';

    /** @var int A data de criação do registro */
    const FIELD_TIMECREATED = 'timecreated';

    /** @var int Id do usuário que criou o registro */
    const FIELD_USERCREATED = 'usercreated';

    /** @var int A data de atualização do registro */
    const FIELD_TIMEUPDATED = 'timemodified';

    /** @var int Id do usuário que fez a alteração */
    const FIELD_USERMODIFIED = 'usermodified';

    /** @var int A data de exclusão do registro */
    const FIELD_TIMEDELETED = 'timedeleted';

    /** @var int Id do usuário que excluiu o registro */
    const FIELD_USERDELETED = 'userdeleted';

    /** @var int O status do registro */
    const FIELD_DELETED = 'timedeleted';

    /**
     * Retorna um tipo de estrutura pelo id.
     *
     * @param int $id
     * @return structure_type|null
     */
    public static function get_by_id($id)
    {
        return self::get_record(['id' => $id, 'timedeleted' => 0]);
    }

    /**
     * Retorna todos os tipos de estrutura.
     * 
     * @return array
     */
    public static function get_all()
    {
        global $DB;

        $types = $DB->get_records(self::TABLE, ['timedeleted' => 0]);

        foreach ($types as $type) {
            $type->uses = self::uses_count($type->id);
        }

        return $types;
    }

    public static function uses_count($id)
    {
        global $DB;

        return $DB->count_records('local_hierarchy_structure', ['typeid' => $id, 'deleted' => 0]);
    }

    /**
     * Define os campos que serão salvos como dados.
     * 
     * @return array
     */
    protected static function define_properties()
    {
        return [
            'name' => [
                'type' => PARAM_TEXT,
                'default' => '',
            ],
            'timecreated' => [
                'type' => PARAM_INT,
                'default' => time(),
            ],
            'usercreated' => [
                'type' => PARAM_INT,
                'default' => null,
            ],
            'timemodified' => [
                'type' => PARAM_INT,
                'default' => time(),
            ],
            'usermodified' => [
                'type' => PARAM_INT,
                'default' => null,
            ],
            'timedeleted' => [
                'type' => PARAM_INT,
                'default' => null,
            ],
            'userdeleted' => [
                'type' => PARAM_INT,
                'default' => null,
            ],
            'deleted' => [
                'type' => PARAM_INT,
                'default' => 0,
            ],
        ];
    }
}
