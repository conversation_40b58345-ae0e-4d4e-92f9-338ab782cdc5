<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_hierarchy\model;

require_once($CFG->libdir . '/phpspreadsheet/vendor/autoload.php');

use local_hierarchy\event\position_created;
use local_hierarchy\event\position_updated;
use local_hierarchy\event\position_deleted;
use local_hierarchy\output\index;

use core\persistent;
use context_system;
use lang_string;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use stdClass;

/**
 * Class positions
 *
 * @package    local_hierarchy
 * @copyright  2024 REVVO <www.somosrevvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

class positions extends persistent
{
    /**
     * The table name.
     */
    const TABLE = 'local_hierarchy_position';

    /**
     * Return the definition of the properties of this model.
     * 
     * @return array
     */
    protected static function define_properties()
    {
        return [
            'name' => [
                'type' => PARAM_TEXT,
                'description' => 'Position name.',
                'default' => '',
                'null' => NULL_NOT_ALLOWED,
            ],
            'structures' => [
                'type' => PARAM_TEXT,
                'description' => 'Structures associated with the position.',
                'default' => '',
                'null' => NULL_NOT_ALLOWED,
            ],
            'managers' => [
                'type' => PARAM_TEXT,
                'description' => 'Managers associated with the position.',
                'default' => '',
                'null' => NULL_ALLOWED,
            ],
            'manage' => [
                'type' => PARAM_BOOL,
                'description' => '1 if is a management position',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'status' => [
                'type' => PARAM_BOOL,
                'description' => '1 if active, 0 otherwise.',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'deleted' => [
                'type' => PARAM_BOOL,
                'description' => '1 if deleted, 0 otherwise.',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'usercreated' => [
                'type' => PARAM_INT,
                'description' => 'User id of creator',
                'null' => NULL_NOT_ALLOWED,
                'default' => function () {
                    global $USER;

                    return $USER->id;
                }
            ],
            'timedeleted' => [
                'type' => PARAM_INT,
                'description' => 'UNIX Timestamp of deletion',
                'default' => 0,
                'null' => NULL_NOT_ALLOWED,
            ],
            'userdeleted' => [
                'type' => PARAM_INT,
                'description' => 'User id of deleter',
                'null' => NULL_ALLOWED,
                'default' => null
            ]
        ];
    }

    /**
     * Hook to execute after a create.
     *
     * @return void
     */
    protected function after_create()
    {
        $id = $this->get('id');

        $data = [
            'context' => context_system::instance(),
            'objectid' => $id
        ];

        $event = position_created::create($data);

        $event->trigger();
    }

    protected function after_update($result)
    {
        global $DB;

        // Verifica se o campo manage foi alterado para false.
        if ($this->get('manage') == false) {
            // Remove os registros relacionados na tabela local_hierarchy_pos_manager.
            $DB->delete_records('local_hierarchy_pos_manager', ['managerid' => $this->get('id')]);
        }

        if ($result) {
            $data = [
                'context' => context_system::instance(),
                'objectid' => $this->get('id')
            ];

            $event = position_updated::create($data);

            $event->trigger();
        }
    }

    public function delete_position()
    {
        global $USER, $DB;

        $this->set('deleted', 1);
        $this->set('userdeleted', $USER->id);

        $this->save();

        // Remove os registros relacionados na tabela local_hierarchy_pos_struct.
        $DB->delete_records('local_hierarchy_pos_struct', ['positionid' => $this->get('id')]);

        // Remove os registros relacionados na tabela local_hierarchy_pos_manager.
        $DB->delete_records('local_hierarchy_pos_manager', ['positionid' => $this->get('id')]);
        $DB->delete_records('local_hierarchy_pos_manager', ['managerid' => $this->get('id')]);

        $associated_users = $this->get_associated_users();

        if ($associated_users) {

            foreach ($associated_users as $user) {
                $sql = "UPDATE {user_info_data} SET data = ? WHERE data = ? AND fieldid = ?";
                $DB->execute($sql, [0, $user->data, $user->fieldid]);
            }
        }

        $data = [
            'context' => context_system::instance(),
            'objectid' => $this->get('id')
        ];

        $event = position_deleted::create($data);

        $event->trigger();

        return $this->get('deleted');
    }

    public static function validate_name_field($positionid, $value)
    {
        $position = new self($positionid);

        return $position->validate_name($value);
    }
    /**
     * Validate the name.
     *
     * @param int $value The value.
     * @return true|lang_string
     */
    protected function validate_name($value)
    {
        $id = $this->get('id');
        if (self::record_exists_select('LOWER(name) = LOWER(:name) AND id != :id AND deleted = 0', ['id' => $id, 'name' => $value])) {
            return new lang_string('namealreadyexistes', 'local_hierarchy');
        }

        return true;
    }

    public static function validate_managerid_field($positionid, $value)
    {
        $position = new self($positionid);

        return $position->validate_managerid($value);
    }
    /**
     * Validate the managerid.
     *
     * @param int $value The value.
     * @return true|lang_string
     */
    protected function validate_managerid($value)
    {
        $id = $this->get('id');

        if ($value) {

            if ($value == $id) {
                return new lang_string('samepositionid', 'local_hierarchy');
            }

            if (!self::record_exists_select('id = :id AND deleted = 0', ['id' => $value])) {
                return new lang_string('positiondoesntexist', 'local_hierarchy');
            }

            if (!self::record_exists_select('id = :id AND manage = 1', ['id' => $value])) {
                return new lang_string('notamanagerposition', 'local_hierarchy');
            }
        }

        return true;
    }

    public function get_subordinate_positions($deleted = true)
    {
        $id = $this->get('id');

        if ($id == 0) {
            return false;
        }

        $data = [
            'managerid' => $this->get('id')
        ];

        if (!$deleted) {
            $data['deleted'] = 0;
        }

        return self::get_records($data);
    }

    public function get_associated_users()
    {
        global $DB;

        $id = $this->get('id');
        $fieldid = $DB->get_field('user_info_field', 'id', ['datatype' => 'position']);

        if ($id == 0) {
            return false;
        }

        $sql = "SELECT *
        FROM {user_info_data}
        WHERE " . $DB->sql_compare_text('data') . " = :data AND fieldid = :fieldid";

        return $DB->get_records_sql($sql, ['data' => $id, 'fieldid' => $fieldid]);
    }

    public static function get_by_name_or_id(
        $search = '',
        int $status = 0,
        bool $onlymanagers = false,
        int $page = 0,
        int $perpage = index::PER_PAGE,
        array $sort = []
    ): array {
        list($where, $params, $fields) = self::get_fetch_sql($search, $status, $onlymanagers);

        $page = ($page * $perpage);

        if (empty($sort)) {
            $sort = [['field' => 'name', 'direction' => 'ASC']];
        }

        $sort = implode(', ', array_map(
            function ($sortItem) {
                $sortItem['field'] === 'is_manager' ? $sortItem['field'] = 'manage' : $sortItem['field'];
                return $sortItem['field'] . ' ' . strtoupper($sortItem['direction']);
            },
            $sort
        ));

        $sql =  self::get_records_select(
            $where,
            $params,
            $sort,
            $fields,
            $page,
            $perpage
        );

        return $sql;
    }

    public static function get_all_positions(
        $search = '',
        int $status = 0,
        bool $onlymanagers = false,
    ): array {
        list($where, $params, $fields) = self::get_fetch_sql($search, $status, $onlymanagers);

        return self::get_records_select(
            $where,
            $params,
            '',
            $fields
        );
    }

    public static function export_positions(array $positions, string $format)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'ID');
        $sheet->setCellValue('B1', 'CARGO');
        $sheet->setCellValue('C1', 'ESTRUTURAS');
        $sheet->setCellValue('D1', 'CARGO GESTOR');
        $sheet->setCellValue('E1', 'REPORTA-SE A');
        $sheet->setCellValue('F1', 'STATUS');

        foreach (range('A', 'F') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        $row = 2;
        foreach ($positions as $position) {
            $sheet->setCellValue('A' . $row, $position['id']);
            $sheet->setCellValue('B' . $row, $position['name']);
            $sheet->setCellValue('C' . $row, $position['structures']);
            $sheet->setCellValue('D' . $row, $position['is_manager']);
            $sheet->setCellValue('E' . $row, $position['managers']);
            $sheet->setCellValue('F' . $row, $position['status']);
            $row++;
        }

        if ($format == 'csv') {
            $writer = new Csv($spreadsheet);
            $writer->setDelimiter(',');
            $writer->setEnclosure('"');
            $writer->setLineEnding("\r\n");
            $writer->setSheetIndex(0);

            $tempFile = tempnam(sys_get_temp_dir(), 'Relatório de Cargos Cadastrados') . '.csv';
            $writer->save($tempFile);

            $csvData = file_get_contents($tempFile);
            unlink($tempFile);

            return $csvData;
        }

        if ($format == 'xls') {
            $writer = new Xlsx($spreadsheet);
            $tempFile = tempnam(sys_get_temp_dir(), 'Relatório de Cargos Cadastrados') . '.xlsx';

            $writer->save($tempFile);

            $xlsData = file_get_contents($tempFile);
            unlink($tempFile);

            return base64_encode($xlsData);
        }
    }

    public static function count_fetched(
        $search,
        int $status = 0,
        bool $onlymanagers = false
    ): int {
        list($where, $params) = self::get_fetch_sql($search, $status, $onlymanagers);

        return self::count_records_select(
            $where,
            $params
        );
    }

    private static function get_fetch_sql(
        $search = '',
        int $status = 0,
        bool $onlymanagers = false
    ): array {
        global $DB;

        $where = 'deleted = 0';
        $params = [];
        $fields = "{local_hierarchy_position}.*,
        CASE 
            WHEN EXISTS (
                SELECT 1 
                FROM {user_info_data} uid 
                JOIN {user_info_field} uif ON uid.fieldid = uif.id
                WHERE uid.data = {local_hierarchy_position}.id
                and uif.shortname = 'position'
            ) THEN 1 
            ELSE 0
        END AS status,
        (
            SELECT GROUP_CONCAT(mlhp_int.name ORDER BY mlhpm.is_master DESC, mlhp_int.name SEPARATOR ', ') 
            FROM {local_hierarchy_pos_manager} mlhpm 
            JOIN {local_hierarchy_position} mlhp_int
                ON mlhp_int.id = mlhpm.managerid AND mlhp_int.deleted = 0
            WHERE mlhpm.positionid = {local_hierarchy_position}.id
            GROUP BY mlhpm.positionid
        ) AS managers,
        (
            SELECT GROUP_CONCAT(lhs.name SEPARATOR ', ')
                FROM {local_hierarchy_pos_struct} lps
                JOIN {local_hierarchy_structure} lhs ON lps.structureid = lhs.id AND lhs.deleted = 0
                WHERE lps.positionid = {local_hierarchy_position}.id
                GROUP BY lps.positionid
        ) AS structures
        ";

        if (!empty($search)) {
            if (is_array($search)) {
                $search_placeholders = [];
                foreach ($search as $index => $label) {
                    $search_placeholders[] = 'name = :search_name_' . $index;
                    $search_placeholders[] = 'id = :search_id_' . $index;
                    $params['search_name_' . $index] = $label;
                    $params['search_id_' . $index] = $label;
                }
                $where .= ' AND (' . implode(' OR ', $search_placeholders) . ')';
            } else {
                // Usar COLLATE para ignorar acentos em MySQL
                $where .= " AND (
                {$DB->sql_like('id', ':search_id', false, false)} OR 
                {$DB->sql_like('name COLLATE utf8mb4_general_ci', ':search_name', false, false)}
            )";
                $params['search_id'] = "%{$search}%";
                $params['search_name'] = "%{$search}%";
            }
        }

        if ($onlymanagers) {
            $where .= " AND manage = 1";
        }


        if ($status > 0) {
            $not = ($status === 2) ? "NOT" : "";

            $where .= " 
                AND {$not} EXISTS (
                    SELECT 1 
                    FROM {user_info_data} uid 
                    JOIN {user_info_field} uif ON uid.fieldid = uif.id
                    WHERE uid.data = {local_hierarchy_position}.id
                    AND uif.shortname = 'position'
                )
            ";
        }

        return [$where, $params, $fields];
    }

    public static function get_managerid_list()
    {
        global $DB;

        $none_selected = [0 => get_string('nonepositionselected', 'local_hierarchy')];

        $managers = $DB->get_records_menu(
            'local_hierarchy_position',
            [
                'manage' => 1,
                'deleted' => 0,
            ],
            'id ASC',
            'id, name'
        );


        return $none_selected + $managers;
    }

    /**
     * Verifica se uma posição está sendo usada
     *
     * @param int $positionid
     * @return bool
     */
    public static function is_position_used(int $positionid)
    {
        global $DB;

        $fieldid = $DB->get_field('user_info_field', 'id', ['datatype' => 'position']);

        if (!$fieldid || !$positionid) {
            return false;
        }

        $sql = $DB->sql_compare_text('data') . ' = :positionid AND fieldid = :fieldid';

        return $DB->record_exists_select('user_info_data', $sql, ['fieldid' => $fieldid, 'positionid' => $positionid]);
    }

    public static function get_position_manager($positionid)
    {
        global $DB;

        $sql = "SELECT p.* 
                FROM {local_hierarchy_position} p
                JOIN {local_hierarchy_pos_manager} pm 
                    ON pm.managerid = p.id 
                    AND pm.is_master = 1
                    AND pm.positionid = :positionid
                WHERE p.deleted = 0
                LIMIT 1";

        $manager = $DB->get_record_sql($sql, ['positionid' => $positionid]);

        if ($manager) {
            $result = new \stdClass();
            $result->position = $manager->name;
            return $result;
        }

        return null;
    }

    /**
     * Verifica se a posição está em uso (status ativo).
     *
     * @return int Retorna 1 se a posição está em uso, 0 caso contrário.
     */
    public function fetch_status(): int
    {
        global $DB;

        // Verifica se a posição está associada a algum usuário.
        $sql = "SELECT 1 
            FROM {user_info_data} uid 
            JOIN {user_info_field} uif ON uid.fieldid = uif.id
            WHERE uid.data = :positionid
            AND uif.datatype = 'position'";

        return $DB->record_exists_sql($sql, ['positionid' => $this->get('id')]) ? 1 : 0;
    }

    /**
     * Retorna os managers relacionados a esta posição.
     *
     * @return array Lista de managers
     */
    public function fetch_managers(): array
    {
        global $DB;

        $sql = "SELECT mlhp.id, mlhp.name, mlhpm.manager_order, mlhpm.is_master 
                FROM {local_hierarchy_pos_manager} mlhpm
                JOIN {local_hierarchy_position} mlhp 
                    ON mlhp.id = mlhpm.managerid AND mlhp.deleted = 0
                WHERE mlhpm.positionid = :positionid
                ORDER BY mlhpm.is_master DESC, mlhpm.manager_order ASC, mlhp.name ASC";

        return $DB->get_records_sql($sql, ['positionid' => $this->get('id')]);
    }

    /**
     * Retorna as estruturas relacionadas a esta posição.
     *
     * @return array Lista de structures
     */
    public function fetch_structures(): array
    {
        global $DB;

        $sql = "SELECT lhs.id, lhs.name 
                FROM {local_hierarchy_pos_struct} lps
                JOIN {local_hierarchy_structure} lhs 
                    ON lps.structureid = lhs.id AND lhs.deleted = 0
                WHERE lps.positionid = :positionid";

        return $DB->get_records_sql($sql, ['positionid' => $this->get('id')]);
    }

    /**
     * Retorna os subordinados de uma posição
     *
     * @return array Lista de subordinados
     */
    public function fetch_subordinates(): array
    {
        global $DB;

        $sql = "SELECT p.id, p.name, p.manage as is_manager
                FROM {local_hierarchy_pos_manager} pm
                JOIN {local_hierarchy_position} p ON p.id = pm.positionid
                WHERE pm.managerid = :positionid
                AND p.deleted = 0";

        $records = $DB->get_records_sql($sql, ['positionid' => $this->get('id')]);

        return array_map(function ($record) {
            return (array) $record;
        }, $records);
    }


    public function fetch_subordinates_recursive($processed = [], $depth = 0, $max_depth = 1)
    {
        // Verifique se atingiu o limite de profundidade
        if ($depth >= $max_depth) {
            return [];
        }

        $subordinates = array_values($this->fetch_subordinates());

        foreach ($subordinates as &$subordinate) {
            $subordinate = (array) $subordinate; // Convertendo para array

            // Evitar processar o mesmo subordinado (evita ciclos infinitos)
            if (in_array($subordinate['id'], $processed)) {
                $subordinate['subordinates'] = [];
            } else {
                $processed[] = $subordinate['id']; // Adiciona ao array de processados
                $subordinate_model = self::get_record(['id' => $subordinate['id']]);
                if ($subordinate_model) {
                    // Passa o contador de profundidade e o limite
                    $subordinate['subordinates'] = $subordinate_model->fetch_subordinates_recursive($processed, $depth + 1, $max_depth);
                } else {
                    $subordinate['subordinates'] = [];
                }
            }
        }

        return $subordinates;
    }

    /**
     * Adiciona managers a uma position
     *
     * @param array $managers
     * @return void
     */
    public function add_managers(array $managers)
    {
        global $DB;

        $positionid = $this->get('id');

        if (!$positionid) {
            throw new \moodle_exception('missing_position_id', 'local_hierarchy');
        }

        // Buscar managers já cadastrados no banco para essa posição.
        $existingManagers = $DB->get_records('local_hierarchy_pos_manager', ['positionid' => $positionid]);

        // Transformar em array associativo para facilitar comparação.
        $existingManagersArr = [];
        foreach ($existingManagers as $record) {
            $existingManagersArr[$record->managerid] = [
                'is_master' => $record->is_master,
                'manager_order' => $record->manager_order,
            ];
        }

        // Transformar os novos managers em um array associativo para comparar.
        $newManagersArr = [];
        foreach ($managers as $manager) {
            $newManagersArr[$manager['id']] = [
                'is_master' => $manager['is_master'] ? 1 : 0,
                'manager_order' => $manager['manager_order'],
            ];
        }

        // Se os arrays forem idênticos, não precisa fazer nada.
        if ($existingManagersArr == $newManagersArr) {
            return;
        }

        // Remove todos os managers existentes para essa posição.
        $DB->delete_records('local_hierarchy_pos_manager', ['positionid' => $positionid]);

        // Adiciona os novos managers.
        foreach ($managers as $manager) {
            $record = new stdClass();
            $record->positionid = $positionid;
            $record->managerid = $manager['id'];
            $record->manager_order = (int) $manager['manager_order'];
            $record->is_master = $manager['is_master'] ? 1 : 0;
            $record->timecreated = time();
            $record->timemodified = time();
            $DB->insert_record('local_hierarchy_pos_manager', $record);
        }
    }
}
