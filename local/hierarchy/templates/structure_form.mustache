<form>
    <h5 class="text-uppercase text-primary my-3">{{#str}} structures_form, local_hierarchy {{/str}}</h5>

    <input type="hidden" name="sesskey" value="{{ sesskey }}">
    <input type="hidden" name="_qf__local_hierarchy_form_structure"
        value="{{ _qf__local_hierarchy_form_structure }}">

    {{{ id }}}

    <div class="row">
       {{! <div class="col">
            <div class="form-group">
                {{{ visibleid --}}
            {{! </div>
        </div> --}}

        <div class="form-group col-lg-4">
            {{{ name }}}
            {{{ name_error }}}
        </div>

        <div class="form-group col-lg-4">
            {{{ managerid }}}
            {{{ managerid_error }}}
        </div>

        <div class="d-flex flex-column form-check align-items-lg-start pl-lg-5 col-lg-4 mb-1 ml-4 ml-lg-auto pt-lg-5 mt-lg-2">
            <div>{{{ manage }}}</div>
            {{{ manage_error }}}
        </div>
    </div>

</form>


{{#js}}
require(['jquery', 'local_hierarchy/select2'], function($, select2) {
    $(document).ready(function() {
        $('select[name="managerid"]').select2({
            selectionCssClass: "form-control",
            dropdownParent: $(".modal"),
            width: '100%'
        }).on('select2:open', () => {
			document.querySelector('.select2-search__field').focus();
		});
    });
});
{{/js}}