<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Version information for Position Manager
 *
 * @package    local_hierarchy
 * @copyright  2024 REVVO <www.somosrevvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$plugin->component    = 'local_hierarchy';
$plugin->release      = '1.0';
$plugin->version      = **********;
$plugin->requires     = **********;
$plugin->supported    = [402, 404];
$plugin->maturity     = MATURITY_STABLE;
$plugin->dependencies = [
    'profilefield_position' => ANY_VERSION,
    'profilefield_structure' => ANY_VERSION,
];
