{"name": "hierarchy", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "nodemon", "build": "set NODE_ENV=production && vite build", "preview": "vite preview"}, "engines": {"node": ">=18.0.0 <=20.0.0"}, "dependencies": {"@balkangraph/orgchart.js": "^8.14.54", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^4.2.0", "vue": "^3.4.21", "vue-router": "^4.2.4", "vue-select": "^4.0.0-beta.6", "vuedraggable": "^4.1.0", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "nodemon": "^3.1.3", "sass": "^1.77.6", "vite": "^5.2.8"}}