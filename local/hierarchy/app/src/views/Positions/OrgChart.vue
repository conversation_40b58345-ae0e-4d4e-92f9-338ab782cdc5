<template>
  <div
    class="d-flex flex-column flex-md-row justify-content-between align-items-stretch align-items-md-center mb-5 mb-md-0"
  >
    <div class="d-flex flex-column">
      <h2>Organograma de Cargos</h2>
    </div>
    <div class="d-flex align-items-center mt-3 mt-md-0">
      <button
        class="btn btn-primary w-100 d-md-inline-block"
        title="Gerenciador de Hierarquia"
        @click="$router.push({ name: 'hierarchy.positions' })"
      >
        <i class="fa fa-cog"></i>
        Gerenciador de Hierarquia
      </button>
    </div>
  </div>
  <OrgChart
    v-if="data.length > 0"
    :data="data"
  />
  <LFLoading :isLoading="isLoading || !data.length > 0" />
</template>

<script>
  import OrgChart from "@/components/OrgChart.vue";
  import LFLoading from "@/components/LFLoading.vue";
  import { ajax } from "@/helpers/moodle";

  export default {
    name: "OrgChartView",
    data() {
      return {
        data: [],
        isLoading: false,
      };
    },
    components: {
      OrgChart,
      LFLoading,
    },
    async mounted() {
      this.fetchPositions();
    },
    computed: {},
    methods: {
      async fetchPositions() {
        this.isLoading = true;
        try {
          const response = await ajax(
            "local_hierarchy_fetch_orgchart_positions",
            {}
          );
          this.data = response.data;
        } catch (error) {
          console.error(error);
        } finally {
          this.isLoading = false;
        }
      },
    },
  };
</script>
