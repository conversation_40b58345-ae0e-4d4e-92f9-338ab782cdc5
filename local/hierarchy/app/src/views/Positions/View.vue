<template>
  <div id="position-view">
    <Modal
      :title="strings.viewposition"
      @close="closeModal"
      :showFooter="false"
    >
      <div v-if="!isLoading">
        <h5 class="text-uppercase text-primary my-3">
          {{ strings.positiondata }}
        </h5>
        <div class="row">
          <div class="col-12 col-md-auto">
            <label for="id">{{ strings.position_id }}</label>
            <p>{{ position.id }}</p>
          </div>
          <div class="col-12 col-md flex-grow-1 text-break">
            <label for="name">{{ strings.position_name }}</label>
            <p>{{ position.name }}</p>
          </div>
          <div class="col-12 col-md-auto">
            <label class="d-none d-md-inline-block"></label>
            <div
              class="form-check d-flex pl-0 pl-md-3 mb-md-2 mb-3 mt-md-0 mt-1"
            >
              <input
                type="checkbox"
                id="is_manager"
                v-model="position.is_manager"
              />
              <label
                class="form-check-label ml-2"
                for="is_manager"
                >{{ strings.position_manager }}</label
              >
            </div>
          </div>
        </div>
        <div class="row align-items-center mb-3">
          <div class="col-12 col-md-auto">
            <label for="managers">
              {{ strings.managers }}
              <a
                class="btn btn-link py-0 px-1"
                role="button"
                data-trigger="focus"
                data-toggle="popover"
                data-placement="right"
                tabindex="0"
                data-container="body"
                data-html="true"
                :data-content="'<div>' + strings.help_managers + '</div>'"
              >
                <i
                  title="Ajuda com reporta-se á"
                  class="far fa-question-circle text-primary"
                ></i>
              </a>
            </label>
            <div class="d-flex flex-wrap text-uppercase">
              <span
                v-for="manager in position.managers"
                :key="manager.id"
                class="badge-custom py-1 px-2 rounded mr-1 mt-2"
                :class="
                  manager.is_master
                    ? 'bg-warning text-dark'
                    : 'bg-primary text-white'
                "
              >
                {{ manager.name }}
                <i
                  v-if="manager.is_master"
                  class="fa fa-star"
                ></i>
              </span>
            </div>
          </div>
        </div>
        <div class="row align-items-center mb-3">
          <div class="col-12 col-md-auto">
            <label for="subordinates">
              {{ strings.position_subordinates }}
              <a
                class="btn btn-link py-0 px-1"
                role="button"
                data-trigger="focus"
                data-toggle="popover"
                data-placement="right"
                tabindex="0"
                data-container="body"
                data-html="true"
                :data-content="'<div>' + strings.help_subordinates + '</div>'"
              >
                <i
                  title="Ajuda com cargos subordinados"
                  class="far fa-question-circle text-primary"
                ></i>
              </a>
            </label>
            <div class="d-flex flex-wrap text-uppercase">
              <span
                v-for="subordinate in position.subordinates"
                :key="subordinate.id"
                class="badge-custom py-1 px-2 rounded mr-1 mt-2 bg-primary text-white"
              >
                {{ subordinate.name }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <LFLoading :isLoading="isLoading"></LFLoading>
    </Modal>
  </div>
</template>

<script>
  import Modal from "@/components/Modal.vue";
  import LFLoading from "@/components/LFLoading.vue";

  import { ajax, getStrings, toastError } from "@/helpers/moodle";

  export default {
    name: "PositionView",
    components: {
      Modal,
      LFLoading,
    },
    data() {
      return {
        position: {},
        strings: {},
        isLoading: false,
      };
    },
    async created() {
      let stringArray = [
        {
          key: "viewposition",
          component: "local_hierarchy",
        },
        {
          key: "positiondata",
          component: "local_hierarchy",
        },
        {
          key: "position_id",
          component: "local_hierarchy",
        },
        {
          key: "position_name",
          component: "local_hierarchy",
        },
        {
          key: "position_manager",
          component: "local_hierarchy",
        },
        {
          key: "managers",
          component: "local_hierarchy",
        },
        {
          key: "position_subordinates",
          component: "local_hierarchy",
        },
        {
          key: "help_managers",
          component: "local_hierarchy",
        },
        {
          key: "help_subordinates",
          component: "local_hierarchy",
        },
      ];

      this.strings = await getStrings(stringArray);
    },
    mounted() {
      this.getPosition(this.$route.params.id);
      $("#items-modal").modal("show");
    },
    computed: {},
    methods: {
      closeModal() {
        $("#items-modal").modal("hide");
        this.$router.push("/positions");
      },
      async getPosition(id) {
        this.isLoading = true;
        try {
          const response = await ajax("local_hierarchy_get_position", {
            id: id,
          });
          this.position = response.item;
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          this.isLoading = false;
        }
      },
    },
  };
</script>
<style lang="scss" scoped>
  #position-view {
    .is-invalid {
      border-color: #dc3545 !important;
    }

    label:not(.form-check label) {
      font-size: 0.875rem;
    }

    p,
    .form-check-label {
      font-size: 1rem;
    }

    input[type="checkbox"] {
      pointer-events: none;
      /* Impede o clique */
      opacity: 0.65;
      /* Faz parecer desabilitado (opcional) */
    }
    input[type="checkbox"]:checked + label {
      pointer-events: none; /* Impede interação com o label */
    }

    .badge-custom {
      font-size: 0.75rem !important;
      font-weight: 700 !important;
    }

    .text-dark {
      color: #343a40 !important;
    }
  }
</style>
