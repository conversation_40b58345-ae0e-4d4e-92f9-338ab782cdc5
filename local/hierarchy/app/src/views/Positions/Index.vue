<template>
  <Header
    :title="strings.positions_manager"
    :labelOrgChart="strings.vieworgchart"
    @openOrgChart="$router.push({ name: 'hierarchy.orgchart.positions' })"
  >
    <Filter
      @updateItems="updatePositions"
      :fetchItems="fetchPositions"
    ></Filter>

    <Navbar @modalItem="positionModal">
      <template
        v-if="positions.total_items"
        #count
        >{{ positions.total_items }}
        {{ strings.position_plural }} encontrados</template
      >
      <template #add>{{ strings.addposition }}</template>
    </Navbar>
  </Header>

  <Body
    :data="positions"
    :isLoading="loading"
    @updateItems="updatePositions"
    @modalItem="positionModal"
    @deleteItem="deletePositionModal"
  ></Body>

  <Footer
    entity="positions"
    titlePage="Gerenciamento de cargos"
    subTitlePage="Cadastrar em lote"
    @exportToXLS="exportToXLS"
    @exportToCSV="exportToCSV"
  ></Footer>

  <router-view
    v-if="$route.meta.isModal"
    @updateItems="updatePositions"
  ></router-view>

  <ConfirmModal
    title="Excluir cargo"
    actionLabel="Excluir"
    :message="confirmDeleteMessage"
    @action="deletePosition"
  ></ConfirmModal>

  <LFLoading :isLoading="loading" />
</template>

<script>
  import Header from "@/components/Header.vue";
  import Filter from "@/components/Header/Filters/PositionsFilter.vue";
  import Navbar from "@/components/Header/Navbar.vue";
  import Body from "@/components/Body.vue";
  import Footer from "@/components/Footer.vue";
  import LFLoading from "@/components/LFLoading.vue";
  import ConfirmModal from "@/components/ConfirmModal.vue";

  import {
    ajax,
    getStrings,
    getURLParams,
    toastSuccess,
    toastError,
  } from "@/helpers/moodle";

  export default {
    name: "IndexView",
    data() {
      return {
        strings: {},
        positions: {},
        loading: true,
        confirmDeleteMessage: "",
        selectedPosition: null,
      };
    },
    components: {
      Header,
      Body,
      Footer,
      LFLoading,
      Filter,
      Navbar,
      ConfirmModal,
    },
    async created() {
      this.loading = true;
      await Promise.all([this.loadStrings()]);
      await this.updatePositions();
      this.loading = false;
    },
    computed: {},
    methods: {
      async updatePositions(
        page = 1,
        size = this.positions.perpage,
        sort = []
      ) {
        this.loading = true;

        let URLParams = getURLParams();

        let args = {
          search: URLParams.get("search") ?? "",
          status: URLParams.get("status")
            ? parseInt(URLParams.get("status"))
            : 0,
          onlymanagers: JSON.parse(URLParams.get("onlymanagers")) ?? false,
          page: page,
          perpage: size,
          sort: sort,
        };

        try {
          const response = await this.fetchPositions(args);
          this.positions = { ...this.positions, ...response };
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          this.loading = false;
        }
      },
      fetchPositions(args) {
        return ajax("local_hierarchy_fetch_positions", args);
      },
      async loadStrings() {
        let stringArray = [
          {
            key: "position_plural",
            component: "local_hierarchy",
          },
          {
            key: "addposition",
            component: "local_hierarchy",
          },
          {
            key: "positions_manager",
            component: "local_hierarchy",
          },
          {
            key: "vieworgchart",
            component: "local_hierarchy",
          },
          {
            key: "editposition",
            component: "local_hierarchy",
          },
          {
            key: "addposition",
            component: "local_hierarchy",
          },
          {
            key: "deleteposition",
            component: "local_hierarchy",
          },
          {
            key: "deleteposition_body",
            component: "local_hierarchy",
          },
          {
            key: "deleteposition_success",
            component: "local_hierarchy",
          },
          {
            key: "delete",
            component: "core",
          },
        ];

        this.strings = await getStrings(stringArray);
      },
      positionModal(id, view = false) {
        if (view && id) {
          this.$router.push({
            name: "hierarchy.positions.view",
            params: { id: id },
          });
        } else if (id) {
          this.$router.push({
            name: "hierarchy.positions.edit",
            params: { id: id },
          });
        } else {
          this.$router.push({ name: "hierarchy.positions.create" });
        }
      },
      deletePositionModal(position) {
        this.confirmDeleteMessage = this.strings.deleteposition_body.replace(
          "{$a}",
          position.name
        );

        this.selectedPosition = position;

        $("#confirm-modal").modal("show");
      },
      async deletePosition() {
        this.loading = true;
        try {
          const response = await ajax("local_hierarchy_delete_position", {
            id: this.selectedPosition.id,
          });

          $("#confirm-modal").modal("hide");
          if (response) {
            toastSuccess(
              this.strings.deleteposition_success.replace(
                "{$a}",
                this.selectedPosition.name
              )
            );
          }
          await this.updatePositions();
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          $("#confirm-modal").modal("hide");
          this.loading = false;
        }
      },
      async exportToXLS(formattedDate) {
        let URLParams = getURLParams();

        const response = await ajax("local_hierarchy_export_positions", {
          format: "xls",
          search: URLParams.get("search") ?? "",
          status: URLParams.get("status")
            ? parseInt(URLParams.get("status"))
            : 0,
          onlymanagers: JSON.parse(URLParams.get("onlymanagers")) ?? false,
        });
        const xls = response.export;

        const blob = new Blob(
          [
            new Uint8Array(
              window
                .atob(xls)
                .split("")
                .map((c) => c.charCodeAt(0))
            ),
          ],
          {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          }
        );

        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          "Relatório de Cargos Cadastrados - " + formattedDate + ".xlsx"
        );

        link.click();
      },
      async exportToCSV(formattedDate) {
        let URLParams = getURLParams();

        const response = await ajax("local_hierarchy_export_positions", {
          format: "csv",
          search: URLParams.get("search") ?? "",
          status: URLParams.get("status")
            ? parseInt(URLParams.get("status"))
            : 0,
          onlymanagers: JSON.parse(URLParams.get("onlymanagers")) ?? false,
        });
        const csv = response.export;

        const blob = new Blob([csv], { type: "text/csv;charset=utf-8" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          "Relatório de Cargos Cadastrados - " + formattedDate + ".csv"
        );
        link.click();
      },
    },
  };
</script>
