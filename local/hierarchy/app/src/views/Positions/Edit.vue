<template>
  <div id="position-edit">
    <Modal
      :title="strings.editposition"
      @close="closeModal"
      @submit="savePosition"
    >
      <div v-if="position.id">
        <h5 class="text-uppercase text-primary my-3">
          {{ strings.positiondata }}
        </h5>
        <div class="form-row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="id">{{ strings.position_id }}</label>
              <input
                type="text"
                id="id"
                v-model="position.id"
                class="form-control"
                disabled
              />
            </div>
          </div>
          <div class="col-md-5">
            <div class="form-group">
              <label for="name"
                >{{ strings.position_name }}
                <i class="fa-solid fa-circle-exclamation text-danger mr-1"></i
              ></label>
              <input
                type="text"
                id="name"
                v-model="position.name"
                class="form-control"
                :class="{ 'is-invalid': v$.position.name.$error }"
                placeholder="Digite o nome do cargo"
                @input="clearError('name')"
              />
              <div
                v-if="v$.position.name.$error"
                class="text-danger mt-1 ml-1"
              >
                {{ v$.position.name.$errors[0].$message }}
              </div>
              <div
                v-if="getError('name')"
                class="text-danger mt-1 ml-1"
              >
                {{ getError("name") }}
              </div>
            </div>
          </div>
          <div class="col-md-auto">
            <label class="d-none d-md-inline-block"></label>
            <div class="form-check mb-2 mt-md-3">
              <input
                type="checkbox"
                class="form-check-input"
                id="is_manager"
                v-model="position.is_manager"
              />
              <label
                class="form-check-label"
                for="is_manager"
                >{{ strings.position_manager }}</label
              >
            </div>
          </div>
          <div
            v-if="v$.position.is_manager.$error"
            class="text-danger mb-2 ml-1"
            style="margin-top: -10px"
          >
            {{ v$.position.is_manager.$errors[0].$message }}
          </div>
        </div>
        <div class="form-row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="managers">
                {{ strings.managers }}
                <a
                  class="btn btn-link py-0 px-1"
                  role="button"
                  data-trigger="click"
                  data-toggle="popover"
                  data-placement="right"
                  tabindex="1"
                  data-container="body"
                  data-html="true"
                  :data-content="'<div>' + strings.help_managers + '</div>'"
                >
                  <i
                    title="Ajuda com reporta-se á"
                    class="far fa-question-circle text-primary"
                  ></i>
                </a>
              </label>
              <div class="d-flex align-items-center">
                <v-select
                  id="managers"
                  v-model="selectedManager"
                  placeholder="Selecionar gestor"
                  :options="searching ? managers : filteredManagers"
                  label="name"
                  @search="getManagers"
                >
                  <template v-slot:no-options="{ search, searching }">
                    <template v-if="searching">
                      Nenhum resultado encontrado para <em>{{ search }}</em
                      >.
                    </template>
                    <em
                      v-else
                      style="opacity: 0.5"
                      >Comece a digitar para pesquisar</em
                    >
                  </template>
                </v-select>
                <button
                  class="btn btn-primary btn-custom"
                  @click="addManager(selectedManager)"
                  title="Adicionar"
                  aria-label="Adicionar"
                >
                  <i class="fa fa-plus"></i>
                </button>
              </div>
            </div>
          </div>
          <div
            class="col-md-6 d-flex align-items-center justify-content-between mt-md-2"
          >
            <div class="d-flex align-items-center">
              <label class="d-none d-md-inline-block"></label>
              <div class="form-check mb-2 mt-md-3">
                <input
                  type="checkbox"
                  class="form-check-input"
                  id="is_master"
                  v-model="isMasterManager"
                />
                <label
                  class="form-check-label"
                  for="is_master"
                  >Cargo gestor principal</label
                >
              </div>
            </div>
            <div class="d-flex align-items-center mb-2 mb-md-0 mt-md-2">
              <button
                class="btn btn-link custom-text-danger"
                @click="cleanManagersModal"
              >
                <i class="far fa-trash-can mr-2"></i>Limpar cargos
              </button>
            </div>
          </div>
        </div>
        <div
          v-if="v$.isMasterManager.$error"
          class="text-danger"
        >
          {{ v$.isMasterManager.$errors[0].$message }}
        </div>
        <div class="row align-items-center mb-3">
          <div class="col-md-auto d-flex flex-wrap text-uppercase">
            <span
              v-for="manager in position.managers"
              :key="manager.id"
              class="badge-custom py-1 px-2 rounded mr-1 mt-2"
              :class="
                manager.is_master
                  ? 'bg-warning text-dark'
                  : 'bg-primary text-white'
              "
            >
              <i
                role="button"
                class="fa fa-times cursor-pointer mr-2"
                @click="removeManager(manager.id)"
                title="Remover"
                aria-label="Remover"
              ></i>
              {{ manager.name }}
              <i
                v-if="manager.is_master"
                class="fa fa-star ml-2"
              ></i>
            </span>
          </div>
        </div>
        <div class="row align-items-center mb-2">
          <div class="col-md-auto">
            <label for="subordinates">
              {{ strings.position_subordinates }}
              <a
                class="btn btn-link py-0 px-1"
                role="button"
                data-trigger="focus"
                data-toggle="popover"
                data-placement="right"
                tabindex="0"
                data-container="body"
                data-html="true"
                :data-content="'<div>' + strings.help_subordinates + '</div>'"
              >
                <i
                  title="Ajuda com cargos subordinados"
                  class="far fa-question-circle text-primary"
                ></i>
              </a>
            </label>
            <div class="col-md-auto d-flex flex-wrap text-uppercase p-0">
              <span
                v-for="subordinate in position.subordinates"
                :key="subordinate.id"
                class="badge-custom py-1 px-2 rounded mr-1 mt-2 bg-primary text-whit"
              >
                {{ subordinate.name }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <LFLoading :isLoading="isLoading"></LFLoading>
      <ConfirmModal
        title="Confirmar limpeza de cargos"
        actionLabel="Limpar"
        :message="confirmMessage"
        @action="cleanManagers"
      ></ConfirmModal>
    </Modal>
  </div>
</template>

<script>
  import Modal from "@/components/Modal.vue";
  import ConfirmModal from "@/components/ConfirmModal.vue";
  import LFLoading from "@/components/LFLoading.vue";
  import vSelect from "vue-select";
  import "vue-select/dist/vue-select.css";
  import { useVuelidate } from "@vuelidate/core";
  import {
    required,
    minLength,
    maxLength,
    helpers,
  } from "@vuelidate/validators";

  import {
    ajax,
    getStrings,
    getURLParams,
    toastSuccess,
    toastError,
  } from "@/helpers/moodle";
  import { error } from "ajv/dist/vocabularies/applicator/dependencies";

  export default {
    name: "PositionEdit",
    components: {
      Modal,
      ConfirmModal,
      LFLoading,
      vSelect,
    },
    data() {
      return {
        selectedManager: null,
        isMasterManager: false,
        isManagerOld: false,
        managers: [],
        position: {
          id: null,
          name: "",
          is_manager: false,
          managers: [],
          status: 0,
        },
        strings: {},
        isLoading: false,
        searching: false,
        errors: [],
        confirmMessage: "",
      };
    },
    async created() {
      let stringArray = [
        {
          key: "editposition",
          component: "local_hierarchy",
        },
        {
          key: "positiondata",
          component: "local_hierarchy",
        },
        {
          key: "position_id",
          component: "local_hierarchy",
        },
        {
          key: "position_name",
          component: "local_hierarchy",
        },
        {
          key: "position_manager",
          component: "local_hierarchy",
        },
        {
          key: "managers",
          component: "local_hierarchy",
        },
        {
          key: "position_subordinates",
          component: "local_hierarchy",
        },
        {
          key: "help_managers",
          component: "local_hierarchy",
        },
        {
          key: "help_subordinates",
          component: "local_hierarchy",
        },
      ];

      this.strings = await getStrings(stringArray);
    },
    validations() {
      return {
        position: {
          name: {
            required: helpers.withMessage(
              "O nome do cargo é obrigatório!",
              required
            ),
          },
          is_manager: {
            canToggle: helpers.withMessage(
              "O cargo selecionado não pode ser marcado como cargo gestor. Existem usuários no sistema que estão associado a este cargo.",
              (value) => {
                // Se o valor de is_manager não foi alterado, não aplica a validação
                if (value === this.isManagerOld) {
                  return true;
                }
                // Aplica a validação apenas se o status for 1 (em uso) e o valor for true
                return this.position.status === 0 || !value;
              }
            ),
          },
        },

        isMasterManager: {
          uniqueMaster: helpers.withMessage(
            "O cargo gestor principal deve ser único!",
            (value) => {
              if (!value) return true;
              return !this.position.managers.some((m) => m.is_master);
            }
          ),
        },
      };
    },
    setup: () => ({ v$: useVuelidate() }),
    computed: {
      filteredManagers() {
        if (!this.managers.length) return []; // Evita erro antes da busca
        return this.managers.filter(
          (manager) =>
            !this.position.managers?.some((m) => m.id === manager.id) &&
            manager.id !== this.position.id
        );
      },
    },
    mounted() {
      this.getPosition(this.$route.params.id);
      this.getManagers();
      $("#items-modal").modal("show");
    },
    methods: {
      closeModal() {
        $("#items-modal").modal("hide");
        this.$router.push("/positions");
      },
      async getPosition(id) {
        try {
          this.isLoading = true;
          const response = await ajax("local_hierarchy_get_position", {
            id: id,
          });
          this.position = response.item;
          this.isManagerOld = this.position.is_manager;
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          this.isLoading = false;
        }
      },
      async getManagers(
        search = "",
        loading,
        page = 1,
        size = 50,
        sort = [{ field: "name", direction: "ASC" }]
      ) {
        if (search && search.length < 3) return;
        this.searching = true;
        if (loading) loading(true);
        try {
          const response = await ajax("local_hierarchy_fetch_positions", {
            search: search,
            status: 0,
            onlymanagers: true,
            page: page,
            perpage: size,
            sort: sort,
          });
          if (search) {
            // Se for uma busca, apenas sobrescreve managers
            this.managers = response.items;
          } else {
            // Se for carregamento inicial, mescla sem duplicar
            this.managers = [...new Set([...this.managers, ...response.items])];
          }
        } catch (error) {
          console.error(error);
        } finally {
          this.searching = false;
          if (loading) loading(false);
        }
      },
      addManager(manager) {
        if (!this.position.managers) {
          this.position.managers = [];
        }

        this.v$.isMasterManager.$touch();

        if (this.v$.isMasterManager.$error) {
          return;
        }

        if (manager) {
          // Define se o novo gestor é o principal.
          const isMaster = this.isMasterManager ?? false;
          // Se for gestor principal, será sempre ordem 1
          // Se não for, será a próxima ordem disponível
          const newOrder = isMaster ? 1 : this.position.managers.length + 1;

          // Se estamos adicionando um gestor principal,
          // precisamos reajustar a ordem dos outros
          if (isMaster) {
            this.position.managers.forEach((m) => {
              m.manager_order += 1;
            });
          }

          // Adiciona o gestor à lista.
          this.position.managers.push({
            id: manager.id,
            name: manager.name,
            is_master: isMaster,
            manager_order: newOrder, // Gestor principal sempre tem order = 1.
          });

          // Reordena a lista após adicionar o gestor.
          this.reorderManagers();

          // Reseta os campos de seleção.
          this.selectedManager = null;
          this.isMasterManager = false;
        }
      },
      removeManager(managerId) {
        // Remove o gestor da lista.
        this.position.managers = this.position.managers.filter(
          (m) => m.id !== managerId
        );

        // Removido de acordo com RF015 do aditivo de requisito
        // Se o gestor removido era o principal, define o próximo como principal.
        // if (removedManager.is_master && this.position.managers.length > 0) {
        //   this.position.managers[0].is_master = true;
        // }

        // Reordena a lista após remover o gestor.
        this.reorderManagers();
      },
      cleanManagersModal() {
        this.confirmMessage =
          "Você tem certeza de que deseja limpar os cargos selecionados?";

        $("#confirm-modal").modal("show");
      },
      cleanManagers() {
        this.position.managers = [];
      },
      reorderManagers() {
        // Primeiro ordena colocando gestores principais no topo
        this.position.managers.sort((a, b) => {
          if (a.is_master && !b.is_master) return -1;
          if (!a.is_master && b.is_master) return 1;
          return a.manager_order - b.manager_order;
        });

        // Atualiza os valores de order mantendo gestores principais primeiro
        this.position.managers.forEach((manager, index) => {
          manager.manager_order = index + 1;
        });
      },
      async savePosition() {
        this.v$.$validate();

        if (this.v$.$error) {
          toastError("Por favor, corrija os erros antes de salvar.");
          return;
        }

        this.isLoading = true;

        try {
          const payload = { ...this.position };
          delete payload.subordinates;
          delete payload.status;

          const response = await ajax("local_hierarchy_edit_position", {
            data: payload,
          });

          if (response.success) {
            this.$emit("update-items");
            this.closeModal();
            toastSuccess(response.message);
          } else {
            this.errors = response.errors;
            toastError("Por favor, corrija os erros antes de salvar.");
          }
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          this.isLoading = false;
        }
      },
      getError(field) {
        if (!this.errors || this.errors.length === 0) return null;

        const error = this.errors.find((e) => e.field === field);
        return error ? error.message : null;
      },
      // Método para limpar o erro de um campo específico
      clearError(field) {
        if (!this.errors || this.errors.length === 0) return;

        const index = this.errors.findIndex((e) => e.field === field);
        if (index !== -1) {
          // Remove o erro do array
          this.errors.splice(index, 1);
          // Ou, se preferir apenas limpar a mensagem:
          // this.errors[index].message = '';
        }
      },
    },
  };
</script>
<style lang="scss">
  #position-edit {
    .is-invalid {
      border-color: #dc3545 !important;
    }

    label:not(.form-check label) {
      font-size: 0.875rem;
    }

    p,
    .form-check-label {
      font-size: 1rem;
    }

    // vue-select
    .v-select {
      display: block;
      width: 100%;
      height: calc(1.5em + 0.75rem + 2px);
      font-size: 0.9375rem;
      font-weight: 400;
      line-height: 1.5;
      color: #dee2e6 !important;
      background-color: #212529 !important;
      border: 1px solid #495057 !important;
      border-radius: 0.5rem 0 0 0.5rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

      &:focus-within {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.2rem
          color-mix(in srgb, var(--primary) 80%, white 20%) !important;
      }
    }

    .vs__dropdown-menu {
      margin-top: 5px;
    }

    .vs__dropdown-option:hover {
      background-color: #495057 !important;
      color: white !important;
    }

    .vs__dropdown-option--highlight {
      background-color: #495057 !important;
      color: white !important;
    }

    .vs__selected-options {
      padding: 0 6px !important;
    }

    .vs__selected {
      background-color: transparent !important;
      color: #dee2e6 !important;
      line-height: unset !important;
    }

    .vs__actions {
      padding: 4px 2px 0 3px !important;
    }

    .vs__open-indicator {
      fill: #dee2e6 !important;
      transform: scale(0.7);
    }

    .vs__search {
      font-size: 0.9375rem !important;
    }

    .btn-custom {
      border-radius: 0 0.5rem 0.5rem 0 !important;
      box-shadow: unset !important;
    }

    .badge-custom {
      font-size: 0.75rem !important;
      font-weight: 700 !important;
    }

    .text-dark {
      color: #343a40 !important;
    }

    .custom-text-danger {
      color: var(--danger) !important;
    }
  }
</style>
