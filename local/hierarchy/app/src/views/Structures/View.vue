<template>
  <div id="structure-view">
    <Modal
      title="Visualizar estrutura"
      @close="closeModal"
      :showFooter="false"
    >
      <div v-if="!isLoading">
        <h5 class="text-uppercase text-primary my-3">Dad<PERSON> da estrutura</h5>
        <div class="row">
          <div class="col-12 col-md-4">
            <label for="id">ID da estrutura</label>
            <p>{{ structure.id }}</p>
          </div>
          <div class="col-12 col-md flex-grow-1 text-break">
            <label for="name">Nome da estrutura</label>
            <p>{{ structure.structure }}</p>
          </div>
        </div>
        <div class="row">
          <div class="col-12 col-md-4">
            <label for="type">Tipo</label>
            <p
              v-for="type in structure.type"
              :key="type.id"
            >
              {{ type.name }}
            </p>
          </div>
          <div class="col-12 col-md flex-grow-1 text-break">
            <label for="subordinate">Subordinada a</label>
            <p
              v-for="subordinate in structure.subordinate"
              :key="subordinate.id"
            >
              {{ subordinate.structure }}
            </p>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-auto">
            <label for="positions">Cargos disponíveis para a estrutura</label>
            <div class="d-flex flex-wrap text-uppercase">
              <span
                v-for="position in structure.positions"
                :key="position.id"
                class="badge-custom bg-primary text-white py-1 px-2 rounded mr-1 mt-2"
              >
                {{ position.name }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <LFLoading :isLoading="isLoading"></LFLoading>
    </Modal>
  </div>
</template>

<script>
  import Modal from "@/components/Modal.vue";
  import LFLoading from "@/components/LFLoading.vue";

  import { ajax, getStrings, toastError } from "@/helpers/moodle";

  export default {
    name: "StructureView",
    components: {
      Modal,
      LFLoading,
    },
    data() {
      return {
        structure: {},
        strings: {},
        isLoading: false,
      };
    },
    async created() {
      let stringArray = [
        {
          key: "viewposition",
          component: "local_hierarchy",
        },
      ];

      this.strings = await getStrings(stringArray);
    },
    mounted() {
      this.getStructure(this.$route.params.id);
      $("#items-modal").modal("show");
    },
    computed: {},
    methods: {
      closeModal() {
        $("#items-modal").modal("hide");
        this.$router.push("/structures");
      },
      async getStructure(id) {
        this.isLoading = true;
        try {
          const response = await ajax("local_hierarchy_get_structure", {
            id: id,
          });
          this.structure = response.item;
          console.log(this.structure);
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          this.isLoading = false;
        }
      },
    },
  };
</script>
<style lang="scss" scoped>
  #structure-view {
    .is-invalid {
      border-color: #dc3545 !important;
    }

    label:not(.form-check label) {
      font-size: 0.875rem;
    }

    p,
    .form-check-label {
      font-size: 1rem;
    }

    input[type="checkbox"] {
      pointer-events: none;
      /* Impede o clique */
      opacity: 0.65;
      /* Faz parecer desabilitado (opcional) */
    }
    input[type="checkbox"]:checked + label {
      pointer-events: none; /* Impede interação com o label */
    }

    .badge-custom {
      font-size: 0.75rem !important;
      font-weight: 700 !important;
    }

    .text-dark {
      color: #343a40 !important;
    }
  }
</style>
