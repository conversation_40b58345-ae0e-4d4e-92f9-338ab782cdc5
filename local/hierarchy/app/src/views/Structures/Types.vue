<template>
  <div id="structures-types">
    <Modal
      :title="strings.structures_type_title"
      :showFooter="false"
      @close="closeModal"
    >
      <div class="d-flex flex-column table-container">
        <!-- <PERSON><PERSON> com scroll - Tabela -->
        <div class="table-scroll-area">
          <div
            v-if="structureTypes.length > 0"
            class="table-responsive"
          >
            <table class="table table-striped table-hover">
              <thead>
                <tr class="table-header">
                  <th>Tipos de Estrutura</th>
                  <th style="width: 120px; text-align: center">Ações</th>
                </tr>
              </thead>
              <tbody>
                <tr v-if="isLoading">
                  <td colspan="100%">
                    <div class="loading-container">
                      <span
                        class="spinner-border spinner-border-sm mr-1"
                        role="status"
                        aria-hidden="true"
                      ></span>
                      Carregando dados...
                    </div>
                  </td>
                </tr>
                <tr
                  class="text-center"
                  v-else-if="!structureTypes.length"
                >
                  <td colspan="100%">Nenhum resultado encontrado...</td>
                </tr>
                <tr
                  v-else
                  v-for="type in structureTypes"
                  :key="type.id"
                >
                  <td class="td-limit-width">
                    <div
                      v-if="!type.isEditing"
                      class="d-flex align-items-center"
                    >
                      <span
                        class="text-truncate"
                        data-trigger="focus"
                        data-toggle="popover"
                        data-placement="top"
                        tabindex="0"
                        data-container="body"
                        data-html="true"
                        :data-content="`<div class='no-overflow'><p class='text-center mb-0 font-size-15'>${type.name}</p></div>`"
                        :aria-label="type.name"
                        >{{ type.name }}</span
                      >
                      <span
                        class="badge-custom bg-danger text-white py-1 px-2 rounded ml-2 text-nowrap"
                        >{{ type.uses }} Estruturas</span
                      >
                    </div>
                    <div
                      v-else
                      class="d-flex flex-column"
                      :class="v$.selectedType.name.$error ? 'mt-2' : ''"
                    >
                      <input
                        type="text"
                        class="form-control"
                        v-model="selectedType.name"
                        :class="{ 'is-invalid': v$.selectedType.name.$error }"
                        @input="v$.selectedType.name.$touch()"
                        @keypress.enter="saveType(selectedType)"
                      />
                      <div
                        v-if="v$.selectedType.name.$error"
                        class="text-danger my-1 ml-1"
                      >
                        {{ v$.selectedType.name.$errors[0].$message }}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div
                      class="d-flex justify-content-end align-items-center"
                      :class="v$.selectedType.name.$error ? 'mb-3' : ''"
                    >
                      <button
                        @click="toggleEditType(type)"
                        class="btn btn-link"
                        :title="type.isEditing ? 'Salvar' : 'Editar'"
                        :aria-label="type.isEditing ? 'Salvar' : 'Editar'"
                      >
                        <i
                          :class="
                            type.isEditing
                              ? 'fas fa-check text-success'
                              : 'fas fa-pen text-primary'
                          "
                        ></i>
                      </button>
                      <button
                        v-if="type.isEditing"
                        @click="cancelEdit(type)"
                        class="btn btn-link custom-text-primary"
                        title="Cancelar"
                        aria-label="Cancelar"
                      >
                        <i class="fas fa-times"></i>
                      </button>
                      <button
                        v-else
                        @click="type.uses !== 0 ? '' : deleteTypeModal(type)"
                        class="btn text-danger"
                        :class="{ 'text-muted': type.uses !== 0 }"
                        :title="
                          type.uses !== 0 ? 'Não é possível excluir' : 'Excluir'
                        "
                        role="button"
                        data-trigger="focus"
                        :data-toggle="type.uses !== 0 ? 'popover' : ''"
                        data-placement="left"
                        tabindex="0"
                        data-container="body"
                        data-html="true"
                        :data-content="`<div class='no-overflow'><p class='text-center mb-0 font-size-15'>Não é possível excluir um tipo de estrutura em uso</p></div>`"
                        :aria-label="'Não é possível excluir um tipo de estrutura em uso'"
                      >
                        <i class="far fa-trash-can"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div
        v-if="!isLoading"
        class="input-fixed-area d-flex align-items-center pl-2 py-2 mt-2 bg-tbody"
      >
        <div class="d-flex flex-column w-100">
          <input
            type="text"
            v-model="newType"
            :disabled="isEditing"
            @keydown.enter="saveType"
            @input="v$.newType.$touch()"
            :placeholder="strings.structures_type_new"
            class="form-control"
            :class="{ 'is-invalid': v$.newType.$error }"
          />
          <div
            v-if="v$.newType.$error"
            class="text-danger my-1 ml-1"
          >
            {{ v$.newType.$errors[0].$message }}
          </div>
        </div>
        <div class="d-flex justify-content-center">
          <button
            @click="saveType"
            class="btn btn-link text-success btn-save"
            :disabled="isEditing"
            title="Salvar"
            aria-label="Salvar"
          >
            <i class="fas fa-check"></i>
          </button>
          <button
            @click="cancelEdit"
            class="btn btn-link custom-text-primary"
            :disabled="isEditing"
            title="Cancelar"
            aria-label="Cancelar"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      <LFLoading :isLoading="isLoading"></LFLoading>
      <ConfirmModal
        :title="strings.structures_type_confirm_delete_title"
        actionLabel="Excluir"
        :message="confirmDeleteMessage"
        @action="deleteType"
      ></ConfirmModal>
    </Modal>
  </div>
</template>

<script>
  import Modal from "@/components/Modal.vue";
  import ConfirmModal from "@/components/ConfirmModal.vue";
  import LFLoading from "@/components/LFLoading.vue";
  import { useVuelidate } from "@vuelidate/core";
  import { required, helpers } from "@vuelidate/validators";
  import { ajax, getStrings, toastSuccess, toastError } from "@/helpers/moodle";

  export default {
    name: "StructuresTypes",
    components: {
      Modal,
      ConfirmModal,
      LFLoading,
    },
    data() {
      return {
        structureTypes: [],
        isLoading: false,
        strings: {},
        selectedType: null,
        newType: "",
        isEditing: false,
        confirmDeleteMessage: "",
      };
    },
    async created() {
      let stringArray = [
        { key: "structures_type_title", component: "local_hierarchy" },
        {
          key: "structures_type_confirm_delete_message",
          component: "local_hierarchy",
        },
        {
          key: "structures_type_confirm_delete_title",
          component: "local_hierarchy",
        },
        { key: "structures_type_new", component: "local_hierarchy" },
      ];

      this.strings = await getStrings(stringArray);
    },
    validations() {
      return {
        selectedType: {
          name: {
            required: helpers.withMessage(
              "O nome do tipo de estrutura é obrigatório!",
              required
            ),
            maxLength: helpers.withMessage(
              "O nome do tipo de estrutura é muito longo! Máximo de 40 caracteres.",
              (value) => value.length <= 40
            ),
            // Validação para nomes duplicados na edição
            noDuplicateEdit: helpers.withMessage(
              "Nome inválido. Já existe um tipo de estrutura com este nome. Por favor, escolha um nome diferente.",
              (value) => {
                if (!this.selectedType || !value) return true;
                return !this.structureTypes.some(
                  (type) =>
                    type.id !== this.selectedType.id &&
                    type.name.toLowerCase() === value.toLowerCase()
                );
              }
            ),
          },
        },
        newType: {
          required: helpers.withMessage(
            "O nome do tipo de estrutura é obrigatório!",
            required
          ),
          maxLength: helpers.withMessage(
            "O nome do tipo de estrutura é muito longo! Máximo de 40 caracteres.",
            (value) => value.length <= 40
          ),
          // Validação para nomes duplicados na criação
          noDuplicateNew: helpers.withMessage(
            "Nome inválido. Já existe um tipo de estrutura com este nome. Por favor, escolha um nome diferente.",
            (value) => {
              if (!value) return true;
              return !this.structureTypes.some(
                (type) => type.name.toLowerCase() === value.toLowerCase()
              );
            }
          ),
        },
      };
    },
    setup: () => ({ v$: useVuelidate() }),
    mounted() {
      this.fetchStructureTypes();
      $("#items-modal").modal("show");
    },
    methods: {
      closeModal() {
        $("#items-modal").modal("hide");
        this.$router.push("/structures");
      },
      async fetchStructureTypes() {
        this.isLoading = true;
        try {
          const response = await ajax("local_hierarchy_fetch_structure_types");
          this.structureTypes = response.types.map((type) => ({
            ...type,
            isEditing: false,
          }));
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          this.isLoading = false;
        }
      },
      deleteTypeModal(type) {
        this.confirmDeleteMessage =
          this.strings.structures_type_confirm_delete_message.replace(
            "{$a}",
            type.name
          );
        this.selectedType = type;
        $("#confirm-modal").modal("show");
      },
      async deleteType() {
        this.isLoading = true;
        try {
          const response = await ajax(
            "local_hierarchy_delete_structure_types",
            {
              id: this.selectedType.id,
            }
          );

          $("#confirm-modal").modal("hide");
          if (response) {
            toastSuccess(
              this.strings.structures_delete_successfully.replace(
                "{$a}",
                this.selectedType.name
              )
            );
          }
          await this.fetchStructureTypes();
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          $("#confirm-modal").modal("hide");
          this.isLoading = false;
        }
      },
      toggleEditType(type) {
        if (type.isEditing) {
          this.saveType(this.selectedType);
        } else {
          this.structureTypes.forEach((t) => (t.isEditing = false));
          type.isEditing = true;
          this.selectedType = { ...type };
          this.isEditing = true;
          this.newType = "";
        }
      },
      cancelEdit(type) {
        type.isEditing = false;
        this.isEditing = false;
        this.newType = "";

        this.v$.$reset();
      },
      async saveType(type) {
        //Validação para edição
        if (this.selectedType) {
          this.v$.selectedType.$validate(); // Valida apenas o selectedType
          if (this.v$.selectedType.$error) {
            toastError("Por favor, corrija os erros antes de salvar.");
            return;
          }
        } else {
          // Validação para criação
          this.v$.newType.$validate(); // Valida apenas o newType
          if (this.v$.newType.$error) {
            toastError("Por favor, insira um nome para o novo tipo.");
            return;
          }
        }
        this.isLoading = true;

        try {
          const payload = this.selectedType
            ? { id: type.id, name: type.name }
            : { name: this.newType };
          const response = await ajax(
            "local_hierarchy_save_structure_types",
            payload
          );
          toastSuccess(response.message);

          await this.fetchStructureTypes();

          this.v$.$reset();

          if (this.selectedType) {
            type.isEditing = false;
            this.isEditing = false;
            this.selectedType = null;
          } else {
            this.newType = "";
          }
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          this.isLoading = false;
        }
      },
    },
  };
</script>
<style lang="scss">
  $background-button: #f8f9fa;
  $color-sortable-th: #6ea8fe;
  $color-sortable-th-hover: darken($background-button, 5%);
  $font-size-label: 14px;
  $font-size-sortable-th: 12px;
  $font-size-th: 16px;
  $border-radius-default: 8px;
  $border-radius-input: 5px;
  $button-width: 44px;
  $checkbox-focus-shadow: none;

  #structures-types {
    .table-container {
      display: flex;
      flex-direction: column;
      height: 70vh; /* Altura do container principal */
      max-height: 70vh;
    }

    .table-scroll-area {
      flex: 1;
      overflow: auto;
      min-height: 0; /* Importante para funcionar o scroll em flex */
    }

    .input-fixed-area {
      flex-shrink: 0; /* Impede que a área do input encolha */
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    table {
      border-collapse: collapse;
      margin-bottom: 0 !important;
      width: 100%;
      background-color: transparent !important;
      border-top: 0 !important;

      th,
      td {
        padding: 0 12px;
        height: 50px;
        vertical-align: middle;
        border-top: 0 !important;
      }

      th {
        color: var(--primary);
        font-size: $font-size-th;
        text-transform: uppercase;
        white-space: nowrap;
        cursor: pointer;

        .fa {
          color: #adb5bd;
          margin-left: 4px;
        }
      }

      tr td.checkboxes {
        width: 50px;
      }

      input[type="checkbox"] {
        &:focus {
          box-shadow: $checkbox-focus-shadow;
        }
      }

      .table-header {
        border-bottom: var(--primary) 3px solid;
      }

      .td-truncate {
        max-width: 200px;
      }

      .td-status {
        color: #fff;
        display: inline-block;
        font-size: 12px;
        text-transform: uppercase;
        font-weight: bold;
        padding: 4px 8px;
        border-radius: 5px;
      }

      .td-manager {
        color: #232426;
        display: inline-block;
        font-size: 12px;
        text-transform: uppercase;
        font-weight: 900;
        padding: 4px 8px;
        border-radius: 100%;
        border: solid #1f281f;
      }

      .td-limit-width {
        max-width: 250px;
      }

      .btn-view,
      .btn-edit {
        color: var(--primary);
        font-size: 16px;
        outline: unset;
        box-shadow: unset;
        transition: color 0.3s ease;

        svg {
          fill: var(--primary);
          width: 16 px;
          margin-top: -1px;
        }

        &:hover {
          color: var(--primary);
          cursor: pointer;
        }
      }

      .btn-delete {
        color: #ce3545;
        font-size: 20px;
        outline: unset;
        box-shadow: unset;
        transition: color 0.3s ease;

        &:hover {
          color: #a12f3f;
          cursor: pointer;
        }
      }
    }

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 20px;
    }
    .is-invalid {
      border-color: #dc3545 !important;
    }

    label:not(.form-check label) {
      font-size: 0.875rem;
    }

    p,
    .form-check-label {
      font-size: 1rem;
    }

    .bg-tbody {
      background-color: rgba(255, 255, 255, 0.05);
    }

    .btn-custom {
      border-radius: 0 0.5rem 0.5rem 0 !important;
      box-shadow: unset !important;
    }

    .badge-custom {
      font-size: 0.75rem !important;
      font-weight: 700 !important;
    }

    .text-dark {
      color: #343a40 !important;
    }

    .custom-text-primary {
      color: #0d6efd !important;
    }
  }
</style>
