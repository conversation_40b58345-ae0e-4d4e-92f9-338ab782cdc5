<template>
  <Header
    :title="strings.structures_manager"
    :labelOrgChart="strings.vieworgchart"
    :tooltip="strings.structures_help"
    @openOrgChart="$router.push({ name: 'hierarchy.orgchart.structures' })"
  >
    <template #actions>
      <button
        href="#"
        class="btn btn-link text-primary"
        @click.prevent="$router.push({ name: 'hierarchy.structures.types' })"
      >
        <i class="fa fa-cog mr-2"></i>
        {{ strings.structures_type_title }}
      </button>
    </template>
    <Filter
      @updateItems="updateStructures"
      :fetchItems="fetchStructures"
    ></Filter>

    <Navbar @modalItem="structureModal">
      <template
        v-if="structures.total_items"
        #count
      >
        {{ structures.total_items }} {{ strings.structures }} encontrados
      </template>
      <template #add>{{ strings.structures_add }}</template>
    </Navbar>
  </Header>

  <Body
    :data="structures"
    :isLoading="loading"
    @updateItems="updateStructures"
    @modalItem="structureModal"
    @deleteItem="deleteStructureModal"
  ></Body>

  <Footer
    entity="structures"
    titlePage="Gerenciamento de estruturas"
    subTitlePage="Cadastrar em lote"
    @exportToXLS="exportToXLS"
    @exportToCSV="exportToCSV"
  ></Footer>

  <router-view
    v-if="$route.meta.isModal"
    @updateItems="updateStructures"
  ></router-view>

  <ConfirmModal
    :title="strings.structures_confirm_delete_title"
    :actionLabel="strings.delete"
    :message="confirmDeleteMessage"
    @action="deleteStructure"
  ></ConfirmModal>

  <LFLoading :isLoading="loading" />
</template>

<script>
  import Header from "@/components/Header.vue";
  import Filter from "@/components/Header/Filters/StructuresFilter.vue";
  import Navbar from "@/components/Header/Navbar.vue";
  import Body from "@/components/Body.vue";
  import Footer from "@/components/Footer.vue";
  import ConfirmModal from "@/components/ConfirmModal.vue";
  import LFLoading from "@/components/LFLoading.vue";

  import {
    ajax,
    toastSuccess,
    toastError,
    getStrings,
    getURLParams,
  } from "@/helpers/moodle";

  export default {
    name: "IndexView",
    components: {
      Header,
      Filter,
      Navbar,
      Body,
      Footer,
      ConfirmModal,
      LFLoading,
    },
    data() {
      return {
        strings: {},
        structures: [],
        loading: true,
        confirmDeleteMessage: "",
        selectedStructure: null,
      };
    },
    async created() {
      await Promise.all([this.loadStrings()]);
      await this.updateStructures();
    },
    methods: {
      async loadStrings() {
        const coreStrings = ["delete", "edit", "cancel"].map((key) => ({
          key,
          component: "core",
        }));
        const localStrings = [
          "structure",
          "structures",
          "structures_self",
          "structures_add",
          "structures_edit",
          "structures_manager",
          "structures_load_error",
          "structures_create_successfully",
          "structures_delete_successfully",
          "structures_update_successfully",
          "structures_create_failed",
          "structures_delete_failed",
          "structures_update_failed",
          "structures_type_create_successfully",
          "structures_type_delete_successfully",
          "structures_type_update_successfully",
          "structures_type_create_failed",
          "structures_type_delete_failed",
          "structures_type_update_failed",
          "structures_view_organogram",
          "structures_type_title",
          "structures_position_select",
          "structures_select",
          "structures_type_select",
          "structures_help",
          "structures_deleted_successfully",
          "structures_confirm_delete_title",
          "structures_confirm_delete_message",
          "structures_name_invalid",
          "vieworgchart",
        ].map((key) => ({ key, component: "local_hierarchy" }));

        const strings = await getStrings([...coreStrings, ...localStrings]);

        this.strings = {
          ...strings,
          confirmation_message: strings.structures_confirm_delete_message,
          confirmation_title: strings.structures_confirm_delete_title,
        };
      },
      async updateStructuresTypes() {
        this.loading = true;
        try {
          const args = {};
          this.structure_types = await ajax(
            "local_hierarchy_fetch_structure_types",
            args
          );
        } catch (error) {
          toastError(this.strings.structures_types_load_error);
        }
        this.loading = false;
      },
      async updateStructures(
        page = 1,
        size = this.structures.perpage,
        sort = []
      ) {
        this.loading = true;
        let URLParams = getURLParams();

        let args = {
          search: URLParams.get("search") ?? "",
          status: URLParams.get("status")
            ? parseInt(URLParams.get("status"))
            : 0,
          page: page,
          perpage: size,
          sort: sort,
        };

        try {
          const response = await this.fetchStructures(args);
          this.structures = { ...this.structures, ...response };
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          this.loading = false;
        }
      },
      fetchStructures(args) {
        return ajax("local_hierarchy_fetch_structures", args);
      },
      structureModal(id, view = false) {
        if (view && id) {
          this.$router.push({
            name: "hierarchy.structures.view",
            params: { id: id },
          });
        } else if (id) {
          this.$router.push({
            name: "hierarchy.structures.edit",
            params: { id: id },
          });
        } else {
          this.$router.push({ name: "hierarchy.structures.create" });
        }
      },
      deleteStructureModal(structure) {
        this.confirmDeleteMessage =
          this.strings.structures_confirm_delete_message.replace(
            "{$a}",
            structure.structure
          );

        this.selectedStructure = structure;

        $("#confirm-modal").modal("show");
      },
      async deleteStructure() {
        this.loading = true;
        try {
          const response = await ajax("local_hierarchy_delete_structure", {
            id: this.selectedStructure.id,
          });

          $("#confirm-modal").modal("hide");
          if (response) {
            toastSuccess(
              this.strings.structures_delete_successfully.replace(
                "{$a}",
                this.selectedStructure.structure
              )
            );
          }
          await this.updateStructures();
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          $("#confirm-modal").modal("hide");
          this.loading = false;
        }
      },
      async exportToXLS(formattedDate) {
        let URLParams = getURLParams();

        const response = await ajax("local_hierarchy_export_structures", {
          format: "xls",
          search: URLParams.get("search") ?? "",
          status: URLParams.get("status")
            ? parseInt(URLParams.get("status"))
            : 0,
        });
        const xls = response.export;

        const blob = new Blob(
          [
            new Uint8Array(
              window
                .atob(xls)
                .split("")
                .map((c) => c.charCodeAt(0))
            ),
          ],
          {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          }
        );

        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          "Relatório de Estruturas Cadastradas - " + formattedDate + ".xlsx"
        );

        link.click();
      },
      async exportToCSV(formattedDate) {
        let URLParams = getURLParams();

        const response = await ajax("local_hierarchy_export_structures", {
          format: "csv",
          search: URLParams.get("search") ?? "",
          status: URLParams.get("status")
            ? parseInt(URLParams.get("status"))
            : 0,
        });
        const csv = response.export;

        const blob = new Blob([csv], { type: "text/csv;charset=utf-8" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          "Relatório de Estruturas Cadastradas - " + formattedDate + ".csv"
        );
        link.click();
      },
    },
  };
</script>
