<template>
  <div id="structure-create">
    <Modal
      title="Adicionar estrutura"
      @close="closeModal"
      @submit="saveStructure"
    >
      <div>
        <h5 class="text-uppercase text-primary my-3"><PERSON><PERSON> da estrutura</h5>
        <div class="form-row">
          <div class="col-md-12">
            <div class="form-group">
              <label for="name"
                >Nome da estrutura
                <i class="fa-solid fa-circle-exclamation text-danger mr-1"></i
              ></label>
              <input
                type="text"
                id="name"
                v-model="structure.structure"
                class="form-control"
                :class="{ 'is-invalid': v$.structure.structure.$error }"
                placeholder="Digite o nome da estrutura"
                @input="
                  [v$.structure.structure.$touch(), clearError('structure')]
                "
              />
              <div
                v-if="v$.structure.structure.$error"
                class="text-danger mt-1 ml-1"
              >
                {{ v$.structure.structure.$errors[0].$message }}
              </div>
              <div
                v-if="getError('structure')"
                class="text-danger mt-1 ml-1"
              >
                {{ getError("structure") }}
              </div>
            </div>
          </div>
        </div>
        <div class="form-row">
          <div class="col-md-4">
            <div class="form-group">
              <label for="type">
                Tipo
                <i class="fa-solid fa-circle-exclamation text-danger mr-1"></i>
              </label>
              <v-select
                id="type"
                v-model="structure.type"
                placeholder="Selecionar tipo de estrutura"
                :options="types"
                label="name"
              >
                <template v-slot:no-options="{ search, searching }">
                  <template v-if="searching">
                    Nenhum resultado encontrado para <em>{{ search }}</em
                    >.
                  </template>
                  <em
                    v-else
                    style="opacity: 0.5"
                    >Comece a digitar para pesquisar</em
                  >
                </template>
              </v-select>
              <div
                v-if="v$.structure.type.$error"
                class="text-danger mt-1 ml-1"
              >
                {{ v$.structure.type.$errors[0].$message }}
              </div>
            </div>
          </div>
          <div class="col-md-8">
            <div class="form-group">
              <label for="subordinate">Subordinado a</label>
              <v-select
                id="subordinate"
                v-model="structure.subordinate"
                placeholder="Selecionar subordinado"
                :options="filteredSubordinates"
                label="structure"
                @search="getSubordinates"
              >
                <template v-slot:no-options="{ search, searching }">
                  <template v-if="searching">
                    Nenhum resultado encontrado para <em>{{ search }}</em
                    >.
                  </template>
                  <em
                    v-else
                    style="opacity: 0.5"
                    >Comece a digitar para pesquisar</em
                  >
                </template>
              </v-select>
            </div>
          </div>
        </div>
        <div class="form-row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="positions">
                Cargos disponíveis para a estrutura
              </label>
              <div class="d-flex align-items-center">
                <v-select
                  id="positions"
                  v-model="selectedPosition"
                  placeholder="Selecionar cargos"
                  :options="searching ? positions : filteredPositions"
                  label="name"
                  @search="getPositions"
                >
                  <template v-slot:no-options="{ search, searching }">
                    <template v-if="searching">
                      Nenhum resultado encontrado para <em>{{ search }}</em
                      >.
                    </template>
                    <em
                      v-else
                      style="opacity: 0.5"
                      >Comece a digitar para pesquisar</em
                    >
                  </template>
                </v-select>
                <button
                  class="btn btn-primary btn-custom"
                  @click="checkPositionBeforeAdd(selectedPosition)"
                  title="Adicionar"
                  aria-label="Adicionar"
                >
                  <i class="fa fa-plus"></i>
                </button>
              </div>
            </div>
          </div>
          <div class="col-md-6 mt-md-2">
            <div
              class="d-flex align-items-center justify-content-md-end mb-2 mb-md-0 mt-md-4"
            >
              <button
                class="btn btn-link custom-text-danger"
                @click="cleanPositionsModal"
              >
                <i class="far fa-trash-can mr-2"></i>Limpar cargos
              </button>
            </div>
          </div>
        </div>
        <div class="row align-items-center mb-3">
          <div class="col-md-auto d-flex flex-wrap text-uppercase">
            <span
              v-for="position in structure.positions"
              :key="position.id"
              class="badge-custom py-1 px-2 rounded mr-1 mt-2"
              :class="
                position.is_manager
                  ? 'bg-warning text-dark'
                  : 'bg-primary text-white'
              "
            >
              <i
                role="button"
                class="fa fa-times cursor-pointer mr-2"
                @click="removePosition(position.id)"
                title="Remover"
                aria-label="Remover"
              ></i>
              {{ position.name }}
              <i
                v-if="position.is_manager"
                class="fa fa-star ml-2"
              ></i>
            </span>
          </div>
        </div>
      </div>
      <LFLoading :isLoading="isLoading"></LFLoading>
      <ConfirmModal
        title="Confirmar limpeza de cargos"
        actionLabel="Limpar"
        :message="confirmMessage"
        @action="cleanPositions"
      ></ConfirmModal>
      <PositionSuggestionModal
        :structureName="structure.structure"
        :position="positionSuggestion"
        @acceptSuggestion="addPositions"
      ></PositionSuggestionModal>
    </Modal>
  </div>
</template>

<script>
  import Modal from "@/components/Modal.vue";
  import ConfirmModal from "@/components/ConfirmModal.vue";
  import PositionSuggestionModal from "@/components/Structure/PositionSuggestionModal.vue";
  import LFLoading from "@/components/LFLoading.vue";
  import vSelect from "vue-select";
  import "vue-select/dist/vue-select.css";
  import { useVuelidate } from "@vuelidate/core";
  import {
    required,
    minLength,
    maxLength,
    helpers,
  } from "@vuelidate/validators";

  import { ajax, getStrings, toastSuccess, toastError } from "@/helpers/moodle";

  export default {
    name: "StructureCreate",
    components: {
      Modal,
      ConfirmModal,
      PositionSuggestionModal,
      LFLoading,
      vSelect,
    },
    data() {
      return {
        selectedPosition: null,
        positions: [],
        types: [],
        subordinates: [],
        structure: {
          id: null,
          structure: "",
          type: [],
          subordinate: [],
          positions: [],
        },
        strings: {},
        isLoading: false,
        searching: false,
        confirmMessage: "",
        positionSuggestion: null,
        errors: [],
      };
    },
    async created() {
      let stringArray = [
        {
          key: "editposition",
          component: "local_hierarchy",
        },
      ];

      this.strings = await getStrings(stringArray);
    },
    validations() {
      return {
        structure: {
          structure: {
            required: helpers.withMessage(
              "O nome da estrutura é obrigatório!",
              required
            ),
            maxLength: helpers.withMessage(
              "O nome da estrutura é muito longo! Máximo de 40 caracteres.",
              (value) => value.length <= 40
            ),
          },
          type: {
            required: helpers.withMessage(
              "O tipo da estrutura é obrigatório!",
              required
            ),
            minLength: helpers.withMessage(
              "Selecione um tipo válido!",
              minLength(1)
            ),
          },
        },
      };
    },
    setup: () => ({ v$: useVuelidate() }),
    computed: {
      filteredPositions() {
        if (!this.positions.length) return []; // Evita erro antes da busca
        return this.positions.filter(
          (position) =>
            !this.structure.positions?.some((p) => p.id === position.id)
        );
      },

      filteredSubordinates() {
        // Remove a propria estrutura da lista de subordinadas
        if (!this.subordinates.length) return []; // Evita erro antes da busca
        return this.subordinates.filter(
          (subordinate) => subordinate.id !== this.structure.id
        );
      },
    },
    mounted() {
      this.getTypes();
      this.getSubordinates();
      this.getPositions();
      $("#items-modal").modal("show");
    },
    methods: {
      closeModal() {
        $("#items-modal").modal("hide");
        this.$router.push("/structures");
      },
      async getTypes(loading) {
        if (loading) loading(true);
        try {
          const response = await ajax("local_hierarchy_fetch_structure_types");
          this.types = response.types;
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          if (loading) loading(false);
        }
      },
      async getSubordinates(
        search = "",
        loading,
        page = 1,
        size = 50,
        sort = [{ field: "name", direction: "ASC" }]
      ) {
        if (search && search.length < 3) return;
        this.searching = true;
        if (loading) loading(true);
        try {
          const response = await ajax("local_hierarchy_fetch_structures", {
            search: search,
            status: 0,
            page: page,
            perpage: size,
            sort: sort,
          });

          this.subordinates = [
            { id: 0, structure: "Nenhum subordinado selecionado" },
            ...response.items,
          ];

          this.structure.subordinate = this.subordinates[0];
        } catch (error) {
          console.error(error);
        } finally {
          this.searching = false;
          if (loading) loading(false);
        }
      },
      async getPositions(
        search = "",
        loading,
        page = 1,
        size = 50,
        sort = [{ field: "name", direction: "ASC" }]
      ) {
        if (search && search.length < 3) return;
        this.searching = true;
        if (loading) loading(true);
        try {
          const response = await ajax("local_hierarchy_fetch_positions", {
            search: search,
            status: 0,
            onlymanagers: false,
            page: page,
            perpage: size,
            sort: sort,
          });
          if (search) {
            // Se for uma busca, apenas sobrescreve positions
            this.positions = response.items;
          } else {
            // Se for carregamento inicial, mescla sem duplicar
            this.positions = [
              ...new Set([...this.positions, ...response.items]),
            ];
          }
        } catch (error) {
          console.error(error);
        } finally {
          this.searching = false;
          if (loading) loading(false);
        }
      },
      addPositions(positions) {
        if (!this.structure.positions) {
          this.structure.positions = [];
        }

        positions = Array.isArray(positions) ? positions : [positions];

        positions.forEach((position) => {
          // Evitar adicionar cargos duplicados
          if (!this.structure.positions.some((p) => p.id === position.id)) {
            this.structure.positions.push({
              id: position.id,
              name: position.name,
              is_manager: position.is_manager,
            });
          }
        });

        this.selectedPosition = null;
      },
      async checkPositionBeforeAdd(position) {
        this.isLoading = true;
        try {
          const response = await ajax("local_hierarchy_get_position", {
            id: position.id,
            include_subordinates: true,
          });

          if (!response.item.subordinates.length > 0) {
            this.addPositions(position);
          } else {
            this.positionSuggestion = response.item;
            $("#position-suggestion-modal").modal("show");
          }
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          this.isLoading = false;
        }
      },
      removePosition(positionId) {
        this.structure.positions = this.structure.positions.filter(
          (p) => p.id !== positionId
        );
      },
      cleanPositionsModal(structure) {
        this.confirmMessage =
          "Você tem certeza de que deseja limpar os cargos selecionados?";

        $("#confirm-modal").modal("show");
      },
      cleanPositions() {
        this.structure.positions = [];
      },
      async saveStructure() {
        this.v$.$validate();

        if (this.v$.$error) {
          toastError("Por favor, corrija os erros antes de salvar.");
          return;
        }

        this.isLoading = true;

        try {
          const payload = {
            id: this.structure.id,
            name: this.structure.structure,
            typeid: this.getTypeId(),
            parentid: this.getParentId(),
            positions: this.structure.positions.map((position) => ({
              id: position.id,
              name: position.name,
            })),
          };

          const response = await ajax("local_hierarchy_save_structure", {
            data: payload,
          });

          if (response.success) {
            this.$emit("update-items");
            this.closeModal();
            toastSuccess(response.message);
          } else {
            this.errors = response.errors;
            toastError("Por favor, corrija os erros antes de salvar.");
          }
        } catch (error) {
          console.error(error);
          toastError(error.message);
        } finally {
          this.isLoading = false;
        }
      },
      getError(field) {
        if (!this.errors || this.errors.length === 0) return null;

        const error = this.errors.find((e) => e.field === field);
        return error ? error.message : null;
      },
      // Método para limpar o erro de um campo específico
      clearError(field) {
        if (!this.errors || this.errors.length === 0) return;

        const index = this.errors.findIndex((e) => e.field === field);
        if (index !== -1) {
          // Remove o erro do array
          this.errors.splice(index, 1);
          // Ou, se preferir apenas limpar a mensagem:
          // this.errors[index].message = '';
        }
      },
      getTypeId() {
        if (!this.structure.type) return null;

        if (Array.isArray(this.structure.type)) {
          return this.structure.type.length > 0
            ? this.structure.type[0].id
            : null;
        }

        return this.structure.type.id || null;
      },

      getParentId() {
        if (!this.structure.subordinate) return null;

        if (Array.isArray(this.structure.subordinate)) {
          if (this.structure.subordinate.length === 0) return null;
          // Se o id for 0, retorna null
          return this.structure.subordinate[0].id === 0
            ? null
            : this.structure.subordinate[0].id;
        }

        // Se o id for 0, retorna null
        return this.structure.subordinate.id === 0
          ? null
          : this.structure.subordinate.id || null;
      },
    },
  };
</script>
<style lang="scss">
  #structure-create {
    .is-invalid {
      border-color: #dc3545 !important;
    }

    label:not(.form-check label) {
      font-size: 0.875rem;
    }

    p,
    .form-check-label {
      font-size: 1rem;
    }

    // vue-select
    .v-select {
      display: block;
      width: 100%;
      height: calc(1.5em + 0.75rem + 2px);
      font-size: 0.9375rem;
      font-weight: 400;
      line-height: 1.5;
      color: #dee2e6 !important;
      background-color: #212529 !important;
      border: 1px solid #495057 !important;
      border-radius: 0.5rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

      &:focus-within {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.2rem
          color-mix(in srgb, var(--primary) 80%, white 20%) !important;
      }
    }

    #positions.v-select {
      border-radius: 0.5rem 0 0 0.5rem !important;
    }

    .vs__dropdown-menu {
      margin-top: 5px;
    }

    .vs__dropdown-option:hover {
      background-color: #495057 !important;
      color: white !important;
    }

    .vs__dropdown-option--highlight {
      background-color: #495057 !important;
      color: white !important;
    }

    .vs__selected-options {
      padding: 0 6px !important;
    }

    .vs__selected {
      background-color: transparent !important;
      color: #dee2e6 !important;
      line-height: unset !important;
    }

    .vs__actions {
      padding: 4px 2px 0 3px !important;
    }

    .vs__open-indicator {
      fill: #dee2e6 !important;
      transform: scale(0.7);
    }

    .vs__search {
      font-size: 0.9375rem !important;
    }

    .btn-custom {
      border-radius: 0 0.5rem 0.5rem 0 !important;
      box-shadow: unset !important;
    }

    .badge-custom {
      font-size: 0.75rem !important;
      font-weight: 700 !important;
    }

    .text-dark {
      color: #343a40 !important;
    }

    .custom-text-danger {
      color: var(--danger) !important;
    }
  }
</style>
