<template>
  <!-- local/hierarchy/app/src/views/Upload.vue -->
  <div
    id="process-upload"
    class="container-fluid"
  >
    <div class="row mb-4">
      <div class="col-12 mb-1">
        <h2 class="m-0">{{ titlePage }}</h2>
      </div>
      <div class="col-12">
        <h5 class="m-0 text-muted">{{ subTitlePage }}</h5>
      </div>
    </div>
    <h5 class="text-uppercase text-primary my-3">Resumo da carga</h5>
    <div class="flexible-wrap">
      <div class="table-responsive">
        <table
          class="generaltable boxaligncenter"
          id="uupreview"
        >
          <thead>
            <tr>
              <th
                v-for="(header, index) in headers"
                :key="index"
                class="text-uppercase header c0"
                style=""
                scope="col"
              >
                {{ header }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, itemIndex) in items"
              :key="itemIndex"
              class=""
            >
              <td
                v-for="(header, headerIndex) in headers"
                :key="headerIndex"
                class="cell"
                :class="[
                  'c' + headerIndex,
                  header.toLowerCase() === 'cargo' ? 'text-nowrap' : '',
                ]"
              >
                <template v-if="header.toLowerCase() === 'status'">
                  <span
                    class="badge p-2"
                    :class="'badge-' + item.status_color"
                  >
                    {{ item.status }}
                  </span>
                </template>
                <template v-else-if="header.toLowerCase() === 'linha do csv'">
                  {{ item.linha_csv }}
                </template>
                <template v-else>
                  {{ item[header.toLowerCase()] || "-" }}
                </template>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="form-group row mt-4">
      <div class="col form-inline justify-content-center align-items-center">
        <button
          class="btn btn-secondary ml-3"
          @click="goBack"
        >
          Voltar
        </button>
      </div>
    </div>
  </div>
  <LFLoading :isLoading="isLoading" />
</template>

<script>
  import { useUploadStore } from "@/stores/uploadStore";
  import Tooltip from "@/components/Tooltip.vue";
  import LFLoading from "@/components/LFLoading.vue";

  import { ajax, getStrings, toastSuccess, toastError } from "@/helpers/moodle";

  export default {
    name: "UploadView",
    data() {
      return {
        strings: {},
        headers: [],
        items: [],
        params: {},
        error: null,
        entity: "",
        titlePage: "",
        subTitlePage: "",
        isLoading: true,
      };
    },
    components: {
      Tooltip,
      LFLoading,
    },
    created() {
      this.loadProcessData();
    },
    mounted() {
      //
    },
    computed: {},
    methods: {
      loadProcessData() {
        const uploadStore = useUploadStore();
        const processData = uploadStore.processData;
        this.entity = uploadStore.entity;
        this.titlePage = uploadStore.titlePage;
        this.subTitlePage = uploadStore.subTitlePage;

        if (!processData) {
          this.error = "Nenhum dado encontrado para processamento.";
          this.isLoading = false;
          return;
        }

        // Preenche os dados do preview
        this.items = processData.items;
        this.headers = processData.headers;
        this.params = processData.params;

        if (processData.error) {
          this.error = processData.error;
        }

        this.isLoading = false; // Finaliza o carregamento
      },
      goBack() {
        this.$router.push({
          path: "/" + this.entity,
        });
      },
    },
  };
</script>

<style lang="scss">
  //#process-upload {}
</style>
