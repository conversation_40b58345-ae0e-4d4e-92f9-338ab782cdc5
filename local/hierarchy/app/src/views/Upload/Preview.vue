<template>
  <!-- local/hierarchy/app/src/views/Upload.vue -->
  <div
    id="preview-upload"
    class="container-fluid"
  >
    <div class="row mb-4">
      <div class="col-12 mb-1">
        <h2 class="m-0">{{ titlePage }}</h2>
      </div>
      <div class="col-12">
        <h5 class="m-0 text-muted">{{ subTitlePage }}</h5>
      </div>
    </div>
    <h5 class="text-uppercase text-primary my-3">Opções de upload</h5>
    <div
      v-if="error"
      class="alert alert-danger mt-3"
    >
      {{ error }}
    </div>
    <div
      v-if="items.length > 0"
      class="flexible-wrap"
    >
      <div class="table-responsive">
        <table
          class="generaltable boxaligncenter"
          id="uupreview"
        >
          <thead>
            <tr>
              <th
                v-for="(header, index) in headers"
                :key="index"
                class="text-uppercase header c0"
                style=""
                scope="col"
              >
                {{ header }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, itemIndex) in items"
              :key="itemIndex"
              class=""
            >
              <td
                v-for="(header, headerIndex) in headers"
                :key="headerIndex"
                class="cell"
                :class="[
                  'c' + headerIndex,
                  header.toLowerCase() === 'cargo' ? 'text-nowrap' : '',
                ]"
              >
                <template v-if="header.toLowerCase() === 'status'">
                  <span
                    class="badge p-2"
                    :class="'badge-' + item.status_color"
                  >
                    {{ item.status }}
                  </span>
                </template>
                <template v-else-if="header.toLowerCase() === 'linha do csv'">
                  {{ item.linha_csv }}
                </template>
                <template v-else>
                  {{ item[header.toLowerCase()] || "-" }}
                </template>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div
      v-if="items.length > 0"
      class="form-group row mt-4"
    >
      <div class="col-md-3 col-form-label d-flex pb-0 pr-md-0">
        <p class="mb-0 d-inline">Tipo de transmissão</p>
      </div>
      <div class="col-md-9 form-inline align-items-center">
        <select
          class="custom-select"
          v-model="selectedTransmissionType"
        >
          <option
            v-for="transmissionType in transmissionsTypes"
            :key="transmissionType.key"
            :value="transmissionType.key"
          >
            {{ transmissionType.label }}
          </option>
        </select>
      </div>
    </div>
    <div class="form-group row mt-4">
      <div class="col-md-3 col-form-label d-flex pb-0 pr-md-0">
        <p class="mb-0 d-inline"></p>
      </div>
      <div
        v-if="items.length > 0"
        class="col-md-9 form-inline align-items-center"
      >
        <button
          v-if="isLoading"
          class="btn btn-primary"
          :disabled="true"
        >
          <span
            class="spinner-border spinner-border-sm"
            role="status"
            aria-hidden="true"
          ></span>
          Salvando...
        </button>
        <button
          v-else
          class="btn btn-primary"
          @click="goToProcess"
          :disabled="hasError"
        >
          Salvar
        </button>
        <button
          class="btn btn-secondary ml-3"
          @click="goBack"
        >
          Cancelar
        </button>
      </div>
    </div>
    <div
      v-if="error"
      class="form-group row"
    >
      <div class="col justify-content-center form-inline align-items-center">
        <button
          class="btn btn-secondary"
          @click="goBack"
        >
          Voltar
        </button>
      </div>
    </div>
  </div>
  <LFLoading :isLoading="isLoading" />
</template>

<script>
  import Tooltip from "@/components/Tooltip.vue";
  import LFLoading from "@/components/LFLoading.vue";
  import { useUploadStore } from "@/stores/uploadStore";
  import { ajax, getStrings, toastSuccess, toastError } from "@/helpers/moodle";

  export default {
    name: "UploadPreviewView",
    data() {
      return {
        strings: {},
        headers: [],
        items: [],
        selectedTransmissionType: 1,
        params: {},
        error: null,
        isLoading: true,
        entity: "",
        titlePage: "",
        subTitlePage: "",
      };
    },
    components: {
      Tooltip,
      LFLoading,
    },
    created() {
      this.loadPreviewData();
    },
    mounted() {
      //
    },
    computed: {
      hasError() {
        // Verifica se existe algum elemento no array items que tenha status_color igual a 'danger'
        return this.items.some((item) => item.status_color === "danger");
      },
      transmissionsTypes() {
        const labels = {
          positions: {
            create: "Cadastrar cargos",
            update: "Atualizar cargos",
          },
          structures: {
            create: "Cadastrar estruturas",
            update: "Atualizar estruturas",
          },
        };

        const entityLabels = labels[this.entity] || labels.positions;

        return [
          { key: 1, label: entityLabels.create },
          { key: 2, label: entityLabels.update },
        ];
      },
    },
    methods: {
      loadPreviewData() {
        const uploadStore = useUploadStore();
        const previewData = uploadStore.previewData;
        this.entity = uploadStore.entity;
        this.titlePage = uploadStore.titlePage;
        this.subTitlePage = uploadStore.subTitlePage;

        if (!previewData) {
          this.error = "Nenhum dado encontrado para pré-visualização.";
          this.isLoading = false;
          return;
        }

        // Preenche os dados do preview
        this.items = previewData.items;
        this.headers = previewData.headers;
        this.params = previewData.params;
        this.params.file = uploadStore.base64Content;

        if (previewData.error) {
          this.error = previewData.error;
        }

        this.isLoading = false; // Finaliza o carregamento
      },
      async goToProcess() {
        try {
          // Exibe o estado de carregamento
          this.isLoading = true;

          // Envia os dados para a API
          const response = await ajax(
            "local_hierarchy_process_upload_" + this.entity,
            {
              file: this.params.file, // Conteúdo do arquivo codificado em Base64
              delimiter: this.params.delimiter,
              encoding: this.params.encoding,
              lines: this.params.lines,
              action: this.selectedTransmissionType,
            }
          );

          // Salva os dados processados na store
          const uploadStore = useUploadStore();
          uploadStore.setProcessData(response);

          // Navega para a rota de preview
          this.$router.push({
            name: "hierarchy.upload.process",
            params: {
              entity: this.entity,
            },
          });
        } catch (error) {
          console.error("Erro ao enviar o arquivo:", error);
          toastError("Erro ao enviar o arquivo. Por favor, tente novamente.");
        } finally {
          // Oculta o estado de carregamento
          this.isLoading = false;
        }
      },
      goBack() {
        this.$router.go(-1);
      },
    },
  };
</script>

<style lang="scss">
  //#preview-upload {}
</style>
