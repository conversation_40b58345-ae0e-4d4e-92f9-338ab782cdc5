<template>
  <div
    id="hierarchy-upload"
    class="container-fluid"
  >
    <div class="row mb-4">
      <div class="col-12 mb-1">
        <h2 class="m-0">{{ titlePage }}</h2>
      </div>
      <div class="col-12">
        <h5 class="m-0 text-muted">{{ subTitlePage }}</h5>
      </div>
    </div>
    <h5 class="text-uppercase text-primary my-3">Geral</h5>

    <div class="row mb-3">
      <div class="col-12 col-md-auto mb-3 mb-md-0">
        Exemplo de arquivo de texto
        <Tooltip
          v-if="tooltipContent"
          :tooltipContent="tooltipContent"
          tooltipTitle="Ajuda"
        />
      </div>
      <div class="col-12 col-md-auto">
        <!-- passar url ../entity/example.csv onde entity é dinamico carregado no data -->
        <a
          :href="`../../examples/${entity}/example.csv`"
          class="text-primary"
        >
          example.csv
        </a>
      </div>
    </div>
    <div class="row mb-3">
      <div class="col-12">
        <label for="file"
          >Arquivo <i class="fa-solid fa-circle-exclamation text-danger"></i
        ></label>
        <div
          class="d-flex flex-column align-items-center dropzone"
          :class="{ 'dropzone-active': isDragging }"
          @dragover.prevent="onDragOver"
          @dragleave.prevent="onDragLeave"
          @drop="onDrop"
          @click="triggerFileInput"
        >
          <template v-if="!file">
            <i class="fa-regular fa-circle-down"></i>
            <p class="text-center mt-3 px-2">
              Você pode arrastar e soltar o arquivo aqui para adicioná-lo.
            </p>
          </template>
          <template v-else>
            <i class="fa-regular fa-file-lines"></i>
            <p class="text-center mt-3 px-2">
              <strong>{{ file.name }}</strong>
              <br />
              <span class="file-size">({{ fileSize }})</span>
              <br />
              <small class="text-muted"
                >Clique ou arraste outro arquivo para substituir</small
              >
            </p>
          </template>
        </div>
        <div class="input-group mt-2">
          <label
            class="btn btn-dark w-100 w-md-auto"
            for="file"
            accept=".csv"
            >Escolher um arquivo...</label
          >
          <input
            type="file"
            id="file"
            class="d-none"
            @change="onFileChange"
          />
        </div>
      </div>
    </div>
    <div class="row mb-3">
      <div class="col-12 col-md-4 mb-2">
        <label for="delimiter">Delimitador CSV</label>
        <select
          class="form-control"
          id="delimiter"
          v-model="selectedDelimiter"
        >
          <option
            v-for="(delimiter, index) in delimiters"
            :key="index"
            :value="delimiter"
          >
            {{ delimiter }}
          </option>
        </select>
      </div>
      <div class="col-12 col-md-4 mb-2">
        <label for="encoding">Codificação</label>
        <select
          class="form-control"
          id="encoding"
          v-model="selectedEncoding"
        >
          <option
            v-for="encoding in encodings"
            :key="encoding.id"
            :value="encoding.name"
          >
            {{ encoding.name }}
          </option>
        </select>
      </div>
      <div class="col-12 col-md-4">
        <label for="lines">Linhas de pré-visualização</label>
        <select
          class="form-control"
          id="lines"
          v-model="selectedLines"
        >
          <option
            v-for="(line, index) in lines"
            :key="index"
            :value="line"
          >
            {{ line }}
          </option>
        </select>
      </div>
    </div>
    <div
      class="d-flex flex-wrap align-items-center justify-content-between mb-3 py-3 border-top"
    >
      <p class="m-0">
        Este formulário contém campos obrigatórios marcados com
        <i class="fa-solid fa-circle-exclamation text-danger"></i>
      </p>
      <div class="d-flex align-items-center w-100 w-md-auto mt-2 mt-md-0">
        <button
          v-if="isLoading"
          type="button"
          class="btn btn-primary w-100 w-md-auto mr-2"
          :disabled="true"
        >
          <span
            class="spinner-border spinner-border-sm"
            role="status"
            aria-hidden="true"
          ></span>
          Carregando...
        </button>
        <button
          v-else
          type="button"
          class="btn btn-primary w-100 w-md-auto mr-2"
          @click="goToPreview"
          :disabled="!file"
        >
          Cadastrar
        </button>
        <button
          type="button"
          class="btn btn-dark w-100 w-md-auto"
          @click="cancelUpload"
        >
          Cancelar
        </button>
      </div>
    </div>
  </div>
  <LFLoading :isLoading="isLoading" />
</template>

<script>
  import Tooltip from "@/components/Tooltip.vue";
  import { TOOLTIPS } from "@/helpers/tooltips";
  import { useUploadStore } from "@/stores/uploadStore";
  import LFLoading from "@/components/LFLoading.vue";
  import { ajax, getStrings, toastSuccess, toastError } from "@/helpers/moodle";

  export default {
    name: "UploadView",
    data() {
      return {
        isLoading: false,
        strings: {},
        delimiters: [",", ";", ":", "\\t"],
        encodings: [],
        lines: [10, 20, 100, 1000],
        selectedDelimiter: ",",
        selectedEncoding: "UTF-8",
        selectedLines: 10,
        file: null,
        isDragging: false,
        tooltipContent: "",
        titlePage: "",
        subTitlePage: "",
        entity: "",
      };
    },
    components: {
      Tooltip,
      LFLoading,
    },
    created() {
      const uploadStore = useUploadStore();
      this.titlePage = uploadStore.titlePage;
      this.subTitlePage = uploadStore.subTitlePage;
      this.entity = uploadStore.entity;
      this.loadTooltipContent();
      this.getEncondings();
    },
    computed: {
      fileSize() {
        if (!this.file) return "";
        const sizeInKB = this.file.size / 1024;
        if (sizeInKB < 1024) {
          return `${sizeInKB.toFixed(2)} KB`;
        }
        return `${(sizeInKB / 1024).toFixed(2)} MB`;
      },
    },
    methods: {
      loadTooltipContent() {
        // Carrega o tooltip com base na entidade
        this.tooltipContent =
          TOOLTIPS[this.entity] || "Texto tooltip não encontrado!";
      },

      // Função para lidar com o arrastar de arquivos sobre o dropzone
      onDragOver() {
        this.isDragging = true;
      },
      // Função para lidar com a saída do arrastar de arquivos do dropzone
      onDragLeave() {
        this.isDragging = false;
      },
      // Função para lidar com o soltar de arquivos no dropzone
      onDrop(event) {
        event.preventDefault();
        this.isDragging = false;
        const file = event.dataTransfer.files[0];
        this.validateAndSetFile(file);
      },
      // Função para abrir o seletor de arquivos ao clicar no dropzone ou botão
      triggerFileInput() {
        document.getElementById("file").click();
      },
      // Função para lidar com a seleção de arquivos via input
      onFileChange(event) {
        const file = event.target.files[0];
        this.validateAndSetFile(file);
      },
      // Função para validar e definir o arquivo
      validateAndSetFile(file) {
        if (file && file.type === "text/csv") {
          this.file = file;
        } else {
          toastError("Por favor, selecione um arquivo CSV válido.");
        }
      },
      // Função para enviar o arquivo para o backend e ir para a rota de preview
      async goToPreview() {
        if (!this.file) {
          toastError("Por favor, selecione um arquivo CSV válido.");
          return;
        }

        const reader = new FileReader();

        reader.onload = (e) => {
          const fileContent = e.target.result; // Conteúdo do arquivo como texto
          const base64Content = window.btoa(
            encodeURIComponent(fileContent).replace(
              /%([0-9A-F]{2})/g,
              (match, p1) => String.fromCharCode(parseInt(p1, 16))
            )
          );

          // Envia os dados para a API
          this.sendToApi(base64Content);
        };

        reader.onerror = (e) => {
          console.error("Erro ao ler o arquivo:", e.target.error);
          toastError("Erro ao ler o arquivo. Por favor, tente novamente.");
        };

        reader.readAsText(this.file); // Lê o arquivo como texto
      },
      async sendToApi(base64Content) {
        try {
          // Exibe o estado de carregamento
          this.isLoading = true;

          // Envia os dados para a API
          const response = await ajax(
            "local_hierarchy_preview_upload_" + this.entity,
            {
              file: base64Content, // Conteúdo do arquivo codificado em Base64
              delimiter: this.selectedDelimiter,
              encoding: this.selectedEncoding,
              lines: this.selectedLines,
            }
          );

          // Salva os dados processados na store
          const uploadStore = useUploadStore();
          uploadStore.setPreviewData(response, base64Content);

          // Navega para a rota de preview
          this.$router.push({
            name: "hierarchy.upload.preview",
            params: {
              entity: this.entity,
            },
          });
        } catch (error) {
          console.error("Erro ao enviar o arquivo:", error);
          toastError("Erro ao enviar o arquivo. Por favor, tente novamente.");
        } finally {
          // Oculta o estado de carregamento
          this.isLoading = false;
        }
      },
      cancelUpload() {
        this.$router.push({ path: "/" + this.entity });
      },
      async getEncondings() {
        this.encodings = await ajax(
          "local_hierarchy_get_encodings_options",
          {}
        );
      },
    },
  };
</script>

<style lang="scss">
  #hierarchy-upload {
    .dropzone {
      background: linear-gradient(
        270.69deg,
        rgba(52, 58, 64, 0.75) 0%,
        rgba(52, 58, 64, 0) 100%
      );
      border-radius: 0.3rem;
      border: 1px dashed #ffffff4d;
      padding: 2.5rem 0;
      color: rgba(255, 255, 255, 0.65);
      font-size: 1rem;
      font-weight: 700;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &.dropzone-active {
        background-color: rgba(52, 58, 64, 0.5);
      }

      i {
        font-size: 3rem;
      }
    }

    .file-info {
      color: #fff;
      font-size: 0.9rem;
    }

    @media (min-width: 768px) {
      .w-md-auto {
        width: auto !important;
      }
    }
  }
</style>
