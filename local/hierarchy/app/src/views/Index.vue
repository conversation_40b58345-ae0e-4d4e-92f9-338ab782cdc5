<template>
  <div
    class="d-flex flex-column flex-md-row justify-content-between align-items-stretch align-items-md-center mb-5"
  >
    <div class="d-flex align-items-center">
      <h2 class="m-0">{{ strings.pluginname }}</h2>
    </div>
  </div>
  <nav>
    <div v-if="isLargeScreen">
      <ul>
        <li
          v-for="item in items"
          :key="item.label"
        >
          <span
            v-if="item.isDisabled"
            :class="$route.name === item.name && 'router-link-active'"
            :data-toggle="!($route.name === item.name) && 'popover'"
            data-container="body"
            data-placement="top"
            :data-content="`<div class='no-overflow'><p class='text-center mb-0 font-size-15'>É necessário salvar as alterações para trocar de aba</p></div>`"
            :aria-label="'É necessário salvar as alterações para trocar de aba'"
            data-html="true"
            tabindex="0"
            data-trigger="focus"
            role="button"
          >
            <i :class="'fa fa-' + item.icon"></i>
            {{ item.label }}
          </span>
          <router-link
            v-else
            :to="{ name: item.name, params: item.params }"
            :class="$route.name === item.name && 'router-link-active'"
          >
            <i :class="'fa  fa-' + item.icon"></i>
            {{ item.label }}
          </router-link>
        </li>
      </ul>
    </div>
    <div v-else>
      <select
        class="custom-select"
        v-model="selectedRoute"
      >
        <option
          v-for="item in items"
          :key="item.label"
          :value="item.name"
          :disabled="item.isDisabled"
        >
          {{ item.label }}
        </option>
      </select>
    </div>
  </nav>
  <div>
    <router-view />
  </div>
</template>

<script>
  import { mapState } from "vuex";
  import {
    ajax,
    getStrings,
    getURLParams,
    getDynamicForm,
    createMoodleModal,
    toastSuccess,
    toastError,
  } from "@/helpers/moodle";

  export default {
    name: "IndexView",
    data() {
      return {
        strings: {},
        loading: true,
        selectedRoute: "",
        isLargeScreen: true,
        items: [],
      };
    },
    components: {},
    async created() {
      let stringArray = [
        {
          key: "pluginname",
          component: "local_hierarchy",
        },
        {
          key: "position_plural",
          component: "local_hierarchy",
        },
        {
          key: "structures",
          component: "local_hierarchy",
        },
        {
          key: "setting_plural",
          component: "local_hierarchy",
        },
      ];

      let strings = await getStrings(stringArray);

      this.strings = strings;

      this.setRouteLinks();
      this.checkScreenSize(window.innerWidth);
      window.addEventListener("resize", this.handleResize);
      this.selectedRoute = this.$route.name;
    },
    beforeUnmount() {
      window.removeEventListener("resize", this.handleResize);
    },
    computed: {
      // ...mapState({
      //     screenWidth: (state) => state.screenWidth
      // }),
    },
    watch: {
      screenWidth(val) {
        this.checkScreenSize(val);
      },
      selectedRoute(newValue) {
        if (newValue) {
          this.$router.push({ name: newValue });
        }
      },
    },
    methods: {
      checkScreenSize(width) {
        this.isLargeScreen = width >= 768; // MD breakpoint
      },
      handleResize() {
        this.checkScreenSize(window.innerWidth);
      },
      setRouteLinks() {
        this.items = this.getCreateRoutes();
      },
      getCreateRoutes() {
        return [
          {
            label: this.strings.position_plural,
            name: "hierarchy.positions",
            params: {},
            icon: "briefcase",
          },
          {
            label: this.strings.structures,
            name: "hierarchy.structures",
            params: {},
            icon: "users",
          },
          // {
          //     label: this.strings.setting_plural,
          //     name: 'hierarchy.settings',
          //     params: {},
          //     icon: 'cog'
          // },
        ];
      },
    },
  };
</script>

<style scoped lang="scss">
  #hierarchy-app {
    nav {
      background-color: transparent;
      padding: 10px 0;
      border-bottom: 1px solid #adb5bd;
      margin-bottom: 30px;
    }

    ul {
      padding: 0;
      margin: 0;
      list-style: none;
      display: flex;
      align-items: center;
    }

    a,
    span {
      text-decoration: none;
      color: #ffffffa6;
      font-size: 16px;
      font-weight: 500;
      position: relative;
      padding: 5px 16px;
      transition: all 0.3s;
      display: flex;
      gap: 10px;
      align-items: center;
      box-shadow: unset;

      &.router-link-active {
        color: #fff;

        &::after {
          background-color: var(--primary);
          content: "";
          position: absolute;
          bottom: -11px;
          left: 50%;
          transform: translateX(-50%);
          width: 100%;
          height: 4px;
          transition: background-color 0.3s;
        }
      }

      .icon {
        font-size: 20px;
        margin: 0;
        height: 20px;
        width: auto;
      }
    }

    .custom-select {
      background-color: #212529;
      color: #fff;
      padding: 8px;
      border-radius: 4px;
      width: 100%;
      height: auto;
    }
  }
</style>
