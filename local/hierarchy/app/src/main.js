import { createApp } from 'vue';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import App from './App.vue';
import router from './router';

const init = (selector) => {
    const app = createApp(App);
    const pinia = createPinia();

    pinia.use(piniaPluginPersistedstate);

    app.use(pinia);
    app.use(router);
    app.mount(selector);
};

export default {
    init,
};