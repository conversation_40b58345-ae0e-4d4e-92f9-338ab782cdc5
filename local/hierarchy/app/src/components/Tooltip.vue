<template>
  <a
    class="btn btn-link py-0 px-1"
    role="button"
    data-container="body"
    data-toggle="popover"
    data-placement="right"
    data-html="true"
    tabindex="0"
    data-trigger="focus"
    aria-label=""
    data-original-title=""
    :data-content="'<div>' + tooltipContent + '</div>'"
  >
    <i
      class="far fa-question-circle text-primary"
      :title="tooltipTitle"
      :aria-label="tooltipTitle"
    ></i>
  </a>
</template>

<script>
  export default {
    props: {
      tooltipContent: {
        type: String,
        required: true,
      },
      tooltipTitle: {
        type: String,
        default: "Ajuda",
      },
    },
  };
</script>
