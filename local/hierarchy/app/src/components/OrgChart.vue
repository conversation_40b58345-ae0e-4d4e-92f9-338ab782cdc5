<template>
  <div
    id="tree"
    ref="tree"
  ></div>
</template>

<script>
  //import Org<PERSON>hart from "@balkangraph/orgchart.js";
  import Org<PERSON>hart from "@/libs/orgchart.js";

  export default {
    name: "Org<PERSON><PERSON>",
    props: {
      data: {
        type: Array,
        required: true,
      },
    },
    data() {
      return {
        chart: null,
        nodes: this.data,
        lastFocusedElement: null,
        focusHandler: null,
        lastFocusedElementId: null,
        isLayoutToolbarHidden: true,
      };
    },
    watch: {
      data: {
        handler(newValue) {
          this.nodes = newValue;
          if (this.chart) {
            this.chart.load(this.nodes);
          }
        },
      },
      isLayoutToolbarHidden(newVal) {
        const layoutToolbar = this.$el.querySelector(".boc-toolbar-layout");
        if (layoutToolbar) {
          layoutToolbar.setAttribute("aria-hidden", newVal.toString());
          layoutToolbar.querySelectorAll("svg").forEach((svg) => {
            svg.setAttribute("tabindex", newVal ? "-1" : "0");
          });
        }
      },
    },
    methods: {
      initOrgChart() {
        const treeElement = this.$refs.tree;
        this.chart = new OrgChart(treeElement, {
          mouseScrool: OrgChart.action.ctrlZoom,
          scaleInitial: OrgChart.match.boundary,
          enableDragDrop: false,
          lazyLoading: true,
          nodeMouseClick: false,
          toolbar: {
            layout: true,
            zoom: true,
            fit: true,
            expandAll: true,
          },
          mode: "dark",
          template: "olivia",
          nodes: this.nodes,
          orderBy: "orderId",
          nodeBinding: {
            //field_0: "title",
            field_1: "title",
            field_2: "subtitle",
          },
          searchDisplayField: "title",
          collapse: {
            level: 2,
            allChildren: true,
          },
          editForm: {
            buttons: {
              edit: null,
              pdf: null,
              share: null,
            },
            elements: [
              {
                type: "textbox",
                label: "ID",
                binding: "id",
              },
              {
                type: "textbox",
                label: "Nome",
                binding: "name",
              },
              {
                type: "textbox",
                label: "Cargo",
                binding: "title",
              },
              {
                type: "textbox",
                label: "E-mail",
                binding: "email",
              },
            ],
          },
        });

        this.chart.onNodeClick((sender, node) => {
          // INFO: Desabilitado na v0.2, pois não irá mais exibir informações extra de usuario
          // this.lastFocusedElement = document.activeElement;
          // this.$nextTick(() => {
          //     const modal = document.querySelector('.boc-edit-form');
          //     if (modal) {
          //         this.openModal(modal);
          //     }
          //     const modalHeader = document.querySelector('.boc-edit-form-header');
          //     if (modalHeader) {
          //         this.addSubtitleToHeader(modalHeader);
          //     }
          //     this.removeInputsToDetails();
          // });
        });

        this.chart.on("render", (sender, args) => {
          this.$nextTick(() => {
            const treeElement = this.$refs.tree;
            const searchElement = treeElement.querySelector(".boc-search");
            const svgElement = treeElement.querySelector("svg");
            // removido pois estava acontecendo erro [LFXP-4501]
            // if (searchElement && svgElement) {
            //   treeElement.insertBefore(searchElement, svgElement);
            // }

            const inputContainer = this.$el.querySelector(
              ".boc-dark .boc-input"
            );
            const buttons = this.$el.querySelectorAll("div[data-tlbr]");
            if (inputContainer) {
              this.addSearchIcon(inputContainer);
            }
            if (buttons) {
              this.addTitleToolbarButtons(buttons);
              this.makeToolbarButtonsAccessible(buttons);
            }

            const nodes = this.$el.querySelectorAll(".node");
            if (nodes) {
              this.wrapTextInSvg(nodes);
            }

            this.setupFocusOrder();
            this.makeLayoutOptionsAccessible();
            this.reorderLayoutToolbar();
          });
        });

        this.chart.on("field", (sender, args) => {
          // if (args.name === 'position_id' && args.data && args.data.name !== null) {
          //     args.element = '';
          // }
          // if (args.name === 'title' && args.data && args.data.name !== null) {
          //     args.element = `<text width="180" class="olivia-f1" x="100" y="65">${args.value}</text>`;
          // }
        });

        this.observeSearchResults();
        OrgChart.SEARCH_PLACEHOLDER = "Buscar...";
        OrgChart.SEARCH_RESULT_LIMIT = 50;
        OrgChart.templates.olivia.size = [300, 100];
        OrgChart.templates.olivia.img_0 = `<circle cx="50" cy="50" r="35"></circle>
                <clipPath id="{randId}"><circle cx="50" cy="50" r="35"></circle></clipPath>
                <image preserveAspectRatio="xMidYMid slice" clip-path="url(#{randId})" xlink:href="{val}" x="15" y="15" width="70" height="70"></image>`;
        OrgChart.templates.olivia.field_0 = `<text ${OrgChart.attr.width}="180" class="olivia-f0" x="100" y="45">{val}</text>`;
        OrgChart.templates.olivia.field_1 = `<text ${OrgChart.attr.width}="300" class="olivia-f1" text-anchor="middle" style="font-size: 18px !important;" x="150" y="45">{val}</text>`;
        OrgChart.templates.olivia.field_2 = `<text ${OrgChart.attr.width}="300" class="olivia-f2" text-anchor="middle" style="font-size: 16px !important;" x="150" y="65">{val}</text>`;

        OrgChart.EDITFORM_CLOSE_BTN = `<div role="button" data-edit-from-close class="boc-edit-form-close" aria-label="Fechar modal" tabindex="0">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" width="24" height="24" aria-label="Fechar modal">
                        <path d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"/>
                    </svg>
                </div>`;
      },
      setupFocusOrder() {
        const nodeElements = Array.from(
          this.$el.querySelectorAll("#tree g.node")
        );
        const expandButtons = Array.from(
          this.$el.querySelectorAll("#tree g[data-ctrl-ec-id]")
        );
        const focusOrder = [];

        nodeElements.forEach((node) => {
          focusOrder.push(node);
          const nodeId = node.getAttribute("data-n-id");
          const expandButton = expandButtons.find(
            (btn) => btn.getAttribute("data-ctrl-ec-id") === nodeId
          );
          if (expandButton) {
            focusOrder.push(expandButton);
          }
        });

        focusOrder.forEach((el) => {
          el.setAttribute("tabindex", "0");
          if (el.hasAttribute("data-ctrl-ec-id")) {
            el.setAttribute("role", "button");
          }
        });

        focusOrder.forEach((el, index) => {
          el.addEventListener("keydown", (event) => {
            if (event.key === "Tab") {
              const isShift = event.shiftKey;
              const isLast = index === focusOrder.length - 1;
              const isFirst = index === 0;

              if (!isShift && isLast) {
                return;
              }

              if (isShift && isFirst) {
                return;
              }

              event.preventDefault();

              if (isShift) {
                const prevIndex = index - 1;
                if (prevIndex >= 0) {
                  focusOrder[prevIndex].focus();
                }
              } else {
                const nextIndex = index + 1;
                if (nextIndex < focusOrder.length) {
                  focusOrder[nextIndex].focus();
                }
              }
            }

            if (el.hasAttribute("data-ctrl-ec-id")) {
              if (event.key === "Enter" || event.key === " ") {
                event.preventDefault();
                el.click();
                const nodeId = el.getAttribute("data-ctrl-ec-id");
                this.lastFocusedElementId = nodeId;
              }
            }
          });

          el.addEventListener("click", () => {
            const isExpanded = el.getAttribute("aria-expanded") === "true";
            el.setAttribute("aria-expanded", !isExpanded);
            el.setAttribute(
              "aria-label",
              !isExpanded ? "Mostrar Equipe" : "Ocultar Equipe"
            );
            const nodeId = el.getAttribute("data-ctrl-ec-id");
            this.lastFocusedElementId = nodeId;
          });
        });

        if (this.lastFocusedElementId) {
          const newButton = this.$el.querySelector(
            `g[data-ctrl-ec-id="${this.lastFocusedElementId}"]`
          );
          if (newButton) {
            newButton.focus();
          }
          this.lastFocusedElementId = null;
        }
      },
      addTitleToolbarButtons(buttons) {
        buttons.forEach((button) => {
          const action = button.getAttribute("data-tlbr");
          const title = this.getButtonTitle(action);

          if (title) {
            const svgElement = button.querySelector("svg");
            if (svgElement) {
              const titleElement = document.createElementNS(
                "http://www.w3.org/2000/svg",
                "title"
              );
              titleElement.textContent = title;
              svgElement.appendChild(titleElement);
            }
          }
        });
      },
      makeToolbarButtonsAccessible(buttons) {
        buttons.forEach((button) => {
          if (button.getAttribute("role")) {
            return;
          }
          button.setAttribute("tabindex", "0");
          button.setAttribute("role", "button");

          const action = button.getAttribute("data-tlbr");
          const title = this.getButtonTitle(action);
          if (title) {
            button.setAttribute("aria-label", title);
          }

          button.addEventListener("keydown", (event) => {
            if (event.key === "Enter" || event.key === " ") {
              event.preventDefault();
              button.click();
            }
          });
        });
      },
      getButtonTitle(action) {
        const titles = {
          expand: "Expandir",
          fit: "Ajustar",
          plus: "Aumentar Zoom",
          minus: "Diminuir Zoom",
          layout: "Alterar Layout",
        };
        return titles[action] || "";
      },
      addSubtitleToHeader(modalHeader) {
        const existingDiv = modalHeader.querySelector(
          ".boc-edit-form-subtitle"
        );
        const title = modalHeader.querySelector(".boc-edit-form-title");
        if (existingDiv) {
          existingDiv.remove();
        }

        const inputValue = this.$el.querySelector(
          '[data-binding="title"]'
        ).value;
        const newDiv = document.createElement("div");
        newDiv.classList.add("boc-edit-form-subtitle");
        newDiv.textContent = inputValue;

        if (newDiv && !title.textContent) {
          newDiv.classList.add("no-user");
        }

        modalHeader.appendChild(newDiv);
      },
      addSearchIcon(inputContainer) {
        const existingIcon = inputContainer.querySelector(".fa-search");
        if (!existingIcon) {
          let searchIcon = document.createElement("i");
          searchIcon.classList.add("fa", "fa-search");

          searchIcon.style.position = "absolute";
          searchIcon.style.right = "10px";
          searchIcon.style.top = "50%";
          searchIcon.style.transform = "translateY(-50%)";
          searchIcon.style.color = "#aaa";

          inputContainer.style.position = "relative";
          inputContainer.appendChild(searchIcon);
        }
      },
      removeInputsToDetails() {
        const img = this.$el.querySelector('[data-binding="img"]');
        const id = this.$el.querySelector('[data-binding="position_id"]');
        const name = this.$el.querySelector('[data-binding="name"]');
        const orderId = this.$el.querySelector('[data-binding="orderId"]');
        const labels = this.$el.querySelectorAll("label");
        const labelImg = Array.from(labels).find(
          (label) => label.textContent.trim() === "img"
        );
        const labelId = Array.from(labels).find(
          (label) => label.textContent.trim() === "ID"
        );
        const labelOrderId = Array.from(labels).find(
          (label) => label.textContent.trim() === "orderId"
        );
        if (img) {
          img.remove();
          if (labelImg) labelImg.remove();
        }
        if (id && name) {
          id.remove();
          if (labelId) labelId.remove();
        }
        if (orderId) {
          orderId.remove();
          if (labelOrderId) labelOrderId.remove();
        }
      },
      wrapTextInSvg(nodes) {
        nodes.forEach((node) => {
          const textElement = node.querySelector("text.olivia-f0");
          const textElement2 = node.querySelector("text.olivia-f1");
          if (textElement && textElement2) {
            const maxWidth = 200;
            const content = textElement2.textContent;
            const words = content.split(" ");

            textElement2.textContent = "";

            let tspanElement = document.createElementNS(
              "http://www.w3.org/2000/svg",
              "tspan"
            );
            let line = "";

            words.forEach((word) => {
              const testLine = line + word + " ";
              tspanElement.textContent = testLine;
              textElement2.appendChild(tspanElement);

              if (this.getTextWidth(tspanElement) > maxWidth) {
                tspanElement.textContent = line.trim();
                line = word + " ";
                tspanElement = document.createElementNS(
                  "http://www.w3.org/2000/svg",
                  "tspan"
                );
                tspanElement.setAttribute("x", "100");
                tspanElement.setAttribute("dy", "1.2em");
                textElement2.appendChild(tspanElement);
              } else {
                line = testLine;
              }
            });

            tspanElement.textContent = line.trim();
          }
        });
      },
      getTextWidth(element) {
        const svg = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "svg"
        );
        const text = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "text"
        );
        text.setAttribute("style", window.getComputedStyle(element).cssText);
        text.textContent = element.textContent;
        svg.appendChild(text);
        document.body.appendChild(svg);
        const width = text.getComputedTextLength();
        document.body.removeChild(svg);
        return width;
      },

      observeSearchResults() {
        const initObserver = () => {
          const searchResultsContainer = this.$el.querySelector(".boc-search");

          if (searchResultsContainer) {
            const observer = new MutationObserver((mutationsList) => {
              mutationsList.forEach((mutation) => {
                if (
                  mutation.type === "childList" &&
                  mutation.addedNodes.length > 0
                ) {
                  const trElements =
                    searchResultsContainer.querySelectorAll("tr");
                  trElements.forEach((tr) => {
                    tr.setAttribute("tabindex", "0");
                    tr.setAttribute("role", "button");

                    const tdElement = tr.querySelector("td.boc-search-text-td");
                    if (tdElement) {
                      const htmlContent = tdElement.innerHTML;
                      const textContent = htmlContent.replace(/<[^>]+>/g, "");

                      tr.setAttribute("aria-label", textContent);
                    }
                  });
                }
              });
            });

            observer.observe(searchResultsContainer, {
              childList: true,
              subtree: true,
            });
          } else {
            setTimeout(initObserver, 500);
          }
        };

        initObserver();
      },

      openModal(modal) {
        modal.setAttribute("role", "dialog");
        modal.setAttribute("aria-modal", "true");
        modal.setAttribute("aria-labelledby", "modal-title");

        modal.style.display = "flex";

        modal.setAttribute("tabindex", "-1");
        modal.focus();

        this.trapFocus(modal);

        const closeButton = modal.querySelector("[data-edit-from-close]");
        if (closeButton) {
          closeButton.addEventListener("click", () => this.closeModal(modal));
          closeButton.setAttribute("tabindex", "0");
          closeButton.setAttribute("role", "button");
          closeButton.setAttribute("aria-label", "Fechar modal");
        }

        const cancelButton = modal.querySelector("[data-edit-from-cancel]");
        if (cancelButton) {
          cancelButton.addEventListener("click", () => this.closeModal(modal));
        }

        const saveButton = modal.querySelector("[data-edit-from-save]");
        if (saveButton) {
          saveButton.addEventListener("click", (e) => {
            e.preventDefault();
            this.closeModal(modal);
          });
        }

        const handleEscape = (e) => {
          if (e.key === "Escape") {
            this.closeModal(modal);
          }
        };
        document.addEventListener("keydown", handleEscape);
        modal.escapeListener = handleEscape;
      },

      closeModal(modal) {
        modal.style.display = "none";
        this.removeTrapFocus();

        if (modal.escapeListener) {
          document.removeEventListener("keydown", modal.escapeListener);
          modal.escapeListener = null;
        }

        if (this.lastFocusedElement) {
          this.lastFocusedElement.focus();
          this.lastFocusedElement = null;
        }
      },

      trapFocus(modal) {
        const focusableSelectors = [
          "a[href]",
          "area[href]",
          "input:not([disabled])",
          "select:not([disabled])",
          "textarea:not([disabled])",
          "button:not([disabled])",
          "iframe",
          "object",
          "embed",
          '[tabindex]:not([tabindex="-1"])',
          "[contenteditable]",
        ];
        const focusableElements = modal.querySelectorAll(
          focusableSelectors.join(",")
        );
        if (focusableElements.length === 0) return;

        const firstFocusable = focusableElements[0];
        const lastFocusable = focusableElements[focusableElements.length - 1];

        const handleTab = (e) => {
          if (e.key === "Tab") {
            if (e.shiftKey) {
              if (document.activeElement === firstFocusable) {
                e.preventDefault();
                lastFocusable.focus();
              }
            } else {
              if (document.activeElement === lastFocusable) {
                e.preventDefault();
                firstFocusable.focus();
              }
            }
          }
        };

        modal.addEventListener("keydown", handleTab);
        this.focusHandler = handleTab;

        firstFocusable.focus();
      },

      removeTrapFocus() {
        const modal = document.querySelector(".boc-edit-form");
        if (modal && this.focusHandler) {
          modal.removeEventListener("keydown", this.focusHandler);
        }
      },

      makeLayoutOptionsAccessible() {
        const layoutOptions = this.$el.querySelectorAll(
          ".boc-toolbar-layout svg[data-layout]"
        );
        layoutOptions.forEach((option) => {
          option.setAttribute("role", "button");
          const layoutName = option.getAttribute("data-layout");
          const ariaLabel = this.getLayoutAriaLabel(layoutName);
          option.setAttribute("aria-label", ariaLabel);

          option.addEventListener("keydown", (event) => {
            if (event.key === "Enter" || event.key === " ") {
              event.preventDefault();
              const clickEvent = new MouseEvent("click", {
                bubbles: true,
                cancelable: true,
              });

              option.dispatchEvent(clickEvent);
            }
          });
        });
      },

      getLayoutAriaLabel(layoutName) {
        const labels = {
          normal: "Layout Normal",
          treeRightOffset: "Layout Árvore com Deslocamento à Direita",
          treeLeftOffset: "Layout Árvore com Deslocamento à Esquerda",
          mixed: "Layout Misto",
          tree: "Layout Árvore",
          grid: "Layout de Grade",
        };
        return labels[layoutName] || "Layout";
      },

      reorderLayoutToolbar() {
        const toolbarContainer = this.$el.querySelector(
          ".boc-toolbar-container"
        );
        const layoutToolbar = this.$el.querySelector(".boc-toolbar-layout");
        // removido pois estava acontecendo erro [LFXP-4501]
        // if (toolbarContainer && layoutToolbar) {
        //   toolbarContainer.parentNode.insertBefore(
        //     layoutToolbar,
        //     toolbarContainer.nextSibling
        //   );
        // }
      },

      hideLayoutToolbar() {
        this.isLayoutToolbarHidden = true;
      },

      showLayoutToolbar() {
        this.isLayoutToolbarHidden = false;
      },

      toggleLayoutToolbar(layoutToolbar) {
        this.isLayoutToolbarHidden = !this.isLayoutToolbarHidden;
        if (!this.isLayoutToolbarHidden) {
          const firstLayoutOption =
            layoutToolbar.querySelector("svg[data-layout]");
          if (firstLayoutOption) {
            firstLayoutOption.focus();
          }
        }
      },

      addLayoutButtonListeners(layoutButton, layoutToolbar) {
        layoutButton.addEventListener("click", () => {
          this.toggleLayoutToolbar(layoutToolbar);
        });
      },

      setupLayoutButtonListener() {
        const layoutButton = this.$el.querySelector('div[data-tlbr="layout"]');
        const layoutToolbar = this.$el.querySelector(".boc-toolbar-layout");

        if (layoutButton && layoutToolbar) {
          this.addLayoutButtonListeners(layoutButton, layoutToolbar);
        } else {
          setTimeout(this.setupLayoutButtonListener, 500);
        }
      },
    },
    mounted() {
      this.initOrgChart();

      setTimeout(() => {
        this.hideLayoutToolbar();
      }, 3000);

      this.setupLayoutButtonListener();
    },
  };
</script>

<style lang="css">
  #tree > svg {
    background-color: #212529;
  }

  #tree {
    width: 100%;
    height: 100%;
  }

  /* Cards do OrgChart - Inicio */
  [data-n-id] rect {
    fill: var(--primary);
    stroke-width: 0;
  }

  [data-n-id] circle {
    stroke: #fff !important;
    stroke-width: 5px !important;
    stroke-opacity: 0.5;
    fill: none !important;
  }

  .boc-dark .olivia-f0,
  .boc-dark .olivia-f1,
  .boc-dark .olivia-f2 {
    fill: #fff !important;
  }

  [data-ctrl-ec-id] circle {
    fill: #343a40 !important;
  }

  [data-ctrl-ec-id] line {
    stroke: #fff !important;
    stroke-width: 2px !important;
  }

  .node.inativo rect {
    fill: #495057;
  }

  /* Cards do OrgChart - Fim */

  /* Input style - Inicio */
  .boc-link-boc-button {
    top: 6px !important;
    right: 30px !important;
    text-decoration: none !important;
  }

  .boc-dark .boc-search .boc-input > input {
    background-color: #212529 !important;
    border-color: #495057 !important;
    color: #dee2e6 !important;
    border-radius: 0.5rem !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    height: calc(1.5em + 0.75rem + 2px) !important;
    padding: 0.375rem 0.75rem !important;
  }

  .boc-dark .boc-search {
    right: unset !important;
  }

  .boc-search > div:has(> table) {
    max-height: 300px !important;
  }

  .boc-dark .boc-search > input:focus {
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 0.2rem color-mix(in srgb, var(--primary) 80%, white 20%) !important;
    outline: 0 !important;
  }

  .boc-search .boc-input > label {
    top: 6px !important;
  }

  .boc-search .boc-input > label.focused {
    display: none !important;
  }

  .boc-search .boc-input > label.focused,
  .boc-search .boc-input > label.hasval {
    display: none !important;
  }

  /* Input style - Fim */

  /* Details OrgChart - Inicio */
  @media screen and (max-width: 1000px) {
    .boc-edit-form {
      width: 400px !important;
    }
  }

  @media screen and (max-width: 500px) {
    .boc-edit-form {
      width: 100% !important;
    }
  }

  .boc-edit-form-header {
    position: relative;
    background-color: var(--primary) !important;
    color: #fff !important;
    height: 120px !important;
    border-radius: 10px 10px 0 0 !important;
    border: 1px solid var(--primary);
    text-align: start !important;
    margin-bottom: 20px !important;
  }

  .no-user {
    padding: 10px 0 !important;
  }

  .boc-edit-form-header::after {
    content: "";
    /* Necessário para que o pseudo-elemento seja renderizado */
    position: absolute;
    /* Posiciona o pseudo-elemento em relação ao contêiner pai */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.75);
    /* Cor preta com 75% de opacidade */
    z-index: 1;
    /* Coloca o pseudo-elemento acima do fundo, mas abaixo do texto */
    border-radius: 10px 10px 0 0 !important;
  }

  .boc-edit-form-header * {
    position: relative;
    /* Necessário para que o texto fique acima do pseudo-elemento */
    z-index: 2;
    /* Garante que o texto fique acima da sobreposição */
  }

  .boc-edit-form-instruments {
    display: none !important;
  }

  .boc-edit-form-avatar {
    background-color: unset !important;
    box-shadow: unset !important;
    border: 5px solid var(--primary) !important;
    transform: translateY(-50%) !important;
    top: 50% !important;
    left: 20px !important;
    width: 80px !important;
    height: 80px !important;
  }

  .boc-edit-form-avatar > svg {
    width: 70px !important;
    height: 70px !important;
    left: 1px;
  }

  .boc-edit-form-title {
    font-size: 1.25rem !important;
    font-weight: 300 !important;
    margin: 35px 0 0 110px !important;
    padding: 0 !important;
  }

  .boc-edit-form-subtitle {
    font-size: 1rem !important;
    margin: 5px 60px 0 110px;
    color: var(--primary) !important;
  }

  .boc-edit-form-close {
    z-index: 3 !important;
    fill: var(--primary) !important;
    top: 50% !important;
    right: 10px !important;
    transform: translateY(-50%) !important;
    height: 24px !important;
    width: 32px !important;
  }

  /* Details OrgChart - Fim */

  /* Toolbar - Inicio */
  .boc-toolbar-container {
    display: flex !important;
    right: unset !important;
  }

  .boc-toolbar-container [data-tlbr="expand"],
  [data-tlbr="fit"],
  [data-tlbr="plus"],
  [data-tlbr="minus"],
  [data-tlbr="layout"] {
    margin-right: 15px !important;
  }

  /* Toolbar Layout - Inicio */
  .boc-toolbar-layout {
    overflow-x: auto !important;
    white-space: nowrap !important;
    padding: 15px !important;
    height: 133px !important;
  }

  /* Toolbar Layout - Fim */

  /* Toolbar - Fim */

  /* Foco Visual para Acessibilidade */
  g.node:focus {
    outline: 2px solid var(--primary);
  }

  g[data-ctrl-ec-id]:focus {
    outline: 2px solid var(--primary);
  }
</style>
