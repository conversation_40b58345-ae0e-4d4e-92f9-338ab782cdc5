<template>
  <div
    id="items-header-filter"
    class="my-4"
  >
    <h5 class="text-uppercase text-primary my-2">{{ strings.filter }}</h5>
    <form class="">
      <div class="form-row col-md-12 my-2 mx-0 px-0">
        <div class="col-12 col-md-3 col-xl-2 p-md-0">
          <label for="search">
            {{ strings.position }}

            <a
              class="btn btn-link py-0 px-1"
              role="button"
              data-trigger="focus"
              data-toggle="popover"
              data-placement="right"
              tabindex="0"
              data-container="body"
              data-html="true"
              :data-content="'<div>' + strings.help_position_search + '</div>'"
            >
              <i
                title="Ajuda com cargo"
                class="far fa-question-circle text-primary"
              ></i>
            </a>
          </label>
          <!-- <input
            type="search"
            name="search"
            id="filter-search"
            class="form-control"
            v-model="search"
          /> -->
          <v-select
            :multiple="true"
            :options="options"
            v-model="selectedOption"
            @search="onSearch"
            :label="label"
            :filter="filterOptions"
          >
            <template v-slot:no-options="{ search, searching }">
              <template v-if="searching">
                Nenhum cargo encontrado para <em>{{ search }}</em
                >.
              </template>
              <em
                v-else
                style="opacity: 0.5"
                >Comece a digitar para pesquisar um cargo</em
              >
            </template>
          </v-select>
        </div>
        <!-- Itens Selecionados - Mobile -->
        <div
          v-if="selectedOption.length > 0"
          class="col-12 my-2 d-md-none"
        >
          <label class="mb-0">{{ strings.selected_positions }}</label>
          <div class="form-row col-md-12 mx-0 px-0">
            <div class="col-10 px-0 d-flex flex-wrap">
              <span
                v-for="option in selectedOption"
                :key="option.id"
                class="bg-primary text-white py-1 px-2 rounded ml-1 mt-2"
              >
                {{ option.name }}
                <i
                  role="button"
                  @click="removeOption(option)"
                  class="fa fa-times cursor-pointer"
                ></i>
              </span>
            </div>
          </div>
        </div>
        <div
          class="col-12 col-md-2 mt-1 mt-md-0"
          v-if="statuses.length"
        >
          <label for="status">{{ strings.status }}</label>
          <select
            name="status"
            id="filter-status"
            class="form-control"
            v-model="selectedStatus"
          >
            <option
              v-for="status in statuses"
              :key="status.id"
              :value="status.id"
            >
              {{ status.name }}
            </option>
          </select>
        </div>
        <div class="d-flex align-items-center ml-1 ml-md-0 pt-3 pt-md-5">
          <div class="d-flex">
            <div class="d-flex align-items-center">
              <input
                type="checkbox"
                name="onlymanagers"
                id="form_managerpositions"
                v-model="onlyManagers"
              />
            </div>
            <label
              for="onlymanagers"
              class="my-0 py-0 ml-2"
            >
              {{ strings.filter_is_manager_question }}</label
            >
          </div>
        </div>
        <div
          class="d-flex align-items-center col-12 col-md-2 mt-4 ml-0 ml-md-2"
        >
          <div class="d-flex flex-column flex-md-row align-items-stretch w-100">
            <button
              class="btn btn-primary mr-md-2"
              @click="applyFilter"
            >
              {{ strings.filter_apply }}
            </button>
            <button
              class="btn btn-dark mt-2 mt-md-0"
              @click="cleanFilter"
            >
              {{ strings.filter_clear }}
            </button>
          </div>
        </div>
      </div>

      <!-- Itens Selecionados - Desktop -->
      <div
        v-if="selectedOption.length > 0"
        class="d-none d-md-block"
      >
        <label class="mb-0">{{ strings.selected_positions }}:</label>
        <div class="form-row col-md-12 mx-0 px-0">
          <div class="col-10 px-0 d-flex flex-wrap">
            <span
              v-for="option in selectedOption"
              :key="option.id"
              class="bg-primary text-white py-1 px-2 rounded ml-1 mt-2"
            >
              {{ option.name }}
              <i
                role="button"
                @click="removeOption(option)"
                class="fa fa-times cursor-pointer"
              ></i>
            </span>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
  import {
    ajax,
    getStrings,
    getURLParams,
    setURLParam,
  } from "@/helpers/moodle";
  import VueSelect from "vue-select";
  import "vue-select/dist/vue-select.css";

  export default {
    name: "Filter",
    components: {
      "v-select": VueSelect,
    },
    data() {
      return {
        strings: {},
        statuses: [],
        selectedStatus: 0,
        onlyManagers: false,
        options: [],
        selectedOption: [],
        label: "name",
      };
    },
    props: {
      fetchItems: {
        type: Function,
        required: true,
      },
    },
    async beforeCreate() {
      let stringArray = [
        {
          key: "filter",
          component: "local_hierarchy",
        },
        {
          key: "position",
          component: "local_hierarchy",
        },
        {
          key: "status",
          component: "local_hierarchy",
        },
        {
          key: "filter_is_manager_question",
          component: "local_hierarchy",
        },
        {
          key: "filter_apply",
          component: "local_hierarchy",
        },
        {
          key: "filter_clear",
          component: "local_hierarchy",
        },
        {
          key: "help_position_search",
          component: "local_hierarchy",
        },
        {
          key: "selected_positions",
          component: "local_hierarchy",
        },
      ];

      let strings = await getStrings(stringArray);

      this.strings = strings;

      let statuses = await this.getStatus();

      this.statuses = statuses;

      let URLParams = getURLParams();

      this.selectedOption = URLParams.has("search")
        ? JSON.parse(URLParams.get("search") || "[]")
        : [];
      this.selectedStatus = statuses
        ? URLParams.has("status")
          ? URLParams.get("status")
          : statuses[0].id
        : "";
      this.onlyManagers = URLParams.has("onlymanagers")
        ? URLParams.get("onlymanagers")
        : false;
    },
    methods: {
      async onSearch(searchTerm, loading) {
        loading(true);
        if (!isNaN(searchTerm) && searchTerm.length >= 1) {
          this.label = "id";
          await this.fetchOptions(searchTerm);
        } else if (searchTerm.length > 2) {
          this.label = "name";
          await this.fetchOptions(searchTerm);
        } else {
          this.options = [];
        }
        loading(false);
      },
      filterOptions(options, searchTerm) {
        if (this.label === "id") {
          // Se a busca for por ID, faz uma comparação exata
          return options.filter((option) => {
            return option.id.toString() === searchTerm;
          });
        } else {
          // Se a busca for por nome, normaliza os termos para ignorar acentos
          const normalizedSearchTerm = this.normalizeString(searchTerm);
          return options.filter((option) => {
            const normalizedOption = this.normalizeString(option[this.label]);
            return normalizedOption.includes(normalizedSearchTerm);
          });
        }
      },
      normalizeString(str) {
        return str
          .normalize("NFD")
          .replace(/[\u0300-\u036f]/g, "")
          .toLowerCase();
      },
      async fetchOptions(searchTerm) {
        const positions = await this.fetchItems({
          search: searchTerm,
          perpage: 50,
        });
        this.options = positions.items.map((position) => {
          return {
            id: position.id,
            name: position.name,
          };
        });
      },
      getStatus() {
        return ajax("local_hierarchy_get_status_options", {});
      },
      applyFilter(event) {
        event.preventDefault();
        setURLParam(
          "search",
          JSON.stringify(
            this.selectedOption.map((option) =>
              this.label === "id" ? option.id : option.name
            )
          )
        );
        setURLParam("status", this.selectedStatus);
        setURLParam("onlymanagers", this.onlyManagers);
        this.$emit("update-items");
      },
      cleanFilter(event) {
        event.preventDefault();
        this.selectedOption = [];
        this.selectedStatus = this.statuses[0].id;
        this.onlyManagers = false;
        setURLParam(
          "search",
          JSON.stringify(this.selectedOption.map((option) => option.name))
        );
        setURLParam("status", this.selectedStatus);
        setURLParam("onlymanagers", this.onlyManagers);
        this.$emit("update-items");
      },
      removeOption(option) {
        this.selectedOption = this.selectedOption.filter(
          (selectedOption) => selectedOption.id !== option.id
        );
      },
    },
  };
</script>

<style lang="scss">
  #items-header-filter {
    .v-select {
      height: 2.125rem;
      font-weight: 400;
      line-height: 1.5;
      border: 1px solid #495057;
      border-radius: 0.5rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

      &:focus-within {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.2rem
          color-mix(in srgb, var(--primary) 80%, white 20%) !important;
      }
    }

    .vs__dropdown-menu {
      margin-top: 5px;
    }

    .vs__dropdown-option:hover {
      background-color: #495057 !important;
      color: white !important;
    }

    .vs__dropdown-option--highlight {
      background-color: #495057 !important;
      color: white !important;
    }

    .vs__selected-options {
      padding: 2px;
    }

    .vs__selected {
      display: none;
    }
  }

  // .vs__open-indicator {
  //   fill: #dee2e6;
  // }
</style>
