<template>
  <div class="d-flex flex-column flex-md-row justify-content-center mt-5 mb-3">
    <button
      class="btn btn-primary"
      @click="openUpload"
    >
      <i class="fa fa-upload"></i>
      Carregar em lote
    </button>

    <button
      class="btn btn-dark ml-md-2 mt-2 mt-md-0"
      @click="exportToXLS"
    >
      <i class="fa fa-download"></i>
      Exportar para XLS
    </button>

    <button
      class="btn btn-dark ml-md-2 mt-2 mt-md-0"
      @click="exportToCSV"
    >
      <i class="fa fa-download"></i>
      Exportar para CSV
    </button>
  </div>
</template>

<script>
  import { ajax, getStrings, getURLParams } from "@/helpers/moodle";
  import { useUploadStore } from "@/stores/uploadStore";

  export default {
    name: "Footer",
    components: {},
    data() {
      return {
        strings: {},
      };
    },
    props: {
      titlePage: {
        type: String,
        default: "",
      },
      subTitlePage: {
        type: String,
        default: "",
      },
      entity: {
        type: String,
        required: true,
      },
    },
    async created() {
      let stringArray = [];

      let strings = await getStrings(stringArray);

      this.strings = strings;
    },
    methods: {
      openUpload() {
        const uploadStore = useUploadStore();
        uploadStore.setUploadData(
          this.titlePage,
          this.subTitlePage,
          this.entity
        ); // Define os dados na store

        this.$router.push({
          name: "hierarchy.upload",
          params: { entity: this.entity },
        });
      },
      exportToXLS() {
        this.$emit("exportToXLS", this.getFormattedDate());
      },
      exportToCSV() {
        this.$emit("exportToCSV", this.getFormattedDate());
      },
      getFormattedDate() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, "0");
        const day = String(now.getDate()).padStart(2, "0");
        const hours = String(now.getHours()).padStart(2, "0");
        const minutes = String(now.getMinutes()).padStart(2, "0");

        return `${day}_${month}_${year}_${hours}${minutes}`;
      },
    },
    emits: ["exportToXLS", "exportToCSV"],
  };
</script>
