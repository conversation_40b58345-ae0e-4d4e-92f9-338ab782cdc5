<template>
  <Modal
    title="Sugestão de cargos"
    id="position-suggestion-modal"
    size="modal-md"
    :requiredMessage="false"
    @click.stop
    @close="close"
    @submit="acceptSuggestion"
  >
    <h5 class="my-3">
      Deseja adicionar os cargos abaixo à estrutura "{{ structureName }}"?
    </h5>

    <div v-if="position">
      <SubordinateTree
        :position="position"
        :selected-positions="selectedPositions"
        @update:selected-positions="updateSelectedPositions"
      />
    </div>
  </Modal>
</template>

<script>
  import Modal from "@/components/Modal.vue";
  import SubordinateTree from "@/components/Structure/SubordinateTree.vue";

  export default {
    name: "PositionSuggestionModal",
    components: { Modal, SubordinateTree },
    data() {
      return {
        selectedPositions: [],
      };
    },
    props: {
      position: Object,
      structureName: String,
    },
    methods: {
      close() {
        $("#position-suggestion-modal").modal("hide");
      },
      acceptSuggestion() {
        this.$emit("accept-suggestion", this.selectedPositions);
        this.close();
      },
      updateSelectedPositions(updated) {
        this.selectedPositions = updated;
      },
    },
  };
</script>
