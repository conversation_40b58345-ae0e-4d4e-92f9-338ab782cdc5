<template>
  <div class="form-check mb-2 ml-2">
    <input
      type="checkbox"
      class="form-check-input"
      :id="position.id"
      :checked="isChecked"
      @change="toggleSelection"
    />
    <label
      class="form-check-label"
      :for="position.id"
    >
      {{ position.name }}
    </label>

    <div v-if="position.subordinates && position.subordinates.length">
      <SubordinateTree
        v-for="sub in position.subordinates"
        :key="sub.id"
        :position="sub"
        :level="level + 1"
        :selected-positions="selectedPositions"
        @update:selected-positions="updateSelection"
      />
    </div>
  </div>
</template>

<script>
  export default {
    name: "SubordinateTree",
    props: {
      position: Object,
      level: {
        type: Number,
        default: 0,
      },
      selectedPositions: Array,
    },
    emits: ["update:selected-positions"],
    computed: {
      isChecked() {
        return this.selectedPositions.some((p) => p.id === this.position.id);
      },
    },
    mounted() {
      this.selectAllOnMount(); // Marcar todos os cargos e subordinados ao iniciar
    },
    methods: {
      toggleSelection() {
        let updated = [...this.selectedPositions];
        const existingIndex = updated.findIndex(
          (p) => p.id === this.position.id
        );

        if (existingIndex !== -1) {
          // Já está selecionado, desmarcar
          updated.splice(existingIndex, 1);
        } else {
          // Adicionar ao array de selecionados
          updated.push({
            id: this.position.id,
            name: this.position.name,
            is_manager: this.position.is_manager,
          });
        }

        // Se o cargo for marcado, garantir que todos os subordinados também sejam selecionados
        if (this.isChecked) {
          this.selectSubordinates(this.position.subordinates, updated);
        }

        // Se o cargo for desmarcado, garantir que todos os subordinados também sejam desmarcados
        if (!this.isChecked) {
          this.deselectSubordinates(this.position.subordinates, updated);
        }

        this.$emit("update:selected-positions", updated);
      },

      selectSubordinates(subordinates, updated) {
        subordinates.forEach((sub) => {
          if (!updated.some((p) => p.id === sub.id)) {
            updated.push({
              id: sub.id,
              name: sub.name,
              is_manager: sub.is_manager,
            });
          }
          if (sub.subordinates.length > 0) {
            this.selectSubordinates(sub.subordinates, updated);
          }
        });
      },

      deselectSubordinates(subordinates, updated) {
        subordinates.forEach((sub) => {
          const idx = updated.findIndex((p) => p.id === sub.id);
          if (idx !== -1) {
            updated.splice(idx, 1);
          }
          if (sub.subordinates.length > 0) {
            this.deselectSubordinates(sub.subordinates, updated);
          }
        });
      },
      selectAllOnMount() {
        let updated = [...this.selectedPositions];
        if (!updated.some((p) => p.id === this.position.id)) {
          updated.push({
            id: this.position.id,
            name: this.position.name,
            is_manager: this.position.is_manager,
          });
        }

        if (
          this.position.subordinates &&
          this.position.subordinates.length > 0
        ) {
          this.selectSubordinates(this.position.subordinates, updated);
        }

        this.$emit("update:selected-positions", updated);
      },
      updateSelection(newSelections) {
        this.$emit("update:selected-positions", newSelections);
      },
    },
  };
</script>
