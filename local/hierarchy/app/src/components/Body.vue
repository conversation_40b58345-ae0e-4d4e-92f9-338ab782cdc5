<template>
  <div
    id="items-body"
    class="my-4 mw-100"
  >
    <Table
      v-if="data.items"
      :data="data.items"
      :headers="data.headers"
      :onView="viewItem"
      :onEdit="editItem"
      :onDelete="deleteItem"
      :isLoading="isLoading"
      @sort-changed="handleSortChanged"
    ></Table>

    <TablePaginate
      v-if="data.items"
      :totalPages="data.total_pages"
      :currentPage="data.page"
      :total="data.total_items"
      @prevPage="prevPage"
      @nextPage="nextPage"
      @changePage="updateItems"
      @changePageSize="updatePageSize"
    ></TablePaginate>
  </div>
</template>

<script>
  import Table from "@/components/Body/Table.vue";
  import TablePaginate from "@/components/Body/TablePaginate.vue";

  export default {
    name: "Body",
    components: {
      Table,
      TablePaginate,
    },
    data() {
      return {
        strings: [],
      };
    },
    props: {
      data: Object,
      isLoading: <PERSON>olean,
    },
    emits: ["update-items", "modal-item", "delete-item"],
    async created() {
      //
    },
    methods: {
      updateItems(page, pageSize, sortParams) {
        this.$emit(
          "update-items",
          page,
          this.data.perpage || pageSize,
          this.data.sort || sortParams
        );
      },
      viewItem(id) {
        this.$emit("modal-item", id, true);
      },
      editItem(id) {
        this.$emit("modal-item", id);
      },

      deleteItem(item) {
        this.$emit("delete-item", item);
      },
      handleSortChanged(sortParams) {
        this.$emit(
          "update-items",
          this.data.page,
          this.data.perpage || 10,
          sortParams
        );
      },
      updatePageSize(size) {
        this.$emit("update-items", 1, size);
      },
      prevPage() {
        this.updateItems(this.data.page - 1);
      },
      nextPage() {
        this.updateItems(this.data.page + 1);
      },
    },
  };
</script>
