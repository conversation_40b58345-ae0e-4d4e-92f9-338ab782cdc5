<template>
  <div
    v-if="totalPages > 1"
    class="d-flex flex-row justify-content-center justify-content-md-between align-items-start align-items-md-center px-1 mt-2 pagination-wrapper"
  >
    <div class="d-flex align-items-center col-12 col-md-6 px-0 pagination-size">
      <select
        v-model="pageSize"
        class="col-3 col-md-3 col-lg-2 form-control"
        @change="changePageSize"
      >
        <option
          v-for="pageSize in pageSizes"
          :key="pageSize"
          :value="pageSize"
        >
          {{ pageSize }}
        </option>
      </select>

      <div class="col-9 d-flex align-items-center">
        {{ strings.showing }} {{ from }} {{ strings.to }} {{ to }}
        {{ strings.of }} {{ total }} {{ strings.result_per_page }}
      </div>
    </div>
    <nav
      class="mt-5 pagination"
      aria-label="Page navigation"
    >
      <ul class="pagination justify-content-center">
        <!-- Bo<PERSON><PERSON> Anterior -->
        <li
          @click="prevPage"
          class="page-item page-item-previous"
          :class="{
            disabled: !canCallPreviousPage,
          }"
          title="Página anterior"
          data-page-number=""
        >
          <button
            class="page-link prev-page"
            :disabled="!canCallPreviousPage"
          >
            <span aria-hidden="true"
              ><i
                class="icon fa fa-angle-left m-0 d-flex justify-content-center align-items-center"
              ></i
            ></span>
          </button>
        </li>

        <!-- Primeira Página -->
        <li
          class="page-item"
          :title="'Página 1'"
          @click="changePage(1)"
        >
          <button
            :class="{
              'page-link': true,
              'page-link': currentPage !== 1,
              'btn-primary active page-link': currentPage === 1,
            }"
          >
            1
          </button>
        </li>

        <!-- Elipsis inicial (se necessário) -->
        <li
          v-if="showStartEllipsis"
          class="page-item disabled"
        >
          <button class="page-link disabled">...</button>
        </li>

        <!-- Páginas do meio -->
        <li
          v-for="pageNumber in middlePagination"
          class="page-item"
          :key="pageNumber"
          :title="'Página ' + pageNumber"
          @click="changePage(pageNumber)"
        >
          <button
            :class="{
              'page-link': true,
              'page-link': pageNumber !== currentPage,
              'btn-primary active page-link': pageNumber === currentPage,
            }"
          >
            {{ pageNumber }}
          </button>
        </li>

        <!-- Elipsis final (se necessário) -->
        <li
          v-if="showEndEllipsis"
          class="page-item disabled"
        >
          <button class="page-link disabled">...</button>
        </li>

        <!-- Última Página (se diferente da primeira) -->
        <li
          v-if="totalPages > 1"
          class="page-item"
          :title="'Página ' + totalPages"
          @click="changePage(totalPages)"
        >
          <button
            :class="{
              'page-link': true,
              'page-link': currentPage !== totalPages,
              'btn-primary active page-link': currentPage === totalPages,
            }"
          >
            {{ totalPages }}
          </button>
        </li>

        <!-- Botão Próximo -->
        <li
          @click="nextPage"
          class="page-item page-item-next"
          :class="{
            disabled: !canCallNextPage,
          }"
          data-page-number=""
          title="Próxima página"
        >
          <button
            class="page-link next-page"
            :disabled="!canCallNextPage"
          >
            <span aria-hidden="true"
              ><i
                class="icon fa fa-angle-right m-0 d-flex justify-content-center align-items-center"
              ></i
            ></span>
          </button>
        </li>
      </ul>
    </nav>
  </div>
</template>

<script>
  import { getStrings } from "@/helpers/moodle";

  export default {
    name: "TablePaginate",
    data() {
      return {
        pageSizes: [5, 10, 25, 50, 100],
        pageSize: 10,
        strings: {},
        maxVisiblePages: 3, // Número de páginas visíveis ao redor de cada lado da página atual
      };
    },
    props: {
      totalPages: Number,
      currentPage: Number,
      total: Number,
    },
    async created() {
      let stringArray = [
        {
          key: "showing",
          component: "local_hierarchy",
        },
        {
          key: "to",
          component: "local_hierarchy",
        },
        {
          key: "of",
          component: "local_hierarchy",
        },
        {
          key: "result_per_page",
          component: "local_hierarchy",
        },
      ];

      let strings = await getStrings(stringArray);

      this.strings = strings;
    },
    computed: {
      canCallPreviousPage() {
        return this.currentPage > 1;
      },

      canCallNextPage() {
        return this.currentPage < this.totalPages;
      },

      from() {
        return (this.currentPage - 1) * this.pageSize + 1;
      },

      to() {
        return Math.min(this.from + this.pageSize - 1, this.total);
      },

      showStartEllipsis() {
        if (this.totalPages <= 5) return false;
        console.log(this.isMobile())
        return (!this.isMobile() && this.currentPage > 5)
          || (this.isMobile() && this.currentPage > 3);
      },

      showEndEllipsis() {
        if (this.totalPages <= 5) return false;
        return (!this.isMobile() && this.currentPage <= this.totalPages - 5)
          || (this.isMobile() && this.currentPage <= this.totalPages - 3);
      },

      middlePagination() {
        if (this.totalPages == 0) return [];
        let treshold = 1;
        const pages = [];
        if (this.totalPages <= 5) {
          for (let i = 2; i <= this.totalPages - 1; i++) {
            pages.push(i);
          }
        } else {

          if ((this.currentPage <= 5 && !this.isMobile()) || (this.currentPage <= 3 && this.isMobile())) {
            //primeiras páginas
            if (this.currentPage <= this.totalPages - 5) {
              treshold = 2;
            }

            if (!this.isMobile()) {
              for (let i = 2; i <= this.currentPage + 3 && i <= this.totalPages - treshold; i++) {
                pages.push(i);
              }
            }
            else {
              for (let i = 2; i <= 3; i++) {
                pages.push(i);
              }
            }
          }
          else if ((this.currentPage > this.totalPages - 5 && !this.isMobile()) || (this.currentPage > this.totalPages - 3 && this.isMobile())) {
            // últimas páginas
            treshold = 2;
            if (this.isMobile()) {
              if (this.currentPage > 3) {
                treshold = this.totalPages - 2;
              }
            }
            else if (this.currentPage >= 6) {
              treshold = this.currentPage - 3;
            }
            for (let i = treshold; i <= this.totalPages - 1; i++) {
              pages.push(i);
            }
          } else {
            // meio
            if (this.isMobile()) {
              pages.push(this.currentPage);
            }
            else {
              for (let i = this.currentPage - 3; i <= this.currentPage + 3; i++) {
                pages.push(i);
              }
            }

          }
        }
        return pages;
      }
    },

    methods: {
      changePageSize() {
        this.$emit("changePageSize", this.pageSize);
      },

      changePage(pageNumber) {
        this.$emit("changePage", pageNumber);
      },
      prevPage() {
        if (this.currentPage > 1) {
          this.$emit("prevPage");
        }
      },
      nextPage() {
        if (this.currentPage < this.totalPages) {
          this.$emit("nextPage");
        }
      },

      isMobile() {
        return window.innerWidth < 601
      }
    },
  };
</script>

<style lang="scss" scoped>
  .page-item:not(.disabled):not(.active) {
    cursor: pointer;

    button {
      [data-theme="dark"] {
        background-color: #41474f !important;
      }

      [data-theme="light"] {
        background-color: #d3d9df !important;
      }
    }
  }

  .page-item-previous {
    margin-right: 12px;
  }

  .page-item-next {
    margin-left: 12px;
  }

  .pagination {
    height: 38px;
  }

  .page-item > button {
    width: 38px;
    height: 38px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
  }

  .btn-primary.active {
    color: white !important;
  }

  .page-item:first-child .page-link,
  .page-item:last-child .page-link {
    border-radius: 6px;
  }

  .page-item:nth-child(2) .page-link {
    margin-left: 0;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .page-item:nth-last-child(2) .page-link {
    margin-left: 0;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .page-item.disabled .page-link {
    background-color: #343a40 !important;
    color: #7b7f83 !important;
  }

  @media (max-width: 600px) {
    .pagination {
      margin: 0 auto;
    }
  }

  @media (max-width: 1200px) {
    .pagination-wrapper {
      flex-direction: column !important;
      align-items: center !important;
    }
    .pagination-size {
      flex: unset;
      max-width: 400px;
    }
  }
</style>
