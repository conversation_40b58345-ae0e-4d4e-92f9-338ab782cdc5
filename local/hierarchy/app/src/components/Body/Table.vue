<template>
  <div class="table-responsive">
    <table class="table table-striped table-hover">
      <thead>
        <tr class="table-header">
          <th v-if="checkboxes">
            <input
              type="checkbox"
              v-model="allSelected"
              @change="toggleAll"
            />
          </th>
          <th
            v-for="header in headers"
            :key="header.key"
            @click="sort(header.key)"
          >
            {{ header.label }}
            <span
              :class="{
                fa: true,
                'fa-sort': !isSortedBy(header.key),
                'fa-sort-up':
                  isSortedBy(header.key) &&
                  getSortDirection(header.key) === 'asc',
                'fa-sort-down':
                  isSortedBy(header.key) &&
                  getSortDirection(header.key) === 'desc',
              }"
              class="sort-icon"
            ></span>
          </th>
          <th
            v-if="showActionsColumn"
            style="width: 120px; text-align: center"
          >
            Ações
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="isLoading">
          <td colspan="100%">
            <div class="loading-container">
              <span
                class="spinner-border spinner-border-sm mr-1"
                role="status"
                aria-hidden="true"
              ></span
              >Carregando dados...
            </div>
          </td>
        </tr>
        <tr
          class="text-center"
          v-else-if="!localData.length"
        >
          <td colspan="100%">Nenhum resultado encontrado...</td>
        </tr>
        <tr
          v-else
          v-for="item in localData"
          :key="item.id"
        >
          <td v-if="checkboxes">
            <input
              type="checkbox"
              v-model="item.selected"
            />
          </td>
          <td
            v-for="field in itemFields"
            :key="field"
            :class="{
              'td-truncate':
                field === 'managers' ||
                field === 'structure' ||
                field === 'name' ||
                field === 'structures',
            }"
          >
            <!-- Verifica se estamos no campo 'is_manager' e renderiza a tag de manager -->
            <template v-if="field === 'is_manager'">
              <div class="d-flex align-items-center">
                <div
                  class="d-flex align-items-center justify-content-center rounded-circle mr-2"
                  style="width: 24px; height: 24px; padding: 4px"
                  :style="{
                    backgroundColor:
                      item.is_manager === true ? '#198754' : '#DC3545',
                  }"
                >
                  <i
                    class="text-white fas"
                    :class="{
                      'fa-check': item.is_manager === true,
                      'fa-xmark': item.is_manager === false,
                    }"
                  ></i>
                </div>
                {{ item.is_manager === true ? "Sim" : "Não" }}
              </div>
            </template>
            <template
              v-else-if="
                field === 'managers' ||
                field === 'structure' ||
                field === 'name' ||
                field === 'structures'
              "
            >
              <div
                class="text-truncate"
                :title="item[field]"
              >
                {{ item[field] }}
              </div>
            </template>
            <!-- Verifica se estamos no campo 'status' e renderiza a tag de status -->
            <template v-else-if="field === 'status'">
              <div class="d-flex align-items-center">
                <div
                  class="d-flex align-items-center justify-content-center rounded-circle mr-2"
                  style="width: 24px; height: 24px; padding: 4px"
                  :style="{
                    backgroundColor: item.status === 1 ? '#198754' : '#DC3545',
                  }"
                >
                  <i
                    class="text-white fas"
                    :class="item.status === 1 ? 'fa-check' : 'fa-xmark'"
                  ></i>
                </div>
                {{ item.status === 1 ? "Ativo" : "Inativo" }}
              </div>
            </template>
            <template v-else>
              <span
                class="text-nowrap"
                v-html="item[field]"
              ></span>
            </template>
          </td>
          <td v-if="showActionsColumn">
            <div class="d-flex justify-content-center">
              <button
                v-if="onView"
                @click="viewItem(item.id)"
                class="btn text-primary"
                title="Visualizar"
              >
                <i class="fas fa-eye"></i>
              </button>
              <button
                v-if="onEdit"
                @click="editItem(item.id)"
                class="btn text-primary"
                title="Editar"
              >
                <i class="fas fa-pen"></i>
              </button>
              <button
                v-if="onDelete"
                @click="item.status === 1 ? '' : deleteItem(item)"
                class="btn text-danger"
                :class="{ 'text-muted': item.status === 1 }"
                :title="item.status === 1 ? '' : 'Excluir'"
                role="button"
                data-trigger="focus"
                :data-toggle="item.status === 1 ? 'popover' : ''"
                data-placement="left"
                tabindex="0"
                data-container="body"
                data-html="true"
                :data-content="`<div class='no-overflow'><p class='text-center mb-0 font-size-15'>Não é possível excluir ${
                  this.$router.currentRoute.value.name === 'hierarchy.positions'
                    ? 'um cargo ativo'
                    : 'uma estrutura ativa'
                }</p></div>`"
                :aria-label="
                  'Não é possível excluir ' + this.$router.currentRoute.name ===
                  'hierarchy.positions'
                    ? 'um cargo ativo'
                    : 'uma estrutura ativa'
                "
              >
                <i class="far fa-trash-can"></i>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
  import { getStrings } from "@/helpers/moodle";

  export default {
    name: "Table",
    components: {},
    props: {
      data: Array,
      headers: Array,
      isLoading: Boolean,
      totalPages: Number,
      currentPage: Number,
      checkboxes: Boolean,
      onView: Function,
      onEdit: Function,
      onDelete: Function,
    },
    data() {
      return {
        allSelected: false,
        localData: [],
        sortParams: [],
        strings: {},
      };
    },
    async created() {
      // let stringArray = [
      //   {
      //     key: "manager",
      //     component: "local_hierarchy",
      //   },
      // ];
      // let strings = await getStrings(stringArray);
      // this.strings = strings;
    },
    computed: {
      itemFields() {
        return this.headers.map((header) => header.key);
      },
      showActionsColumn() {
        return !!this.onView || !!this.onEdit || !!this.onDelete;
      },
    },
    watch: {
      data: {
        handler(newData) {
          this.localData = [...newData];
        },
        deep: true,
        immediate: true,
      },
    },
    methods: {
      toggleAll() {
        if (this.checkboxes) {
          const isAllSelected = this.allSelected;
          this.localData.forEach((item) => {
            this.$set(item, "selected", isAllSelected);
          });
        }
      },
      sort(key) {
        const existingSort = this.sortParams.find(
          (param) => param.field === key
        );

        if (existingSort) {
          if (existingSort.direction === "asc") {
            // Altera a direção para descendente se já estiver em ascendente
            existingSort.direction = "desc";
          } else if (existingSort.direction === "desc") {
            // Remove a ordenação se já estiver em descendente
            this.sortParams = this.sortParams.filter(
              (param) => param.field !== key
            );
          }
        } else {
          // Adiciona uma nova ordenação com direção ascendente
          this.sortParams.push({
            field: key,
            direction: "asc",
          });
        }

        // Emite os parâmetros de ordenação múltipla
        this.$emit("sort-changed", this.sortParams);
      },
      isSortedBy(key) {
        return this.sortParams.some((param) => param.field === key);
      },
      getSortDirection(key) {
        const sortParam = this.sortParams.find((param) => param.field === key);
        return sortParam ? sortParam.direction : "asc";
      },
      viewItem(id) {
        if (this.onView) {
          this.onView(id);
        }
      },
      editItem(id) {
        if (this.onEdit) {
          this.onEdit(id);
        }
      },
      deleteItem(id) {
        if (this.onDelete) {
          this.onDelete(id);
        }
      },
    },
  };
</script>

<style lang="scss">
  /* Estilização da tabela */
  $background-button: #f8f9fa;
  $border-color: #adb5bd;
  $color-white: #fff;
  $color-sortable-th: #6ea8fe;
  $color-sortable-th-hover: darken($background-button, 5%);
  $font-size-label: 14px;
  $font-size-sortable-th: 12px;
  $font-size-th: 16px;
  $border-radius-default: 8px;
  $border-radius-input: 5px;
  $button-width: 44px;
  $checkbox-focus-shadow: none;

  #hierarchy-app .table-responsive {
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 20px;
    }

    table {
      border-collapse: collapse;
      margin-bottom: 0 !important;
      width: 100%;
      background-color: transparent !important;
      border-top: 0 !important;

      th,
      td {
        padding: 0 12px;
        height: 50px;
        vertical-align: middle;
        border-top: 0 !important;
      }

      th {
        color: var(--primary);
        font-size: $font-size-th;
        text-transform: uppercase;
        white-space: nowrap;
        cursor: pointer;

        .fa {
          color: #adb5bd;
          margin-left: 4px;
        }
      }

      tr td.checkboxes {
        width: 50px;
      }

      input[type="checkbox"] {
        &:focus {
          box-shadow: $checkbox-focus-shadow;
        }
      }

      .table-header {
        border-bottom: var(--primary) 3px solid;
      }

      .td-truncate {
        max-width: 200px;
      }

      .td-status {
        color: #fff;
        display: inline-block;
        font-size: 12px;
        text-transform: uppercase;
        font-weight: bold;
        padding: 4px 8px;
        border-radius: 5px;
      }

      .td-manager {
        color: #232426;
        display: inline-block;
        font-size: 12px;
        text-transform: uppercase;
        font-weight: 900;
        padding: 4px 8px;
        border-radius: 100%;
        border: solid #1f281f;
      }

      .btn-view,
      .btn-edit {
        color: var(--primary);
        font-size: 16px;
        outline: unset;
        box-shadow: unset;
        transition: color 0.3s ease;

        svg {
          fill: var(--primary);
          width: 16 px;
          margin-top: -1px;
        }

        &:hover {
          color: var(--primary);
          cursor: pointer;
        }
      }

      .btn-delete {
        color: #ce3545;
        font-size: 20px;
        outline: unset;
        box-shadow: unset;
        transition: color 0.3s ease;

        &:hover {
          color: #a12f3f;
          cursor: pointer;
        }
      }
    }
  }
</style>
