<template>
  <transition name="fade">
    <div
      class="modal fade"
      :id="id"
      data-backdrop="static"
      data-keyboard="false"
      tabindex="-1"
      aria-labelledby="staticBackdropLabel"
      aria-hidden="true"
    >
      <div
        class="modal-dialog"
        :class="[size, { 'modal-dialog-scrollable': scrollable }]"
        tabindex="0"
      >
        <div class="modal-content">
          <div class="modal-header">
            <h5
              class="modal-title"
              id="staticBackdropLabel"
            >
              {{ title }}
            </h5>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              title="Fechar"
              aria-label="Fechar"
              @click="$emit('close')"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="modal-body">
            <slot></slot>
          </div>
          <div
            v-if="showFooter"
            class="modal-footer d-flex align-items-center"
            :class="
              requiredMessage
                ? 'justify-content-between'
                : 'justify-content-end'
            "
          >
            <p v-if="requiredMessage">
              Este formulário contém campos obrigatórios marcados com
              <i class="fa-solid fa-circle-exclamation text-danger"></i>
            </p>
            <div class="d-flex align-items-center">
              <button
                type="button"
                class="btn btn-dark mr-2"
                data-dismiss="modal"
                title="Fechar"
                aria-label="Fechar"
                @click="$emit('close')"
              >
                {{ strings.cancel }}
              </button>
              <button
                type="button"
                class="btn btn-primary"
                title="Salvar"
                aria-label="Salvar"
                @click="$emit('submit')"
              >
                {{ strings.save }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
  import { getStrings } from "@/helpers/moodle";

  export default {
    name: "Modal",
    props: {
      title: String,
      showFooter: {
        type: Boolean,
        default: true,
      },
      id: {
        type: String,
        default: "items-modal",
      },
      size: {
        type: String,
        default: "modal-lg",
      },
      requiredMessage: {
        type: Boolean,
        default: true,
      },
      scrollable: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        strings: {},
      };
    },
    async created() {
      let stringArray = [
        {
          key: "cancel",
          component: "local_hierarchy",
        },
        {
          key: "save",
          component: "local_hierarchy",
        },
      ];

      this.strings = await getStrings(stringArray);
    },
    methods: {},
  };
</script>

<style lang="scss" scoped>
  #page-local-hierarchy-index {
    .modal-header {
      background-color: #292d31;

      .close {
        color: var(--primary) !important;
      }
    }
  }
</style>
