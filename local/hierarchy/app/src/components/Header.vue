<template>
  <div id="items-header">
    <div
      class="d-flex flex-column flex-md-row justify-content-between align-items-stretch align-items-md-center mb-5"
    >
      <div class="d-flex align-items-center">
        <h4 class="m-0 font-weight-normal">
          {{ title }}
          <a
            v-if="tooltip"
            class="btn btn-link py-0 px-1"
            role="button"
            data-trigger="focus"
            data-toggle="popover"
            data-placement="right"
            tabindex="0"
            data-container="body"
            data-html="true"
            :data-content="'<div>' + tooltip + '</div>'"
          >
            <i
              title="Ajuda"
              class="far fa-question-circle text-primary"
            ></i>
          </a>
        </h4>
      </div>
      <div
        class="d-flex flex-column flex-md-row align-items-start align-items-md-center mt-3 mt-md-0"
      >
        <slot name="actions"></slot>
        <button
          class="btn btn-link ml-md-2"
          title="Ver Organograma"
          @click="$emit('open-org-chart')"
        >
          <i class="fa fa-users mr-2"></i>
          {{ labelOrgChart }}
        </button>
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
  export default {
    name: "Header",
    components: {},
    data() {
      return {};
    },
    props: {
      title: String,
      labelOrgChart: String,
      tooltip: String,
    },
    methods: {},
    emits: ["open-org-chart"],
  };
</script>
