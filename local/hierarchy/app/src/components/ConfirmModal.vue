<template>
  <transition name="fade">
    <div
      @click.stop
      class="modal fade"
      id="confirm-modal"
      data-backdrop="static"
      data-keyboard="false"
      tabindex="-1"
      aria-labelledby="staticBackdropLabel"
      aria-hidden="true"
    >
      <div
        class="modal-dialog"
        tabindex="0"
      >
        <div class="modal-content">
          <div class="modal-header">
            <h5
              class="modal-title"
              id="staticBackdropLabel"
            >
              {{ title }}
            </h5>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
              @click="close"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="modal-body">
            <p>{{ message }}</p>
          </div>
          <div
            class="modal-footer d-flex justify-content-end align-items-center"
          >
            <div class="d-flex align-items-center">
              <button
                type="button"
                class="btn btn-dark mr-2"
                data-dismiss="modal"
                @click="close"
              >
                {{ strings.cancel }}
              </button>
              <button
                type="button"
                class="btn btn-primary"
                @click="action"
              >
                {{ actionLabel }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
  import { getStrings } from "@/helpers/moodle";

  export default {
    name: "ConfirmDeleteModal",
    props: {
      title: String,
      actionLabel: String,
      message: String,
    },
    data() {
      return {
        strings: {},
      };
    },
    async created() {
      let stringArray = [
        {
          key: "cancel",
          component: "local_hierarchy",
        },
      ];

      this.strings = await getStrings(stringArray);
    },
    methods: {
      close() {
        $("#confirm-modal").modal("hide");
      },
      action() {
        this.$emit("action");
        this.close();
      },
    },
  };
</script>

<style lang="scss" scoped>
  #page-local-hierarchy-index {
    .modal-header {
      background-color: #292d31;

      .close {
        color: var(--primary) !important;
      }
    }
  }
</style>
