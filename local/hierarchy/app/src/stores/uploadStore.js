import { defineStore } from "pinia";

export const useUploadStore = defineStore("upload", {
    state: () => ({
        titlePage: "",
        subTitlePage: "",
        entity: "",
        previewData: {},
        processData: {},
    }),
    actions: {
        setUploadData(titlePage, subTitlePage, entity) {
            this.titlePage = titlePage;
            this.subTitlePage = subTitlePage;
            this.entity = entity;
        },
        setPreviewData(data, base64Content) {
            this.previewData = data;
            this.base64Content = base64Content;
        },
        setProcessData(data) {
            this.processData = data;
        },
    },
    persist: true
});