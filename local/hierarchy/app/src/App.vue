<template>
  <div class="">
    <RouterView />
  </div>
</template>

<script>
  import { RouterView } from "vue-router";

  export default {
    components: {
      RouterView,
    },
    mounted() {
      this.setupVSelectGlobalConfig(); // Configurações globais para o vue-select
    },
    methods: {
      // Método principal para configurar o vue-select globalmente
      setupVSelectGlobalConfig() {
        this.updateClearButtonAttributes(); // Atualiza os botões de limpar
        this.setupMutationObserver(); // Configura o MutationObserver para mudanças dinâmicas
      },

      // Método para atualizar os atributos dos botões de limpar
      updateClearButtonAttributes() {
        const clearButtons = document.querySelectorAll(".vs__clear");
        clearButtons.forEach((button) => {
          button.setAttribute("title", "Limpar seleção");
          button.setAttribute("aria-label", "Limpar seleção");
        });
      },

      // Método para configurar o MutationObserver
      setupMutationObserver() {
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === "childList") {
              this.updateClearButtonAttributes(); // Atualiza os botões de limpar quando o DOM muda
            }
          });
        });

        // Inicia a observação no corpo do documento
        observer.observe(document.body, {
          childList: true, // Observa adições/remoções de elementos
          subtree: true, // Observa em toda a árvore do DOM
        });
      },
    },
  };
</script>
