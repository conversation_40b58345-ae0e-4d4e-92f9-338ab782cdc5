import * as Config from 'core/config'
import IndexView from '../views/Index.vue'
import { createRouter, createWebHistory } from 'vue-router'

const basePath = '/local/hierarchy/'

const dynamicPath = (() => {
  const host = window.location.host
  const currentPath = window.location.pathname
  const relativePath = Config.wwwroot
    .replace(/^https?\:\/\//i, '')
    .replace(host, '')
    .concat(basePath)
  return currentPath.includes('index.php') ? relativePath + 'index.php' : relativePath
})()

const router = createRouter({
  history: createWebHistory(dynamicPath),
  routes: [
    {
      path: '/',
      name: 'hierarchy.index',
      component: IndexView,
      children: [
        {
          path: '/positions',
          name: 'hierarchy.positions',
          component: () => import('../views/Positions/Index.vue'),
          children: [
            {
              path: 'view/:id',
              name: 'hierarchy.positions.view',
              component: () => import('../views/Positions/View.vue'),
              meta: {
                isModal: true
              }
            },
            {
              path: 'create',
              name: 'hierarchy.positions.create',
              component: () => import('../views/Positions/Create.vue'),
              meta: {
                isModal: true
              }
            },
            {
              path: 'edit/:id',
              name: 'hierarchy.positions.edit',
              component: () => import('../views/Positions/Edit.vue'),
              meta: {
                isModal: true
              }
            },
          ]
        },
        {
          path: '/structures',
          name: 'hierarchy.structures',
          component: () => import('../views/Structures/Index.vue'),
          children: [
            {
              path: 'types',
              name: 'hierarchy.structures.types',
              component: () => import('../views/Structures/Types.vue'),
              meta: {
                isModal: true
              }
            },
            {
              path: 'view/:id',
              name: 'hierarchy.structures.view',
              component: () => import('../views/Structures/View.vue'),
              meta: {
                isModal: true
              }
            },
            {
              path: 'create',
              name: 'hierarchy.structures.create',
              component: () => import('../views/Structures/Create.vue'),
              meta: {
                isModal: true
              }
            },
            {
              path: 'edit/:id',
              name: 'hierarchy.structures.edit',
              component: () => import('../views/Structures/Edit.vue'),
              meta: {
                isModal: true
              }
            },
          ]
        },
      ]
    },
    {
      path: '/upload/:entity',
      name: 'hierarchy.upload',
      component: () => import('../views/Upload/Index.vue'),
    },
    {
      path: '/upload/:entity/preview',
      name: 'hierarchy.upload.preview',
      component: () => import('../views/Upload/Preview.vue'),
    },
    {
      path: '/upload/:entity/process',
      name: 'hierarchy.upload.process',
      component: () => import('../views/Upload/Process.vue'),
    },
    {
      path: '/orgchart/positions',
      name: 'hierarchy.orgchart.positions',
      component: () => import('../views/Positions/OrgChart.vue'),
    },
    {
      path: '/orgchart/structures',
      name: 'hierarchy.orgchart.structures',
      component: () => import('../views/Structures/OrgChart.vue'),
    }
  ]
})

export default router
