// app/helpers/tooltips.js
export const TOOLTIPS = {
  positions: `
      <p>Para cadastrar novos cargos ou atualizar os existentes no gerenciador, baixe o arquivo "exemplo.csv". Abra o arquivo em um editor de texto ou planilhas, mantenha a primeira linha inalterada e edite apenas as linhas subsequentes (registros). Após as alterações, salve o arquivo no formato CSV e faça o upload.</p>
      <p>Esse arquivo de exemplo também pode ser utilizado para testes, permitindo pré-visualizar os cargos antes de cadastrá-los ou atualizá-los, com a opção de cancelar a ação antes da confirmação no gerenciador.</p>
      <ul>
        <li>Para cadastrar novos cargos, preencha apenas as colunas "Cargo" e "Gestor":</li>
        <ul>
          <li>A coluna "Cargo" deve conter o nome do novo cargo;</li>
          <li>A coluna "Gestor" deve indicar "Sim" se o cargo for de gestão, ou "Não" se não for.</li>
        </ul>
        <li>Para atualizar cargos existentes, preencha as colunas "id_cargo" e "Cargo":</li>
        <ul>
          <li>A coluna "id_cargo" deve conter o número identificador do cargo;</li>
          <li>A coluna "Cargo" deve conter o novo nome do cargo a ser atualizado.</li>
        </ul>
      </ul>
      <p>Obs: Quando um cargo é “Atualizado“, não é possível ativar ou inativar a coluna “Gestor“.</p>
    `,
  structures: `
      <p>Para cadastrar novas estruturas ou atualizar as existentes no gerenciador, baixe o arquivo "exemplo.csv". Abra o arquivo em um editor de texto ou planilhas, mantenha a primeira linha inalterada e edite apenas as linhas subsequentes (registros). Após as alterações, salve o arquivo no formato CSV e faça o upload.</p>
      <p>Esse arquivo de exemplo também pode ser utilizado para testes, permitindo pré-visualizar as estruturas antes de cadastrá-las ou atualizá-las, com a opção de cancelar a ação antes da confirmação no gerenciador.</p>
      <ul>
        <li>Para cadastrar novas estruturas, preencha apenas as colunas "estrutura" e "tipo":</li>
        <ul>
          <li>A coluna "estrutura" deve conter o nome da nova estrutura;</li>
          <li>A coluna "tipo" deve conter o tipo da nova estrutura;</li>
        </ul>
        <li>Para atualizar as estruturas existentes, preencha as colunas "id_estrutura" e "estrutura":</li>
        <ul>
          <li>A coluna "id_estrutura" deve conter o número identificador da estrutura;</li>
          <li>A coluna "estrutura" deve conter o novo nome da nova estrutura;</li>
          <li>A coluna "tipo" deve conter o novo tipo da estrutura a ser atualizada.</li>
        </ul>
      </ul>
    `,
};