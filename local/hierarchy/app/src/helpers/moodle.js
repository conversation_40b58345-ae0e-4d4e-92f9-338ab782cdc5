// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
import * as Str from 'core/str';
import Ajax from 'core/ajax';
import Notification from 'core/notification';
import Toast from 'core/toast';
import ModalForm from 'core_form/modalform';
import ModalFactory from 'core/modal_factory';

const getStrings = async (stringsArray) => {
  let strs = await Str.get_strings(stringsArray);
  let stringObj = {};
  stringsArray.forEach(function (value, index) {
    stringObj[value.key] = strs[index];
  });

  return stringObj;
};

const ajax = async (method, args) => {
  const request = {
    methodname: method,
    args: Object.assign({}, args)
  }

  try {
    return await Ajax.call([request])[0]
  } catch (e) {
    Notification.exception(e)
    throw e
  }
}

const getURLParams = () => {
  return new URLSearchParams(window.location.search);
};

const setURLParam = (param, value) => {
  const urlParams = new URLSearchParams(window.location.search);

  urlParams.set(param, value);

  const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
  window.history.pushState({}, '', newUrl);
};

const toastSuccess = (message) => {
  Toast.add(message, { type: 'success' })
};

const toastError = (message) => {
  Toast.add(message, { type: 'danger' })
};

const toastWarning = (message) => {
  Toast.add(message, { type: 'warning' })
};

const getDynamicForm = (classname, params, title) => {
  // Crie uma instância do ModalForm
  const modal = new ModalForm({
    formClass: classname,
    args: params,
    modalConfig: {
      title: title,
      type: ModalFactory.types.SAVE_CANCEL
    }
  });

  modal.addEventListener(modal.events.LOADED, () => {
    setTimeout(() => {

      const footer = modal.modal.getFooter();

      const saveButton = footer.find("[data-action='save']");
      const cancelButton = footer.find("[data-action='cancel']");

      if (saveButton.length && cancelButton.length) {

        saveButton.remove();
        cancelButton.remove();

        const textElement = $('<div class="modal-footer-text col">Este formulário contém campos obrigatórios marcados com <i class="icon fa fa-exclamation-circle text-danger fa-fw" title="Campo obrigatório" role="img" aria-label="Campo obrigatório"></i></div>');
        footer.prepend(textElement);

        saveButton.text(modal.config.saveButtonText || 'Salvar');
        saveButton.addClass('w-100');
        cancelButton.removeClass('btn-secondary');
        cancelButton.addClass('btn-dark ml-2 w-100');

        const buttonWrapper = $('<div class="modal-footer-buttons col-lg-4 d-flex"></div>');
        
        buttonWrapper.append(saveButton);
        buttonWrapper.append(cancelButton);

        footer.addClass('row d-flex justify-content-between');

        footer.append(buttonWrapper);
      }
    }, 0);
  });

  return modal;
};


const createMoodleModal = (title, body, type = 'SAVE_CANCEL') => {
  return ModalFactory.create({
    type: ModalFactory.types[type],
    title: title,
    removeOnClose: true,
    body: body
  })
};

export {
  ajax,
  getStrings,
  getURLParams,
  setURLParam,
  toastSuccess,
  toastError,
  toastWarning,
  getDynamicForm,
  createMoodleModal
};