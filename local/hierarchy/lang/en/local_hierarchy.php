<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * English language pack for Position Manager
 *
 * @package    local_hierarchy
 * @category   string
 * @copyright  2024 REVVO <www.somosrevvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Hierarchy Manager';
$string['positions_manager'] = 'Positions Manager';
$string['structures_manager'] = 'Structures Manager';
$string['hierarchy:manage'] = 'Manage the Hierarchy Manager plugin';
$string['privacy:metadata'] = 'The Hierarchy Manager plugin doesn\'t store any personal data.';
$string['vieworgchart'] = "View Organogram";

// settings orgchart
$string['settings:orgchart_title'] = 'Orgchart Positions';
$string['settings:orgchart_manager_main_heading_title'] = 'Main Manager Position';
$string['settings:orgchart_manager_main_heading_desc'] = '';
$string['settings:orgchart_manager_main'] = 'Manager position';
$string['settings:orgchart_manager_main_desc'] = 'This will be the default primary manager for the job chart if none is selected.';

// uploaduser
$string['invalidpositionid'] = 'Position ID not found';

// form
$string['positiondata'] = 'Data of the Position';

// general & tab cargos
$string['filter'] = 'Filter';
$string['is_manager_question'] = 'Qualify as a manager position';
$string['filter_is_manager_question'] = 'Management Positions';
$string['is_lock_edit'] = 'Block manual editing of position';
$string['filter_apply'] = 'Apply';
$string['filter_clear'] = 'Clear';
$string['id'] = 'ID';
$string['position_id'] = 'ID of the Position';
$string['position'] = 'Position';
$string['position_plural'] = 'Positions';
$string['position_name'] = 'Position name';
$string['found'] = 'Found';
$string['found_plural'] = 'Founds';
$string['position_manager'] = 'Position manager';
$string['manager'] = 'Manager';
$string['status'] = 'Status';
$string['active'] = 'Active';
$string['active_plural'] = 'Actives';
$string['inactive'] = 'Inactive';
$string['inactive_plural'] = 'Inactives';
$string['action_plural'] = 'Actions';
$string['pagination:legend'] = 'Showing {$a->firstelementcount} out {$a->firstelementcount} of {$a->perpage} results per page';
$string['footer:bashupload'] = 'Bash upload';
$string['footer:exportxlsx'] = 'Export to XLSX';
$string['footer:exportcsv'] = 'Export to CSV';
$string['addposition'] = 'Add position';
$string['viewposition'] = 'View position';
$string['editposition'] = 'Edit position';
$string['deleteposition'] = 'Delete position';
$string['deleteposition_body'] = 'Are you sure you want to delete the position "{$a}"? This operation is irreversible!';
$string['deleteposition_success'] = 'The position "{$a}" was deleted successfully!';
$string['deleteposition_error'] = 'Something went wrong when deleting the position "{$a}"!';
$string['nonepositionselected'] = 'No selected position';
$string['nonestructureselected'] = 'No selected strcuture';
$string['name'] = 'Position';
$string['managerid'] = 'Manager ID';
$string['managers'] = 'Reports to';
$string['is_manager'] = 'Manager';
$string['selected_positions'] = 'Selected positions';
$string['position_manager_master'] = 'Position master manager';
$string['cleanpositions'] = 'Clean positions';
$string['position_subordinates'] = 'Position subordinates';
$string['positionupdated'] = 'Position updated!';
$string['positioncreated'] = 'Position created!';


// tab structures
$string['structure'] = 'Structure';
$string['structures'] = 'Structures';
$string['subordinate'] = 'Subordinate to';
$string['type'] = 'Type';

// tab settings
$string['setting'] = 'Setting';
$string['setting_plural'] = 'Settings';
$string['settingsnotfound'] = 'Settings not found';
$string['settings_manager'] = 'Settings Manager';
$string['settings_load_error'] = 'Error loading settings';

// structures strings
$string['structures_actions'] = 'Actions';
$string['structures_add'] = 'Add structure';
$string['structures_self'] = 'Self';
$string['structures_create_successfully'] = 'Structure created successfully!';
$string['structures_delete_successfully'] = 'Structure "{$a}" deleted successfully!';
$string['structures_update_successfully'] = 'Structure updated successfully!';
$string['structures_create_failed'] = 'Structure creation failed!';
$string['structures_delete_failed'] = 'Structure deletion failed!';
$string['structures_update_failed'] = 'Structure edit failed!';
$string["structures_confirm_delete_title"] = 'Delete structure';
$string["structures_confirm_delete_message"] = 'Are you sure you want to delete the structure "{$a}"? This operation is irreversible!';
$string['structures_description'] = 'Description';
$string['structures_edit'] = 'Structure edit';
$string['structures_form'] = 'Structure form';
$string['structures_level'] = 'Level';
$string['structures_locked'] = 'Locked';
$string['structures_load_error'] = 'Error loading structures';
$string['structures_name'] = 'Name';
$string['structures_name_invalid'] = 'Name invalid. Already exists a structure with this name. Please choose a different name.';
$string['structures_position_select'] = 'Select position';
$string['structures_section'] = 'Structures';
$string['structures_selected'] = 'Selected structure';
$string['structures_select'] = 'Select structure';
$string['structures_type_area'] = 'Area';
$string['structures_type_branch'] = 'Branch';
$string['structures_type_business_unit'] = 'Business Unit';
$string['structures_type_create_successfully'] = 'Structure type created successfully!';
$string['structures_type_delete_successfully'] = 'Structure type deleted successfully!';
$string['structures_type_update_successfully'] = 'Structure type updated successfully!';
$string['structures_type_create_failed'] = 'Structure type creation failed!';
$string['structures_type_delete_failed'] = 'Structure type deletion failed!';
$string['structures_type_update_failed'] = 'Structure type edit failed!';
$string['structures_type_department'] = 'Department';
$string['structures_type_directory'] = 'Directory';
$string['structures_type_franchise'] = 'Franchise';
$string['structures_type_section'] = 'Section';
$string['structures_type_select'] = 'Select structure type';
$string['structures_type_store'] = 'Store';
$string['structures_type'] = 'Type';
$string["structures_type_title"] = 'Structure Types';
$string['structures_type_name'] = 'Name';
$string['structures_type_name_invalid'] = 'Name invalid. Already exists a structure type with this name. Please choose a different name.';
$string["structures_type_actions"] = 'Actions';
$string["structures_type_new"] = 'Structure Type Name';
$string["structures_type_cannot_delete"] = 'Structure Type in Use';
$string["structures_type_confirm_delete_title"] = 'Delete Structure Type';
$string["structures_type_confirm_delete_message"] = 'Are you sure you want to delete the structure type "{$a}"? This operation is irreversible!';
$string["structures_type_required_fields"] = 'Required Fields cannot be empty';
$string['structures_unlocked'] = 'Unlocked';
$string['structures_update_error'] = 'Error updating structure';
$string['structures_update_success'] = 'Structure updated successfully';
$string['structures_help'] = "The structure is a generic term to describe a specific organization within the company, making it easier to categorize and manage operational sectors. The name can be customized in the 'Settings' tab of the 'Hierarchy Manager'.";
$string['structures_help_search'] = "Search by structure name or ID";

// structure events
$string['event:structure_created'] = 'Structure created';
$string['event:structure_updated'] = 'Structure updated';
$string['event:structure_deleted'] = 'Structure deleted';
$string['event:structure_created_desc'] = 'The user with id \'{$a->userid}\' created a structure id \'{$a->objectid}\'';
$string['event:structure_updated_desc'] = 'The user with id \'{$a->userid}\' updated the structure id \'{$a->objectid}\'';
$string['event:structure_deleted_desc'] = 'The user with id \'{$a->userid}\' deleted the structure id \'{$a->objectid}\'';

// pagination
$string['showing'] = 'Showing';
$string['of'] = 'of';
$string['to'] = 'to';
$string['previous'] = 'Previous';
$string['next'] = 'Next';
$string['result_per_page'] = 'results per page';

//events
$string['position_locked'] = 'Manual edit LOCKED successfully.';
$string['position_unlocked'] = 'Manual edit UNLOCKED successfully.';
$string['event:position_created'] = 'Position created';
$string['event:position_updated'] = 'Position updated';
$string['event:position_deleted'] = 'Position deleted';
$string['event:position_created_desc'] = 'The user with id \'{$a->userid}\' created a position id \'{$a->objectid}\'';
$string['event:position_updated_desc'] = 'The user with id \'{$a->userid}\' updated the position id \'{$a->objectid}\'';
$string['event:position_deleted_desc'] = 'The user with id \'{$a->userid}\' deleted the position id \'{$a->objectid}\'';

// errors
$string['namealreadyexistes'] = 'Invalid name. A position with this name already exists. Please choose a different name.';
$string['structurealreadyexistes'] = 'Invalid name. A structure with this name already exists. Please choose a different name.';
$string['samepositionid'] = 'You cannot select your own position as the manager position';
$string['positiondoesntexist'] = 'The selected position does not exist!';
$string['notamanagerposition'] = 'The position selected as manager position is not a management position!';
$string['hassubordinates'] = 'You cannot remove the position {$a} as a management position because there are positions in the system that are subordinate to this position.';
$string['cannotmarkasmanager'] = 'You cannot mark the position as a management position because there are users in the system who report to that position.';
$string['lockediterror'] = 'Error updating edit lock status.';
$string['lockediterrorbody'] = 'Failed to update edit lock status.';
$string['geteditlockstatuserror'] = 'Error getting manual edit lock status.';
$string['position_manager_in_use'] = 'The manager position is already being used by another user.';

//help tooltips
$string['help_position_search'] = 'Search by position name or ID';
$string['help_blockedit'] = 'By enabling this setting, the user will not be able to edit their own “Position” on their profile page.';
$string['help_managers'] = 'This field indicates the superior position(s) of the current position.';
$string['help_subordinates'] = 'This block lists the positions that are subordinate to the currently selected position, presenting them hierarchically.';


//btn footer
$string['btn_upload_positions'] = 'Bash upload';
$string['btn_exportxls'] = 'Export to XLSX';
$string['btn_exportcsv'] = 'Export to CSV';


//upload module
$string['uploadpositions'] = 'Batch Load Jobs';
$string['help_uploadpositions'] = 'Help with Bulk Uploading Jobs';
$string['examplefile'] = 'Example text file';
$string['help_examplefile'] = 'Help with Example Text File';
$string['fileupload'] = 'File';
$string['selectfile'] = 'Choose a file';
$string['dragdropfile'] = 'You can drag and drop files here to add them.';
$string['fileerror'] = 'Please select a CSV file before continuing.';
$string['delimiter'] = 'CSV delimiter';
$string['encoding'] = 'Encoding';
$string['lines'] = 'Preview lines';
$string['transmission'] = 'Transmission Type';
$string['required_fields'] = 'Required fields';
$string['save'] = 'Save';
$string['cancel'] = 'Cancel';
$string['close'] = 'Close';
$string['back'] = 'Back';
$string['validate_id_exists'] = 'Invalid ID. A position with this ID already exists. Please choose a different ID.';
$string['validate_name_exists'] = 'Invalid name. A position with this name already exists. Please choose a different name.';
$string['validate_manage_update'] = 'It is not possible to change the manager column if the operation is update.';
$string['validate_update'] = 'OK - Update';
$string['validate_create'] = 'OK - Create';
$string['validate_invalid_type'] = 'There is no type of structure with that name.';
$string['validate_id_exists_structure'] = 'There is no structure with this ID.';
$string['validate_name_exists_structure'] = 'There is already a structure with this name.';
