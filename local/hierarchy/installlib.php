<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Install library for Position Manager
 *
 * Documentation: {@link https://moodledev.io/docs/apis/subsystems/access}
 *
 * @package    local_hierarchy
 * @category   access
 * @copyright  2024 REVVO <www.somosrevvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use \tool_lfxp\helpers\user\profile\custom_profile_category;

/**
 * This lib is not automatically loaded.
 *
 * Put functions that will assist the installation
 * process.
 *
 */

function tool_hierarchy_get_default_custom_profile_fields(): array
{
    return [
        [
            'shortname'         => 'position',
            'name'              => 'Cargo',
            'datatype'          => 'position',
            'description'       => '<p>Cargo via Gerenciador de Cargos</p>',
            'descriptionformat' => 1,
            'required'          => 1,
            'locked'            => 0,
            'visible'           => 3,
            'forceunique'       => 0,
            'signup'            => 1,
            'defaultdata'       => '',
            'defaultdataformat' => 0,
            'param1'            => null,
            'param2'            => null,
            'param3'            => null,
            'param4'            => null,
            'param5'            => null,
        ],
        [
            'shortname'         => 'structure',
            'name'              => 'Estrutura',
            'datatype'          => 'structure',
            'description'       => '<p>Estrutura via Gerenciador de Estruturas</p>',
            'descriptionformat' => 1,
            'sortorder'         => 1,
            'required'          => 1,
            'locked'            => 0,
            'visible'           => 3,
            'forceunique'       => 0,
            'signup'            => 1,
            'defaultdata'       => '',
            'defaultdataformat' => 0,
            'param1'            => null,
            'param2'            => null,
            'param3'            => null,
            'param4'            => null,
            'param5'            => null,
        ],
    ];
}

function tool_hierarchy_get_default_custom_profile_category(): array
{
    return [
        'name' => 'Dados Profissionais',
    ];
}

/**
 * Creates the default custom profile fields for the LearningFlix XP
 * @param bool $override_existing
 * @return void
 */
function tool_hierarchy_install_default_custom_profile_fields($override_existing = false)
{
    global $CFG, $DB;

    $category_raw = tool_hierarchy_get_default_custom_profile_category();
    $category_name = $category_raw['name'];

    $category = custom_profile_category::get_by_name($category_name);
    if (!$category) {
        $category = custom_profile_category::create($category_name);
    }

    foreach (tool_hierarchy_get_default_custom_profile_fields() as $field_raw) {
        try {
            $category->create_child($field_raw, $override_existing);
        } catch (\Throwable $th) {
            debugging($th->getMessage(), DEBUG_DEVELOPER);
        }
    }
}
