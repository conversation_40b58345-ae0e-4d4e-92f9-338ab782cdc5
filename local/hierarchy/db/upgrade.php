<?php
// This file is part of Moodle - https://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.

/**
 * Plugin upgrade steps are defined here.
 *
 * @package     local_hierarchy
 * @category    upgrade
 * @copyright   2023 Revvo <<EMAIL>>
 * @license     https://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use local_hierarchy\model\positions;

defined('MOODLE_INTERNAL') || die();

/**
 * Execute local_hierarchy upgrade from the given old version.
 *
 * @param int $oldversion
 * @return bool
 */
function xmldb_local_hierarchy_upgrade($oldversion)
{
    global $DB, $USER;

    $dbman = $DB->get_manager();

    // Verifica a versão do plugin.
    if ($oldversion < **********) {
        // Etapa 1: Obter o ID do campo antigo (cargo legado do tipo texto).
        $old_field_record = $DB->get_record('user_info_field', ['shortname' => 'cargo', 'datatype' => 'text'], 'id');
        if (!$old_field_record) {
            // Erro: campo legado não encontrado.
            throw new moodle_exception('Campo legado "cargo" não encontrado.');
        }
        $old_field_id = $old_field_record->id;

        // Etapa 2: Obter os cargos distintos (evitando repetição).
        $distinct_cargos = $DB->get_records_sql("
            SELECT DISTINCT data
            FROM {user_info_data}
            WHERE fieldid = :fieldid
            AND data <> ''
        ", ['fieldid' => $old_field_id]);

        if (empty($distinct_cargos)) {
            // Erro: Nenhum cargo encontrado.
            throw new moodle_exception('Nenhum cargo encontrado no campo "cargo".');
        }

        // Etapa 3: Inserir cargos distintos na tabela `mdl_local_hierarchy_position`.
        foreach ($distinct_cargos as $cargo) {
            // Verifica se o cargo já existe na tabela de posições usando o model `positions`.
            $existing_position = positions::get_record(['name' => $cargo->data]);

            if (!$existing_position) {
                // Cria uma nova instância do model positions.
                $new_position = new positions();

                // Define o nome da posição.
                $new_position->set('name', $cargo->data);

                // Salva a nova posição no banco de dados.
                $new_position->save();
            }
        }

        // Etapa 4: Obter o ID do campo novo "position" (ligado ao plugin local_hierarchy).
        $new_field_record = $DB->get_record('user_info_field', ['shortname' => 'position', 'datatype' => 'position'], 'id');
        if (!$new_field_record) {
            // Erro: Campo "position" não encontrado.
            throw new moodle_exception('Campo "position" não encontrado.');
        }
        $new_field_id = $new_field_record->id;

        // Etapa 5: Atualizar o campo "position" dos usuários com base no campo antigo.
        $user_cargos = $DB->get_records_sql("
            SELECT userid, data
            FROM {user_info_data}
            WHERE fieldid = :fieldid
        ", ['fieldid' => $old_field_id]);

        foreach ($user_cargos as $user_cargo) {
            // Obter o cargo correspondente na tabela `local_hierarchy_position` usando o model positions.
            $position_record = positions::get_record(['name' => $user_cargo->data]);

            if ($position_record) {
                // Insere ou atualiza o campo "position" do usuário com o ID da posição.
                $new_data = new stdClass();
                $new_data->userid = $user_cargo->userid;
                $new_data->fieldid = $new_field_id;
                $new_data->data = $position_record->get('id'); // Usando o método get() para obter o ID.

                // Verifica se o registro já existe usando o model.
                $existing_record = $DB->get_record('user_info_data', ['userid' => $user_cargo->userid, 'fieldid' => $new_field_id]);
                if ($existing_record) {
                    // Atualiza o registro existente.
                    $new_data->id = $existing_record->id;
                    $DB->update_record('user_info_data', $new_data);
                } else {
                    // Insere o novo registro.
                    $DB->insert_record('user_info_data', $new_data);
                }
            }
        }

        // Atualiza a versão do plugin.
        upgrade_plugin_savepoint(true, **********, 'local', 'hierarchy');
    }

    if ($oldversion < 2024101500) {
        // Atualiza o campo com shortname 'cargo'.
        $profile_field_cargo = $DB->get_record('user_info_field', ['shortname' => 'cargo']);
        if ($profile_field_cargo) {
            $profile_field_cargo->signup = 0;
            $profile_field_cargo->visible = 0;
            $DB->update_record('user_info_field', $profile_field_cargo);
        }

        // Atualiza o campo com shortname 'position'.
        $profile_field_positions = $DB->get_record('user_info_field', ['shortname' => 'position']);
        if ($profile_field_positions) {
            $profile_field_positions->signup = 1;
            $profile_field_positions->visible = 3;
            $DB->update_record('user_info_field', $profile_field_positions);
        }

        // Atualiza a versão do plugin.
        upgrade_plugin_savepoint(true, 2024101500, 'local', 'hierarchy');
    }

    if ($oldversion < 2025011701) { // Change this to the current version of your plugin.
        // Define table local_hierarchy_position_structure.
        $table = new xmldb_table('local_hierarchy_pos_struct');

        // Add fields.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('positionid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('structureid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('usercreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);

        // Add keys.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $table->add_key('positionid', XMLDB_KEY_FOREIGN, ['positionid'], 'local_hierarchy_position', ['id']);
        $table->add_key('structureid', XMLDB_KEY_FOREIGN, ['structureid'], 'local_hierarchy_structure', ['id']);

        // Conditionally create the table if it doesn't exist.
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Update the plugin version.
        upgrade_plugin_savepoint(true, 2025011701, 'local', 'hierarchy');
    }

    if ($oldversion < 2025011703) {
        // Define table local_hierarchy_pos_manager.
        $table = new xmldb_table('local_hierarchy_pos_manager');

        // Add fields.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('positionid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('managerid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('is_master', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);

        // Add keys.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $table->add_key('position_manager_unique', XMLDB_KEY_UNIQUE, ['positionid', 'managerid']);
        $table->add_key('fk_positionid', XMLDB_KEY_FOREIGN, ['positionid'], 'local_hierarchy_position', ['id']);

        // Add indexes.
        $table->add_index('idx_managerid', XMLDB_INDEX_NOTUNIQUE, ['managerid']);

        // Conditionally create the table.
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        upgrade_plugin_savepoint(true, 2025011703, 'local', 'hierarchy');
    }

    if ($oldversion < 2025011704) {

        // Step 1: Migrate data from 'managerid' to 'local_hierarchy_pos_manager'.
        $positions = $DB->get_records('local_hierarchy_position');
        foreach ($positions as $position) {
            if ($position->managerid) {
                // Prepare the data to insert into local_hierarchy_pos_manager.
                $record = new stdClass();
                $record->positionid = $position->id;
                $record->managerid = $position->managerid;
                $record->is_master = 1; // Assuming all migrated managers are masters by default.
                $record->timecreated = time();
                $record->timemodified = time();

                // Insert into the new table.
                $DB->insert_record('local_hierarchy_pos_manager', $record);
            }
        }

        // Step 2: Remove the index associated with 'managerid'.
        $table = new xmldb_table('local_hierarchy_position');
        $index = new xmldb_index('locahierposi_man_ix', XMLDB_INDEX_NOTUNIQUE, ['managerid']);
        if ($dbman->index_exists($table, $index)) {
            $dbman->drop_index($table, $index);
        }

        // Step 3: Drop the old 'managerid' field.
        $field = new xmldb_field('managerid');
        if ($dbman->field_exists($table, $field)) {
            $dbman->drop_field($table, $field);
        }


        upgrade_plugin_savepoint(true, 2025011704, 'local', 'hierarchy');
    }

    if ($oldversion < 2025011705) {

        // Define a tabela local_hierarchy_structure para ser criada.
        $table = new xmldb_table('local_hierarchy_structure');

        // Adiciona os campos à tabela.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL, null, null);
        $table->add_field('typeid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('parentid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('deleted', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('usercreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('timedeleted', XMLDB_TYPE_INTEGER, '10', null, null, null, '0');
        $table->add_field('userdeleted', XMLDB_TYPE_INTEGER, '10', null, null, null, null);

        // Adiciona as chaves primária e estrangeiras à tabela.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $table->add_key('parentid', XMLDB_KEY_FOREIGN, ['parentid'], 'local_hierarchy_structure', ['id']);
        $table->add_key('usercreated', XMLDB_KEY_FOREIGN, ['usercreated'], 'user', ['id']);
        $table->add_key('usermodified', XMLDB_KEY_FOREIGN, ['usermodified'], 'user', ['id']);
        $table->add_key('userdeleted', XMLDB_KEY_FOREIGN, ['userdeleted'], 'user', ['id']);

        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Atualiza a versão do plugin.
        upgrade_plugin_savepoint(true, 2025011705, 'local', 'hierarchy');
    }

    if ($oldversion < 2025012002) {

        // Define a tabela local_hierarchy_struc_type para ser criada.
        $table = new xmldb_table('local_hierarchy_struc_type');


        // Adiciona os campos à tabela.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('name', XMLDB_TYPE_CHAR, '255', null, XMLDB_NOTNULL, null, null);
        $table->add_field('deleted', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('usercreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('timedeleted', XMLDB_TYPE_INTEGER, '10', null, null, null, '0');
        $table->add_field('userdeleted', XMLDB_TYPE_INTEGER, '10', null, null, null, null);

        // Adiciona a chave primária à tabela.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);

        // Cria a tabela.
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Tipos de estrutura padrão.
        $types = [
            'Diretoria',
            'Departamento',
            'Setor',
            'Unidade de Negócio',
            'Filial',
            'Franquia',
            'Loja',
            'Área',
        ];

        // Insere os tipos padrão na tabela.
        foreach ($types as $type) {
            if (!$DB->record_exists('local_hierarchy_struc_type', ['name' => $type])) {
                $record = new stdClass();
                $record->name = $type;
                $record->deleted = 0;
                $record->timecreated = time();
                $record->usercreated = $USER->id;
                $record->timemodified = time();
                $record->usermodified = $USER->id;
                $DB->insert_record('local_hierarchy_struc_type', $record);
            }
        }


        // Atualiza a versão do plugin.
        upgrade_plugin_savepoint(true, 2025012002, 'local', 'hierarchy');
    }

    if ($oldversion < 2025022001) {
        $table = new xmldb_table('local_hierarchy_structure');

        if ($dbman->table_exists($table)) {
            // Define o campo a ser removido.
            $field = new xmldb_field('active');

            // Verifica se o campo existe antes de removê-lo.
            if ($dbman->field_exists($table, $field)) {
                $dbman->drop_field($table, $field);
            }
        }

        // Atualiza a versão do plugin.
        upgrade_plugin_savepoint(true, 2025022001, 'local', 'hierarchy');
    }

    if ($oldversion < 2025022801) {
        $table = new xmldb_table('local_hierarchy_pos_manager');

        // Verifica se a tabela existe.
        if ($dbman->table_exists($table)) {
            // Define o campo "order".
            $field = new xmldb_field('manager_order', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, 0, 'managerid');

            // Verifica se o campo já existe antes de adicioná-lo.
            if (!$dbman->field_exists($table, $field)) {
                // Adiciona o campo "order" à tabela.
                $dbman->add_field($table, $field);
            }
        }


        // Atualiza a versão do plugin.
        upgrade_plugin_savepoint(true, 2025022801, 'local', 'hierarchy');
    }


    if ($oldversion < 2025030101) {

        // Define os nomes das funções que serão removidas.
        $functions_to_delete = [
            'local_hierarchy_preview_upload',
            'local_hierarchy_process_upload'
        ];

        // Remove os registros da tabela external_functions.
        foreach ($functions_to_delete as $function_name) {
            $DB->delete_records('external_functions', ['name' => $function_name]);
        }

        // Atualiza a versão do plugin.
        upgrade_plugin_savepoint(true, 2025030101, 'local', 'hierarchy');
    }


    if ($oldversion < 2025031201) {
        global $CFG;

        require_once($CFG->dirroot . '/local/hierarchy/installlib.php');
        tool_hierarchy_install_default_custom_profile_fields(false);

        upgrade_plugin_savepoint(true, 2025031201, 'local', 'hierarchy');
    }

    if ($oldversion < 2025031701) {

        // Define os nomes das funções que serão removidas.
        $functions_to_delete = [
            'local_hierarchy_fetch_orgchart_data',
        ];

        // Remove os registros da tabela external_functions.
        foreach ($functions_to_delete as $function_name) {
            $DB->delete_records('external_functions', ['name' => $function_name]);
        }

        // Atualiza a versão do plugin.
        upgrade_plugin_savepoint(true, 2025031701, 'local', 'hierarchy');
    }


    if ($oldversion < 2025032401) {

        // Ocultar campo de cargo antigo e renomear.
        $profile_field_cargo = $DB->get_record('user_info_field', ['shortname' => 'cargo']);
        if ($profile_field_cargo) {
            $profile_field_cargo->name = 'Cargo(Antigo)';
            $profile_field_cargo->visible = PROFILE_VISIBLE_NONE;
            $DB->update_record('user_info_field', $profile_field_cargo);
        } else {
            mtrace("Cargo antigo não encontrado.");
        }

        // Atualiza a versão do plugin.
        upgrade_plugin_savepoint(true, 2025032401, 'local', 'hierarchy');
    }


    return true;
}
