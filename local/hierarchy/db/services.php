<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * External functions and service declaration for Position Manager
 *
 * Documentation: {@link https://moodledev.io/docs/apis/subsystems/external/description}
 *
 * @package    local_hierarchy
 * @category   webservice
 * @copyright  2024 2024 REVVO
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$functions = [
    'local_hierarchy_fetch_positions' => [
        'classname' => local_hierarchy\external\positions::class,
        'methodname' => 'fetch',
        'description' => 'Fetch positions',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_get_position' => [
        'classname' => local_hierarchy\external\positions::class,
        'methodname' => 'get',
        'description' => 'Get position',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_create_position' => [
        'classname' => local_hierarchy\external\positions::class,
        'methodname' => 'create',
        'description' => 'Create position',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_edit_position' => [
        'classname' => local_hierarchy\external\positions::class,
        'methodname' => 'edit',
        'description' => 'Edit position',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_export_positions' => [
        'classname' => local_hierarchy\external\positions::class,
        'methodname' => 'export',
        'description' => 'Export positions',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_export_structures' => [
        'classname' => local_hierarchy\external\structures::class,
        'methodname' => 'export',
        'description' => 'Export structures',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_get_status_options' => [
        'classname' => local_hierarchy\external\positions::class,
        'methodname' => 'get_status',
        'description' => 'Get options of status',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_delete_position' => [
        'classname' => local_hierarchy\external\positions::class,
        'methodname' => 'delete_position',
        'description' => 'Delete position',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_get_encodings_options' => [
        'classname' => local_hierarchy\external\positions_upload::class,
        'methodname' => 'get_encodings',
        'description' => 'Get options of encodings',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_preview_upload_positions' => [
        'classname' => local_hierarchy\external\positions_upload::class,
        'methodname' => 'preview',
        'description' => 'Preview upload positions',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_process_upload_positions' => [
        'classname' => local_hierarchy\external\positions_upload::class,
        'methodname' => 'process',
        'description' => 'Process upload positions',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_preview_upload_structures' => [
        'classname' => local_hierarchy\external\structures_upload::class,
        'methodname' => 'preview',
        'description' => 'Preview upload structures',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_process_upload_structures' => [
        'classname' => local_hierarchy\external\structures_upload::class,
        'methodname' => 'process',
        'description' => 'Process upload structures',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_fetch_orgchart_positions' => [
        'classname' => local_hierarchy\external\positions_orgchart::class,
        'methodname' => 'fetch_orgchart_data',
        'description' => 'Fetch orgchart positions',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_fetch_orgchart_structures' => [
        'classname' => local_hierarchy\external\structures_orgchart::class,
        'methodname' => 'fetch_orgchart_data',
        'description' => 'Fetch orgchart structures',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_fetch_settings' => [
        'classname' => local_hierarchy\external\settings::class,
        'methodname' => 'fetch',
        'description' => 'Fetch positions',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_update_settings' => [
        'classname'   => local_hierarchy\external\settings::class,
        'methodname'  => 'update_settings',
        'description' => 'Update hierarchy settings.',
        'type'        => 'write',
        'ajax'        => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_fetch_structures' => [
        'classname' => local_hierarchy\external\structures::class,
        'methodname' => 'fetch',
        'description' => 'Fetch structures',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_get_structure' => [
        'classname' => local_hierarchy\external\structures::class,
        'methodname' => 'get',
        'description' => 'Get structure',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_save_structure' => [
        'classname' => local_hierarchy\external\structures::class,
        'methodname' => 'save_structure',
        'description' => 'Save structures',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_delete_structure' => [
        'classname' => local_hierarchy\external\structures::class,
        'methodname' => 'delete_structure',
        'description' => 'Delete structure',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_fetch_structure_types' => [
        'classname' => local_hierarchy\external\structure_type::class,
        'methodname' => 'fetch',
        'description' => 'Fetch structures',
        'type' => 'read',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_save_structure_types' => [
        'classname' => local_hierarchy\external\structure_type::class,
        'methodname' => 'save',
        'description' => 'Save structure type',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_delete_structure_types' => [
        'classname' => local_hierarchy\external\structure_type::class,
        'methodname' => 'delete',
        'description' => 'Delete structure type',
        'type' => 'write',
        'ajax' => true,
        'capabilities' => 'local/hierarchy:manage',
    ],
    'local_hierarchy_get_positions_by_structure' => [
        'classname'   => local_hierarchy\external\positions::class,
        'methodname'  => 'get_positions_by_structure',
        'description' => 'Get positions by structure',
        'type'        => 'read',
        'ajax'        => true
    ]
];

$services = [];
