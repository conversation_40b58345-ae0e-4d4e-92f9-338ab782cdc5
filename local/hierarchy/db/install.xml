<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="local/hierarchy/db" VERSION="20240614" COMMENT="XMLDB file for Moodle local/hierarchy"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd">
    <TABLES>
        <TABLE NAME="local_hierarchy_position" COMMENT="Position instances to create company hierarchy">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" COMMENT="Position ID."/>
                <FIELD NAME="name" TYPE="char" LENGTH="256" NOTNULL="true" SEQUENCE="false" COMMENT="Position name."/>
                <!-- removido na v0.2 <FIELD NAME="managerid" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id of Manager"/> removido na v0.2-->
                <FIELD NAME="manage" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="1 if is a management position"/>
                <FIELD NAME="deleted" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="1 If deleted, 0 otherwise."/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of creation"/>
                <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User id of creator"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of modification"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id of modifier"/>
                <FIELD NAME="timedeleted" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of deletion"/>
                <FIELD NAME="userdeleted" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" COMMENT="User id of deleter"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
                <!-- removido na v0.2 <KEY NAME="managerid" TYPE="foreign" FIELDS="managerid" REFTABLE="user" REFFIELDS="id"/> removido na v0.2-->
                <KEY NAME="usercreated" TYPE="foreign" FIELDS="usercreated" REFTABLE="user" REFFIELDS="id"/>
                <KEY NAME="usermodified" TYPE="foreign" FIELDS="usermodified" REFTABLE="user" REFFIELDS="id"/>
                <KEY NAME="userdeleted" TYPE="foreign" FIELDS="userdeleted" REFTABLE="user" REFFIELDS="id"/>
            </KEYS>
        </TABLE>
        <TABLE NAME="local_hierarchy_pos_manager" COMMENT="Relation between positions and managers">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
                <FIELD NAME="positionid" TYPE="int" LENGTH="10" NOTNULL="true" COMMENT="FK to local_hierarchy_position"/>
                <FIELD NAME="managerid" TYPE="int" LENGTH="10" NOTNULL="true" COMMENT="FK to user"/>
                <FIELD NAME="is_master" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" COMMENT="Defines if the manager is the master"/>
                <FIELD NAME="manager_order" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" COMMENT="Position order"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
                <KEY NAME="position_manager_unique" TYPE="unique" FIELDS="positionid, managerid"/>
                <KEY NAME="fk_positionid" TYPE="foreign" FIELDS="positionid" REFERENCES="local_hierarchy_position" REFFIELDS="id"/>
            </KEYS>
            <INDEXES>
                <INDEX NAME="idx_managerid" FIELDS="managerid"/>
            </INDEXES>
        </TABLE>
        <TABLE NAME="local_hierarchy_pos_struct" COMMENT="Association between positions and structures">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" COMMENT="Unique ID for the association"/>
                <FIELD NAME="positionid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="Position ID"/>
                <FIELD NAME="structureid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="Structure ID"/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of creation"/>
                <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User ID of creator"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
                <KEY NAME="positionid" TYPE="foreign" FIELDS="positionid" REFTABLE="local_hierarchy_position" REFFIELDS="id"/>
                <KEY NAME="structureid" TYPE="foreign" FIELDS="structureid" REFTABLE="local_hierarchy_structure" REFFIELDS="id"/>
            </KEYS>
        </TABLE>
        <TABLE NAME="local_hierarchy_struc_type" COMMENT="Stores hierarchy structure type">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" COMMENT="Structure type ID."/>
                <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false" COMMENT="Structure type name."/>
                <FIELD NAME="description" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false" COMMENT="Structure type name."/>
                <FIELD NAME="created_at" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of creation"/>
                <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User id of creator"/>
                <FIELD NAME="updated_at" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of modification"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id of modifier"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
            </KEYS>
        </TABLE>
        <TABLE NAME="local_hierarchy_structure" COMMENT="Stores hierarchy structure">
            <FIELDS>
                <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" COMMENT="Structure ID."/>
                <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false" COMMENT="Structure name."/>
                <FIELD NAME="typeid" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Position type id."/>
                <FIELD NAME="parentid" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Parent structure id."/>
                <FIELD NAME="deleted" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="1 If deleted, 0 otherwise."/>
                <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of creation"/>
                <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User id of creator"/>
                <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of modification"/>
                <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id of modifier"/>
                <FIELD NAME="timedeleted" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of deletion"/>
                <FIELD NAME="userdeleted" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" COMMENT="User id of deleter"/>
            </FIELDS>
            <KEYS>
                <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
                <KEY NAME="parentid" TYPE="foreign" FIELDS="parentid" REFTABLE="local_hierarchy_structure" REFFIELDS="id"/>
                <KEY NAME="usercreated" TYPE="foreign" FIELDS="usercreated" REFTABLE="user" REFFIELDS="id"/>
                <KEY NAME="usermodified" TYPE="foreign" FIELDS="usermodified" REFTABLE="user" REFFIELDS="id"/>
                <KEY NAME="userdeleted" TYPE="foreign" FIELDS="userdeleted" REFTABLE="user" REFFIELDS="id"/>
            </KEYS>
        </TABLE>
    </TABLES>
</XMLDB>
