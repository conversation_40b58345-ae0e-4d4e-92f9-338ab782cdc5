<?php

use local_hierarchy\model\positions;

function local_hierarchy_add_menubar_client_menu()
{
    global $PAGE;

    return [
        "name" => get_string('pluginname', 'local_hierarchy'),
        "url" => new \moodle_url("/local/hierarchy/index.php/positions"),
        "active" => strstr($PAGE->url, "local/hierarchy/") ? "active" : "",
    ];
}

/* removido na v0.2
function local_hierarchy_get_manager_positions_array()
{
    $positions_menu = positions::get_main_manager_list();

    return $positions_menu;
}
*/