<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * TODO describe file index
 *
 * @package    local_hierarchy
 * @copyright  2024 REVVO <www.somosrevvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require('../../config.php');

require_login();

$context = context_system::instance();
require_capability('local/hierarchy:manage', $context);
$url = new moodle_url('/local/hierarchy/index.php/positions', []);
$title = get_string('pluginname', 'local_hierarchy');
$PAGE->navbar->add($title, new moodle_url($url));

$PAGE->set_url($url);
$PAGE->set_context($context);
$PAGE->set_title($title);
$PAGE->set_heading($title);
$PAGE->requires->css('/local/hierarchy/amd/build/style.css');
$PAGE->requires->css('/local/hierarchy/amd/build/select2.min.css');
$PAGE->requires->css('/local/hierarchy/amd/build/custom-form.css');
$PAGE->requires->js_call_amd('local_hierarchy/app-lazy', 'init', ['#hierarchy-app']);
//$renderable = new \local_hierarchy\output\index();

echo $OUTPUT->header();
echo html_writer::div('', '', ['id' => 'hierarchy-app']);
//echo $OUTPUT->render($renderable);
echo $OUTPUT->footer();
