<?php namespace local_judy\exceptions;

use \moodle_exception;
use \local_judy\exceptions\judy_exception;
use \local_judy\integration\dtos\http_response;
use \local_judy\integration\dtos\http_request;

class judy_integration_exception extends judy_exception{

    protected $data = null;
    protected $request = null;

    public function setData(?object $data){
        $this->data = $data;
    }

    public function getData() : ?object {
        return $this->data;
    }

    public function setCode(int $code){
        $this->code = $code;
    }

    public function getRequest() : ?http_request {
        return $this->request;
    }

    public function setRequest(http_request $request){
        $this->request = $request;
    }

    public function getSimpleMessage() : string {
        if(!is_object($this->data)){
            return $this->getMessage();
        }

        if(!empty($this->data->message)){
            return $this->data->message;
        }

        if(!empty($this->data->error)){
            return $this->data->error;
        }

        if(!empty($this->data->errors)){
            $errors = (array) $this->data->errors;
            return implode("\n", $errors);
        }
    }

    public static function from_http_response(http_response $response) : judy_integration_exception {
        $message = $response->error ?: "exception:integration_error";
        $exception = new static($message);
        $exception->setCode($response->httpStatus);
        $exception->setData($response->data);
        $exception->setRequest($response->get_request());
        throw $exception;
    }

    public function export() : object {
        global $CFG;

        $data = $this->getData();

        if(empty($data)){
            $data = (object) [
                'error' => $this->getSimpleMessage(),
            ];
        }

        if($CFG->debugdeveloper){
            $data->debug = (object) [
                'request' => $this->getRequest(),
            ];
        }

        return $data;
    }
}