<?php namespace local_judy\settings;

use \local_judy\config;
use \admin_settingpage;
use \admin_setting_configcheckbox;
use \admin_setting_configselect;
use \admin_setting_heading;
use \admin_setting_configtext;
use \lang_string;
use \admin_setting_description;

abstract class sso_settings{

    protected static $user_custom_fields_options = [];

    protected static function get_user_custom_fields_options() : array {
        global $DB;

        if(empty(self::$user_custom_fields_options)){
            $recordset = $DB->get_recordset('user_info_field', null, '', 'shortname, name');
            foreach ($recordset as $record) {
                $key = "profile_field_" . $record->shortname;
                $name = $record->name . " (" . $key . ")";
                self::$user_custom_fields_options[$key] = $name;
            }
            $recordset->close();
        }

        return self::$user_custom_fields_options;
    }

    protected static function make_user_fields_options(array $extra_options = []) : array {
        $options = self::get_user_custom_fields_options();

        foreach ($extra_options as $key => $option) {
            if(is_numeric($key)){
                $options[$option] = get_string($option) . " ($option)";
            }else{
                $options[$key] = $option;
            }
        }

        return $options;
    }

    public static function create_page($category = "local_judy_integration_settings") : admin_settingpage {
        return new admin_settingpage(
            $category,
            new lang_string('settings:integration_title', 'local_judy'),
            'local/judy:config'
        );
    }

    /**
     * Adds settings definition to a settings page.
     *
     * @param admin_settingpage $page
     * @return void
     */
    public static function make(admin_settingpage &$page){

        $name = config::JUDY_API_URL_SETTING;
        $page->add(
            new admin_setting_configtext(
                "local_judy/$name",
                new lang_string('settings:api_url', 'local_judy'),
                new lang_string('settings:api_url_desc', 'local_judy'),
                config::JUDY_PRODUCTION_URL,
                PARAM_URL
            )
        );

        $name = config::CLIENT_TOKEN_SETTING;
        $setting = new admin_setting_configtext(
            "local_judy/$name",
            new lang_string('settings:client_token', 'local_judy'),
            new lang_string('settings:client_token_desc', 'local_judy'),
            "",
            PARAM_RAW
        );
        $setting->set_updatedcallback([self::class, 'fetch_api_user']);
        $page->add($setting);

        // API user's username
        if($api_user_username = config::get_api_user_username()){
            $name = config::API_USER_USERNAME_KEY;
            $page->add(new admin_setting_description(
                "local_judy/$name",
                new lang_string('settings:api_user_username', 'local_judy'),
                $api_user_username
            ));
        }

        // API user's company name
        if($api_company_name = config::get_api_user_company_name()){
            $name = config::API_USER_COMPANY_NAME_KEY;
            $page->add(new admin_setting_description(
                "local_judy/$name",
                new lang_string('settings:api_company_name', 'local_judy'),
                $api_company_name
            ));
        }

        // SSO Heading
        $page->add(
            new admin_setting_heading(
                'local_judy/sso_heading',
                new lang_string('settings:sso_heading', 'local_judy'),
                new lang_string('settings:sso_heading_desc', 'local_judy')
            )
        );

        // SSO Enabled
        $name = config::SSO_ENABLED_SETTING;
        $page->add(new admin_setting_configcheckbox(
            "local_judy/$name",
            new lang_string('settings:' . $name, 'local_judy'),
            new lang_string('settings:' . $name . '_desc', 'local_judy'),
            1
        ));

        // User creation
        $name = config::USER_CREATION_ENABLED_SETTING;
        $page->add(new admin_setting_configcheckbox(
            "local_judy/$name",
            new lang_string('settings:' . $name, 'local_judy'),
            new lang_string('settings:' . $name . '_desc', 'local_judy'),
            1
        ));

        // Mappings
        $page->add(
            new admin_setting_heading(
                'local_judy/fields_mapping_header',
                new lang_string('settings:fields_mapping_heading', 'local_judy'),
                new lang_string('settings:fields_mapping_heading_desc', 'local_judy')
            )
        );

        // Username mapping
        $name = config::USER_USERNAME_FIELD_SETTING;
        $page->add(new admin_setting_configselect(
            "local_judy/$name",
            new lang_string('settings:' . $name, 'local_judy'),
            new lang_string('settings:' . $name . '_desc', 'local_judy'),
            'username',
            self::make_user_fields_options([
                'idnumber',
                'username',
                'email',
                'phone1',
                'phone2',
            ]),
        ));

        // Firstname mapping
        $name = config::USER_FIRSTNAME_FIELD_SETTING;
        $page->add(new admin_setting_configselect(
            "local_judy/$name",
            new lang_string('settings:' . $name, 'local_judy'),
            new lang_string('settings:' . $name . '_desc', 'local_judy'),
            'firstname',
            self::make_user_fields_options([
                'firstname',
            ]),
        ));

        // Lastname mapping
        $name = config::USER_LASTNAME_FIELD_SETTING;
        $page->add(new admin_setting_configselect(
            "local_judy/$name",
            new lang_string('settings:' . $name, 'local_judy'),
            new lang_string('settings:' . $name . '_desc', 'local_judy'),
            'lastname',
            self::make_user_fields_options([
                'lastname',
            ]),
        ));

        // Email mapping
        $name = config::USER_EMAIL_FIELD_SETTING;
        $page->add(new admin_setting_configselect(
            "local_judy/$name",
            new lang_string('settings:' . $name, 'local_judy'),
            new lang_string('settings:' . $name . '_desc', 'local_judy'),
            'email',
            self::make_user_fields_options([
                'email',
            ]),
        ));

        // Phone mapping
        $name = config::USER_PHONE_FIELD_SETTING;
        $page->add(new admin_setting_configselect(
            "local_judy/$name",
            new lang_string('settings:' . $name, 'local_judy'),
            new lang_string('settings:' . $name . '_desc', 'local_judy'),
            'phone1',
            self::make_user_fields_options([
                'phone1',
                'phone2',
            ]),
        ));
        
        // Company role mapping
        $name = config::USER_COMPANY_ROLE_FIELD_SETTING;
        $options = self::make_user_fields_options(['' => get_string('none')]);
        $page->add(new admin_setting_configselect(
            "local_judy/$name",
            new lang_string('settings:' . $name, 'local_judy'),
            new lang_string('settings:' . $name . '_desc', 'local_judy'),
            !empty($options['profile_field_position']) ? 'profile_field_position' : '',
            $options
        ));
    }


    public static function fetch_api_user(){
        $task = new \local_judy\task\fetch_api_user_task();
        $task->execute_or_enqueue();
    }
}