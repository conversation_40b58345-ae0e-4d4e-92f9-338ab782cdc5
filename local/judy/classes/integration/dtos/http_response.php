<?php namespace local_judy\integration\dtos;

use \local_judy\integration\dtos\http_request;

class http_response{
    public $data = null;
    public int $httpStatus = 0;
    public string $error = '';
    protected $request = null;

    public function is_error(){
        return $this->httpStatus < 200 || $this->httpStatus >= 300;
    }

    public function set_request(http_request $request){
        $this->request = $request;
    }

    public function get_request() : ?http_request {
        return $this->request ?: null;
    }
}