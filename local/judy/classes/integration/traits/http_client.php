<?php namespace local_judy\integration\traits;

use \local_judy\integration\dtos\http_response;
use \local_judy\integration\dtos\http_request;
use \Exception;

trait http_client {
    protected static function execute_curl(string $method, string $url, array $data = [], $headers = []) : http_response {

        $method = trim(strtoupper($method));

        if($method == 'GET' && !empty($data)) {
            $url = $url . '?' . http_build_query($data);
        }

        $return = new http_response();
        $return->set_request(new http_request($method, $url, $data));

        try {
			$curl = curl_init();

            $opts = array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => $method,
                CURLOPT_HTTPHEADER => $headers,
            );

            if($method == 'POST') {
                $opts[CURLOPT_POSTFIELDS] = json_encode($data);
                $opts[CURLOPT_HTTPHEADER][] = "Content-Length: " . strlen($opts[CURLOPT_POSTFIELDS]);
            }

            curl_setopt_array($curl, $opts);

            $return->data = json_decode(curl_exec($curl));
            $return->httpStatus = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            if(empty($return->httpStatus)) {
                throw new Exception(curl_error($curl));
            }

            curl_close($curl);

		} catch (\Throwable $e) {
			$return->error = $e->getMessage();
		} finally{
            return $return;
        }
    }


    public static function get(string $url, array $data = [], $headers = false) : http_response {
        return self::execute_curl('GET', $url, $data, $headers);
    }

    public static function post(string $url, array $data = [], $headers = false) : http_response {
        return self::execute_curl('POST', $url, $data, $headers);
    }
}