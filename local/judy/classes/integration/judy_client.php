<?php namespace local_judy\integration;

use \local_judy\config;
use \local_judy\exceptions\judy_integration_exception;
use \local_judy\exceptions\judy_config_exception;
use \local_judy\exceptions\judy_exception;
use \moodle_url;
use \local_judy\integration\traits\http_client;
use \local_judy\event\sso_request_failed;
use \local_judy\event\sso_token_created;
use \local_judy\event\sso_url_created;
use \Throwable;

class judy_client {
    use http_client;

    const SSO_TARGET_WEB = 'web';
    const SSO_TARGET_EMBEDDED = 'embedded';

    const SSO_TARGETS = [
        self::SSO_TARGET_WEB,
        self::SSO_TARGET_EMBEDDED,
    ];    

    protected static function make_request_headers() : array {
        $token = config::get_api_token();
        return [
            'Accept: application/json',
            'Content-Type: application/json',
            "Authorization: Bearer {$token}",
        ];
    }

    protected static function get_user_username(object $user) : string {
        $username_field = config::get(config::USER_USERNAME_FIELD_SETTING, null);

        if(!$username_field){
            throw new judy_config_exception('exception:username_field_not_configured_properly');
        }

        if(!empty($user->$username_field)){
            return $user->$username_field;
        }

        throw new judy_integration_exception('exception:empty_username');
    }

    protected static function get_user_creation_data(object &$user) : array {
        global $DB;

        $map = [
            'first_name' => config::USER_FIRSTNAME_FIELD_SETTING,
            'last_name' => config::USER_LASTNAME_FIELD_SETTING,
            'email' => config::USER_EMAIL_FIELD_SETTING,
            'phone' => config::USER_PHONE_FIELD_SETTING,
            'company_role' => config::USER_COMPANY_ROLE_FIELD_SETTING,
        ];

        $data = [];
        foreach ($map as $field => $setting) {
            if($fieldname = config::get($setting, null)){
                if(!empty($user->$fieldname)){
                    $data[$field] = $user->$fieldname;
                }
            }
        }

        return $data;
    }

    protected static function make_api_url(string $endpoint) : string {
        return config::get_api_url() . '/api/' . trim($endpoint, '/');
    }

    protected static function fix_profile_position_field_values(object $user){
        global $DB;

        if(!class_exists(\profilefield_position\positions::class)){
            return;
        }

        $positions = \profilefield_position\positions::get_menu(false);

        foreach ($DB->get_records('user_info_field', ['datatype' => 'position'], '', 'shortname') as $field) {
            $property_name = 'profile_field_' . $field->shortname;
            if(empty($user->{$property_name})){
                continue;
            }
            $user->{$property_name} = isset($positions[$user->{$property_name}]) ? $positions[$user->{$property_name}] : null;
        }

    }

    /**
     * Requests a SSO for an user.
     *
     * @param object $user
     * @param string|null $target (web|embedded)
     * @param moodle_url|null $redirect_to
     * @return object
     */
    public static function request_sso(object $user, string $target = 'web', ?moodle_url $redirect_to = null) : object {
        if(!config::is_sso_enabled()){
            throw new judy_exception("exception:sso_disabled");
        }

        profile_load_data($user);
        self::fix_profile_position_field_values($user);

        $url = self::make_api_url('auth/sso');
        $headers = self::make_request_headers();

        $payload = [
            'username' => self::get_user_username($user),
            'target' => $target,
        ];

        if($target == self::SSO_TARGET_WEB){
            $payload['redirect'] = ($redirect_to ?? new moodle_url('/'))->out(false);
            $event_class = sso_url_created::class;
        }else{
            $event_class = sso_token_created::class;
        }

        if(config::can_create_user()){
            $payload['user'] = self::get_user_creation_data($user);
        }

        try {
            $response = self::post($url, $payload, $headers);
            
            if($response->is_error()){
                throw judy_integration_exception::from_http_response($response);
            }

            $event = $event_class::from_user($user);
            $event->trigger();

            return $response->data;

        } catch (Throwable $th) {
            $event = sso_request_failed::from_throwable($user, $th);
            $event->trigger();

            throw $th;
        }
    }

    /**
     * List all assistants that are available for the
     * current access token.
     *
     * @return array
     */
    public static function list_assistants() : array {
        $url = self::make_api_url('assistants');
        $headers = self::make_request_headers();

        $response = self::get($url, [], $headers);
        
        if($response->is_error()){
            throw judy_integration_exception::from_http_response($response);
        }

        return $response->data;
    }

    /**
     * Get the current user (token owner)
     *
     * @return array
     */
    public static function get_token_user() : object {
        $url = self::make_api_url('auth/user');
        $headers = self::make_request_headers();

        $response = self::get($url, [], $headers);

        if($response->is_error()){
            throw judy_integration_exception::from_http_response($response);
        }

        return $response->data->user;
    }

    /**
     * Generates the embedding code for an assistant
     *
     * @param string $assistant_id
     * @return string
     */
    public static function get_assistant_code(string $assistant_id, array $options = []) : string {
        $url = self::make_api_url("assistants/$assistant_id/embedding/code");
        $headers = self::make_request_headers();
        $payload = [
            'options' => $options,
        ];

        $response = self::post($url, $payload, $headers);

        if($response->is_error()){
            throw judy_integration_exception::from_http_response($response);
        }

        return $response->data->code ?? "";
    }
}