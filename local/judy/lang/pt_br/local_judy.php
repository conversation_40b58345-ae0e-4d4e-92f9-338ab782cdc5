<?php

$string['pluginname'] = 'Judy';
$string['settings:judy_category'] = 'Judy';
$string['settings:integration_title'] = 'Configurações de integração';

$string['settings:sso_heading'] = 'SSO (Single sign-on)';
$string['settings:sso_heading_desc'] = '';

$string['settings:sso_enabled'] = 'Habilitar SSO';
$string['settings:sso_enabled_desc'] = '';

$string['settings:user_creation_enabled'] = 'Habilitar criação de usuários';
$string['settings:user_creation_enabled_desc'] = 'Se o usuário não existir, este será criado durante o processo de SSO.';

$string['settings:user_username_field'] = 'Nome de usuário';
$string['settings:user_username_field_desc'] = 'Este campo é utilizado como identificador único do usuário';

$string['settings:user_firstname_field'] = 'Nome';
$string['settings:user_firstname_field_desc'] = '';

$string['settings:user_lastname_field'] = 'Sobrenome';
$string['settings:user_lastname_field_desc'] = '';

$string['settings:user_email_field'] = 'Email';
$string['settings:user_email_field_desc'] = '';

$string['settings:user_phone_field'] = 'Telefone';
$string['settings:user_phone_field_desc'] = '';

$string['settings:user_company_role_field'] = 'Cargo';
$string['settings:user_company_role_field_desc'] = '';

$string['settings:fields_mapping_heading'] = 'Mapeamento de campos de usuário';
$string['settings:fields_mapping_heading_desc'] = '';

$string['settings:api_url'] = 'URL da API da Judy';
$string['settings:api_url_desc'] = '';

$string['settings:client_token'] = 'Token de acesso';
$string['settings:client_token_desc'] = '';

$string['settings:api_user_username'] = 'Username do usuário da API';
$string['settings:api_company_name'] = 'Empresa do usuário da API';

$string['error:could_not_get_api_user_try_later'] = 'Não foi possível buscar informações sobre o usuário da API. Tentaremos novamente mais tarde.';
$string['success:api_user_data_fetched'] = 'Informações do usuário da API sincronizados com sucesso!';

$string['exception:missing_api_token'] = 'Token da API não configurado.';
$string['exception:missing_api_url'] = 'URL da API não configurada';
$string['exception:username_field_not_configured_properly'] = 'Campo de nome de usuário não configurado';
$string['exception:empty_username'] = 'Nome de usuário vazio';
$string['exception:sso_disabled'] = 'SSO desabilitado';