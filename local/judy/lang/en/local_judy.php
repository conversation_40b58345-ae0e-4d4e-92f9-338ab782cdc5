<?php

$string['pluginname'] = 'Judy';
$string['settings:judy_category'] = 'Judy';
$string['settings:integration_title'] = 'Integration Settings';

$string['settings:sso_heading'] = 'SSO (Single sign-on)';
$string['settings:sso_heading_desc'] = '';

$string['settings:sso_enabled'] = 'Enable SSO';
$string['settings:sso_enabled_desc'] = '';

$string['settings:user_creation_enabled'] = 'Enable user creation';
$string['settings:user_creation_enabled_desc'] = 'If the user does not exist, it will be created during the SSO process.';

$string['settings:user_username_field'] = 'Username';
$string['settings:user_username_field_desc'] = 'This field is used as the user\'s unique identifier.';

$string['settings:user_firstname_field'] = 'First name';
$string['settings:user_firstname_field_desc'] = '';

$string['settings:user_lastname_field'] = 'Last name';
$string['settings:user_lastname_field_desc'] = '';

$string['settings:user_email_field'] = 'Email';
$string['settings:user_email_field_desc'] = '';

$string['settings:user_phone_field'] = 'Phone';
$string['settings:user_phone_field_desc'] = '';

$string['settings:user_company_role_field'] = 'Job title';
$string['settings:user_company_role_field_desc'] = '';

$string['settings:fields_mapping_heading'] = 'User fields mapping';
$string['settings:fields_mapping_heading_desc'] = '';

$string['settings:api_url'] = 'Judy API URL';
$string['settings:api_url_desc'] = '';

$string['settings:client_token'] = 'Access token';
$string['settings:client_token_desc'] = '';

$string['settings:api_user_username'] = 'API user\'s username';
$string['settings:api_company_name'] = 'API user\'s company';

$string['error:could_not_get_api_user_try_later'] = 'Could not fetch information about the tokens user. It will be retried later.';
$string['success:api_user_data_fetched'] = 'API user information successfully synchronized!';

$string['exception:missing_api_token'] = 'Token da API não configurado.';
$string['exception:missing_api_url'] = 'URL da API não configurada';
$string['exception:username_field_not_configured_properly'] = 'Campo de nome de usuário não configurado';
$string['exception:empty_username'] = 'Nome de usuário vazio';
$string['exception:sso_disabled'] = 'SSO desabilitado';
