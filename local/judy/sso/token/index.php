<?php
define('NO_DEBUG_DISPLAY', true);
define('AJAX_SCRIPT', true);

// Allow CORS requests.
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

require_once(__DIR__ . '/../../../../config.php');

use \local_judy\integration\judy_client;

try {
    require_login(null, false, null, false, true);

    $judy_client = new judy_client();
    $response = $judy_client::request_sso($USER, judy_client::SSO_TARGET_EMBEDDED);

    echo json_encode($response);

} catch (\local_judy\exceptions\judy_integration_exception $jie) {
    http_response_code($jie->getCode());
    echo json_encode($jie->export());

} catch (\Throwable $th) {
    http_response_code(500);
    echo json_encode([
        'error' => $th->getMessage(),
    ]);
}
