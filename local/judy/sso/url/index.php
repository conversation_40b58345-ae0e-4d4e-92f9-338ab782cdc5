<?php

define('NO_DEBUG_DISPLAY', true);

require_once(__DIR__ . '/../../../../config.php');

use \local_judy\integration\judy_client;
use \core\output\notification;

$redirect = new moodle_url(optional_param('redirect', '/', PARAM_URL));

try {
    require_login(null, false, null, false, true);
    $PAGE->set_context(\context_system::instance());

    $judy_client = new judy_client();

    $response = $judy_client::request_sso($USER, judy_client::SSO_TARGET_WEB, $redirect);

    if(!empty($response->url)){
        redirect($response->url);
        exit();
    }

} catch (\local_judy\exceptions\judy_integration_exception $jie) {
    redirect($redirect->out(), $jie->getSimpleMessage(), null, notification::NOTIFY_ERROR);
    exit();
    
} catch (\Throwable $th) {
    redirect($redirect->out(), $th->getMessage(), null, notification::NOTIFY_ERROR);
    exit();
}
