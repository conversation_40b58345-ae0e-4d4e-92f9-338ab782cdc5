<?php

require_once('../../config.php');

require_login();

$PAGE->set_context(\context_system::instance());
$PAGE->requires->css(new moodle_url('/local/audience/styles.css'));
$PAGE->requires->css(new moodle_url('/local/audience/amd/build/style.css'));

$PAGE->requires->js_call_amd('local_audience/app-lazy', 'init', ['#root-audience']);

$url = new moodle_url('/local/audience/index.php');
$title = get_string('pluginname', 'local_audience');

$PAGE->set_url(new moodle_url($url));
$PAGE->set_pagelayout('admin');
$PAGE->set_title($title);
$PAGE->set_heading($title);

echo $OUTPUT->header();
echo $OUTPUT->heading($title);

echo html_writer::div('', '', ['id' => 'root-audience']);

echo $OUTPUT->footer();
