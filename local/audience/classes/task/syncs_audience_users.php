<?php

namespace local_audience\task;

use \core\task\scheduled_task;
use local_audience\event_manager;
use local_audience\query_builder;
use tool_lfxp\helpers\cli;
use moodle_exception;

/**
 * Sync audience users with more than 3 hours without
 */
class syncs_audience_users extends scheduled_task
{
    /**
     * Returns the task name.
     *
     * @return string
     */
    public function get_name(): string
    {
        return get_string('task:syncs_audience_users', 'local_audience');
    }

    /**
     * Executes the synchronization task.
     */
    public function execute(): void
    {
        try {
            cli::mtrace("Starting...");

            $audiences = $this->get_qualified_audiences();

            foreach ($audiences as $audienceid => $criteria_list) {

                cli::mtrace("Processing audience: $audienceid");

                $this->process_audience($audienceid, $criteria_list);
            }

            cli::mtrace("Finished.");
        } catch (\Throwable $th) {
            debugging($th->getMessage(), DEBUG_DEVELOPER, $th->getTrace());
        }
    }

    /**
     * Get qualified audiences for synchronization.
     *
     * @return array An associative array of audiences and their criteria.
     */
    public function get_qualified_audiences(): array
    {
        global $DB;

        $audiences = [];

        $sql = "SELECT 
                    laa.id AS audienceid, lac.field, lac.`data`
                FROM {local_audience_criteria} lac
                    RIGHT JOIN {local_audience_audiences} laa ON laa.id = lac.audienceid
                WHERE 
                    laa.cronlastruntime < UNIX_TIMESTAMP(DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00') - INTERVAL 3 HOUR)
                ORDER BY laa.cronlastruntime ASC";

        $recordset = $DB->get_recordset_sql($sql);

        foreach ($recordset as $record) {
            $audiences[$record->audienceid][] = (array)$record;
        }

        $recordset->close();

        return $audiences;
    }

    /**
     * Process a specific audience for synchronization.
     *
     * @param int $audienceid The ID of the audience.
     * @param array $criteria_list The list of criteria for the audience.
     */
    private function process_audience($audienceid, $criteria_list): void
    {
        $event = new event_manager;

        $event->init($audienceid);

        try {
            if (empty($criteria_list[0]['data'])) {
                // If it doesn't have criteria, it shouldn't have members
                $this->delete_audience_members($audienceid);
            } else {
                $query_builder = new query_builder("local_custom_fields_user");

                foreach ($criteria_list as $criteria) {
                    $data = json_decode($criteria['data']);

                    if (empty($data)) {
                        continue;
                    }


                    $query_builder->add_filter($criteria['field'], $data);
                }

                $query_builder->select_columns([
                    "$audienceid AS audienceid",
                    "userid",
                    "0 AS manual"
                ]);

                $sql = $query_builder->build_query();

                $this->synchronize_audience_members($audienceid, $sql);
            }

            $event->trigger();

            $this->update_cronlastruntime($audienceid);
        } catch (moodle_exception $e) {
            $event->clear();
        }
    }

    /**
     * Synchronize audience members based on the generated SQL query.
     *
     * @param int $audienceid The ID of the audience.
     * @param string $sql The SQL query to execute.
     */
    private function synchronize_audience_members($audienceid, $sql): void
    {
        global $DB;

        $this->delete_audience_members($audienceid);

        // It's ugly, but performative
        $DB->execute("INSERT INTO {local_audience_members} (audienceid, userid, manual) ($sql)");
    }

    /**
     * Unlinks all users from the target audience
     *
     * @param int $audienceid
     * @return void
     */
    public function delete_audience_members($audienceid): void
    {
        global $DB;

        $DB->delete_records('local_audience_members', [
            "manual"     => 0,
            'audienceid' => $audienceid,
        ]);
    }

    /**
     * Updates the last cron run date for that target audience
     *
     * @param int $audienceid
     * @return void
     */
    public function update_cronlastruntime($audienceid): void
    {
        global $DB;

        $DB->update_record('local_audience_audiences', [
            'id' => $audienceid,
            'cronlastruntime' => time()
        ]);
    }
}
