<?php

namespace local_audience\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_multiple_structure;
use core_external\external_single_structure;
use core_external\external_value;

/**
 * Web service to returns users to the list of courses they are enrolled in.
 *
 * @package local_audience
 */
class get_user_custom_field_values extends external_api
{
    /**
     * Describes the parameters.
     *
     * @return external_function_parameters
     */
    public static function api_parameters()
    {
        return new external_function_parameters([
            'shortname' => new external_value(PARAM_TEXT, 'Shortname of custom field', VALUE_REQUIRED),
            'search'    => new external_value(PARAM_TEXT, 'Keyword to search for custom field values', VALUE_OPTIONAL),
        ]);
    }

    /**
     * Describes the return structure of the service.
     *
     * @return external_multiple_structure
     */
    public static function api_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_value(PARAM_TEXT, 'Opções'),
            'Array de opções',
            VALUE_DEFAULT,
            []
        );
    }

    /**
     * Executes the service.
     *
     * @return array
     */
    public static function api($shortname, $search): array
    {
        global $DB, $USER;

        if (!isloggedin()) {
            return [];
        }

        $params = compact('shortname', 'search');

        self::validate_parameters(self::api_parameters(), $params);

        $context = \context_system::instance();
        self::validate_context($context);

        $data = [];
        $params = [];
        $searchquery = "";


        if (!empty($search)) {
            $searchquery = "AND " . $DB->sql_like($shortname, ':search', false, false);
            $params['search'] = "%{$search}%";
        }

        $sql = "SELECT 
                    DISTINCT({$shortname})
                FROM {local_custom_fields_user}
                WHERE 
                    {$shortname} IS NOT NULL {$searchquery}
                ORDER BY {$shortname} ASC
                LIMIT 250";

        $results = $DB->get_recordset_sql($sql, $params);

        foreach ($results as $result) {
            $data[] = $result->{$shortname};
        }

        $results->close();


        return $data;
    }
}
