<?php

defined('MOODLE_INTERNAL') || die();

function local_courseblockapi_is_course_favourite($courseid, $component = 'core_course', $userid = 0): bool
{
    global $USER;

    if (!$userid) {
        $userid = $USER->id;
    }

    $usercontext = \context_user::instance($userid, IGNORE_MISSING);
    $coursecontext = \context_course::instance($courseid, IGNORE_MISSING);

    if (!$coursecontext || !$usercontext) {

        return false;
    }

    $ufservice = \core_favourites\service_factory::get_service_for_user_context($usercontext);

    return $ufservice->favourite_exists($component, 'courses', $courseid, $coursecontext);
}

function render_courses_templates($template, $datas): string{
    global $OUTPUT;
    return $OUTPUT->render_from_template($template, $datas);
}

function get_courses_strings($string, $from, $datas): string{
    $separator = '|:|';
    $arrParameters = array();
    foreach ($datas as $key => $value) {
        $exp = explode($separator, $value);
        $arrParameters[$exp[0]] = $exp[1];
    }
    return get_string($string, $from, $arrParameters);
}

/**
 * Debugguer for web and cli
 */
if (!function_exists('dd')) {
    function dd()
    {

        $args = func_get_args();

        if (CLI_SCRIPT) {

            fwrite(STDOUT, PHP_EOL);

            foreach ($args as $arg) {
                fwrite(STDOUT, var_dump($arg) . PHP_EOL);
            }

            die(1);
        }

        echo "<pre>";

        call_user_func_array('var_dump', $args);

        debugging('debug trace:');

        echo "</pre>";

        die(1);
    }
}
