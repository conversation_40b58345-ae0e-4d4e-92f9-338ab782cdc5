import Templates from "core/templates";
import * as Notification from "core/notification";

import API from "local_courseblockapi/api";
import Favourite from "local_courseblockapi/favourite";
import Visibility from "local_courseblockapi/visibility";
import Card from "local_courseblockapi/card";

const unenrolled_blocks = ["block_freecourses", "block_recommendedcourses"];

export const init = (component, args) => {
  if (!component) {
    return false;
  }

  update_courses(component, args);
  header_listener(component);
  window.addEventListener("resize", function () {
    Card.calc_card_height(component);
  });
};

export const update_courses = (component, args) => {
  args = prepare_page_params(component, args);
  update_url_params(args);
  update_course_page(component, args);

  return true;
};

export const update_course_page = (component, args = {}) => {
  toogle_loader(component);

  let cards = document.querySelector(`.${component} .cards`);
  fade(cards);
  cards.innerHTML = "";

  API.get_courses(component, args)
    .then((result) => {
      update_modality(component, result);
      Templates.render(`local_courseblockapi/page-cards`, result)
        .then(function (html) {
          toogle_loader(component);
          cards.innerHTML = html;
          Card.calc_card_height(component);
          unfade(cards);
          Favourite.init(component);
          Visibility.page_listener(component);
        })
        .fail(Notification.exception);

      update_pagination(component, result);

      return true;
    })
    .fail(Notification.exception);

  return false;
};

const update_pagination = (component, results) => {
  let pag_elem = document.querySelector(`.${component} .pagination`);
  let pagination = prepare_page_pagination(results);

  if (pagination) {
    Templates.render("local_courseblockapi/course-pagination", pagination)
      .then(function (html) {
        pag_elem.innerHTML = html;
        unfade(pag_elem);
        pagination_listener(component);
      })
      .fail(Notification.exception);
  } else {
    fade(pag_elem);
    pag_elem.innerHTML = "";
  }

  return true;
};

const update_modality = (component, results) => {
  let modality_elem = document.querySelector(
    `.${component} .page-header-modality`
  );
  let modalitySelect = modality_elem.querySelector("#modality");

  if (!results.modalities?.length) {
    if (modalitySelect) modalitySelect.setAttribute("disabled", "");
    return false;
  }

  if (modalitySelect) modalitySelect.removeAttribute("disabled");

  Templates.render(`local_courseblockapi/page-header-modalities`, results)
    .then(function (html) {
      modality_elem.innerHTML = html;
      unfade(modality_elem);
      modality_listener(component);
    })
    .fail(Notification.exception);
  return true;
};

const header_listener = (component) => {
  let elems = document.querySelectorAll(
    `.${component} form #status,
     .${component} form #sort
    `
  );
  elems.forEach((el) => {
    el.addEventListener("change", () => {
      let args = serialize_form(component);
      update_courses(component, args);
    });
  });

  let search_btn = document.querySelector(`.${component} form #button-search`);

  search_btn.addEventListener("click", () => {
    let args = serialize_form(component);
    update_courses(component, args);
  });

  let search = document.querySelector(`.${component} form #search`);

  search.addEventListener("keypress", (e) => {
    e.stopPropagation();

    if (e.key === "Enter") {
      e.preventDefault();

      let args = serialize_form(component);
      update_courses(component, args);
    }
  });

  return true;
};

const modality_listener = (component) => {
  document
    .querySelector(`.${component} form #modality`)
    .addEventListener("change", () => {
      let args = serialize_form(component);
      update_courses(component, args);
    });
};

export const serialize_form = (component) => {
  let form = document.querySelector(`.${component} .courses-page-header form`);
  return Object.fromEntries(new FormData(form).entries());
};

const update_url_params = (args) => {
  let url = new URL(window.location.href);

  if (!args?.page) {
    url.searchParams.delete("page");
  }

  for (let [key, value] of Object.entries(args)) {
    url.searchParams.set(key, value);
  }
  window.history.replaceState(null, null, url);
};

const toogle_loader = (component) => {
  let loader = document.querySelector(`.${component} .page-loader`);
  loader.classList.toggle("d-flex");
  loader.classList.toggle("d-none");

  return true;
};

const prepare_page_params = (component, params) => {
  if (params.status == "hiddencourses") {
    params.hidden = 1;
    params.status = "all";
  }

  if (params.status == "favourite") {
    params.favourite = 1;
    params.status = "all";
  }

  if (unenrolled_blocks.includes(component)) {
    delete params.status;
  }

  params.per_page = params.per_page ?? 8;

  delete params.component;

  return params;
};

const pagination_listener = (component) => {
  let buttons = document.querySelectorAll(
    `.${component} .courses-page-footer .pagination .page-link`
  );

  buttons.forEach((btn) => {
    btn.addEventListener("click", (event) => {
      event.preventDefault();

      let page = btn.dataset.page;
      let args = serialize_form(component);

      if (!page || btn.disabled) return;

      args.page = page;
      update_courses(component, args);
    });
  });
};

const prepare_page_pagination = (results) => {
  if (!results?.total_pages || results.total_pages == 1) {
    return {};
  }


  let isMobileView = window.innerWidth <= 600/*  */
  let pagination = {
    label: "Page",
    pages: [],
    haspages: true,
    pagesize: results.per_page || 10,
  };

  let currentPage = results.page;
  let totalPages = results.total_pages;

  if (currentPage > 1) {
    pagination.previous = { page: currentPage - 1, url: `?page=${currentPage - 1}` };
  }

  if (currentPage < totalPages) {
    pagination.next = { page: currentPage + 1, url: `?page=${currentPage + 1}` };
  }

  let treshold = 0;
  if (totalPages <= 5) {
    for (let i = 1; i <= totalPages; i++) {
      pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });
    }
  }
  else {

    if ((currentPage <= 5 && !isMobileView) || (currentPage <= 3 && isMobileView)) {
      // primeiras páginas
      if (currentPage <= totalPages - 5) {
        treshold = 2;
      }

      if (!isMobileView) {
        for (let i = 1; i <= currentPage + 3 && i <= totalPages - treshold; i++) {
          pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });
        }
      }
      else {
        for (let i = 1; i <= 3; i++) {
          pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });
        }
      }

      if (treshold == 2 || isMobileView) {
        pagination.last = { page: totalPages, active: totalPages == currentPage, url: `?page=${totalPages}` };
      }
      else if (currentPage + 3 < totalPages) {
        pagination.pages.push({ page: totalPages, active: totalPages === currentPage, url: `?page=${totalPages}` });
      }

    } else if ((currentPage > totalPages - 5 && !isMobileView) || (currentPage > totalPages - 3 && isMobileView)) {
      // últimas páginas
      treshold = 1;
      if (isMobileView) {
        if (currentPage > 3) {
          treshold = totalPages - 2;
          pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };
        }
      }
      else if (currentPage >= 6) {
        treshold = currentPage - 3;
        pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };
      }

      for (let i = treshold; i <= totalPages; i++) {
        pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });
      }
    } else {
      // meio
      pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };
      if (isMobileView) {
        pagination.pages.push({ page: currentPage, active: true, url: `?page=${currentPage}` });
      }
      else {
        for (let i = currentPage - 3; i <= currentPage + 3; i++) {
          pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });
        }
      }
      pagination.last = { page: totalPages, active: totalPages == currentPage, url: `?page=${totalPages}` };
    }
  }
  return pagination;
};


const fade = (element) => {
  element.style.opacity = 0;
};

const unfade = (element) => {
  element.style.opacity = 1;
};

export default {
  init,
  update_courses,
  serialize_form,
};
