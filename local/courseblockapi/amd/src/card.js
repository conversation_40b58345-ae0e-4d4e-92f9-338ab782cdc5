export const calc_card_height = (component) => {
  let imgOriginalWidth = 600;
  let imgOriginalHeight = 350;
  let card = document.querySelector(
    `.${component} .local_courseblockapi .card`
  );
  let elem = document.querySelector(`.${component} .local_courseblockapi`);

  if (!card) {
    return false;
  }

  let card_width = Math.max.apply(
    null,
    Array.from(
      document.querySelectorAll(`.${component} .local_courseblockapi .card`)
    ).map(function (a) {
      return a.offsetWidth || a.offsetWidth || a.scrollWidth;
    })
  );

  let new_image_height = (card_width * imgOriginalHeight) / imgOriginalWidth;
  let new_card_height = new_image_height + 16; //add 16px

  elem.style.setProperty("--img-height", new_image_height + "px");
  elem.style.setProperty("--card-height", new_card_height + "px");

  let card_body_height = Math.max.apply(
    null,
    Array.from(
      document.querySelectorAll(
        `.${component} .local_courseblockapi .card-body`
      )
    ).map(function (a) {
      return a.offsetHeight || a.offsetHeight || a.scrollHeight;
    })
  );

  let new_card_height_hover =
    card_body_height +
    parseInt(getComputedStyle(elem).getPropertyValue("--img-height")) +
    5;
  elem.style.setProperty("--card-height-hover", new_card_height_hover + "px");

  return true;
};

export default {
  calc_card_height
};
