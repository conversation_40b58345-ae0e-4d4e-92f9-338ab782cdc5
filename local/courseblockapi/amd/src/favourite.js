import * as Notification from "core/notification";
import API from "local_courseblockapi/api";

export const init = (component) => {
  let spans = document.querySelectorAll(
    `.${component} .card .card-body span.card-course-favourite`
  );

  spans.forEach((el) => {
    el.addEventListener("click", function (event) {
      event.stopPropagation();
      let courseid = this.dataset.courseid;
      let status =
        this.dataset.favourite == 0 || this.dataset.favourite == false;

      let promise = API.set_favourite({
        courses: [
          {
            component: null, //null means it favourites in system context
            id: courseid,
            favourite: status,
          },
        ],
      });

      promise
        .then((result) => {
          if (result.warnings) {
            result.warnings.forEach((warning) => {
              Notification.addNotification({
                message: warning.message,
                type: "error",
              });

              return false;
            });
          }

          let allCards = document.querySelectorAll(
            `.card-courses span.card-course-favourite[data-courseid='${courseid}']`
          );

          let allIcon = document.querySelectorAll(
            `.card-courses span.card-course-favourite[data-courseid='${courseid}'] i`
          );

          allCards.forEach(function (button) {
            button.dataset.favourite = status ? 1 : 0;
          });

          allIcon.forEach(function (icon) {
            icon.classList.toggle("fa-regular");
            icon.classList.toggle("fa");
          });

          return true;
        })
        .fail(Notification.exception);

      return false;
    });
  });
};

export default {
  init,
};
