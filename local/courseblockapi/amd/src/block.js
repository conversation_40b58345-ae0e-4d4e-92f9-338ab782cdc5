import Templates from "core/templates";
import * as Notification from "core/notification";
import API from "local_courseblockapi/api";
import Card from "local_courseblockapi/card";

export const init = (component) => {
  Card.calc_card_height(component);
  window.addEventListener("resize", function () {
    Card.calc_card_height(component);
  });
};

export const update_courses = (component, args = {}) => {
  API.get_courses(component, args)
    .then((result) => {
      Templates.render(`local_courseblockapi/block-cards`, result)
        .then(function (html) {
          let block = document.querySelector(`.${component} .cards`);
          block.innerHTML = html;
          Card.calc_card_height(component);
          return true;
        })
        .fail(Notification.exception);
    })
    .fail(Notification.exception);

  return false;
};

export default {
  init,
  update_courses,
};
