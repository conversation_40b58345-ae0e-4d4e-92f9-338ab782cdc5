import Ajax from "core/ajax";

const get_ajax_function = (component) => {
  let functions = {
    block_mycourses: "local_courseblockapi_get_my_courses",
    block_freecourses: "local_courseblockapi_get_free_courses",
    block_popularcourses: "local_courseblockapi_get_popular_courses",
    block_recommendedcourses: "local_courseblockapi_get_recommended_courses",
  };
  return functions[component];
};

/**
 * Set the favourite state on a list of courses.
 *
 * Valid args are:
 * Array courses  list of course id numbers.
 *
 * @param {string} component component name.
 * @param {Object} args Arguments send to the webservice.
 * @return {Promise} Resolve with warnings.
 */
export const get_courses = (component, args = {}) => {
  const request = {
    methodname: get_ajax_function(component),
    args: args,
  };

  return Ajax.call([request])[0];
};

/**
 * Set the visibility state on a course.
 *
 * Valid args are:
 * Array courses  list of course id numbers.
 *
 * @param {Object} args Arguments send to the webservice.
 * @return {Promise} Resolve with warnings.
 */
export const set_visibility = (args) => {
  const request = {
    methodname: "local_courseblockapi_toggle_course_visibility",
    args: args,
  };

  return Ajax.call([request])[0];
};

/**
 * Set the favourite state on a list of courses.
 *
 * Valid args are:
 * Array courses  list of course id numbers.
 *
 * @param {Object} args Arguments send to the webservice.
 * @return {Promise} Resolve with warnings.
 */
export const set_favourite = (args) => {
  const request = {
    methodname: "local_courseblockapi_set_favourite_courses",
    args: args,
  };

  return Ajax.call([request])[0];
};

// To maintain backwards compatability we export default here.
export default {
  get_courses,
  set_visibility,
  set_favourite,
};
