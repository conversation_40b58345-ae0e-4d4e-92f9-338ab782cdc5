define("local_courseblockapi/favourite",["exports","core/notification","local_courseblockapi/api"],(function(_exports,Notification,_api){var obj;function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=_exports.default=void 0,Notification=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(Notification),_api=(obj=_api)&&obj.__esModule?obj:{default:obj};const init=component=>{document.querySelectorAll(".".concat(component," .card .card-body span.card-course-favourite")).forEach((el=>{el.addEventListener("click",(function(event){event.stopPropagation();let courseid=this.dataset.courseid,status=0==this.dataset.favourite||0==this.dataset.favourite;return _api.default.set_favourite({courses:[{component:null,id:courseid,favourite:status}]}).then((result=>{result.warnings&&result.warnings.forEach((warning=>(Notification.addNotification({message:warning.message,type:"error"}),!1)));let allCards=document.querySelectorAll(".card-courses span.card-course-favourite[data-courseid='".concat(courseid,"']")),allIcon=document.querySelectorAll(".card-courses span.card-course-favourite[data-courseid='".concat(courseid,"'] i"));return allCards.forEach((function(button){button.dataset.favourite=status?1:0})),allIcon.forEach((function(icon){icon.classList.toggle("fa-regular"),icon.classList.toggle("fa")})),!0})).fail(Notification.exception),!1}))}))};_exports.init=init;var _default={init:init};return _exports.default=_default,_exports.default}));

//# sourceMappingURL=favourite.min.js.map