define("local_courseblockapi/card",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=_exports.calc_card_height=void 0;const calc_card_height=component=>{let card=document.querySelector(".".concat(component," .local_courseblockapi .card")),elem=document.querySelector(".".concat(component," .local_courseblockapi"));if(!card)return!1;let new_image_height=350*Math.max.apply(null,Array.from(document.querySelectorAll(".".concat(component," .local_courseblockapi .card"))).map((function(a){return a.offsetWidth||a.offsetWidth||a.scrollWidth})))/600,new_card_height=new_image_height+16;elem.style.setProperty("--img-height",new_image_height+"px"),elem.style.setProperty("--card-height",new_card_height+"px");let new_card_height_hover=Math.max.apply(null,Array.from(document.querySelectorAll(".".concat(component," .local_courseblockapi .card-body"))).map((function(a){return a.offsetHeight||a.offsetHeight||a.scrollHeight})))+parseInt(getComputedStyle(elem).getPropertyValue("--img-height"))+5;return elem.style.setProperty("--card-height-hover",new_card_height_hover+"px"),!0};_exports.calc_card_height=calc_card_height;var _default={calc_card_height:calc_card_height};return _exports.default=_default,_exports.default}));

//# sourceMappingURL=card.min.js.map