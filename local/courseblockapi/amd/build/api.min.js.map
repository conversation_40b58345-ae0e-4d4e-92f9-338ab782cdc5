{"version": 3, "file": "api.min.js", "sources": ["../src/api.js"], "sourcesContent": ["import Ajax from \"core/ajax\";\n\nconst get_ajax_function = (component) => {\n  let functions = {\n    block_mycourses: \"local_courseblockapi_get_my_courses\",\n    block_freecourses: \"local_courseblockapi_get_free_courses\",\n    block_popularcourses: \"local_courseblockapi_get_popular_courses\",\n    block_recommendedcourses: \"local_courseblockapi_get_recommended_courses\",\n  };\n  return functions[component];\n};\n\n/**\n * Set the favourite state on a list of courses.\n *\n * Valid args are:\n * Array courses  list of course id numbers.\n *\n * @param {string} component component name.\n * @param {Object} args Arguments send to the webservice.\n * @return {Promise} Resolve with warnings.\n */\nexport const get_courses = (component, args = {}) => {\n  const request = {\n    methodname: get_ajax_function(component),\n    args: args,\n  };\n\n  return Ajax.call([request])[0];\n};\n\n/**\n * Set the visibility state on a course.\n *\n * Valid args are:\n * Array courses  list of course id numbers.\n *\n * @param {Object} args Arguments send to the webservice.\n * @return {Promise} Resolve with warnings.\n */\nexport const set_visibility = (args) => {\n  const request = {\n    methodname: \"local_courseblockapi_toggle_course_visibility\",\n    args: args,\n  };\n\n  return Ajax.call([request])[0];\n};\n\n/**\n * Set the favourite state on a list of courses.\n *\n * Valid args are:\n * Array courses  list of course id numbers.\n *\n * @param {Object} args Arguments send to the webservice.\n * @return {Promise} Resolve with warnings.\n */\nexport const set_favourite = (args) => {\n  const request = {\n    methodname: \"local_courseblockapi_set_favourite_courses\",\n    args: args,\n  };\n\n  return Ajax.call([request])[0];\n};\n\n// To maintain backwards compatability we export default here.\nexport default {\n  get_courses,\n  set_visibility,\n  set_favourite,\n};\n"], "names": ["get_ajax_function", "component", "block_mycourses", "block_freecourses", "block_popularcourses", "block_recommendedcourses", "get_courses", "args", "request", "methodname", "Ajax", "call", "set_visibility", "set_favourite"], "mappings": "0SAEMA,kBAAqBC,YACT,CACdC,gBAAiB,sCACjBC,kBAAmB,wCACnBC,qBAAsB,2CACtBC,yBAA0B,gDAEXJ,YAaNK,YAAc,SAACL,eAAWM,4DAAO,SACtCC,QAAU,CACdC,WAAYT,kBAAkBC,WAC9BM,KAAMA,aAGDG,cAAKC,KAAK,CAACH,UAAU,2CAYjBI,eAAkBL,aACvBC,QAAU,CACdC,WAAY,gDACZF,KAAMA,aAGDG,cAAKC,KAAK,CAACH,UAAU,iDAYjBK,cAAiBN,aACtBC,QAAU,CACdC,WAAY,6CACZF,KAAMA,aAGDG,cAAKC,KAAK,CAACH,UAAU,sDAIf,CACbF,YAAAA,YACAM,eAAAA,eACAC,cAAAA"}