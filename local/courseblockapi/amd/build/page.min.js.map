{"version": 3, "file": "page.min.js", "sources": ["../src/page.js"], "sourcesContent": ["import Templates from \"core/templates\";\r\nimport * as Notification from \"core/notification\";\r\n\r\nimport API from \"local_courseblockapi/api\";\r\nimport Favourite from \"local_courseblockapi/favourite\";\r\nimport Visibility from \"local_courseblockapi/visibility\";\r\nimport Card from \"local_courseblockapi/card\";\r\n\r\nconst unenrolled_blocks = [\"block_freecourses\", \"block_recommendedcourses\"];\r\n\r\nexport const init = (component, args) => {\r\n  if (!component) {\r\n    return false;\r\n  }\r\n\r\n  update_courses(component, args);\r\n  header_listener(component);\r\n  window.addEventListener(\"resize\", function () {\r\n    Card.calc_card_height(component);\r\n  });\r\n};\r\n\r\nexport const update_courses = (component, args) => {\r\n  args = prepare_page_params(component, args);\r\n  update_url_params(args);\r\n  update_course_page(component, args);\r\n\r\n  return true;\r\n};\r\n\r\nexport const update_course_page = (component, args = {}) => {\r\n  toogle_loader(component);\r\n\r\n  let cards = document.querySelector(`.${component} .cards`);\r\n  fade(cards);\r\n  cards.innerHTML = \"\";\r\n\r\n  API.get_courses(component, args)\r\n    .then((result) => {\r\n      update_modality(component, result);\r\n      Templates.render(`local_courseblockapi/page-cards`, result)\r\n        .then(function (html) {\r\n          toogle_loader(component);\r\n          cards.innerHTML = html;\r\n          Card.calc_card_height(component);\r\n          unfade(cards);\r\n          Favourite.init(component);\r\n          Visibility.page_listener(component);\r\n        })\r\n        .fail(Notification.exception);\r\n\r\n      update_pagination(component, result);\r\n\r\n      return true;\r\n    })\r\n    .fail(Notification.exception);\r\n\r\n  return false;\r\n};\r\n\r\nconst update_pagination = (component, results) => {\r\n  let pag_elem = document.querySelector(`.${component} .pagination`);\r\n  let pagination = prepare_page_pagination(results);\r\n\r\n  if (pagination) {\r\n    Templates.render(\"local_courseblockapi/course-pagination\", pagination)\r\n      .then(function (html) {\r\n        pag_elem.innerHTML = html;\r\n        unfade(pag_elem);\r\n        pagination_listener(component);\r\n      })\r\n      .fail(Notification.exception);\r\n  } else {\r\n    fade(pag_elem);\r\n    pag_elem.innerHTML = \"\";\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nconst update_modality = (component, results) => {\r\n  let modality_elem = document.querySelector(\r\n    `.${component} .page-header-modality`\r\n  );\r\n  let modalitySelect = modality_elem.querySelector(\"#modality\");\r\n\r\n  if (!results.modalities?.length) {\r\n    if (modalitySelect) modalitySelect.setAttribute(\"disabled\", \"\");\r\n    return false;\r\n  }\r\n\r\n  if (modalitySelect) modalitySelect.removeAttribute(\"disabled\");\r\n\r\n  Templates.render(`local_courseblockapi/page-header-modalities`, results)\r\n    .then(function (html) {\r\n      modality_elem.innerHTML = html;\r\n      unfade(modality_elem);\r\n      modality_listener(component);\r\n    })\r\n    .fail(Notification.exception);\r\n  return true;\r\n};\r\n\r\nconst header_listener = (component) => {\r\n  let elems = document.querySelectorAll(\r\n    `.${component} form #status,\r\n     .${component} form #sort\r\n    `\r\n  );\r\n  elems.forEach((el) => {\r\n    el.addEventListener(\"change\", () => {\r\n      let args = serialize_form(component);\r\n      update_courses(component, args);\r\n    });\r\n  });\r\n\r\n  let search_btn = document.querySelector(`.${component} form #button-search`);\r\n\r\n  search_btn.addEventListener(\"click\", () => {\r\n    let args = serialize_form(component);\r\n    update_courses(component, args);\r\n  });\r\n\r\n  let search = document.querySelector(`.${component} form #search`);\r\n\r\n  search.addEventListener(\"keypress\", (e) => {\r\n    e.stopPropagation();\r\n\r\n    if (e.key === \"Enter\") {\r\n      e.preventDefault();\r\n\r\n      let args = serialize_form(component);\r\n      update_courses(component, args);\r\n    }\r\n  });\r\n\r\n  return true;\r\n};\r\n\r\nconst modality_listener = (component) => {\r\n  document\r\n    .querySelector(`.${component} form #modality`)\r\n    .addEventListener(\"change\", () => {\r\n      let args = serialize_form(component);\r\n      update_courses(component, args);\r\n    });\r\n};\r\n\r\nexport const serialize_form = (component) => {\r\n  let form = document.querySelector(`.${component} .courses-page-header form`);\r\n  return Object.fromEntries(new FormData(form).entries());\r\n};\r\n\r\nconst update_url_params = (args) => {\r\n  let url = new URL(window.location.href);\r\n\r\n  if (!args?.page) {\r\n    url.searchParams.delete(\"page\");\r\n  }\r\n\r\n  for (let [key, value] of Object.entries(args)) {\r\n    url.searchParams.set(key, value);\r\n  }\r\n  window.history.replaceState(null, null, url);\r\n};\r\n\r\nconst toogle_loader = (component) => {\r\n  let loader = document.querySelector(`.${component} .page-loader`);\r\n  loader.classList.toggle(\"d-flex\");\r\n  loader.classList.toggle(\"d-none\");\r\n\r\n  return true;\r\n};\r\n\r\nconst prepare_page_params = (component, params) => {\r\n  if (params.status == \"hiddencourses\") {\r\n    params.hidden = 1;\r\n    params.status = \"all\";\r\n  }\r\n\r\n  if (params.status == \"favourite\") {\r\n    params.favourite = 1;\r\n    params.status = \"all\";\r\n  }\r\n\r\n  if (unenrolled_blocks.includes(component)) {\r\n    delete params.status;\r\n  }\r\n\r\n  params.per_page = params.per_page ?? 8;\r\n\r\n  delete params.component;\r\n\r\n  return params;\r\n};\r\n\r\nconst pagination_listener = (component) => {\r\n  let buttons = document.querySelectorAll(\r\n    `.${component} .courses-page-footer .pagination .page-link`\r\n  );\r\n\r\n  buttons.forEach((btn) => {\r\n    btn.addEventListener(\"click\", (event) => {\r\n      event.preventDefault();\r\n\r\n      let page = btn.dataset.page;\r\n      let args = serialize_form(component);\r\n\r\n      if (!page || btn.disabled) return;\r\n\r\n      args.page = page;\r\n      update_courses(component, args);\r\n    });\r\n  });\r\n};\r\n\r\nconst prepare_page_pagination = (results) => {\r\n  if (!results?.total_pages || results.total_pages == 1) {\r\n    return {};\r\n  }\r\n\r\n\r\n  let isMobileView = window.innerWidth <= 600/*  */\r\n  let pagination = {\r\n    label: \"Page\",\r\n    pages: [],\r\n    haspages: true,\r\n    pagesize: results.per_page || 10,\r\n  };\r\n\r\n  let currentPage = results.page;\r\n  let totalPages = results.total_pages;\r\n\r\n  if (currentPage > 1) {\r\n    pagination.previous = { page: currentPage - 1, url: `?page=${currentPage - 1}` };\r\n  }\r\n\r\n  if (currentPage < totalPages) {\r\n    pagination.next = { page: currentPage + 1, url: `?page=${currentPage + 1}` };\r\n  }\r\n\r\n  let treshold = 0;\r\n  if (totalPages <= 5) {\r\n    for (let i = 1; i <= totalPages; i++) {\r\n      pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });\r\n    }\r\n  }\r\n  else {\r\n\r\n    if ((currentPage <= 5 && !isMobileView) || (currentPage <= 3 && isMobileView)) {\r\n      // primeiras páginas\r\n      if (currentPage <= totalPages - 5) {\r\n        treshold = 2;\r\n      }\r\n\r\n      if (!isMobileView) {\r\n        for (let i = 1; i <= currentPage + 3 && i <= totalPages - treshold; i++) {\r\n          pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });\r\n        }\r\n      }\r\n      else {\r\n        for (let i = 1; i <= 3; i++) {\r\n          pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });\r\n        }\r\n      }\r\n\r\n      if (treshold == 2 || isMobileView) {\r\n        pagination.last = { page: totalPages, active: totalPages == currentPage, url: `?page=${totalPages}` };\r\n      }\r\n      else if (currentPage + 3 < totalPages) {\r\n        pagination.pages.push({ page: totalPages, active: totalPages === currentPage, url: `?page=${totalPages}` });\r\n      }\r\n\r\n    } else if ((currentPage > totalPages - 5 && !isMobileView) || (currentPage > totalPages - 3 && isMobileView)) {\r\n      // últimas páginas\r\n      treshold = 1;\r\n      if (isMobileView) {\r\n        if (currentPage > 3) {\r\n          treshold = totalPages - 2;\r\n          pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };\r\n        }\r\n      }\r\n      else if (currentPage >= 6) {\r\n        treshold = currentPage - 3;\r\n        pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };\r\n      }\r\n\r\n      for (let i = treshold; i <= totalPages; i++) {\r\n        pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });\r\n      }\r\n    } else {\r\n      // meio\r\n      pagination.first = { page: 1, active: 1 == currentPage, url: `?page=${1}` };\r\n      if (isMobileView) {\r\n        pagination.pages.push({ page: currentPage, active: true, url: `?page=${currentPage}` });\r\n      }\r\n      else {\r\n        for (let i = currentPage - 3; i <= currentPage + 3; i++) {\r\n          pagination.pages.push({ page: i, active: i === currentPage, url: `?page=${i}` });\r\n        }\r\n      }\r\n      pagination.last = { page: totalPages, active: totalPages == currentPage, url: `?page=${totalPages}` };\r\n    }\r\n  }\r\n  return pagination;\r\n};\r\n\r\n\r\nconst fade = (element) => {\r\n  element.style.opacity = 0;\r\n};\r\n\r\nconst unfade = (element) => {\r\n  element.style.opacity = 1;\r\n};\r\n\r\nexport default {\r\n  init,\r\n  update_courses,\r\n  serialize_form,\r\n};\r\n"], "names": ["_getRequireWildcardCache", "e", "WeakMap", "r", "t", "_interopRequireDefault", "__esModule", "default", "_templates", "Notification", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_interopRequireWildcard", "_api", "_favourite", "_visibility", "_card", "unenrolled_blocks", "init", "component", "args", "update_courses", "header_listener", "window", "addEventListener", "Card", "calc_card_height", "_exports", "prepare_page_params", "update_url_params", "update_course_page", "arguments", "length", "undefined", "toogle_loader", "cards", "document", "querySelector", "fade", "innerHTML", "API", "get_courses", "then", "result", "update_modality", "Templates", "render", "html", "unfade", "Favourite", "Visibility", "page_listener", "fail", "exception", "update_pagination", "results", "pag_elem", "pagination", "prepare_page_pagination", "pagination_listener", "modality_elem", "modalitySelect", "modalities", "removeAttribute", "modality_listener", "setAttribute", "querySelectorAll", "for<PERSON>ach", "el", "serialize_form", "stopPropagation", "key", "preventDefault", "form", "fromEntries", "FormData", "entries", "url", "URL", "location", "href", "page", "searchParams", "delete", "value", "history", "replaceState", "loader", "classList", "toggle", "params", "status", "hidden", "favourite", "includes", "per_page", "btn", "event", "dataset", "disabled", "total_pages", "isMobile<PERSON>iew", "innerWidth", "label", "pages", "haspages", "pagesize", "currentPage", "totalPages", "previous", "next", "treshold", "push", "active", "last", "first", "element", "style", "opacity"], "mappings": "4RAM6C,SAAAA,yBAAAC,GAAA,GAAA,mBAAAC,QAAA,OAAA,KAAA,IAAAC,EAAAD,IAAAA,QAAAE,EAAAF,IAAAA,eAAAF,yBAAA,SAAAC,GAAAA,OAAAA,EAAAG,EAAAD,IAAAF,EAAA,CAAA,SAAAI,uBAAAJ,GAAAA,OAAAA,GAAAA,EAAAK,WAAAL,EAAAM,CAAAA,QAAAN,EAAA,2KAN7CO,WAAAH,uBAAAG,YACAC,aAK6C,SAAAR,EAAAE,GAAAA,IAAAA,GAAAF,GAAAA,EAAAK,WAAAL,OAAAA,EAAAA,GAAAA,OAAAA,GAAAA,iBAAAA,GAAAA,mBAAAA,EAAAM,MAAAA,CAAAA,QAAAN,GAAAG,IAAAA,EAAAJ,yBAAAG,GAAA,GAAAC,GAAAA,EAAAM,IAAAT,GAAA,OAAAG,EAAAO,IAAAV,GAAA,IAAAW,EAAA,CAAAC,UAAA,MAAAC,EAAAC,OAAAC,gBAAAD,OAAAE,yBAAA,IAAA,IAAAC,KAAAjB,EAAAiB,GAAAA,YAAAA,GAAAC,CAAAA,EAAAA,eAAAC,KAAAnB,EAAAiB,GAAAG,CAAAA,IAAAA,EAAAP,EAAAC,OAAAE,yBAAAhB,EAAAiB,GAAAG,KAAAA,IAAAA,EAAAV,KAAAU,EAAAC,KAAAP,OAAAC,eAAAJ,EAAAM,EAAAG,GAAAT,EAAAM,GAAAjB,EAAAiB,GAAAN,OAAAA,EAAAL,QAAAN,EAAAG,GAAAA,EAAAkB,IAAArB,EAAAW,GAAAA,CAAA,CAL7CW,CAAAd,cAEAe,KAAAnB,uBAAAmB,MACAC,WAAApB,uBAAAoB,YACAC,YAAArB,uBAAAqB,aACAC,MAAAtB,uBAAAsB,OAEA,MAAMC,kBAAoB,CAAC,oBAAqB,4BAEnCC,KAAOA,CAACC,UAAWC,QAC9B,IAAKD,UACH,OAAO,EAGTE,eAAeF,UAAWC,MAC1BE,gBAAgBH,WAChBI,OAAOC,iBAAiB,UAAU,WAChCC,MAAAA,QAAKC,iBAAiBP,UACxB,GAAE,EACFQ,SAAAT,KAAAA,KAEK,MAAMG,eAAiBA,CAACF,UAAWC,QACxCA,KAAOQ,oBAAoBT,UAAWC,MACtCS,kBAAkBT,MAClBU,mBAAmBX,UAAWC,OAEvB,GACPO,SAAAN,eAAAA,eAEK,MAAMS,mBAAqB,SAACX,WAAyB,IAAdC,KAAIW,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAA,EACnDG,cAAcf,WAEd,IAAIgB,MAAQC,SAASC,cAAc,IAAIlB,oBAwBvC,OAvBAmB,KAAKH,OACLA,MAAMI,UAAY,GAElBC,KAAG5C,QAAC6C,YAAYtB,UAAWC,MACxBsB,MAAMC,SACLC,gBAAgBzB,UAAWwB,QAC3BE,WAAAA,QAAUC,OAAO,kCAAmCH,QACjDD,MAAK,SAAUK,MACdb,cAAcf,WACdgB,MAAMI,UAAYQ,KAClBtB,MAAAA,QAAKC,iBAAiBP,WACtB6B,OAAOb,OACPc,WAAAA,QAAU/B,KAAKC,WACf+B,YAAAA,QAAWC,cAAchC,UAC1B,IACAiC,KAAKtD,aAAauD,WAErBC,kBAAkBnC,UAAWwB,SAEtB,KAERS,KAAKtD,aAAauD,YAEd,GACP1B,SAAAG,mBAAAA,mBAEF,MAAMwB,kBAAoBA,CAACnC,UAAWoC,WACpC,IAAIC,SAAWpB,SAASC,cAAc,IAAIlB,yBACtCsC,WAAaC,wBAAwBH,SAezC,OAbIE,WACFZ,WAAAA,QAAUC,OAAO,yCAA0CW,YACxDf,MAAK,SAAUK,MACdS,SAASjB,UAAYQ,KACrBC,OAAOQ,UACPG,oBAAoBxC,UACrB,IACAiC,KAAKtD,aAAauD,YAErBf,KAAKkB,UACLA,SAASjB,UAAY,KAGhB,CAAI,EAGPK,gBAAkBA,CAACzB,UAAWoC,WAClC,IAAIK,cAAgBxB,SAASC,cAC3B,IAAIlB,mCAEF0C,eAAiBD,cAAcvB,cAAc,aAEjD,OAAKkB,QAAQO,YAAY9B,QAKrB6B,gBAAgBA,eAAeE,gBAAgB,YAEnDlB,WAAAA,QAAUC,OAAO,8CAA+CS,SAC7Db,MAAK,SAAUK,MACda,cAAcrB,UAAYQ,KAC1BC,OAAOY,eACPI,kBAAkB7C,UACnB,IACAiC,KAAKtD,aAAauD,YACd,IAbDQ,gBAAgBA,eAAeI,aAAa,WAAY,KACrD,EAYE,EAGP3C,gBAAmBH,YACXiB,SAAS8B,iBACnB,IAAI/C,kCACAA,8BAGAgD,SAASC,KACbA,GAAG5C,iBAAiB,UAAU,KAC5B,IAAIJ,KAAOiD,eAAelD,WAC1BE,eAAeF,UAAWC,KAAK,GAC/B,IAGagB,SAASC,cAAc,IAAIlB,iCAEjCK,iBAAiB,SAAS,KACnC,IAAIJ,KAAOiD,eAAelD,WAC1BE,eAAeF,UAAWC,KAAK,IAGpBgB,SAASC,cAAc,IAAIlB,0BAEjCK,iBAAiB,YAAalC,IAGnC,GAFAA,EAAEgF,kBAEY,UAAVhF,EAAEiF,IAAiB,CACrBjF,EAAEkF,iBAEF,IAAIpD,KAAOiD,eAAelD,WAC1BE,eAAeF,UAAWC,KAC5B,MAGK,GAGH4C,kBAAqB7C,YACzBiB,SACGC,cAAc,IAAIlB,4BAClBK,iBAAiB,UAAU,KAC1B,IAAIJ,KAAOiD,eAAelD,WAC1BE,eAAeF,UAAWC,KAAK,GAC/B,EAGOiD,eAAkBlD,YAC7B,IAAIsD,KAAOrC,SAASC,cAAc,IAAIlB,uCACtC,OAAOf,OAAOsE,YAAY,IAAIC,SAASF,MAAMG,UAAU,EACvDjD,SAAA0C,eAAAA,eAEF,MAAMxC,kBAAqBT,OACzB,IAAIyD,IAAM,IAAIC,IAAIvD,OAAOwD,SAASC,MAE7B5D,MAAM6D,MACTJ,IAAIK,aAAaC,OAAO,QAG1B,IAAK,IAAKZ,IAAKa,SAAUhF,OAAOwE,QAAQxD,MACtCyD,IAAIK,aAAavE,IAAI4D,IAAKa,OAE5B7D,OAAO8D,QAAQC,aAAa,KAAM,KAAMT,IAAI,EAGxC3C,cAAiBf,YACrB,IAAIoE,OAASnD,SAASC,cAAc,IAAIlB,0BAIxC,OAHAoE,OAAOC,UAAUC,OAAO,UACxBF,OAAOC,UAAUC,OAAO,WAEjB,CAAI,EAGP7D,oBAAsBA,CAACT,UAAWuE,UACjB,iBAAjBA,OAAOC,SACTD,OAAOE,OAAS,EAChBF,OAAOC,OAAS,OAGG,aAAjBD,OAAOC,SACTD,OAAOG,UAAY,EACnBH,OAAOC,OAAS,OAGd1E,kBAAkB6E,SAAS3E,mBACtBuE,OAAOC,OAGhBD,OAAOK,SAAWL,OAAOK,UAAY,SAE9BL,OAAOvE,UAEPuE,QAGH/B,oBAAuBxC,YACbiB,SAAS8B,iBACrB,IAAI/C,yDAGEgD,SAAS6B,MACfA,IAAIxE,iBAAiB,SAAUyE,QAC7BA,MAAMzB,iBAEN,IAAIS,KAAOe,IAAIE,QAAQjB,KACnB7D,KAAOiD,eAAelD,WAErB8D,OAAQe,IAAIG,WAEjB/E,KAAK6D,KAAOA,KACZ5D,eAAeF,UAAWC,MAAK,GAC/B,GACF,EAGEsC,wBAA2BH,UAC/B,IAAKA,SAAS6C,aAAsC,GAAvB7C,QAAQ6C,YACnC,MAAO,GAIT,IAAIC,aAAe9E,OAAO+E,YAAc,IACpC7C,WAAa,CACf8C,MAAO,OACPC,MAAO,GACPC,UAAU,EACVC,SAAUnD,QAAQwC,UAAY,IAG5BY,YAAcpD,QAAQ0B,KACtB2B,WAAarD,QAAQ6C,YAErBO,YAAc,IAChBlD,WAAWoD,SAAW,CAAE5B,KAAM0B,YAAc,EAAG9B,IAAK,UAAS8B,YAAc,KAGzEA,YAAcC,aAChBnD,WAAWqD,KAAO,CAAE7B,KAAM0B,YAAc,EAAG9B,IAAK,SAAS8B,YAAc,MAGzE,IAAII,SAAW,EACf,GAAIH,YAAc,EAChB,IAAK,IAAIlG,EAAI,EAAGA,GAAKkG,WAAYlG,IAC/B+C,WAAW+C,MAAMQ,KAAK,CAAE/B,KAAMvE,EAAGuG,OAAQvG,IAAMiG,YAAa9B,IAAK,SAASnE,WAK5E,GAAKiG,aAAe,IAAMN,cAAkBM,aAAe,GAAKN,aAAe,CAM7E,GAJIM,aAAeC,WAAa,IAC9BG,SAAW,GAGRV,aAMH,IAAK,IAAI3F,EAAI,EAAGA,GAAK,EAAGA,IACtB+C,WAAW+C,MAAMQ,KAAK,CAAE/B,KAAMvE,EAAGuG,OAAQvG,IAAMiG,YAAa9B,IAAK,SAASnE,WAN5E,IAAK,IAAIA,EAAI,EAAGA,GAAKiG,YAAc,GAAKjG,GAAKkG,WAAaG,SAAUrG,IAClE+C,WAAW+C,MAAMQ,KAAK,CAAE/B,KAAMvE,EAAGuG,OAAQvG,IAAMiG,YAAa9B,IAAK,SAASnE,MAS9D,GAAZqG,UAAiBV,aACnB5C,WAAWyD,KAAO,CAAEjC,KAAM2B,WAAYK,OAAQL,YAAcD,YAAa9B,IAAK,SAAS+B,cAEhFD,YAAc,EAAIC,YACzBnD,WAAW+C,MAAMQ,KAAK,CAAE/B,KAAM2B,WAAYK,OAAQL,aAAeD,YAAa9B,IAAK,SAAS+B,cAGhG,MAAO,GAAKD,YAAcC,WAAa,IAAMP,cAAkBM,YAAcC,WAAa,GAAKP,aAAe,CAE5GU,SAAW,EACPV,aACEM,YAAc,IAChBI,SAAWH,WAAa,EACxBnD,WAAW0D,MAAQ,CAAElC,KAAM,EAAGgC,OAAQ,GAAKN,YAAa9B,IAAK,YAGxD8B,aAAe,IACtBI,SAAWJ,YAAc,EACzBlD,WAAW0D,MAAQ,CAAElC,KAAM,EAAGgC,OAAQ,GAAKN,YAAa9B,IAAK,YAG/D,IAAK,IAAInE,EAAIqG,SAAUrG,GAAKkG,WAAYlG,IACtC+C,WAAW+C,MAAMQ,KAAK,CAAE/B,KAAMvE,EAAGuG,OAAQvG,IAAMiG,YAAa9B,IAAK,SAASnE,KAE9E,KAAO,CAGL,GADA+C,WAAW0D,MAAQ,CAAElC,KAAM,EAAGgC,OAAQ,GAAKN,YAAa9B,IAAK,WACzDwB,aACF5C,WAAW+C,MAAMQ,KAAK,CAAE/B,KAAM0B,YAAaM,QAAQ,EAAMpC,IAAK,SAAS8B,qBAGvE,IAAK,IAAIjG,EAAIiG,YAAc,EAAGjG,GAAKiG,YAAc,EAAGjG,IAClD+C,WAAW+C,MAAMQ,KAAK,CAAE/B,KAAMvE,EAAGuG,OAAQvG,IAAMiG,YAAa9B,IAAK,SAASnE,MAG9E+C,WAAWyD,KAAO,CAAEjC,KAAM2B,WAAYK,OAAQL,YAAcD,YAAa9B,IAAK,SAAS+B,aACzF,CAEF,OAAOnD,UAAU,EAIbnB,KAAQ8E,UACZA,QAAQC,MAAMC,QAAU,CAAC,EAGrBtE,OAAUoE,UACdA,QAAQC,MAAMC,QAAU,CAAC,EACzB3F,SAAA/B,QAEa,CACbsB,UACAG,8BACAgD,+BACD,OAAA1C,SAAA/B,OAAA"}