{"version": 3, "file": "visibility.min.js", "sources": ["../src/visibility.js"], "sourcesContent": ["import * as Notification from \"core/notification\";\nimport { get_string as getString } from \"core/str\";\n\nimport Block from \"local_courseblockapi/block\";\nimport Page from \"local_courseblockapi/page\";\nimport API from \"local_courseblockapi/api\";\n\nexport const page_listener = (component) => {\n  let spans = document.querySelectorAll(\n    `.${component} .card .card-body span.card-course-visibility`\n  );\n\n  spans.forEach((el) => {\n    el.addEventListener(\"click\", function (event) {\n      event.stopPropagation();\n      let courseid = this.dataset.courseid;\n      let coursename = this.dataset.coursename;\n\n      let promise = API.set_visibility({\n        component: component,\n        courseid: courseid,\n      });\n\n      promise\n        .then((result) => {\n          if (!result) {\n            getString(\"visibilityerror\", \"local_courseblockapi\", {\n              coursename: coursename,\n              component: component,\n            }).done(function (s) {\n              Notification.addNotification({\n                message: s,\n                type: \"error\",\n              });\n            });\n\n            return false;\n          }\n\n          getString(\"visibilitysuccess\", \"local_courseblockapi\", {\n            coursename: coursename,\n            component: component,\n          }).done(function (s) {\n            Notification.addNotification({\n              message: s,\n              type: \"success\",\n            });\n\n            let args = Page.serialize_form(component);\n            Page.update_courses(component, args);\n\n            return true;\n          });\n\n          return false;\n        })\n        .fail(Notification.exception);\n    });\n  });\n};\n\nexport const block_listener = (component) => {\n  let spans = document.querySelectorAll(\n    `.${component} .card .card-body span.card-course-visibility`\n  );\n\n  spans.forEach((el) => {\n    el.addEventListener(\"click\", function (event) {\n      event.stopPropagation();\n      let courseid = this.dataset.courseid;\n      let coursename = this.dataset.coursename;\n\n      let promise = API.set_visibility({\n        component: component,\n        courseid: courseid,\n      });\n\n      promise.then((result) => {\n        if (!result) {\n          getString(\"visibilityerror\", \"local_courseblockapi\", {\n            coursename: coursename,\n            component: component,\n          }).done(function (s) {\n            Notification.addNotification({\n              message: s,\n              type: \"error\",\n            });\n          });\n\n          return false;\n        }\n\n        getString(\"visibilitysuccess\", \"local_courseblockapi\", {\n          coursename: coursename,\n          component: component,\n        }).done(function (s) {\n          Notification.addNotification({\n            message: s,\n            type: \"success\",\n          });\n\n          Block.update_courses(component);\n\n          return true;\n        });\n\n        return false;\n      });\n    });\n  });\n};\n\nexport default {\n  block_listener,\n  page_listener,\n};\n"], "names": ["page_listener", "component", "document", "querySelectorAll", "for<PERSON>ach", "el", "addEventListener", "event", "stopPropagation", "courseid", "this", "dataset", "coursename", "API", "set_visibility", "then", "result", "done", "s", "Notification", "addNotification", "message", "type", "args", "Page", "serialize_form", "update_courses", "fail", "exception", "block_listener"], "mappings": "89CAOaA,cAAiBC,YAChBC,SAASC,4BACfF,4DAGAG,SAASC,KACbA,GAAGC,iBAAiB,SAAS,SAAUC,OACrCA,MAAMC,sBACFC,SAAWC,KAAKC,QAAQF,SACxBG,WAAaF,KAAKC,QAAQC,WAEhBC,aAAIC,eAAe,CAC/Bb,UAAWA,UACXQ,SAAUA,WAITM,MAAMC,QACAA,4BAcK,oBAAqB,uBAAwB,CACrDJ,WAAYA,WACZX,UAAWA,YACVgB,MAAK,SAAUC,GAChBC,aAAaC,gBAAgB,CAC3BC,QAASH,EACTI,KAAM,gBAGJC,KAAOC,cAAKC,eAAexB,gCAC1ByB,eAAezB,UAAWsB,OAExB,MAGF,wBA5BK,kBAAmB,uBAAwB,CACnDX,WAAYA,WACZX,UAAWA,YACVgB,MAAK,SAAUC,GAChBC,aAAaC,gBAAgB,CAC3BC,QAASH,EACTI,KAAM,cAIH,KAoBVK,KAAKR,aAAaS,6DAKdC,eAAkB5B,YACjBC,SAASC,4BACfF,4DAGAG,SAASC,KACbA,GAAGC,iBAAiB,SAAS,SAAUC,OACrCA,MAAMC,sBACFC,SAAWC,KAAKC,QAAQF,SACxBG,WAAaF,KAAKC,QAAQC,WAEhBC,aAAIC,eAAe,CAC/Bb,UAAWA,UACXQ,SAAUA,WAGJM,MAAMC,QACPA,4BAcK,oBAAqB,uBAAwB,CACrDJ,WAAYA,WACZX,UAAWA,YACVgB,MAAK,SAAUC,UAChBC,aAAaC,gBAAgB,CAC3BC,QAASH,EACTI,KAAM,2BAGFI,eAAezB,YAEd,MAGF,wBA3BK,kBAAmB,uBAAwB,CACnDW,WAAYA,WACZX,UAAWA,YACVgB,MAAK,SAAUC,GAChBC,aAAaC,gBAAgB,CAC3BC,QAASH,EACTI,KAAM,cAIH,gEAuBF,CACbO,eAAAA,eACA7B,cAAAA"}