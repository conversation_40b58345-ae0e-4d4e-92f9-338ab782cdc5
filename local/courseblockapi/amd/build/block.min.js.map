{"version": 3, "file": "block.min.js", "sources": ["../src/block.js"], "sourcesContent": ["import Templates from \"core/templates\";\nimport * as Notification from \"core/notification\";\nimport API from \"local_courseblockapi/api\";\nimport Card from \"local_courseblockapi/card\";\n\nexport const init = (component) => {\n  Card.calc_card_height(component);\n  window.addEventListener(\"resize\", function () {\n    Card.calc_card_height(component);\n  });\n};\n\nexport const update_courses = (component, args = {}) => {\n  API.get_courses(component, args)\n    .then((result) => {\n      Templates.render(`local_courseblockapi/block-cards`, result)\n        .then(function (html) {\n          let block = document.querySelector(`.${component} .cards`);\n          block.innerHTML = html;\n          Card.calc_card_height(component);\n          return true;\n        })\n        .fail(Notification.exception);\n    })\n    .fail(Notification.exception);\n\n  return false;\n};\n\nexport default {\n  init,\n  update_courses,\n};\n"], "names": ["init", "component", "calc_card_height", "window", "addEventListener", "update_courses", "args", "get_courses", "then", "result", "render", "html", "document", "querySelector", "innerHTML", "fail", "Notification", "exception"], "mappings": "g8CAKaA,KAAQC,0BACdC,iBAAiBD,WACtBE,OAAOC,iBAAiB,UAAU,yBAC3BF,iBAAiBD,wCAIbI,eAAiB,SAACJ,eAAWK,4DAAO,uBAC3CC,YAAYN,UAAWK,MACxBE,MAAMC,4BACKC,0CAA2CD,QAClDD,MAAK,SAAUG,aACFC,SAASC,yBAAkBZ,sBACjCa,UAAYH,mBACbT,iBAAiBD,YACf,KAERc,KAAKC,aAAaC,cAEtBF,KAAKC,aAAaC,YAEd,uDAGM,CACbjB,KAAAA,KACAK,eAAAA"}