{"version": 3, "file": "card.min.js", "sources": ["../src/card.js"], "sourcesContent": ["export const calc_card_height = (component) => {\n  let imgOriginalWidth = 600;\n  let imgOriginalHeight = 350;\n  let card = document.querySelector(\n    `.${component} .local_courseblockapi .card`\n  );\n  let elem = document.querySelector(`.${component} .local_courseblockapi`);\n\n  if (!card) {\n    return false;\n  }\n\n  let card_width = Math.max.apply(\n    null,\n    Array.from(\n      document.querySelectorAll(`.${component} .local_courseblockapi .card`)\n    ).map(function (a) {\n      return a.offsetWidth || a.offsetWidth || a.scrollWidth;\n    })\n  );\n\n  let new_image_height = (card_width * imgOriginalHeight) / imgOriginalWidth;\n  let new_card_height = new_image_height + 16; //add 16px\n\n  elem.style.setProperty(\"--img-height\", new_image_height + \"px\");\n  elem.style.setProperty(\"--card-height\", new_card_height + \"px\");\n\n  let card_body_height = Math.max.apply(\n    null,\n    Array.from(\n      document.querySelectorAll(\n        `.${component} .local_courseblockapi .card-body`\n      )\n    ).map(function (a) {\n      return a.offsetHeight || a.offsetHeight || a.scrollHeight;\n    })\n  );\n\n  let new_card_height_hover =\n    card_body_height +\n    parseInt(getComputedStyle(elem).getPropertyValue(\"--img-height\")) +\n    5;\n  elem.style.setProperty(\"--card-height-hover\", new_card_height_hover + \"px\");\n\n  return true;\n};\n\nexport default {\n  calc_card_height\n};\n"], "names": ["calc_card_height", "component", "card", "document", "querySelector", "elem", "new_image_height", "Math", "max", "apply", "Array", "from", "querySelectorAll", "map", "a", "offsetWidth", "scrollWidth", "new_card_height", "style", "setProperty", "new_card_height_hover", "offsetHeight", "scrollHeight", "parseInt", "getComputedStyle", "getPropertyValue"], "mappings": "mLAAaA,iBAAoBC,gBAG3BC,KAAOC,SAASC,yBACdH,2CAEFI,KAAOF,SAASC,yBAAkBH,yCAEjCC,YACI,MAYLI,iBAnBoB,IAUPC,KAAKC,IAAIC,MACxB,KACAC,MAAMC,KACJR,SAASS,4BAAqBX,4CAC9BY,KAAI,SAAUC,UACPA,EAAEC,aAAeD,EAAEC,aAAeD,EAAEE,gBAhBxB,IAqBnBC,gBAAkBX,iBAAmB,GAEzCD,KAAKa,MAAMC,YAAY,eAAgBb,iBAAmB,MAC1DD,KAAKa,MAAMC,YAAY,gBAAiBF,gBAAkB,UAatDG,sBAXmBb,KAAKC,IAAIC,MAC9B,KACAC,MAAMC,KACJR,SAASS,4BACHX,iDAENY,KAAI,SAAUC,UACPA,EAAEO,cAAgBP,EAAEO,cAAgBP,EAAEQ,iBAM/CC,SAASC,iBAAiBnB,MAAMoB,iBAAiB,iBACjD,SACFpB,KAAKa,MAAMC,YAAY,sBAAuBC,sBAAwB,OAE/D,2DAGM,CACbpB,iBAAAA"}