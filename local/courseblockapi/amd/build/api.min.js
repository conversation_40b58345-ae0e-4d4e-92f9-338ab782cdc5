define("local_courseblockapi/api",["exports","core/ajax"],(function(_exports,_ajax){var obj;Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.set_visibility=_exports.set_favourite=_exports.get_courses=_exports.default=void 0,_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj};const get_ajax_function=component=>({block_mycourses:"local_courseblockapi_get_my_courses",block_freecourses:"local_courseblockapi_get_free_courses",block_popularcourses:"local_courseblockapi_get_popular_courses",block_recommendedcourses:"local_courseblockapi_get_recommended_courses"}[component]),get_courses=function(component){let args=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const request={methodname:get_ajax_function(component),args:args};return _ajax.default.call([request])[0]};_exports.get_courses=get_courses;const set_visibility=args=>{const request={methodname:"local_courseblockapi_toggle_course_visibility",args:args};return _ajax.default.call([request])[0]};_exports.set_visibility=set_visibility;const set_favourite=args=>{const request={methodname:"local_courseblockapi_set_favourite_courses",args:args};return _ajax.default.call([request])[0]};_exports.set_favourite=set_favourite;var _default={get_courses:get_courses,set_visibility:set_visibility,set_favourite:set_favourite};return _exports.default=_default,_exports.default}));

//# sourceMappingURL=api.min.js.map