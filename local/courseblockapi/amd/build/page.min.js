define("local_courseblockapi/page",["exports","core/templates","core/notification","local_courseblockapi/api","local_courseblockapi/favourite","local_courseblockapi/visibility","local_courseblockapi/card"],(function(_exports,_templates,Notification,_api,_favourite,_visibility,_card){function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(_getRequireWildcardCache=function(e){return e?t:r})(e)}function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.update_courses=_exports.update_course_page=_exports.serialize_form=_exports.init=_exports.default=void 0,_templates=_interopRequireDefault(_templates),Notification=function(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u]}return n.default=e,t&&t.set(e,n),n}(Notification),_api=_interopRequireDefault(_api),_favourite=_interopRequireDefault(_favourite),_visibility=_interopRequireDefault(_visibility),_card=_interopRequireDefault(_card);const unenrolled_blocks=["block_freecourses","block_recommendedcourses"],init=(component,args)=>{if(!component)return!1;update_courses(component,args),header_listener(component),window.addEventListener("resize",(function(){_card.default.calc_card_height(component)}))};_exports.init=init;const update_courses=(component,args)=>(args=prepare_page_params(component,args),update_url_params(args),update_course_page(component,args),!0);_exports.update_courses=update_courses;const update_course_page=function(component){let args=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};toogle_loader(component);let cards=document.querySelector(`.${component} .cards`);return fade(cards),cards.innerHTML="",_api.default.get_courses(component,args).then((result=>(update_modality(component,result),_templates.default.render("local_courseblockapi/page-cards",result).then((function(html){toogle_loader(component),cards.innerHTML=html,_card.default.calc_card_height(component),unfade(cards),_favourite.default.init(component),_visibility.default.page_listener(component)})).fail(Notification.exception),update_pagination(component,result),!0))).fail(Notification.exception),!1};_exports.update_course_page=update_course_page;const update_pagination=(component,results)=>{let pag_elem=document.querySelector(`.${component} .pagination`),pagination=prepare_page_pagination(results);return pagination?_templates.default.render("local_courseblockapi/course-pagination",pagination).then((function(html){pag_elem.innerHTML=html,unfade(pag_elem),pagination_listener(component)})).fail(Notification.exception):(fade(pag_elem),pag_elem.innerHTML=""),!0},update_modality=(component,results)=>{let modality_elem=document.querySelector(`.${component} .page-header-modality`),modalitySelect=modality_elem.querySelector("#modality");return results.modalities?.length?(modalitySelect&&modalitySelect.removeAttribute("disabled"),_templates.default.render("local_courseblockapi/page-header-modalities",results).then((function(html){modality_elem.innerHTML=html,unfade(modality_elem),modality_listener(component)})).fail(Notification.exception),!0):(modalitySelect&&modalitySelect.setAttribute("disabled",""),!1)},header_listener=component=>(document.querySelectorAll(`.${component} form #status,\n     .${component} form #sort\n    `).forEach((el=>{el.addEventListener("change",(()=>{let args=serialize_form(component);update_courses(component,args)}))})),document.querySelector(`.${component} form #button-search`).addEventListener("click",(()=>{let args=serialize_form(component);update_courses(component,args)})),document.querySelector(`.${component} form #search`).addEventListener("keypress",(e=>{if(e.stopPropagation(),"Enter"===e.key){e.preventDefault();let args=serialize_form(component);update_courses(component,args)}})),!0),modality_listener=component=>{document.querySelector(`.${component} form #modality`).addEventListener("change",(()=>{let args=serialize_form(component);update_courses(component,args)}))},serialize_form=component=>{let form=document.querySelector(`.${component} .courses-page-header form`);return Object.fromEntries(new FormData(form).entries())};_exports.serialize_form=serialize_form;const update_url_params=args=>{let url=new URL(window.location.href);args?.page||url.searchParams.delete("page");for(let[key,value]of Object.entries(args))url.searchParams.set(key,value);window.history.replaceState(null,null,url)},toogle_loader=component=>{let loader=document.querySelector(`.${component} .page-loader`);return loader.classList.toggle("d-flex"),loader.classList.toggle("d-none"),!0},prepare_page_params=(component,params)=>("hiddencourses"==params.status&&(params.hidden=1,params.status="all"),"favourite"==params.status&&(params.favourite=1,params.status="all"),unenrolled_blocks.includes(component)&&delete params.status,params.per_page=params.per_page??8,delete params.component,params),pagination_listener=component=>{document.querySelectorAll(`.${component} .courses-page-footer .pagination .page-link`).forEach((btn=>{btn.addEventListener("click",(event=>{event.preventDefault();let page=btn.dataset.page,args=serialize_form(component);page&&!btn.disabled&&(args.page=page,update_courses(component,args))}))}))},prepare_page_pagination=results=>{if(!results?.total_pages||1==results.total_pages)return{};let isMobileView=window.innerWidth<=600,pagination={label:"Page",pages:[],haspages:!0,pagesize:results.per_page||10},currentPage=results.page,totalPages=results.total_pages;currentPage>1&&(pagination.previous={page:currentPage-1,url:"?page="+(currentPage-1)}),currentPage<totalPages&&(pagination.next={page:currentPage+1,url:`?page=${currentPage+1}`});let treshold=0;if(totalPages<=5)for(let i=1;i<=totalPages;i++)pagination.pages.push({page:i,active:i===currentPage,url:`?page=${i}`});else if(currentPage<=5&&!isMobileView||currentPage<=3&&isMobileView){if(currentPage<=totalPages-5&&(treshold=2),isMobileView)for(let i=1;i<=3;i++)pagination.pages.push({page:i,active:i===currentPage,url:`?page=${i}`});else for(let i=1;i<=currentPage+3&&i<=totalPages-treshold;i++)pagination.pages.push({page:i,active:i===currentPage,url:`?page=${i}`});2==treshold||isMobileView?pagination.last={page:totalPages,active:totalPages==currentPage,url:`?page=${totalPages}`}:currentPage+3<totalPages&&pagination.pages.push({page:totalPages,active:totalPages===currentPage,url:`?page=${totalPages}`})}else if(currentPage>totalPages-5&&!isMobileView||currentPage>totalPages-3&&isMobileView){treshold=1,isMobileView?currentPage>3&&(treshold=totalPages-2,pagination.first={page:1,active:1==currentPage,url:"?page=1"}):currentPage>=6&&(treshold=currentPage-3,pagination.first={page:1,active:1==currentPage,url:"?page=1"});for(let i=treshold;i<=totalPages;i++)pagination.pages.push({page:i,active:i===currentPage,url:`?page=${i}`})}else{if(pagination.first={page:1,active:1==currentPage,url:"?page=1"},isMobileView)pagination.pages.push({page:currentPage,active:!0,url:`?page=${currentPage}`});else for(let i=currentPage-3;i<=currentPage+3;i++)pagination.pages.push({page:i,active:i===currentPage,url:`?page=${i}`});pagination.last={page:totalPages,active:totalPages==currentPage,url:`?page=${totalPages}`}}return pagination},fade=element=>{element.style.opacity=0},unfade=element=>{element.style.opacity=1};_exports.default={init:init,update_courses:update_courses,serialize_form:serialize_form};return _exports.default}));

//# sourceMappingURL=page.min.js.map