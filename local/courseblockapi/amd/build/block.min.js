define("local_courseblockapi/block",["exports","core/templates","core/notification","local_courseblockapi/api","local_courseblockapi/card"],(function(_exports,_templates,Notification,_api,_card){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.update_courses=_exports.init=_exports.default=void 0,_templates=_interopRequireDefault(_templates),Notification=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(Notification),_api=_interopRequireDefault(_api),_card=_interopRequireDefault(_card);const init=component=>{_card.default.calc_card_height(component),window.addEventListener("resize",(function(){_card.default.calc_card_height(component)}))};_exports.init=init;const update_courses=function(component){let args=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return _api.default.get_courses(component,args).then((result=>{_templates.default.render("local_courseblockapi/block-cards",result).then((function(html){return document.querySelector(".".concat(component," .cards")).innerHTML=html,_card.default.calc_card_height(component),!0})).fail(Notification.exception)})).fail(Notification.exception),!1};_exports.update_courses=update_courses;var _default={init:init,update_courses:update_courses};return _exports.default=_default,_exports.default}));

//# sourceMappingURL=block.min.js.map