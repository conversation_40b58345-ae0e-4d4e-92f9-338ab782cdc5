{"version": 3, "file": "favourite.min.js", "sources": ["../src/favourite.js"], "sourcesContent": ["import * as Notification from \"core/notification\";\nimport API from \"local_courseblockapi/api\";\n\nexport const init = (component) => {\n  let spans = document.querySelectorAll(\n    `.${component} .card .card-body span.card-course-favourite`\n  );\n\n  spans.forEach((el) => {\n    el.addEventListener(\"click\", function (event) {\n      event.stopPropagation();\n      let courseid = this.dataset.courseid;\n      let status =\n        this.dataset.favourite == 0 || this.dataset.favourite == false;\n\n      let promise = API.set_favourite({\n        courses: [\n          {\n            component: null, //null means it favourites in system context\n            id: courseid,\n            favourite: status,\n          },\n        ],\n      });\n\n      promise\n        .then((result) => {\n          if (result.warnings) {\n            result.warnings.forEach((warning) => {\n              Notification.addNotification({\n                message: warning.message,\n                type: \"error\",\n              });\n\n              return false;\n            });\n          }\n\n          let allCards = document.querySelectorAll(\n            `.card-courses span.card-course-favourite[data-courseid='${courseid}']`\n          );\n\n          let allIcon = document.querySelectorAll(\n            `.card-courses span.card-course-favourite[data-courseid='${courseid}'] i`\n          );\n\n          allCards.forEach(function (button) {\n            button.dataset.favourite = status ? 1 : 0;\n          });\n\n          allIcon.forEach(function (icon) {\n            icon.classList.toggle(\"fa-regular\");\n            icon.classList.toggle(\"fa\");\n          });\n\n          return true;\n        })\n        .fail(Notification.exception);\n\n      return false;\n    });\n  });\n};\n\nexport default {\n  init,\n};\n"], "names": ["init", "component", "document", "querySelectorAll", "for<PERSON>ach", "el", "addEventListener", "event", "stopPropagation", "courseid", "this", "dataset", "status", "favourite", "API", "set_favourite", "courses", "id", "then", "result", "warnings", "warning", "Notification", "addNotification", "message", "type", "allCards", "allIcon", "button", "icon", "classList", "toggle", "fail", "exception"], "mappings": "kuCAGaA,KAAQC,YACPC,SAASC,4BACfF,2DAGAG,SAASC,KACbA,GAAGC,iBAAiB,SAAS,SAAUC,OACrCA,MAAMC,sBACFC,SAAWC,KAAKC,QAAQF,SACxBG,OACwB,GAA1BF,KAAKC,QAAQE,WAA4C,GAA1BH,KAAKC,QAAQE,iBAEhCC,aAAIC,cAAc,CAC9BC,QAAS,CACP,CACEf,UAAW,KACXgB,GAAIR,SACJI,UAAWD,WAMdM,MAAMC,SACDA,OAAOC,UACTD,OAAOC,SAAShB,SAASiB,UACvBC,aAAaC,gBAAgB,CAC3BC,QAASH,QAAQG,QACjBC,KAAM,WAGD,SAIPC,SAAWxB,SAASC,mFACqCM,gBAGzDkB,QAAUzB,SAASC,mFACsCM,yBAG7DiB,SAAStB,SAAQ,SAAUwB,QACzBA,OAAOjB,QAAQE,UAAYD,OAAS,EAAI,KAG1Ce,QAAQvB,SAAQ,SAAUyB,MACxBA,KAAKC,UAAUC,OAAO,cACtBF,KAAKC,UAAUC,OAAO,UAGjB,KAERC,KAAKV,aAAaW,YAEd,yCAKE,CACbjC,KAAAA"}