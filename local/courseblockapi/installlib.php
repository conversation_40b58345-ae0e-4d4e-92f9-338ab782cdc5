<?php
use core_course\customfield\course_handler;
use core_customfield\field_controller;

defined('MOODLE_INTERNAL') || die();

require_once(__DIR__.'/../../config.php');

function local_courseblockapi_create_course_fields(){
    $categoryname = 'Informações do curso para os blocos';

    $fields = local_courseblockapi_get_fields();

    foreach($fields as $key => $field){

        local_courseblockapi_create_course_customfield($field,$categoryname);
    }

    return true;
}

function local_courseblockapi_get_fields(){
    $fields = [];

    $configdata = new \stdClass;
    $configdata->required = '0';
    $configdata->uniquevalues = '0';
    $configdata->locked = '0';
    $configdata->visibility = '2';
    $configdata->maxlength = 30; 
    $configdata->displaysize = 50;
    $configdata->ispassword = '0';

    $fieldparam = new \stdClass;
    $fieldparam->name = 'Modalidade';
    $fieldparam->shortname = 'modality';
    $fieldparam->type = 'text';
    $fieldparam->description = 'Modalidade do curso';
    $fieldparam->descriptionformat = 1;
    $fieldparam->configdata = json_encode($configdata);

    $fields[] = $fieldparam;

    $configdata = new \stdClass;
    $configdata->required = '0';
    $configdata->uniquevalues = '0';
    $configdata->locked = '0';
    $configdata->visibility = '2';
    $configdata->maxlength = 30; 
    $configdata->displaysize = 50;
    $configdata->ispassword = '0';

    $fieldparam = new \stdClass;
    $fieldparam->name = 'Nível';
    $fieldparam->shortname = 'level';
    $fieldparam->type = 'text';
    $fieldparam->description = 'Nível do curso';
    $fieldparam->descriptionformat = 1;
    $fieldparam->configdata = json_encode($configdata);

    $fields[] = $fieldparam;

    $configdata = new \stdClass;
    $configdata->required = '0';
    $configdata->uniquevalues = '0';
    $configdata->locked = '0';
    $configdata->visibility = '2';
    $configdata->defaultunit = 3600;

    $fieldparam = new \stdClass;
    $fieldparam->name = 'Carga horária';
    $fieldparam->shortname = 'workload';
    $fieldparam->type = 'duration';
    $fieldparam->description = 'Carga horária do curso';
    $fieldparam->descriptionformat = 1;
    $fieldparam->configdata = json_encode($configdata);

    $fields[] = $fieldparam;

    return $fields;
}

function local_courseblockapi_create_course_customfield(stdClass $fieldparam,string $categoryname = '')
{
    $course_handler = course_handler::create();
    
    if(!isset($course_handler->get_available_field_types()[$fieldparam->type])
    && !$course_handler->get_available_field_types()[$fieldparam->type])
    {
        mtrace(get_string('customfieldtypenotfound','local_courseblockapi'));
        return ;
    }

    $category = local_courseblockapi_get_customfield_category($course_handler,$categoryname);

    if(!$category)
    {
        $categoryid = $course_handler->create_category($categoryname);
        $category = $course_handler->get_categories_with_fields()[$categoryid];
    }

    $field = local_courseblockapi_get_customfield_field($category,$fieldparam->shortname);

    if($field)
    {
        mtrace(get_string('customfieldalreadyset','local_courseblockapi'));
        return ;
    }

    $field = field_controller::create(0,(object) ['type'=>$fieldparam->type],$category);
    
    $field_handler = $field->get_handler();
    $field_handler->save_field_configuration($field, $fieldparam);

    mtrace(get_string('customfieldcreated','local_courseblockapi'));
    return true;
}

function local_courseblockapi_get_customfield_category(course_handler $course_handler, string $categoryname)
{
    $categories = $course_handler->get_categories_with_fields();

    $category = local_courseblockapi_array_usearch($categories,function($cat) use ($categoryname)
    {
        return $cat->get_formatted_name() === $categoryname;
    });
    
    if(!$category)
    {
        return null;
    }

    return $category;
}

function local_courseblockapi_get_customfield_field(core_customfield\category_controller $category_controller, string $fieldshortname)
{

    $field = local_courseblockapi_array_usearch($category_controller->get_fields(),function($field) use ($fieldshortname)
    {
        return $field->get('shortname') === $fieldshortname;
    });
    if(!$field)
    {
        return null;
    }

    return $field;
}

function local_courseblockapi_array_usearch(array $array, Closure $test) {
    $found = false;
    $iterator = new ArrayIterator($array);

    while ($found === false && $iterator->valid()) {
        if ($test($iterator->current())) {
            $found = $iterator->key();
        }
        $iterator->next();
    }
    if(!$found) {
        return '';
    }
    return $array[$found];
}