<?php

namespace local_courseblockapi\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_multiple_structure;
use core_external\external_single_structure;
use core_external\external_value;
use invalid_parameter_exception;
use local_courseblockapi\recommended_courses;
use local_courseblockapi\support\recommended_courses_sorters;

require_once($CFG->dirroot . '/local/courseblockapi/locallib.php');

/**
 * Web service to returns users to the list of courses they are enrolled in.
 *
 * @package local_courseblockapi
 */
class get_recommended_courses extends external_api
{
    /**
     * Describes the parameters.
     *
     * @return external_function_parameters
     */
    public static function api_parameters()
    {
        return new external_function_parameters([
            'page'           => new external_value(PARAM_INT, 'Current page', VALUE_DEFAULT, 1),
            'per_page'       => new external_value(PARAM_INT, 'Items per page', VALUE_DEFAULT, 4),
            'search'         => new external_value(PARAM_TEXT, 'Search by fullname or shortname of the course', VALUE_DEFAULT, ''),
            'modality'       => new external_value(PARAM_TEXT, 'Course modality filter', VALUE_DEFAULT, 'all'),
            'level'          => new external_value(PARAM_TEXT, 'Course level filter', VALUE_DEFAULT, 'all'),
            'favourite'      => new external_value(PARAM_BOOL, 'Only favourite course', VALUE_DEFAULT, false),
            'hidden'         => new external_value(PARAM_BOOL, 'Only hidden course', VALUE_DEFAULT, false),
            'sort'           => new external_value(PARAM_TEXT, 'Course sort', VALUE_DEFAULT, recommended_courses_sorters::COURSE_CREATED),
        ]);
    }

    /**
     * Describes the return structure of the service.
     *
     * @return external_single_structure
     */
    public static function api_returns()
    {
        return new external_single_structure([
            'page'        => new external_value(PARAM_INT, 'Current page'),
            'per_page'    => new external_value(PARAM_INT, 'Items per page'),
            'total'       => new external_value(PARAM_INT, 'Total items found'),
            'total_pages' => new external_value(PARAM_INT, 'Total pages'),
            'show_all'    => new external_value(PARAM_BOOL, 'True if more courses than showed'),
            'emptyurl'    => new external_value(PARAM_URL, 'Empty courses url.'),
            'courses'     => new external_multiple_structure(
                new external_single_structure(
                    [
                        'courseid'            => new external_value(PARAM_INT, 'Course ID.'),
                        'course'              => new external_value(PARAM_TEXT, 'Course fullname.'),
                        'categoryid'          => new external_value(PARAM_INT, 'Course category ID.'),
                        'category'            => new external_value(PARAM_TEXT, 'Course category.'),
                        'cover'               => new external_single_structure(
                            [
                                'logo' => new external_value(PARAM_RAW, 'Course logo.'),
                                'card' => new external_value(PARAM_RAW, 'Course card.'),
                                'image' => new external_value(PARAM_RAW, 'Course image.'),
                            ]
                        ),
                        'url'                 => new external_value(PARAM_URL, 'Course url.'),
                        'modality'            => new external_value(PARAM_TEXT, 'Course modality.', VALUE_OPTIONAL),
                        'level'               => new external_value(PARAM_TEXT, 'Course level.', VALUE_OPTIONAL),
                        'workload'            => new external_value(PARAM_TEXT, 'Course workload.', VALUE_OPTIONAL),
                        'is_favourite'        => new external_value(PARAM_BOOL, 'If course is favorite.'),
                        'is_hidden'           => new external_value(PARAM_BOOL, 'If course is hidden.'),
                        'is_enrolled'         => new external_value(PARAM_BOOL, 'If user is enrolled.'),
                        'teachers'            => new external_value(PARAM_TEXT, 'Course teachers.', VALUE_OPTIONAL),
                        'total_sections'      => new external_value(PARAM_TEXT, 'String with total sections within the course.'),
                        'total_new_updates'   => new external_value(PARAM_INT, 'Number of new features in the course.', VALUE_DEFAULT, 0),
                        'gamification'        => new external_single_structure(
                            [
                                'points' => new external_value(PARAM_RAW, 'Points.', VALUE_OPTIONAL),
                                'coins' => new external_value(PARAM_RAW, 'Coins.', VALUE_OPTIONAL),
                            ],
                            'Gamification information.',
                            VALUE_OPTIONAL
                        ),
                    ]
                )
            ),
            'modalities' => new external_multiple_structure(
                new external_single_structure(
                    [
                        'modality'      => new external_value(PARAM_TEXT, 'Modality.'),
                        'modality_name' => new external_value(PARAM_TEXT, 'Modality.'),
                        'selected'      => new external_value(PARAM_BOOL, 'If modality is selected.'),
                    ]
                )
            )
        ]);
    }

    /**
     * Executes the service.
     *
     * @param integer $page
     * @param integer $per_page
     * @param string $search
     * @param string $modality
     * @param string $level
     * @param bool $favourite
     * @param bool $hidden
     * @param string $sort
     * @return recommended_courses[]
     */
    public static function api(int $page = 1, $per_page = 4, string $search = '', string $modality = 'all', string $level = 'all', bool $favourite = false, bool $hidden = false, string $sort = recommended_courses_sorters::COURSE_CREATED)
    {
        global $USER;

        if (!isloggedin()) {
            return [];
        }

        $params = compact('page', 'search', 'modality', 'level', 'favourite', 'hidden', 'sort');

        self::validate_parameters(self::api_parameters(), $params);

        $context = \context_system::instance();
        self::validate_context($context);


        if ($page < 1) {
            $page = 1;
        }

        if (!empty($sort) && !recommended_courses_sorters::isValid($sort)) {
            $accepted_sorters = implode(', ', recommended_courses_sorters::getValues());

            throw new invalid_parameter_exception(get_string('exception:accepted_sorters', 'local_courseblockapi', $accepted_sorters));
        }

        return recommended_courses::get(
            $USER,
            $page,
            $per_page,
            $search,
            $modality,
            $level,
            $favourite,
            $hidden,
            $sort
        );
    }
}
