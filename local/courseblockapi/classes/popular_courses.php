<?php

namespace local_courseblockapi;

use core_completion\progress;
use local_courseblockapi\support\enrollment_status;
use local_courseblockapi\support\popular_courses_sorters;
use stdClass;
use local_courseblockapi\support\user_custom_fields;
use local_courseblockapi\traits\course_trait;
use local_courseblockapi\traits\query_trait;
use local_courseblockapi\traits\search_trait;
use local_courseblockapi\traits\tag_trait;
use local_gamification\repositories\reward_repository;
use moodle_url;

require_once($CFG->dirroot . '/local/courseblockapi/locallib.php');

class popular_courses
{
    use query_trait, course_trait, tag_trait, search_trait;

    /**
     * Block component
     * @var string
     */
    const block_setting = 'block_popularcourses';

    /**
     * Get the courses for a user based on the provided parameters.
     *
     * @param object $user The user object.
     * @param int $page The page number.
     * @param int $per_page The number of courses per page.
     * @param string $search The search string.
     * @param string $status The status filter.
     * @param string $modality The modality filter.
     * @param string $level The level filter.
     * @param bool $favourite Filter favourite course.
     * @param bool $hidden Filter hidden course.
     * @param bool $sort Course sort.
     * @return array The array of courses.
     */
    public static function get(object $user, int $page = 1, int $per_page = 4, string $search = '', string $status = 'all', string $modality = 'all', string $level = 'all', bool $favourite = false, bool $hidden = false, string $sort = popular_courses_sorters::MOST_VISITED): array
    {
        global $DB, $OUTPUT;

        $courses = [];

        $modality_shortname = user_custom_fields::MODALITY_SHORTNAME;
        $level_shortname    = user_custom_fields::LEVEL_SHORTNAME;
        $workload_shortname = user_custom_fields::WORKLOAD_SHORTNAME;

        $params = compact('user', 'search', 'status', 'modality', 'level', 'favourite', 'hidden', 'sort');

        $query = self::build_query($params);
        $sql = self::get_query_sql($query);

        // Calculates the starting record for the current page
        $start_from = ($page - 1) * $per_page;

        //Get the total number of rows
        $total = self::get_total_rows($sql, $query->params);

        // imageurl when none course was found
        $emptyurl = !$total ? $OUTPUT->image_url('courses', 'block_myoverview')->out() : '';

        // Calculates the total number of pages
        $total_pages = ceil($total / $per_page);

        $modalities = self::get_field_list($params, $modality_shortname, $modality);

        $recordset = $DB->get_recordset_sql($sql, $query->params, $start_from, $per_page);

        foreach ($recordset as $record) {

            $record->id = $record->ccourseid;
            $record->courseid = $record->ccourseid;

            $cover          = self::get_course_image($record);
            $teachers       = self::get_course_teachers($record->courseid, true);
            $is_favourite   = local_courseblockapi_is_course_favourite($record->courseid);
            $is_hidden      = self::course_is_hidden(self::get_block_name(), $record->courseid, $user->id);
            $progress       = progress::get_course_progress_percentage($record, $user->id);
            $url            = (new moodle_url('/course/view.php', ['id' => $record->courseid]))->out(false);
            $total_sections = self::get_course_total_sections($record->courseid, $record->total_sections);
            $gamification = self::get_course_gamification($record->courseid);

            $courses[] = [
                'courseid'            => $record->courseid,
                'course'              => $record->course,
                'categoryid'          => $record->categoryid,
                'category'            => $record->category,
                'cover'               => $cover,
                'url'                 => $url,
                'modality'            => ucfirst($record->$modality_shortname ?? ""),
                'level'               => ucfirst($record->$level_shortname ?? ""),
                'workload'            => $record->$workload_shortname ?? 0,
                'is_favourite'        => $is_favourite,
                'is_hidden'           => $is_hidden,
                'is_enrolled'         => !is_null($progress),
                'teachers'            => $teachers,
                'completed'           => !!$record->timecompleted,
                'progress_percentage' => round($progress ?? 0),
                'total_sections'      => $total_sections,
                'total_new_updates'   => $record->total_new_updates ?? 0,
                'gamification'        => $gamification
            ];
        }

        $recordset->close();

        //true if number of courses bigger than courses fetched
        $show_all = $total > COUNT($courses);

        return [
            'page'        => $page,
            'per_page'    => $per_page,
            'total'       => $total,
            'total_pages' => $total_pages,
            'show_all'    => $show_all,
            'courses'     => $courses,
            'modalities'  => $modalities,
            'emptyurl'    => $emptyurl
        ];
    }

    /**
     * Build the query object based on the provided parameters.
     *
     * @param array $params The query parameters.
     * @return stdClass The query object.
     */
    public static function build_query($params): stdClass
    {
        extract($params);

        $query = new stdClass();

        $query->params = [];

        $query->fields = "DISTINCT mc.id ccourseid,
            mc.format format,
            mc.fullname course,
            mc.startdate startdate,
            mcc.id categoryid,
            mcc.name category,
            sections.num_sections total_sections,
            mlcfc.*,
            0 total_new_updates,
            mcc2.timestarted timestarted,
	        mcc2.timecompleted timecompleted,
            mc.cacherev,
            mc.enablecompletion,
            mc.timecreated,
            daily_views.views,
            mul.timeaccess";

        $query->from = "{course} mc
            JOIN {user} mu ON mu.id = :userid 
            JOIN {course_categories} mcc ON mcc.id = mc.category
            JOIN {enrol} me ON me.courseid = mc.id 
            JOIN (
                SELECT MAX(section) num_sections, mcs.course courseid 
                FROM {course_sections} mcs 
                GROUP BY mcs.course
            ) sections ON sections.courseid = mc.id
            LEFT JOIN {local_custom_fields_course} mlcfc ON mlcfc.courseid = mc.id
            LEFT JOIN (
                SELECT mue.userid, courseid
                FROM {user_enrolments} mue 
                    JOIN {enrol} me2 ON (me2.id = mue.enrolid)
                    JOIN {role_assignments} mra ON mra.userid = mue.userid AND mra.roleid = :student_roleid
    	            JOIN {context} mc2 ON mc2.id = mra.contextid AND mc2.instanceid = me2.courseid
                WHERE mue.status = 0
                    AND me2.status = 0
                    AND mc2.contextlevel = 50
                    AND UNIX_TIMESTAMP() BETWEEN mue.timestart AND IF(NOT mue.timeend, UNIX_TIMESTAMP(), mue.timeend)
            ) enrols ON enrols.courseid = mc.id AND enrols.userid = mu.id
            LEFT JOIN (
                SELECT SUM(view_counter) views, courseid
                FROM {courseviews_daily_views} daily_views
                WHERE daily_views.viewing_date > UNIX_TIMESTAMP(DATE(NOW() - INTERVAL 30 DAY))
                GROUP BY courseid
            ) daily_views ON daily_views.courseid = mc.id
            LEFT JOIN {course_completions} mcc2 ON mcc2.course = mc.id AND mcc2.userid = mu.id
            LEFT JOIN {user_lastaccess} mul ON mul.courseid = mc.id AND mul.userid = mu.id
            LEFT JOIN {favourite} mf ON mf.component = 'core_course' AND mf.itemtype = 'courses' AND mf.itemid = mc.id AND mf.userid = mu.id";

        $where_list[] = "mc.visible = 1"; // Course visibility 
        $where_list[] = "((me.enrol = 'self' AND me.status = 0) OR enrols.userid = mu.id)"; // Self enrollment method
        $where_list[] = "UNIX_TIMESTAMP() BETWEEN mc.startdate AND IF(NOT mc.enddate, UNIX_TIMESTAMP(), mc.enddate)"; // Course period

        if (!$hidden) {
            $where_list[] = "NOT EXISTS (
                SELECT 1
                FROM {user_preferences} mup 
                WHERE mup.userid = mu.id
                    AND mup.name = CONCAT(:component, '_hidden_course_', mc.id)
                    AND mup.value = 1
            )"; // Cannot have hidden courses on block
        } else {
            $where_list[] = "EXISTS (
                SELECT 1
                FROM {user_preferences} mup 
                WHERE mup.userid = mu.id
                    AND mup.name = CONCAT(:component, '_hidden_course_', mc.id)
                    AND mup.value = 1
            )"; // Have hidden courses
        }

        if ($favourite) {
            $where_list[] = "mf.userid IS NOT NULL";
        }

        $query->params['userid'] = $user->id;
        $query->params['student_roleid']  = "5";
        $query->params['component']  = self::get_block_name();

        if (!empty($modality) && $modality !== user_custom_fields::ALL) {
            $modality_shortname        = user_custom_fields::MODALITY_SHORTNAME;
            $where_list[]              = "LOWER(mlcfc.$modality_shortname) = :modality";
            $query->params['modality'] = strtolower(trim($modality));
        }

        if (!empty($level) && $level !== user_custom_fields::ALL) {
            $level_shortname        = user_custom_fields::LEVEL_SHORTNAME;
            $where_list[]           = "LOWER(mlcfc.$level_shortname) = :level";
            $query->params['level'] = strtolower(trim($level));
        }

        if (!empty($status) && $status !== enrollment_status::ALL) {

            switch ($status) {
                case enrollment_status::NOT_STARTED:
                    $where_list[] = "mcc2.timestarted = 0";
                    break;

                case enrollment_status::IN_PROGRESS:
                    $where_list[] = "mcc2.timestarted > 0";
                    $where_list[] = "mcc2.timecompleted IS NULL";
                    break;

                case enrollment_status::COMPLETED:
                    $where_list[] = "mcc2.timecompleted IS NOT NULL";
                    break;
            }
        }

        $query->where = implode(' AND ', $where_list);
        $query->groupby = "";

        static::build_search_query($query, $search);

        switch ($sort) {
            case popular_courses_sorters::COURSE_NAME:
                $query->orderby = "mc.fullname ASC";
                break;
            case popular_courses_sorters::COURSE_CREATED:
                $query->orderby = "mc.timecreated DESC";
                break;
            case popular_courses_sorters::MOST_VISITED:
            default:
                $query->orderby = "daily_views.views DESC, mul.timeaccess DESC";
                break;
        }

        return $query;
    }
}
