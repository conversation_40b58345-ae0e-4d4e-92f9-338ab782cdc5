<?php

namespace local_courseblockapi;

use local_courseblockapi\support\recommended_courses_sorters;
use stdClass;
use local_courseblockapi\support\user_custom_fields;
use local_courseblockapi\traits\course_trait;
use local_courseblockapi\traits\query_trait;
use local_courseblockapi\traits\search_trait;
use local_courseblockapi\traits\tag_trait;
use local_gamification\repositories\reward_repository;
use moodle_url;

require_once($CFG->dirroot . '/local/courseblockapi/locallib.php');

class recommended_courses
{
    use query_trait, course_trait, tag_trait, search_trait;

    /**
     * Block component
     * @var string
     */
    const block_setting = 'block_recommendedcourses';

    /**
     * Get the courses for a user based on the provided parameters.
     *
     * @param object $user The user object.
     * @param int $page The page number.
     * @param int $per_page The number of courses per page.
     * @param string $search The search string.
     * @param string $modality The modality filter.
     * @param string $level The level filter.
     * @param bool $favourite Filter favourite course.
     * @param bool $hidden Filter hidden course.
     * @param bool $sort Course sort.
     * @return array The array of courses.
     */
    public static function get(object $user, int $page = 1, int $per_page = 4, string $search = '', string $modality = 'all', string $level = 'all', bool $favourite = false, bool $hidden = false, string $sort = recommended_courses_sorters::COURSE_CREATED): array
    {
        global $DB, $OUTPUT;

        $courses = [];

        $modality_shortname = user_custom_fields::MODALITY_SHORTNAME;
        $level_shortname    = user_custom_fields::LEVEL_SHORTNAME;
        $workload_shortname = user_custom_fields::WORKLOAD_SHORTNAME;

        $params = compact('user', 'search',  'modality', 'level', 'favourite', 'hidden', 'sort');

        $query = self::build_query($params);
        $sql = self::get_query_sql($query);

        // Calculates the starting record for the current page
        $start_from = ($page - 1) * $per_page;

        //Get the total number of rows
        $total = self::get_total_rows($sql, $query->params);

        // imageurl when none course was found
        $emptyurl = !$total ? $OUTPUT->image_url('courses', 'block_myoverview')->out() : '';

        // Calculates the total number of pages
        $total_pages = ceil($total / $per_page);

        $modalities = self::get_field_list($params, $modality_shortname, $modality);

        $recordset = $DB->get_recordset_sql($sql, $query->params, $start_from, $per_page);

        foreach ($recordset as $record) {

            $record->id = $record->ccourseid;
            $record->courseid = $record->ccourseid;

            $cover          = self::get_course_image($record);
            $teachers       = self::get_course_teachers($record->courseid, true);
            $is_favourite   = local_courseblockapi_is_course_favourite($record->courseid);
            $is_hidden      = self::course_is_hidden(self::get_block_name(), $record->courseid, $user->id);
            $url            = (new moodle_url('/course/view.php', ['id' => $record->courseid]))->out(false);
            $total_sections = self::get_course_total_sections($record->courseid, $record->total_sections);
            $gamification = self::get_course_gamification($record->courseid);

            $courses[] = [
                'courseid'          => $record->courseid,
                'course'            => $record->course,
                'categoryid'        => $record->categoryid,
                'category'          => $record->category,
                'cover'             => $cover,
                'url'               => $url,
                'modality'          => ucfirst($record->$modality_shortname ?? ""),
                'level'             => ucfirst($record->$level_shortname ?? ""),
                'workload'          => $record->$workload_shortname ?? 0,
                'is_favourite'      => $is_favourite,
                'is_hidden'         => $is_hidden,
                'is_enrolled'       => false,
                'teachers'          => $teachers,
                'total_sections'    => $total_sections,
                'total_new_updates' => $record->total_new_updates ?? 0,
                'gamification'      => $gamification
            ];
        }

        $recordset->close();

        //true if number of courses bigger than courses fetched
        $show_all = $total > COUNT($courses);

        return [
            'page'        => $page,
            'per_page'    => $per_page,
            'total'       => $total,
            'total_pages' => $total_pages,
            'show_all'    => $show_all,
            'courses'     => $courses,
            'modalities'  => $modalities,
            'emptyurl'    => $emptyurl
        ];
    }

    /**
     * Build the query object based on the provided parameters.
     *
     * @param array $params The query parameters.
     * @return stdClass The query object.
     */
    public static function build_query($params): stdClass
    {
        extract($params);

        $query = new stdClass();

        $query->params = [];

        $query->fields = "DISTINCT mc.id ccourseid, 
            mc.format format,
            mc.fullname course,
            mc.startdate startdate,
            mcc.id categoryid,
            mcc.name category,
            sections.num_sections total_sections,
            mlcfc.*,
            0 total_new_updates,
            mc.cacherev,
            mc.enablecompletion,
            mc.timecreated";

        $query->from = "{course} mc
			JOIN {course_categories} mcc ON (mcc.id = mc.category)
			JOIN {tool_interestareas} i ON (i.categoryid = mcc.id)
			JOIN {tool_interestareas_courses} ic ON (ic.courseid = mc.id)
			JOIN {tool_interestareas_user_rel} iu ON (iu.interestid = i.id)
			JOIN {enrol} me ON (me.courseid = mc.id)            
			JOIN (
                SELECT MAX(section) num_sections, mcs.course courseid 
                FROM {course_sections} mcs 
                GROUP BY mcs.course
            ) sections ON sections.courseid = mc.id
            LEFT JOIN {local_custom_fields_course} mlcfc ON mlcfc.courseid = mc.id
            LEFT JOIN {favourite} mf ON mf.component = 'core_course' AND mf.itemtype = 'courses' AND mf.itemid = mc.id AND mf.userid = iu.userid
		";

        $where_list[] = "iu.userid = :userid";
        $where_list[] = "i.active = 1";
        $where_list[] = "ic.active = 1";
        $where_list[] = "mc.visible = 1"; // Course visibility 
        $where_list[] = "mcc.visible = 1"; // Category visibility 
        $where_list[] = "me.enrol = 'self'"; // Self enrollment method
        $where_list[] = "me.status = 0"; // Enrollment status 
        $where_list[] = "mc.id NOT IN(
			SELECT se.courseid
			FROM {enrol} se 
			JOIN {user_enrolments} ue ON (ue.enrolid = se.id)
			JOIN {role_assignments} ra ON (ra.userid = ue.userid AND ra.roleid = :student_roleid)
			JOIN {context} ctx ON (ctx.id = ra.contextid AND ctx.instanceid = se.courseid)
			WHERE ue.userid = iu.userid
			AND se.courseid = mc.id
		)";

        if (!$hidden) {
            $where_list[] = "NOT EXISTS (
                SELECT 1
                FROM {user_preferences} mup 
                WHERE mup.userid = iu.userid
                    AND mup.name = CONCAT(:component, '_hidden_course_', mc.id)
                    AND mup.value = 1
            )"; // Cannot have hidden courses
        } else {
            $where_list[] = "EXISTS (
                SELECT 1
                FROM {user_preferences} mup 
                WHERE mup.userid = iu.userid
                    AND mup.name = CONCAT(:component, '_hidden_course_', mc.id)
                    AND mup.value = 1
            )"; // Have hidden courses
        }

        if ($favourite) {
            $where_list[] = "mf.userid IS NOT NULL";
        }

        $query->params['userid'] = $user->id;
        $query->params['student_roleid']  = "5";
        $query->params['component']  = self::get_block_name();

        if (!empty($modality) && $modality !== user_custom_fields::ALL) {
            $modality_shortname        = user_custom_fields::MODALITY_SHORTNAME;
            $where_list[]              = "LOWER(mlcfc.$modality_shortname) = :modality";
            $query->params['modality'] = strtolower(trim($modality));
        }

        if (!empty($level) && $level !== user_custom_fields::ALL) {
            $level_shortname        = user_custom_fields::LEVEL_SHORTNAME;
            $where_list[]           = "LOWER(mlcfc.$level_shortname) = :level";
            $query->params['level'] = strtolower(trim($level));
        }

        $query->where = implode(' AND ', $where_list);
        $query->groupby = "";

        static::build_search_query($query, $search);

        switch ($sort) {
            case recommended_courses_sorters::COURSE_NAME:
                $query->orderby = "mc.fullname ASC";
                break;
            case recommended_courses_sorters::COURSE_CREATED:
            default:
                $query->orderby = "mc.timecreated DESC";
                break;
        }

        return $query;
    }
}
