<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_courseblockapi\output;

use plugin_renderer_base;
use stdClass;

defined('MOODLE_INTERNAL') || die;

/**
 * Recently accessed courses block renderer
 *
 * @package    local_courseblockapi
 * @copyright  2023 REVVOV <www.somosrevvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class renderer extends plugin_renderer_base
{

    const unenrolled_courses = [

        'block_freecourses',
        'block_recommendedcourses'
    ];

    const block_sorters = [

        'block_mycourses' => [
            'path' => '/local/courseblockapi/classes/support/my_courses_sorters.php',
            'class' => 'local_courseblockapi\support\my_courses_sorters'
        ],
        'block_freecourses' => [
            'path' => '/local/courseblockapi/classes/support/free_courses_sorters.php',
            'class' => 'local_courseblockapi\support\free_courses_sorters'
        ],
        'block_popularcourses' => [
            'path' => '/local/courseblockapi/classes/support/popular_courses_sorters.php',
            'class' => 'local_courseblockapi\support\popular_courses_sorters'
        ],
        'block_recommendedcourses' => [
            'path' => 'local/courseblockapi/classes/support/recommended_courses_sorters.php',
            'class' => 'local_courseblockapi\support\recommended_courses_sorters'
        ]
    ];

    const statues = [

        'all' => [
            'status_name' => 'all'
        ],
        'in_progress' => [
            'status_name' => 'in_progress'
        ],
        'not_started' => [
            'status_name' => 'not_started'
        ],
        'completed' => [
            'status_name' => 'completed'
        ],
        'favourite' => [
            'status_name' => 'favourite'
        ],
        'hiddencourses' => [
            'status_name' => 'hiddencourses'
        ],
    ];

    public function render_page($params)
    {
        global $PAGE, $OUTPUT;

        $isMobile = isset($params['mobile']) && $params['mobile'];
        unset($params['mobile']);

        $content = new \stdClass();
        $content->text = "";
        $content->footer = "";
        $content->otherdata = [];
        $content->javascripts = "";

        $perpage = (int) get_config($params['component'], 'per_page');
        $params['per_page'] = $perpage >= 8 ? $perpage : 8;
        $params['search'] = $this->get_sanitized_search($params);

        $header_params = $this->get_page_header_params($params);
        $pagetitle = get_string('pluginname', $params['component']);

        if ($isMobile) {
            $content->text .= '<div class="padding-content">';
            $content->text .= '<h2>' . $pagetitle . '</h2>';
            $content->text .= $OUTPUT->render_from_template('local_courseblockapi/mobile/page', $header_params);
            $content->text .= '</div>';
            $content->otherdata = [
                "component" => $params['component'],
                "params" => json_encode($params)
            ];
            $content->javascripts = $this->get_mobile_javascripts();
        } else {

            $pluginname = str_replace('block_', '', $params['component']);
            // Start setting up the page
            $PAGE->set_url("/blocks/{$pluginname}/index.php", $params);
            $PAGE->set_pagelayout('standard');

            $PAGE->set_pagetype($params['component'] . "-index");

            $PAGE->set_title($pagetitle);
            $PAGE->set_heading($pagetitle);
            $PAGE->navbar->add($pagetitle);

            echo $OUTPUT->header();
            echo \html_writer::tag('h2', $pagetitle, ['class' => 'mb-1']);
            echo $OUTPUT->render_from_template('local_courseblockapi/page', $header_params);

            $PAGE->requires->js_call_amd(

                'local_courseblockapi/page',
                'init',
                [
                    $params['component'],
                    $params
                ]
            );

            echo $OUTPUT->footer();
        }

        if ($isMobile) return $content;
    }

    /**
     * Obtém e sanitiza a string de pesquisa dos parâmetros fornecidos.
     *
     * @param array $params Parâmetros que podem conter a chave 'search'.
     * @return string String de pesquisa sanitizada.
     */
    private function get_sanitized_search(array $params): string
    {
        $search = $params['search'] ?? '';

        return htmlspecialchars(trim($search), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Function to prepare header parameters for page header (useful on server side)
     */
    private function get_page_header_params($params)
    {

        if (!isset($params['component']) && !$params['component']) {
            return false;
        }

        $params['status'] = $this->get_status_object($params);
        $params['sort'] = $this->get_block_sorts($params['component'], $params['sort']);

        return $params;
    }

    private function get_status_object($params)
    {

        $status = isset($params['status']) ? $params['status'] : 'all';

        $statues = self::statues;

        if ($this->is_unenrolled_block($params['component'])) {
            unset(
                $statues['in_progress'],
                $statues['not_started'],
                $statues['completed']
            );
        }
        if (isset($statues[$status])) {
            $statues[$status]['selected'] = 1;
        } else {
            $statues['all']['selected'] = 1;
        }

        foreach ($statues as $key => $value) {
            $statues[$key]['name'] = get_string($key, 'local_courseblockapi');
        }

        return ['statues' => array_values($statues)];
    }


    private function get_block_sorts($component, $sort_selected = 'course_created')
    {
        global $CFG;

        $block_sorters = self::block_sorters;

        if (!class_exists($block_sorters[$component]['class'])) {

            require_once($CFG->dirroot . $block_sorters[$component]['path']);
        }

        $sorts = [];

        foreach ($block_sorters[$component]['class']::getValues() as $sort_name) {

            $sorts['sorts'][] = [
                'selected' => $sort_selected == $sort_name ? 1 : 0,
                'name' => get_string($sort_name, 'local_courseblockapi'),
                'shortname' => $sort_name
            ];
        }

        return $sorts;
    }


    private function is_unenrolled_block($component)
    {

        return in_array($component, self::unenrolled_courses);
    }


    public function get_mobile_javascripts()
    {
        global $CFG;
        $jsNameFiles = [
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/loader.js', 'title' => 'mobile-loader'],
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/api.js', 'title' => 'mobile-api'],
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/card.js', 'title' => 'mobile-card'],
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/favourite.js', 'title' => 'mobile-favourite'],
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/visibility.js', 'title' => 'mobile-visibility'],
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/page.js', 'title' => 'mobile-page', 'call' => 'init'],
        ];
        $jsContent = "";
        $runFinalCalls = "";
        foreach ($jsNameFiles as $jsFile) {
            if (file_exists($jsFile['url'])) {
                $jsContent .= file_get_contents($jsFile['url']) . "\n";
                if (isset($jsFile['call'])) {
                    $jsFileName = explode('/', $jsFile['url']);
                    $jsFileName = $jsFileName[count($jsFileName) - 1];
                    $jsFileName = explode('.', $jsFileName);
                    $jsFileName = $jsFileName[0] . 'NS';
                    $callInitFunction = $jsFile['call'];
                    $runFinalCalls .= <<<JS
                    {$jsFileName}.{$callInitFunction}();
                    JS;
                }
            } else {
                $titleFileErro = $jsFile['title'];
                $titleBlockErro = $this->name();
                $jsContent .= <<<JS
                (async function (t) {
                    const alert = await t.AlertController.create({
                    header: '{$titleFileErro}',
                    subHeader: 'For: {$titleBlockErro}',
                    message: 'Módulo do bloco não foi carregado!',
                    buttons: ['OK'],
                    });
                    await alert.present();
                })(this);
                JS;
            }
        }
        $jsContent .= $runFinalCalls;
        $jsContent = str_replace(["\r", "\n", "\t"], '', $jsContent);
        return $jsContent;
    }
}
