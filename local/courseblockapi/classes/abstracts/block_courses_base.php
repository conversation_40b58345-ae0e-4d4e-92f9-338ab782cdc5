<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_courseblockapi\abstracts;

use moodle_url;

require_once($CFG->dirroot . '/local/courseblockapi/lib.php');

if (!class_exists('block_base')) {
    // Faz o require do arquivo que contém a classe
    require_once($CFG->dirroot . '/blocks/moodleblock.class.php');
}

/**
 * Block definition class for the block_pluginname plugin.
 *
 * @package   block_courses_base
 * @copyright 2023, REVVO <www.somosrevvo.com.br>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

abstract class block_courses_base extends \block_base
{
    /**
     * Initialises the block.
     *
     * @return void
     */
    public function init()
    {
        $this->component = "block_{$this->name()}";
        $this->name = $this->name();
        $this->class = $this->get_block_class();
        $this->title = $this->get_block_title();
    }
    /**
     * Gets the block contents.
     *
     * @return string The block HTML.
     */
    public function get_content($mobile = false) {
        global $OUTPUT, $USER, $PAGE, $CFG;

        if ($this->content !== null) {
            return $this->content;
        }

        $this->content = new \stdClass();

        $params = $this->class::get($USER);

        $params['title'] = $this->title;
        $params['name'] = $this->name;
        $params['index_url'] = (new moodle_url("/blocks/{$this->name}/index.php"))->__toString();
        if($mobile) $params['index_url'] = 'mobile_listpage';
        if($mobile) $params['component'] = $this->component;

        $blockRenderTemplate  = 'local_courseblockapi/block';
        if($mobile) $blockRenderTemplate  = 'local_courseblockapi/mobile/block';
        $this->content->text = $OUTPUT->render_from_template($blockRenderTemplate, $params);
        $this->content->footer = "";

        if (!$params['total']) {
            return $this->content;
        }

        $PAGE->requires->js_call_amd(
            'local_courseblockapi/favourite',
            'init',
            [$this->component]
        );

        $PAGE->requires->js_call_amd(
            'local_courseblockapi/visibility',
            'block_listener',
            [$this->component]
        );

        $PAGE->requires->js_call_amd(
            'local_courseblockapi/block',
            'init',
            [$this->component]
        );

        if($mobile) {
            $this->content->otherdata = [
                "component" => $this->component,
                "params" => json_encode($params)
            ];
            $this->content->javascripts = $this->get_mobile_javascripts();
        }
        return $this->content;
    }

    /**
     * Defines in which pages this block can be added.
     *
     * @return array of the pages where the block can be added.
     */
    public function applicable_formats()
    {
        return [
            'site-index' => true,
            'my' => true,
        ];
    }

    public function has_config()
    {
        return true;
    }

    public function instance_allow_multiple()
    {
        return false;
    }

    public function hide_header()
    {
        return true;
    }

    private function get_block_class()
    {
        global $CFG;

        $component = isset($this->component) ? $this->component : "block_{$this->name()}";
        $block_class = local_courseblockapi_get_block_class($component);

        if (!class_exists($block_class['class'])) {

            require_once($CFG->dirroot . $block_class['path']);
        }

        return $block_class['class'];
    }

    protected function get_block_title()
    {
        if (!$title = get_config($this->component, 'title')) {
            return get_string('pluginname', $this->component);
        }

        return $title;
    }

    public function get_mobile_javascripts() {
        global $CFG;
        $jsNameFiles = [
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/loader.js', 'title' => 'mobile-loader'],
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/api.js', 'title' => 'mobile-api'],
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/card.js', 'title' => 'mobile-card'],
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/visibility.js', 'title' => 'mobile-visibility', 'call' => 'block_listener'],
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/favourite.js', 'title' => 'mobile-favourite', 'call' => 'init'],
            ['url' => $CFG->dirroot . '/local/courseblockapi/amd/src/mobile/block.js', 'title' => 'mobile-block', 'call' => 'init']
        ];
        $jsContent = "";
        $runFinalCalls = "";
        foreach ($jsNameFiles as $jsFile) {
            if (file_exists($jsFile['url'])) {
                $jsContent .= file_get_contents($jsFile['url']) . "\n";
                if(isset($jsFile['call'])){
                    $jsFileName = explode('/', $jsFile['url']);
                    $jsFileName = $jsFileName[count($jsFileName)-1];
                    $jsFileName = explode('.', $jsFileName);
                    $jsFileName = $jsFileName[0].'NS';
                    $callInitFunction = $jsFile['call'];
                    $runFinalCalls .= <<<JS
                    {$jsFileName}.{$callInitFunction}();
                    JS;
                }
            } else {
                $titleFileErro = $jsFile['title'];
                $titleBlockErro = $this->name();
                $jsContent .= <<<JS
                (async function (t) {
                    const alert = await t.AlertController.create({
                    header: '{$titleFileErro}',
                    subHeader: 'For: {$titleBlockErro}',
                    message: 'Módulo do bloco não foi carregado!',
                    buttons: ['OK'],
                    });
                    await alert.present();
                })(this);
                JS;
            }
        }
        $jsContent .= $runFinalCalls;
        $jsContent = str_replace(["\r", "\n", "\t"], '', $jsContent);
        return $jsContent;
    }
}
