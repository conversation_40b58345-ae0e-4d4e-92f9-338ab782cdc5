<?php

/**
 * External service definitions for the accessreview block.
 *
 * @package local_courseblockapi
 */

defined('MOODLE_INTERNAL') || die();

$functions = [
    'local_courseblockapi_get_my_courses' => [
        'classname'     => 'local_courseblockapi\external\get_my_courses',
        'methodname'    => 'api',
        'description'   => 'Returns the list of courses the user is enrolled in.',
        'type'          => 'read',
        'ajax'          => true,
        'services'      => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ],

    'local_courseblockapi_get_free_courses' => [
        'classname'     => 'local_courseblockapi\external\get_free_courses',
        'methodname'    => 'api',
        'description'   => 'Returns the list of courses that have the self-enrollment method active.',
        'type'          => 'read',
        'ajax'          => true,
        'services'      => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ],

    'local_courseblockapi_get_recommended_courses' => [
        'classname'     => 'local_courseblockapi\external\get_recommended_courses',
        'methodname'    => 'api',
        'description'   => 'Returns the list of recommended courses for the user.',
        'type'          => 'read',
        'ajax'          => true,
        'services'      => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ],

    'local_courseblockapi_get_popular_courses' => [
        'classname'     => 'local_courseblockapi\external\get_popular_courses',
        'methodname'    => 'api',
        'description'   => 'Returns the list of popular courses.',
        'type'          => 'read',
        'ajax'          => true,
        'services'      => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ],

    'local_courseblockapi_toggle_course_visibility' => [
        'classname'     => 'local_courseblockapi\external\toggle_course_visibility',
        'methodname'    => 'api',
        'description'   => 'Toggle course visibility.',
        'type'          => 'write',
        'ajax'          => true,
        'services'      => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ],

    'local_courseblockapi_set_favourite_courses' => [
        'classname'     => 'local_courseblockapi\external\courses',
        'methodname'    => 'set_favourite_courses',
        'description'   => 'Set course as favourite in the block scope or system scope if component is set null',
        'type'          => 'write',
        'ajax'          => true,
        'services'      => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ],

    'local_courseblockapi_is_course_favourite' => [
        'classname'     => 'local_courseblockapi\external\courses',
        'methodname'    => 'is_course_favourite',
        'description'   => 'Check if course is favourite for user in the block scope or system scope if component is set null',
        'type'          => 'read',
        'ajax'          => true,
        'services'      => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ],

    'local_courseblockapi_get_templates' => [
        'classname'     => 'local_courseblockapi\external\courses',
        'methodname'    => 'get_courses_templates',
        'description'   => 'Get templates of courseblockapi to render externally and dynamic (AJAX)',
        'type'          => 'write',
        'ajax'          => true,
        'services'      => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ],

    'local_courseblockapi_get_string_lang' => [
        'classname'     => 'local_courseblockapi\external\courses',
        'methodname'    => 'get_string_lang',
        'description'   => 'Returns string of langs.',
        'type'          => 'write',
        'ajax'          => true,
        'services'      => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ],
    // my courses
    // free courses
    // recommended courses
    // popular courses
];

/* $services = array(
    'BlockCoursesApi Block webservices' => array(
            'functions' => array (
                'local_courseblockapi_get_my_courses',
                'local_courseblockapi_get_free_courses',
                'local_courseblockapi_get_recommended_courses',
                'local_courseblockapi_get_popular_courses',
                'local_courseblockapi_toggle_course_visibility',
                'local_courseblockapi_set_favourite_courses',
                'local_courseblockapi_is_course_favourite'
            ),
            'restrictedusers' => 0,            
            'enabled' => 0,
            'shortname' => 'block_blockcoursesapi_ws'
    )
);
 */