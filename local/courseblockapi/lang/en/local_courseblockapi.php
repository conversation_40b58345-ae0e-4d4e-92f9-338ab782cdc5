<?php
// This file is part of Moodle - https://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.

/**
 * Plugin strings are defined here.
 *
 * @package     local_courseblockapi
 * @category    string
 * @copyright   2023 Revvo <<EMAIL>>
 * @license     https://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Course Blocks API';

$string['settings:general_settings_heading'] = 'Blocks that use the API';
$string['settings:block_mycourses'] = 'Block My courses';
$string['settings:block_freecourses'] = 'Block Free courses';
$string['settings:block_recommendedcourses'] = 'Block Recommended courses';
$string['settings:block_popularcourses'] = 'Block Popular courses';
$string['settings:component_name_desc'] = 'Component name';

$string['exception:invalid_status'] = 'Invalid status';
$string['exception:accepted_statuses'] = 'Accepted statuses: {$a}';
$string['exception:accepted_sorters'] = 'Accepted sorts: {$a}';
$string['exception:component_not_exist'] = 'Component {$a} does not exist';

// Strings for configurations and installation
$string['customfieldtypenotfound'] = 'The selected customfield type is not installed in you system';
$string['customfieldalreadyset'] = 'The customfield already exist';
$string['customfieldcreated'] = 'The customfield was created!';
$string['per_page'] = 'Cards number per page';
$string['per_page_help'] = 'It specifies the number of cards shown in block page.';

// String default for blocks

$string['all'] = 'All';
$string['and'] = 'and';
$string['showall'] = 'Show all';
$string['showall_arialabel'] = 'Show all';
$string['with'] = 'with';
$string['visibilitysuccess'] = 'The course {$a->coursename} was successfuly hidden in the {$a->component} context.';
$string['visibilityerror'] = 'It was not possible to hide the course {$a->coursename} in the {$a->component} context';
$string['configtitle'] = 'Block title';

//Strings for page
$string['modalities'] = 'Modalities';
$string['course_name'] = 'Course name';
$string['course_created'] = 'Course creation date';
$string['last_access'] = 'Last accessed';
$string['enrollment_start'] = 'Enrolment start date';
$string['most_visited'] = 'Most watched';
$string['not_started'] = 'Not Started';
$string['in_progress'] = 'In progress';
$string['completed'] = 'Completed';
$string['favourite'] = 'Favourites';
$string['hiddencourses'] = 'Removed from view';
