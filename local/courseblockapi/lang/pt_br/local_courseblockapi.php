<?php
// This file is part of Moodle - https://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <https://www.gnu.org/licenses/>.

/**
 * Plugin strings are defined here.
 *
 * @package     local_courseblockapi
 * @category    string
 * @copyright   2023 Revvo <<EMAIL>>
 * @license     https://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'API de blocos de cursos';


$string['settings:general_settings_heading'] = 'Blocos que utilizam a API';
$string['settings:block_mycourses'] = 'Blocos Meus cursos';
$string['settings:block_freecourses'] = 'Blocos Cursos livres';
$string['settings:block_recommendedcourses'] = 'Blocos Recomendados para você';
$string['settings:block_popularcourses'] = 'Blocos Em alta';
$string['settings:component_name_desc'] = 'Nome do componente';

$string['exception:invalid_status'] = 'Status inválido';
$string['exception:accepted_statuses'] = 'Status aceitos: {$a}';
$string['exception:accepted_sorters'] = 'Ordenações aceitas: {$a}';
$string['exception:component_not_exist'] = 'Componente {$a} não existe';

// Strings for configurations and installation
$string['customfieldtypenotfound'] = 'O tipo de custom field selecionado não existe nesta plataforma';
$string['customfieldalreadyset'] = 'O campo customizável já estava configurado';
$string['customfieldcreated'] = 'O campo customizável foi criado!';
$string['per_page'] = 'Número de cards na página';
$string['per_page_help'] = 'Especifica o número de cards que devem ser visualizados na página de cursos do plugin.';

// String default for blocks
$string['all'] = 'Todos';
$string['and'] = 'e';
$string['showall'] = 'Ver todos';
$string['showall_arialabel'] = 'Ver todos os';
$string['with'] = 'com';
$string['visibilitysuccess'] = 'O curso {$a->coursename} foi escondido no contexto do bloco {$a->component} com sucesso.';
$string['visibilityerror'] = 'Não foi possível esconder o curso {$a->coursename} no contexto do bloco {$a->component}';
$string['configtitle'] = 'Título do bloco';

//Strings for page
$string['modalities'] = 'Modalidades';
$string['course_name'] = 'Nome do curso';
$string['course_created'] = 'Data de criação do curso';
$string['last_access'] = 'Últimos acessados';
$string['enrollment_start'] = 'Data de início da inscrição';
$string['most_visited'] = 'Mais assistidos';
$string['not_started'] = 'Não iniciados';
$string['in_progress'] = 'Em andamento';
$string['completed'] = 'Encerrados';
$string['favourite'] = 'Favoritos';
$string['hiddencourses'] = 'Removidos da visualização';
