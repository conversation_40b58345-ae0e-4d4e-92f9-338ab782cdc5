.local_courseblockapi [class*="danger"]{
	filter: brightness(150%); /*alterar no tema depois*/
}
.local_courseblockapi {
  --img-height: 226px;
  --card-height: 240px;
  --card-height-hover:  calc(1.6 * var(--img-height));
}

.local_courseblockapi .card-courses {
  height: inherit;
}
.local_courseblockapi .card-container {
  position: relative;
  height: var(--card-height);
}
.local_courseblockapi .icon{
  margin-right: 0px;
  width: auto;
  height: auto;
}
.local_courseblockapi .card-courses .card {
  flex-basis: 600px;

  transition: all 0.3s ease;
  height: var(--img-height);
  z-index: 1;
}

.local_courseblockapi .card-courses .card:hover {
  box-shadow: 0px 3px 6px #00000029;
  transform: scale(1.1);
  height: var(--card-height-hover);
  z-index: 2;
}

.local_courseblockapi .card-courses .card .card-title {
  font-size: 15px;
  font-family: 'Helvetica Neue', Helvetica, Arial, Helvetica, sans-serif;
  font-weight: bold;
}

.local_courseblockapi .card-courses .card .card-link {
  z-index: 1;
}
.local_courseblockapi .card-courses .card .card-img-top,
.local_courseblockapi .card-courses .card .card-img-overlay {
  background: transparent linear-gradient(180deg, #00000000 0%, #000000 100%) 0% 0% no-repeat padding-box;
  border-bottom-left-radius: calc(0.5rem - 1px);
  border-bottom-right-radius: calc(0.5rem - 1px);
  height: var(--img-height);
  color: #fff;
}

.local_courseblockapi .card-courses .card:hover .card-img-top,
.local_courseblockapi .card-courses .card:hover .card-img-overlay {
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}
.local_courseblockapi .card-courses .card .card-body {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 0;
  padding: 8px;
  width: 100%;
}

.local_courseblockapi .blocktitle {
  font-size: 1.171875rem;
}

.local_courseblockapi .card-courses .card .card-body .card-icons .is_favourite {
  background: linear-gradient(45deg,white 1%,#ff1414);
}
.local_courseblockapi
  .card-courses
  .card
  .card-body
  .card-icons
  .is_favourite
  i:before {
  color: white;
}

.local_courseblockapi .card-action-link.icon-small .icon{
	font-size:12px !important;
}

.local_courseblockapi .card-action-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  transition: opacity .7s;
}
.local_courseblockapi .card-action-link:hover {
  text-decoration: none;
  cursor: pointer;
  opacity: .9;
}

.local_courseblockapi .card-course-category-text {
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 600;
}

.local_courseblockapi .progress {
  height: 6px;
}

.local_courseblockapi .card-progress-value {
  font-size: 12px;
}

.local_courseblockapi .card-course-sections {
  font-size: 14px;
}

.local_courseblockapi .card-course-gamification {
  font-size: 14px;
}

/* Page css*/

.courses-page .courses-page-header #search {
  border-top-left-radius: 0.5rem !important;
  border-bottom-left-radius: 0.5rem !important;
  border-top-right-radius: 0rem !important;
  border-bottom-right-radius: 0rem !important;
}

.courses-page .courses-page-header #button-search {
  border-top-right-radius: 0.5rem !important;
  border-bottom-right-radius: 0.5rem !important;
  border-top-left-radius: 0rem !important;
  border-bottom-left-radius: 0rem !important;
}

.courses-page .courses-page-header .page-header-modality,
.courses-page .courses-page-body .cards,
.courses-page .courses-page-footer .pagination,
.courses-page .courses-page-footer .loader {
  transition: opacity ease 0.5s;
}

/* The animation code */

.courses-page .courses-page-footer .loader {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 6rem;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.courses-page .courses-page-footer .loader:before,
.courses-page .courses-page-footer .loader:after {
  content: "";
  position: absolute;
  border-radius: 50%;
  animation: pulsOut 1.8s ease-in-out infinite;
  filter: drop-shadow(0 0 1rem var(--primary));
}
.courses-page .courses-page-footer .loader:before {
  width: 100%;
  padding-bottom: 100%;
  box-shadow: inset 0 0 0 1rem var(--primary);
  animation-name: pulsIn;
}
.courses-page .courses-page-footer .loader:after {
  width: calc(100% - 2rem);
  padding-bottom: calc(100% - 2rem);
  box-shadow: 0 0 0 0 var(--primary);
}

@keyframes pulsIn {
  0% {
    box-shadow: inset 0 0 0 1rem var(--primary);
    opacity: 1;
  }
  50%,
  100% {
    box-shadow: inset 0 0 0 0 var(--primary);
    opacity: 0;
  }
}

@keyframes pulsOut {
  0%,
  50% {
    box-shadow: 0 0 0 0 var(--primary);
    opacity: 0;
  }
  100% {
    box-shadow: 0 0 0 1rem var(--primary);
    opacity: 1;
  }
}

/*Animação do "Ver mais" do titulo dos Blocos de Curso API */
.local_courseblockapi a{
  text-decoration: none !important;
  color: white !important;
}

.local_courseblockapi a h5{
  line-height: 20px;
}

.local_courseblockapi .card-showall h6{
  color: #FFFFFFAA;
  display: block;
  line-height: 20px;
  width: auto;
  overflow: hidden;
  text-wrap: nowrap;
  text-indent: 0px;
  padding-right: 3px;
  opacity: 1;
  transition:cubic-bezier(0.19, 1, 0.22, 1) .6s;
}

.local_courseblockapi .card-showall i.fa-chevron-right::before{
  color: white !important;
  line-height: 20px;
}

.local_courseblockapi .d-inline-flex.align-items-center:hover .card-showall h6{
  opacity: 1;
  padding-right: 3px;
  text-indent: 0px;  
}
/**
* FIM - Animação do "Ver mais" do titulo dos Blocos de Curso API 
*/

@media (max-width: 576px) {
	.local_courseblockapi {
	  /*--img-height: auto !important;*/
	  --card-height: auto !important;
	  --card-height-hover:  auto !important;
	}
	
	.local_courseblockapi .card-container {
		margin-top:1rem;
	}
	
	.local_courseblockapi .card-courses .card{
		height: auto !important;
	}

	.local_courseblockapi .card-courses .card:hover {
	  transform: scale(1) !important;
	}
	
	.local_courseblockapi .card-courses .card .card-body {
	  position: static;
	}
	
	.block.card > .card-body > .card-text.content.mt-3{
	  margin-top:0 !important;
	}
	
	.local_courseblockapi .card-showall{
		align-items:center;
	}
	
	.local_courseblockapi .blocktitle + div h6{
	  display:none;
	}
	
	.local_courseblockapi .blocktitle{
	  font-size: 1.5rem !important;
	  line-height: initial !important;
	}
	
	.local_courseblockapi .card-courses .card .card-img-top, 
	.local_courseblockapi .card-courses .card .card-img-overlay{
	  border-bottom-left-radius: 0 !important;
	  border-bottom-right-radius: 0 !important;
	}
}	

/**
* Adaptações mobile - paginação
*/
@media (max-width: 768px) {
    .courses-page .courses-page-footer .pagination {
        margin: 0 !important;
        padding: 0 !important;
    }
    .courses-page-footer .container {
        padding: 0 !important;
    }
}