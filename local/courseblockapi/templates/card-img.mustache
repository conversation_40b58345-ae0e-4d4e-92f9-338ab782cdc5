<a class="card-link bg-dark" href="{{ url }}">
  {{#cover}}
  <img
    class="card-img-top {{#visible}}visible{{/visible}}"
    src="{{#card}}{{ card }}{{/card}}{{^card}}{{ image }}{{/card}}"
    alt="{{ course }}"
  />
  {{^card}}
  {{#logo}}
  <div
    class="card-img-overlay p-0 d-flex align-items-center justify-content-center {{#visible}}visible{{/visible}}"
  >
    <div class="card-content d-flex justify-content-center p-4">
      <img src="{{ logo }}" alt="{{ course }}" class="card-logo w-75" />
    </div>
  </div>
  {{/logo}}
  {{^logo}}
  <div class="card-img-overlay p-0 d-flex align-items-end {{#visible}}visible{{/visible}}">
    <div class="card-content p-2">
      <h6 class="card-title m-0 text-uppercase">{{ course }}</h6>
      {{#teachers}}
      <p class="card-subtitle m-0 text-uppercase teacher-name">
        {{#str}}with, local_courseblockapi{{/str}} {{ teachers }}
      </p>
      {{/teachers}}
      {{^teachers}}
      <p class="card-subtitle m-0 text-uppercase">&nbsp</p>
      {{/teachers}}
    </div>
  </div>
  {{/logo}}
  {{/card}}
  {{/cover}}
</a>

<script>
const teacherElements = document.querySelectorAll('.teacher-name');
  teacherElements.forEach(function(teacherElement) {
    let teacherName = teacherElement.textContent.trim();

    if (teacherName.length > 60) {
      teacherElement.textContent = teacherName.substring(0, 60) + '...';
    }
  });
</script>