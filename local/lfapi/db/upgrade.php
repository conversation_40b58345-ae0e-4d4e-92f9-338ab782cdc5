<?php

defined('MOODLE_INTERNAL') || die();

function xmldb_local_lfapi_upgrade($oldversion) {
    global $DB, $CFG;

    require_once($CFG->dirroot . '/local/lfapi/db/upgradelib.php');

    // Adding capabilities to roles
    local_lfapi_assign_capabilities_to_roles();

    // Adding client credentials
    if ($oldversion < 2025033002) {
        mtrace('Adding user credentials');
        local_lfapi_add_client_credentials_to_api_users();
        upgrade_plugin_savepoint(true, 2025033002, 'local', 'lfapi');
    }

    return true;
}