<?php

defined('MOODLE_INTERNAL') || die();

use \local_lfapi\models\users\abstract_user;
use \local_lfapi\models\users\admin_user;
use \local_lfapi\models\users\client_user;

function local_lfapi_assign_capabilities_to_roles(){
    global $DB;

    $manager_roles = array_keys($DB->get_records('role', ['archetype' => 'manager']));

    $capabilities = [
        'webservice/soap:use',
        'webservice/rest:use',
        'webservice/restful:use',
        'webservice/api:use',
    ];

    $context = context_system::instance();
    
    foreach ($capabilities as $capability) {
        if(!get_capability_info($capability)){
            continue;
        }
        foreach ($manager_roles as $roleid) {
            assign_capability($capability, CAP_ALLOW, $roleid, $context->id, true);
        }
    }
}

function local_lfapi_add_client_credentials_to_api_users(){
    $admin = admin_user::exists() ? admin_user::get() : admin_user::create();
    local_lfapi_add_client_credentials_to_api_user($admin);

    $admin = client_user::exists() ? client_user::get() : client_user::create();
    local_lfapi_add_client_credentials_to_api_user($admin);

    \local_lfapi\config::set(\local_lfapi\config::CONFIG_API_USERS_CREATED, true);
}

function local_lfapi_add_client_credentials_to_api_user(abstract_user $user){
    try {
        if(!$user->has_client_credentials()){
            $user->generate_client_credentials();
        }
    } catch (\Throwable $th) {
        mtrace($th->getMessage());
    }
}