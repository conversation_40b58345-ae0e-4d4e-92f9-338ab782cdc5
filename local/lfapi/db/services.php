<?php

$functions = [

    // Users API
    'lfapi_create_users'       => [
        'classpath'    => 'local/lfapi/classes/v1/external/user.php',
        'classname'    => '\local_lfapi\v1\external\user',
        'methodname'   => 'create_users',
        'description'  => 'Create users.',
        'type'         => 'write',
        'capabilities' => 'moodle/user:create',
    ],
    'lfapi_delete_users'       => [
        'classpath'    => 'local/lfapi/classes/v1/external/user.php',
        'classname'    => '\local_lfapi\v1\external\user',
        'methodname'   => 'delete_users',
        'description'  => 'Delete users.',
        'type'         => 'write',
        'capabilities' => 'moodle/user:delete',
    ],
    'lfapi_get_users'          => [
        'classpath'    => 'local/lfapi/classes/v1/external/user.php',
        'classname'    => '\local_lfapi\v1\external\user',
        'methodname'   => 'get_users',
        'description'  => 'search for users matching the parameters',
        'type'         => 'read',
        'capabilities' => 'moodle/user:viewdetails, moodle/user:viewhiddendetails, moodle/course:useremail, moodle/user:update',
    ],
    'lfapi_update_users'       => [
        'classpath'    => 'local/lfapi/classes/v1/external/user.php',
        'classname'    => '\local_lfapi\v1\external\user',
        'methodname'   => 'update_users',
        'description'  => 'Update users.',
        'type'         => 'write',
        'capabilities' => 'moodle/user:update',
        'ajax'         => true,
    ],


    // Manual Enrolment API
    'lfapi_enrol_users'        => [
        'classpath'    => 'local/lfapi/classes/v1/external/enrolment.php',
        'classname'    => '\local_lfapi\v1\external\enrolment',
        'methodname'   => 'enrol_users',
        'description'  => 'Manual enrol users',
        'capabilities' => 'enrol/manual:enrol',
        'type'         => 'write',
    ],

    'lfapi_unenrol_users'  => [
        'classpath'    => 'local/lfapi/classes/v1/external/enrolment.php',
        'classname'    => '\local_lfapi\v1\external\enrolment',
        'methodname'   => 'unenrol_users',
        'description'  => 'Manual unenrol users',
        'capabilities' => 'enrol/manual:unenrol',
        'type'         => 'write',
    ],
    'lfapi_get_enrolment_details'  => [
        'classpath'    => 'local/lfapi/classes/v1/external/enrolment.php',
        'classname'    => '\local_lfapi\v1\external\enrolment',
        'methodname'   => 'get_enrolment_details',
        'description'  => "Details about a user's enrolment on a course",
        'capabilities' => 'enrol/manual:enrol',
        'type'         => 'read',
    ],


    // Courses API
    'lfapi_get_courses'    => [
        'classpath'    => 'local/lfapi/classes/v1/external/course.php',
        'classname'    => '\local_lfapi\v1\external\course',
        'methodname'   => 'get_courses',
        'description'  => 'Return course details',
        'type'         => 'read',
        'capabilities' => 'moodle/course:view, moodle/course:update, moodle/course:viewhiddencourses',
        'ajax'         => true,
    ],
    'lfapi_create_courses' => [
        'classpath'    => 'local/lfapi/classes/v1/external/course.php',
        'classname'    => '\local_lfapi\v1\external\course',
        'methodname'   => 'create_courses',
        'description'  => 'Create new courses',
        'type'         => 'write',
        'capabilities' => 'moodle/course:create, moodle/course:visibility',
    ],
    'lfapi_delete_courses' => [
        'classpath'    => 'local/lfapi/classes/v1/external/course.php',
        'classname'    => '\local_lfapi\v1\external\course',
        'methodname'   => 'delete_courses',
        'description'  => 'Deletes all specified courses',
        'type'         => 'write',
        'capabilities' => 'moodle/course:delete',
    ],
    'lfapi_update_courses' => [
        'classpath'    => 'local/lfapi/classes/v1/external/course.php',
        'classname'    => '\local_lfapi\v1\external\course',
        'methodname'   => 'update_courses',
        'description'  => 'Update courses',
        'type'         => 'write',
        'capabilities' => 'moodle/course:update, moodle/course:changecategory, moodle/course:changefullname, '
            . 'moodle/course:changeshortname, moodle/course:changeidnumber, moodle/course:changesummary, moodle/course:visibility',
    ],

    // Categories API
    'lfapi_get_categories' => [
        'local/lfapi/classes/v1/external/course.php',
        'classname'    => '\local_lfapi\v1\external\course',
        'methodname' => 'get_categories',
        'classpath' => 'course/externallib.php',
        'description' => 'Return category details',
        'type' => 'read',
        'capabilities' => 'moodle/category:viewhiddencategories',
    ],
    'lfapi_create_categories' => [
        'local/lfapi/classes/v1/external/course.php',
        'classname'    => '\local_lfapi\v1\external\course',
        'methodname' => 'create_categories',
        'classpath' => 'course/externallib.php',
        'description' => 'Create course categories',
        'type' => 'write',
        'capabilities' => 'moodle/category:manage'
    ],
    'lfapi_update_categories' => [
        'local/lfapi/classes/v1/external/course.php',
        'classname'    => '\local_lfapi\v1\external\course',
        'methodname' => 'update_categories',
        'classpath' => 'course/externallib.php',
        'description' => 'Update categories',
        'type' => 'write',
        'capabilities' => 'moodle/category:manage',
    ],
    'lfapi_delete_categories' => [
        'local/lfapi/classes/v1/external/course.php',
        'classname'    => '\local_lfapi\v1\external\course',
        'methodname' => 'delete_categories',
        'classpath' => 'course/externallib.php',
        'description' => 'Delete course categories',
        'type' => 'write',
        'capabilities' => 'moodle/category:manage'
    ],


    // User Key (Auth)
    'lfapi_request_login_url' => [
        'classname'   => 'local_lfapi\v1\external\userkey',
        'methodname'  => 'request_login_url',
        'classpath'   => 'local/lfapi/classes/v1/external/userkey.php',
        'description' => 'Return one time key based login URL',
        'type'        => 'write',
    ],


    // Credentials
    'local_lfapi_regenerate_credentials' => [
        'classpath'    => 'local/lfapi/external/external_client_credentials.php',
        'classname'    => '\local_lfapi\external\external_client_credentials',
        'methodname'   => 'generate',
        'description'  => 'Generate or regenerate credentials',
        'type'         => 'write',
        'capabilities' => 'local/lfapi:config',
        'ajax'          => true,
        'loginrequired' => true,
    ],
];

$services = [
    'LearningFlix API' => [
        'functions'       => array_keys($functions),
        'shortname'       => \local_lfapi\config::SERVICE_SHORTNAME,
        'restrictedusers' => 0,
        'enabled'         => true,
        'downloadfiles'   => true,
        'uploadfiles'     => true,
    ],
];