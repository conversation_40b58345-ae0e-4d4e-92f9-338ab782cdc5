<?php

defined('MOODLE_INTERNAL') || die();

function xmldb_local_lfapi_install() {
    global $DB, $CFG;

    require_once($CFG->dirroot . '/local/lfapi/db/upgradelib.php');

    // Adding capabilities to roles
    local_lfapi_assign_capabilities_to_roles();

    // Adding users
    $phpunit_test = defined('PHPUNIT_TEST') && PHPUNIT_TEST;
    $phpunit_util = defined('PHPUNIT_UTIL') && PHPUNIT_UTIL;
    if(!$phpunit_test && !$phpunit_util){
        \local_lfapi\task\create_default_api_users_task::create_and_enqueue();
    }

    // Setting default configs
    \local_lfapi\config::set('admin_api_user_token', '');
    \local_lfapi\config::set('client_api_user_token', '');
    \local_lfapi\config::set(\local_lfapi\config::CONFIG_API_USERS_CREATED, false);

    return true;
}

