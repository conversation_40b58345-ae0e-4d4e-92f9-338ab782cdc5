<?php namespace local_lfapi;


defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once($CFG->dirroot . '/webservice/tests/helpers.php');

use \context;
use \ReflectionMethod;
use \advanced_testcase;

use \local_lfapi\config;
use \local_lfapi\models\users\admin_user;
use \local_lfapi\models\users\client_user;
use \local_lfapi\task\create_default_api_users_task;

class setup_test extends advanced_testcase{

    public static function setUpBeforeClass(): void {
        parent::setUpBeforeClass();
    }

    public static function has_role(int $userid, $roleid, ?context $context = null){
        global $DB;

        if(!$context){
            $context = \context_system::instance();
        }

        return $DB->record_exists('role_assignments', [
            'contextid' => $context->id,
            'userid'    => $userid,
            'roleid'    => $roleid,
        ]);
    }

    /**
     * @runInSeparateProcess
     */
    public function test_create_default_api_users_task(){
        global $USER, $CFG, $DB;

        if (!defined('PHPUNIT_ISOLATED_TEST') || !PHPUNIT_ISOLATED_TEST) {
            $this->markTestSkipped('There is something wrong with your PHPUnit setup. This test should be proccess isolated.');
        }

        $this->resetAfterTest(true);
        $this->setAdminUser();

        // Retrieving service
        require_once($CFG->dirroot . '/webservice/lib.php');
        $webservice_manager = new \webservice;
        $service = $webservice_manager->get_external_service_by_shortname(config::SERVICE_SHORTNAME);
        
        // Fetching roles
        $manager_roles = array_keys($DB->get_records('role', ['archetype' => 'manager']));
        $client_roles = array_keys($DB->get_records('role', ['archetype' => 'client']));

        if(empty($client_roles)){
            $client_roles = $manager_roles;
        }

        // Creating adhoc task
        create_default_api_users_task::create_and_enqueue();

        // Executing
        ob_start();
        $this->runAdhocTasks(create_default_api_users_task::class);
        $output = ob_get_contents();
        ob_end_clean();

        // Checking admin user
        $admin_user = admin_user::get();
        
        $reflection_method = new ReflectionMethod(admin_user::class, 'define_user');
        $reflection_method->setAccessible(true);
        $admin_definition = $reflection_method->invoke($admin_user);

        foreach ($admin_definition as $key => $value) {
            $this->assertEquals($value, $admin_user->$key);
        }

        foreach ($manager_roles as $roleid) {
            $this->assertTrue(self::has_role($admin_user->id, $roleid));
        }

        $this->assertNotEmpty($admin_user->get_token());


        // Checking client user
        $client_user = client_user::get();
        
        $reflection_method = new ReflectionMethod(client_user::class, 'define_user');
        $reflection_method->setAccessible(true);
        $admin_definition = $reflection_method->invoke($client_user);

        foreach ($admin_definition as $key => $value) {
            $this->assertEquals($value, $client_user->$key);
        }

        foreach ($client_roles as $roleid) {
            $this->assertTrue(self::has_role($client_user->id, $roleid));
        }

        $this->assertNotEmpty($client_user->get_token());
    }

    public function test_create_credentials_for_default_api_users(){
        $this->resetAfterTest(true);
        $this->setAdminUser();

        $admin = admin_user::exists() ? admin_user::get() : admin_user::create();
        $client = client_user::exists() ? client_user::get() : client_user::create();

        $this->assertNull($admin->get_client_credentials());
        $this->assertNull($client->get_client_credentials());

        $admin_credentials = $admin->generate_client_credentials();
        $client_credentials = $client->generate_client_credentials();

        $this->assertNotEmpty($admin_credentials->get('client_id'));
        $this->assertNotEmpty($admin_credentials->get_secret());
        $this->assertNotEmpty($client_credentials->get('client_id'));
        $this->assertNotEmpty($client_credentials->get_secret());
    }
}