<?php namespace local_lfapi;


defined('MOODLE_INTERNAL') || die();

global $CFG;

require_once(__DIR__ . '/traits/mocked_request_trait.php');

use \advanced_testcase;
use \Laminas\Diactoros\Response\EmptyResponse;
use \local_lfapi\traits\mocked_request_trait;
use \local_lfapi\v3\controllers\user_controller;
use \local_lfapi\v3\exceptions\http_exception;

class user_controller_test extends advanced_testcase {

    use mocked_request_trait;

    /**
     * This property holds all the created users separated in groups.
     *
     * Groups:
     * - group_a: 20 active users with username prefix "usera_"
     * - group_b: 10 suspended users with username prefix "userb_"
     * - group_c: 10 active users with profile_field_menufield "option2"
     *              and profile_field_datetimefield 1325386837
     * - group_d: 10 active users with profile_field_datetimefield 978318037
     *              and profile_field_textfield "test"
     *
     * @var array
     */
    protected $users = [];
    
    protected function setUp(): void{
        $this->resetAfterTest();
        $this->setAdminUser();

        set_debugging(DEBUG_NONE, false);
        $this->create_custom_fields();
        $this->create_test_users();
        set_debugging(DEBUG_DEVELOPER, true);

    }

    protected function create_custom_fields(){
        global $DB;

        $category = (object)[
            'name' => 'Test Custom Profile Fields',
            'visible' => 1,
            'sortorder' => 1,
            'description' => 'Category for custom profile fields added for testing.',
        ];
        $category->id = $DB->insert_record('user_info_category', $category);

        $fields = [
            // Datetime field.
            [
                'shortname'   => 'datetimefield',
                'name'        => 'Datetime Field',
                'datatype'    => 'datetime',
                'description' => 'A custom datetime field for testing.',
                'categoryid'  => $category->id,
                'required'    => 0,
                'locked'      => 0,
                'visible'     => 2, // Visible to users.
                'forceunique' => 0,
                'defaultdata' => '',
            ],
            // Text field.
            [
                'shortname'   => 'textfield',
                'name'        => 'Text Field',
                'datatype'    => 'text',
                'description' => 'A custom text field for testing.',
                'categoryid'  => $category->id,
                'required'    => 0,
                'locked'      => 0,
                'visible'     => 2,
                'forceunique' => 0,
                'defaultdata' => '',
            ],
            // Menu field.
            [
                'shortname'   => 'menufield',
                'name'        => 'Menu Field',
                'datatype'    => 'menu',
                'description' => 'A custom menu field for testing.',
                'categoryid'  => $category->id,
                'required'    => 0,
                'locked'      => 0,
                'visible'     => 2,
                'forceunique' => 0,
                'defaultdata' => 'option1',
                'param1'      => "option1\noption2\noption3",
            ]
        ];

        foreach ($fields as $field) {
            $DB->insert_record('user_info_field',  $field);
        }
    }

    protected function create_test_users(){
        // Group A: 20 active users with username prefix "usera_"
        // and profile_field_textfield "etc"
        $this->users['group_a'] = [];
        for ($i = 1; $i <= 20; $i++) {
            $user = $this->getDataGenerator()->create_user([
                'username'  => 'usera_' . $i,
                'firstname' => 'UserA',
                'lastname'  => 'Test ' . $i,
                'email'     => "usera{$i}@example.com",
                'suspended' => 0,
                'profile_field_textfield' => "etc"
            ]);
            $this->users['group_a'][$user->id] = $user;
        }

        // Group B: 10 suspended users with username prefix "userb_"
        $this->users['group_b'] = [];
        for ($i = 21; $i <= 30; $i++) {
            $user = $this->getDataGenerator()->create_user([
                'username'  => 'userb_' . $i,
                'firstname' => 'UserB',
                'lastname'  => 'Test ' . $i,
                'email'     => "userb{$i}@example.com",
                'suspended' => 1
            ]);
            $this->users['group_b'][$user->id] = $user;
        }

        // Group C: 10 active users with profile_field_menufield "option2"
        // and profile_field_datetimefield 1325386837
        $this->users['group_c'] = [];
        for ($i = 31; $i <= 40; $i++) {
            $user = $this->getDataGenerator()->create_user([
                'username'   => 'userc_' . $i,
                'firstname'  => 'UserC',
                'lastname'   => 'Test ' . $i,
                'email'      => "userc{$i}@example.com",
                'suspended'  => 0,
                'profile_field_menufield' => 'option2',
                'profile_field_datetimefield' => 1325386837,
            ]);
            $this->users['group_c'][$user->id] = $user;
        }

        // Group D: 10 active users with profile_field_datetimefield 978318037
        // and profile_field_textfield "test"
        $this->users['group_d'] = [];
        for ($i = 41; $i <= 50; $i++) {
            $user = $this->getDataGenerator()->create_user([
                'username'   => 'userd_' . $i,
                'firstname'  => 'UserD',
                'lastname'   => 'Test ' . $i,
                'email'      => "userd{$i}@example.com",
                'suspended'  => 0,
                'profile_field_textfield' => 'test',
                'profile_field_datetimefield' => 978318037,
            ]);
            $this->users['group_d'][$user->id] = $user;
        }
    }
    
    /**
     * @group !current
     * @return void
     */
    public function test_list_users_single_page() {
        // Build initial query parameters for filtering:
        //   - suspended equals 0.
        //   - username like user*.
        parse_str("suspended[eq]=0&username[like]=user*", $params);
        // Add pagination limit parameter.
        $params['_limit'] = 50;
        
        // Create the initial request using the mocked request trait.
        $request = $this->mock_request('GET', '/v3/users', $params);

        $controller = new user_controller();
        $response = $controller->list_users($request)->jsonSerialize();

        $this->assertCount(40, $response['_embedded']['users'], "Expected 40 users matching the filter criteria.");
    }

    /**
     * @group !current
     * @return void
     */
    public function test_list_users_single_page_filter_by_custom_fields() {
        // Build initial query parameters for filtering:
        //   - profile_field_menufield equals 'option2'
        //   - username like user*.
        parse_str("profile_field_menufield[eq]=option2&username[like]=user*", $params);
        // Add pagination limit parameter.
        $params['_limit'] = 50;
        
        // Create the initial request using the mocked request trait.
        $request = $this->mock_request('GET', '/v3/users', $params);

        $controller = new user_controller();
        $response = $controller->list_users($request)->jsonSerialize();

        // sleep(30);

        $this->assertCount(10, $response['_embedded']['users'], "Expected 10 users matching the filter criteria.");
    }


    /**
     * @group !current
     */
    public function test_list_categories_paginated_iteration(){
        // Build initial query parameters for filtering:
        //   - username like "usera_*"
        //   - 5 per page
        parse_str("username[like]=usera_*", $params);
        $params['_limit'] = 5;

        $request = $this->mock_request('GET', '/v3/users', $params);

        $controller = new user_controller();
        $all_users = [];

        do {
            $response = $controller->list_users($request)->jsonSerialize();

            // Assert that the response
            $this->assertArrayHasKey('_embedded', $response, 'Response must have _embedded data.');
            $this->assertArrayHasKey('users', $response['_embedded'], 'Embedded data must contain users.');

            foreach ($response['_embedded']['users'] as $user){
                /** @var \local_lfapi\v3\response\resources\entities\user_resource */
                $all_users[$user->get_attribute('id')] = $user;
            }

            // Check if a next page is available.
            if (isset($response['_links']['next']['href'])) {
                $next_url = $response['_links']['next']['href'];
                $parsed_url = parse_url($next_url);

                $path = isset($parsed_url['path']) ? $parsed_url['path'] : '/v3/users';
                $next_params = [];
                if (isset($parsed_url['query'])) {
                    parse_str($parsed_url['query'], $next_params);
                }

                $request = $this->mock_request('GET', $path, $next_params);
            } else {
                $request = null;
            }
        } while ($request !== null);

        $this->assertCount(20, $all_users, "Expected 20 users with username like usera_*");
    }

    /**
     * @group !current
     */
    public function test_get_user(){
        $user = reset($this->users['group_a']);
        $request = $this->mock_request('GET', "/v3/users/$user->id");
        $controller = new user_controller();

        $response = $controller->get_user($request, ['userid' => $user->id])->jsonSerialize();

        $this->assertEquals($user->id, $response['id']);
        $this->assertArrayHasKey('_links', $response);
        $this->assertArrayHasKey('self', $response['_links']);
        $this->assertArrayHasKey('fullname', $response);
        $this->assertArrayHasKey('profileimageurl', $response);
        $this->assertArrayHasKey('profileimageurlsmall', $response);
        $this->assertArrayHasKey('lastcourseaccess', $response);
        $this->assertArrayHasKey('customfields', $response);
        $this->assertCount(1, array_filter($response['customfields'], fn($field) => $field['shortname'] == 'textfield'));
        $this->assertCount(1, array_filter($response['customfields'], fn($field) => $field['shortname'] == 'menufield'));
    }

    /**
     * @group !current
     */
    public function test_get_user_not_found(){
        $request = $this->mock_request('GET', "/v3/users/0");
        $controller = new user_controller();

        $this->expectException(http_exception::class);
        $this->expectExceptionMessage(get_string('exception:user_not_found', 'local_lfapi'));
        $controller->get_user($request, ['userid' => 0])->jsonSerialize();
    }

    /**
     * @group !current
     */
    public function test_create_user(){
        global $DB, $CFG;

        require_once($CFG->dirroot.'/user/profile/lib.php');

        $body = [
            'user' => [
                'username' => 'testuser01',
                'idnumber' => 'test-user-01',
                'firstname' => "Test",
                'lastname' => "User 01",
                'email' => "<EMAIL>",
                'description' => "Lorem ipsum...",
                'password' => "Senha@123",
                'customfields' => [
                    ['type' => 'textfield', 'value' => 'Test user'],
                    ['type' => 'menufield', 'value' => 'option3'],
                ],
                'preferences' => [
                    ['type' => 'some_preference', 'value' => 'yes'],
                ],
                'interests' => 'physics,books,hydromel',
            ],
        ];
        $request = $this->mock_request('POST', '/v3/users', [], $body);
        $controller = new user_controller();
        $response = $controller->create_users($request)->getPayload()->jsonSerialize();

        $this->assertArrayHasKey('id', $response);
        $this->assertArrayHasKey('_links', $response);
        $this->assertArrayHasKey('self', $response['_links']);

        $user = $DB->get_record('user', ['id' => $response['id']]);
        $this->assertNotEmpty($user);
        profile_load_data($user);

        $this->assertEquals($body['user']['email'], $user->email);
        $this->assertEquals($body['user']['username'], $user->username);
        $this->assertEquals($body['user']['description'], $user->description);
        $this->assertEquals($body['user']['customfields'][0]['value'], $user->profile_field_textfield);
        $this->assertEquals($body['user']['customfields'][1]['value'], $user->profile_field_menufield);
    }

    /**
     * @group !current
     */
    public function test_create_users(){
        global $DB;

        $body = [
            'users' => [
                [
                    'username' => 'testuser01',
                    'idnumber' => 'test-user-01',
                    'firstname' => "Test",
                    'lastname' => "User 01",
                    'email' => "<EMAIL>",
                    'password' => "Senha@123",
                ],
                [
                    'username' => 'testuser02',
                    'idnumber' => 'test-user-02',
                    'firstname' => "Test",
                    'lastname' => "User 02",
                    'email' => "<EMAIL>",
                    'password' => "Senha@123",
                ],
            ],
        ];
        $request = $this->mock_request('POST', '/v3/users', [], $body);
        $controller = new user_controller();
        $response = $controller->create_users($request)->getPayload()->jsonSerialize();

        $this->assertCount(2, $response['_embedded']['users']);
        $users = [
            $response['_embedded']['users'][0]->jsonSerialize(),
            $response['_embedded']['users'][1]->jsonSerialize(),
        ];

        foreach ([0,1] as $index) {
            $user = (array) $DB->get_record('user', ['id' => $users[$index]['id']]);

            $this->assertEquals($body['users'][$index]['username'], $users[$index]['username']);

            foreach (['username', 'idnumber', 'firstname', 'email', 'lastname'] as $key) {
                $this->assertEquals($body['users'][$index][$key], $user[$key]);
            }
        }        
    }


    /**
     * @group !current
     */
    public function test_update_users(){
        global $DB, $CFG;

        require_once($CFG->dirroot.'/user/profile/lib.php');

        $user2 = end($this->users['group_c']);
        $user1 = reset($this->users['group_c']);

        $body = [
            'users' => [
                [
                    'id' => $user1->id,
                    'idnumber' => 'test-user-01',
                    'firstname' => "Test",
                    'lastname' => "User 01",
                    'email' => "<EMAIL>",
                ],
                [
                    'id' => $user2->id,
                    'idnumber' => 'test-user-02',
                    'firstname' => "Test",
                    'lastname' => "User 02",
                    'email' => "<EMAIL>",
                    'customfields' => [
                        ['type' => 'menufield', 'value' => 'option3'],
                    ],
                ],
            ],
        ];
        $request = $this->mock_request('PATCH', '/v3/users/', [], $body);
        $controller = new user_controller();
        $response = $controller->update_users($request);

        $this->assertInstanceOf(EmptyResponse::class, $response);

        $updated_user1 = $DB->get_record('user', ['id' => $user1->id]);
        $updated_user2 = $DB->get_record('user', ['id' => $user2->id]);
        profile_load_data($updated_user2);

        $this->assertEquals($body['users'][0]['id'], $updated_user1->id);
        $this->assertEquals($body['users'][0]['idnumber'], $updated_user1->idnumber);
        $this->assertEquals($body['users'][0]['firstname'], $updated_user1->firstname);
        $this->assertEquals($body['users'][0]['email'], $updated_user1->email);

        $this->assertEquals($body['users'][1]['id'], $updated_user2->id);
        $this->assertEquals($body['users'][1]['idnumber'], $updated_user2->idnumber);
        $this->assertEquals($body['users'][1]['firstname'], $updated_user2->firstname);
        $this->assertEquals($body['users'][1]['email'], $updated_user2->email);
        $this->assertEquals($body['users'][1]['customfields'][0]['value'], $updated_user2->profile_field_menufield);
    }

    /**
     * @group !current
     */
    public function test_update_user(){
        global $DB, $CFG;

        require_once($CFG->dirroot.'/user/profile/lib.php');

        $user = reset($this->users['group_a']);

        $body = [
            'user' => [
                'id' => $user->id,
                'idnumber' => 'test-user-01',
                'firstname' => "Test",
                'lastname' => "User 01",
                'email' => "<EMAIL>",
                'customfields' => [
                    ['type' => 'menufield', 'value' => 'option3'],
                ],
            ],
        ];
        $request = $this->mock_request('PATCH', '/v3/categories/' . $user->id, [], $body);
        $controller = new user_controller();
        $response = $controller->update_user($request, ['userid' => $user->id]);

        $this->assertInstanceOf(EmptyResponse::class, $response);

        $updated_user = $DB->get_record('user', ['id' => $user->id]);

        $this->assertNotEmpty($updated_user);
        profile_load_data($updated_user);
        $this->assertEquals($body['user']['idnumber'], $updated_user->idnumber);
        $this->assertEquals($body['user']['firstname'], $updated_user->firstname);
        $this->assertEquals($body['user']['lastname'], $updated_user->lastname);
        $this->assertEquals($body['user']['email'], $updated_user->email);
        $this->assertEquals($body['user']['customfields'][0]['value'], $updated_user->profile_field_menufield);
    }

    /**
     * @group !current
     */
    public function test_delete_user(){
        global $DB;

        $user = reset($this->users['group_a']);

        $request = $this->mock_request('DELETE', '/v3/users/' . $user->id);
        $controller = new user_controller();
        $response = $controller->delete_user($request, ['userid' => $user->id]);

        $this->assertInstanceOf(EmptyResponse::class, $response);

        $category = $DB->get_record('user', ['id' => $user->id, 'deleted' => 0]);
        $this->assertEmpty($category);
    }
}