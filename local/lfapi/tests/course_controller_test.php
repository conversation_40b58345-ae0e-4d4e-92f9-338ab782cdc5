<?php namespace local_lfapi;

defined('MOODLE_INTERNAL') || die();

global $CFG;

require_once(__DIR__ . '/traits/mocked_request_trait.php');

use \advanced_testcase;
use \Laminas\Diactoros\Response\JsonResponse;
use \Laminas\Diactoros\Response\EmptyResponse;
use \local_lfapi\traits\mocked_request_trait;
use \local_lfapi\v3\controllers\course_controller;
use \local_lfapi\v3\exceptions\http_exception;
use \local_lfapi\v3\filters\custom_course_fields_sql_filter;
use \local_lfapi\v3\response\resources\entities\course_resource;
use \webservice_api\exceptions\validation_exception;

class course_controller_test extends advanced_testcase {

    use mocked_request_trait;

    /**
     * This property holds the test courses organized in groups:
     * - group_a: 20 visible courses with shortname prefix "coursea_"
     *      and category = $this->categories[0]
     *      and startdate = time() - YEARSECS
     *      and enddate = time() + YEARSECS
     *      and custom_field_textfield with prefix "test_"
     *      and custom_field_selectfield with value "option3"
     * - group_b: 10 non-visible courses with shortname prefix "courseb_"
     *      and startdate = time() - 2*YEARSECS
     *      and enddate = time() - YEARSECS
     * - group_c: 10 visible courses  with shortname prefix "coursec_"
     *      and category = $this->categories[1]
     *      and custom_field_checkboxfield = true 
     *      and custom_field_datefield = strtotime('-1 month')
     * - group_d: 10 10 visible courses with shortname prefix "coursed_"
     *      and custom_field_checkboxfield = false
     *      and custom_field_datefield = strtotime('+2 months')
     *
     * @var array
     */
    protected $courses = [];

    protected $categories = [];

    protected function setUp(): void{
        $this->resetAfterTest();
        $this->setAdminUser();

        set_debugging(DEBUG_NONE, false);
        $this->create_custom_fields();
        $this->create_test_courses_and_categories();
        set_debugging(DEBUG_DEVELOPER, true);
    }

    protected function create_custom_fields(){
        $course_handler = \core_course\customfield\course_handler::create();
        $category_id = $course_handler->create_category("testcoursefieldcategory");
        $category_controller = \core_customfield\category_controller::create($category_id);

        // Text field
        $field = (object)['shortname' => 'textfield', 'name' => "textfield", 'type' => 'text'];
        $field_controller = \core_customfield\field_controller::create(0, $field, $category_controller);
        $course_handler->save_field_configuration($field_controller, $field_controller->to_record());

        // Checkbox field
        $field = (object)['shortname' => 'checkboxfield', 'name' => "checkboxfield", 'type' => 'checkbox'];
        $field_controller = \core_customfield\field_controller::create(0, $field, $category_controller);
        $course_handler->save_field_configuration($field_controller, $field_controller->to_record());

        // Date field
        $field = (object)['shortname' => 'datefield', 'name' => "datefield", 'type' => 'date'];
        $field_controller = \core_customfield\field_controller::create(0, $field, $category_controller);
        $course_handler->save_field_configuration($field_controller, $field_controller->to_record());

        // Select field
        $field = (object)[
            'shortname' => 'selectfield',
            'name' => "selectfield",
            'type' => 'select',
            'type' => 'select',
            'configdata' => json_encode([
                'options' => implode("\r\n",['option1', 'option2', 'option3']),
            ])
        ];
        $field_controller = \core_customfield\field_controller::create(0, $field, $category_controller);
        $course_handler->save_field_configuration($field_controller, $field_controller->to_record());
    }

    protected function create_test_courses_and_categories(){
        $this->categories[] = $this->getDataGenerator()->create_category([
            'name'         => 'Category 1',
            'idnumber'     => 'cat-01',
        ]);

        $this->categories[] = $this->getDataGenerator()->create_category([
            'name'         => 'Subcategory 1',
            'idnumber'     => 'subcat-1',
            'parent' => $this->categories[0]->id,
        ]);

        // Group A: 20 visible courses with shortname prefix "coursea_"
        // and category = $this->categories[0]
        // and startdate = time() - YEARSECS
        // and enddate = time() + YEARSECS
        // and custom_field_textfield with prefix "test_"
        // and custom_field_selectfield with value "option3"
        $this->courses['group_a'] = [];
        for($i=1;$i<=20;$i++){
            $course = $this->getDataGenerator()->create_course([
                'fullname'  => 'Group A Course '.$i,
                'shortname' => 'coursea_'.$i,
                'visible'   => 1,
                'category'  => $this->categories[0]->id,
                'summary'   => 'Group A summary',
                'startdate' => time() - YEARSECS,
                'enddate' => time() + YEARSECS,
                'customfields' => [
                    [
                        'shortname' => 'textfield',
                        'value' => 'test_' . $i,
                    ],
                    [
                        'shortname' => 'selectfield',
                        'value' => 3,
                    ],
                ],
            ]);
            $this->courses['group_a'][$course->id] = $course;
        }

        // Group B: 10 non-visible courses with shortname prefix "courseb_"
        // and startdate = time() - 2*YEARSECS
        // and enddate = time() - YEARSECS
        $this->courses['group_b'] = [];
        for($i=21;$i<=30;$i++){
            $course = $this->getDataGenerator()->create_course([
                'fullname'  => 'Group B Course '.$i,
                'shortname' => 'courseb_'.$i,
                'visible'   => 0,
                'summary'   => 'Group B summary',
                'startdate' => time() - 2 * YEARSECS,
                'enddate' => time() - YEARSECS,
            ]);
            $this->courses['group_b'][$course->id] = $course;
        }


        // Group C: 10 visible courses with shortname prefix "coursec_"
        // and category = $this->categories[1]
        // and custom_field_checkboxfield = true
        // and custom_field_datefield = strtotime('-1 month')
        $this->courses['group_c'] = [];
        for($i=31;$i<=40;$i++){
            $course = $this->getDataGenerator()->create_course([
                'fullname'  => 'Group C Course '.$i,
                'shortname' => 'coursec_'.$i,
                'visible'   => 1,
                'category' => $this->categories[1]->id,
                'customfields' => [
                    [
                        'shortname' => 'checkboxfield',
                        'value' => 1,
                    ],
                    [
                        'shortname' => 'datefield',
                        'value' => strtotime("-1 month"),
                    ]  
                ]
            ]);
            $this->courses['group_c'][$course->id] = $course;
        }

        // Group D: 10 visible courses with shortname prefix "coursed_"
        // and custom_field_checkboxfield = false
        // and custom_field_datefield = strtotime('+2 months')
        $this->courses['group_d'] = [];
        for($i=41;$i<=50;$i++){
            $course = $this->getDataGenerator()->create_course([
                'fullname'  => 'Group D Course '.$i,
                'shortname' => 'coursed_'.$i,
                'visible'   => 1,
                'summary'   => 'Group D summary',
                'customfields' => [
                    [
                        'shortname' => 'checkboxfield',
                        'value' => 0,
                    ],
                    [
                        'shortname' => 'datefield',
                        'value' => strtotime("+1 month"),
                    ]
                ]
            ]);
            $this->courses['group_d'][$course->id] = $course;
        }
    }

    protected static function load_custom_fields(object $course) {
        $handler = \core_course\customfield\course_handler::create();
        $course->customfields = new \stdClass();
        foreach ($handler->get_instance_data($course->id, true) as $data) {
            $course->customfields->{$data->get_field()->get('shortname')} = $data;
        }
    }

    /**
     * @group !current
     */
    public function test_list_courses_single_page(){
        // Build initial query parameters:
        //   - visible equals 1.
        //   - shortname like "course*"
        parse_str("visible[eq]=1&shortname[like]=course*", $params);
        $params['_limit'] = 50;

        $request = $this->mock_request('GET','/v3/courses',$params);
        $controller = new course_controller();
        $response = $controller->list_courses($request)->jsonSerialize();

        $this->assertArrayHasKey('_embedded',$response);
        $this->assertArrayHasKey('courses',$response['_embedded']);

        $this->assertCount(40,$response['_embedded']['courses'],'Expected 40 visible courses with shortname like course*');
    }

    /**
     * @group !current
     */
    public function test_list_courses_paginated_iteration(){
        custom_course_fields_sql_filter::reset_cache();
        
        // Build query parameters:
        //   - custom_field_textfield like "test*"
        //   - 5 courses per page
        parse_str("custom_field_textfield[like]=test*", $params);
        // $params['_limit'] = 5;

        $request = $this->mock_request('GET','/v3/courses',$params);
        $controller = new course_controller();
        $all_courses = [];

        do{
            $response = $controller->list_courses($request)->jsonSerialize();

            $this->assertArrayHasKey('_embedded',$response);
            $this->assertArrayHasKey('courses',$response['_embedded']);

            foreach($response['_embedded']['courses'] as $course){
                $all_courses[$course->get_attribute('id')] = $course;
            }

            if(isset($response['_links']['next']['href'])){
                $next_url = $response['_links']['next']['href'];
                $parsed_url = parse_url($next_url);
                $path = isset($parsed_url['path']) ? $parsed_url['path'] : '/v3/courses';
                $next_params = [];
                if(isset($parsed_url['query'])){
                    parse_str($parsed_url['query'],$next_params);
                }
                $request = $this->mock_request('GET',$path,$next_params);
            }
            else{
                $request = null;
            }
        }while($request !== null);

        // Expecting 20 courses from group_a.
        $this->assertCount(20,$all_courses,'Expected 20 courses with custom_field_textfield like test*');
    }

    /**
     * @group !current
     * 
     * A variation of the previous test so we can test the
     * select custom field
     */
    public function test_list_courses_paginated_iteration2(){
        custom_course_fields_sql_filter::reset_cache();
        
        // Build query parameters:
        //   - custom_field_selectfield = "option3"
        //   - 5 courses per page
        parse_str("custom_field_selectfield[eq]=option3", $params);
        $params['_limit'] = 5;

        $request = $this->mock_request('GET','/v3/courses',$params);
        $controller = new course_controller();
        $all_courses = [];

        do{
            $response = $controller->list_courses($request)->jsonSerialize();

            $this->assertArrayHasKey('_embedded',$response);
            $this->assertArrayHasKey('courses',$response['_embedded']);

            foreach($response['_embedded']['courses'] as $course){
                $all_courses[$course->get_attribute('id')] = $course;
            }

            if(isset($response['_links']['next']['href'])){
                $next_url = $response['_links']['next']['href'];
                $parsed_url = parse_url($next_url);
                $path = isset($parsed_url['path']) ? $parsed_url['path'] : '/v3/courses';
                $next_params = [];
                if(isset($parsed_url['query'])){
                    parse_str($parsed_url['query'],$next_params);
                }
                $request = $this->mock_request('GET',$path,$next_params);
            }
            else{
                $request = null;
            }
        }while($request !== null);

        // Expecting 20 courses from group_a.
        $this->assertCount(20,$all_courses,'Expected 20 courses with custom_field_selectfield = "option3"');
    }

    /**
     * @group !current
     */
    public function test_get_course(){
        $course = reset($this->courses['group_a']);
        $request = $this->mock_request('GET','/v3/courses/'.$course->id);
        $controller = new course_controller();

        $response = $controller->get_course($request,['courseid' => $course->id])->jsonSerialize();

        $this->assertEquals($course->id,$response['id']);
        $this->assertArrayHasKey('_links',$response);
        $this->assertArrayHasKey('self',$response['_links']);
    }

    /**
     * @group !current
     */
    public function test_get_course_not_found(){
        $this->expectException(http_exception::class);
        $this->expectExceptionMessage(get_string('exception:course_not_found','local_lfapi'));
        $request = $this->mock_request('GET','/v3/courses/0');
        $controller = new course_controller();
        $controller->get_course($request,['courseid' => 0])->jsonSerialize();
    }

    /**
     * @group !current
     */
    public function test_create_course(){
        global $DB,$CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        $body = [
            'course' => [
                'fullname'   => 'New Test Course',
                'shortname'  => 'new_test_course',
                'categoryid' => $this->categories[1]->id,
                'summary'    => 'Course created via API',
                'visible'    => 1,
                'customfields' => [
                    [
                        'shortname' => 'checkboxfield',
                        'value' => 0,
                    ],
                    [
                        'shortname' => 'datefield',
                        'value' => time(),
                    ],
                    [
                        'shortname' => 'selectfield',
                        'value' => 'option2',
                    ],
                ]
            ],
        ];
        $request = $this->mock_request('POST','/v3/courses',[], $body);
        $controller = new course_controller();
        $response = $controller->create_courses($request);
        $this->assertInstanceOf(JsonResponse::class, $response);

        $response = $response->getPayload()->jsonSerialize();
        $this->assertArrayHasKey('id', $response);

        $course = $DB->get_record('course',['id' => $response['id']]);
        $this->assertNotEmpty($course);
        self::load_custom_fields($course);
        $this->assertEquals($body['course']['fullname'], $course->fullname);
        $this->assertEquals($body['course']['shortname'], $course->shortname);

        $this->assertEquals(
            $body['course']['customfields'][0]['value'],
            $course->customfields->checkboxfield->get_value()
        );

        $this->assertEquals(
            $body['course']['customfields'][1]['value'],
            $course->customfields->datefield->get_value()
        );

        $this->assertEquals(
            $body['course']['customfields'][2]['value'],
            $course->customfields->selectfield->export_value()
        );
    }

    /**
     * @group !current
     */
    public function test_create_courses(){
        global $DB,$CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        $body = [
            'courses' => [
                [
                    'fullname'   => 'New Test Course',
                    'shortname'  => 'new_test_course',
                    'categoryid' => $this->categories[1]->id,
                    'summary'    => 'Course created via API',
                    'visible'    => 1,
                    'customfields' => [
                        [
                            'shortname' => 'selectfield',
                            'value' => 'option2',
                        ],
                    ]
                ],
                [
                    'fullname'   => 'New Test Course 2',
                    'shortname'  => 'new_test_course_2',
                    'categoryid' => $this->categories[0]->id,
                    'summary'    => 'Course created via API',
                    'visible'    => 1,
                    'customfields' => [
                        [
                            'shortname' => 'selectfield',
                            'value' => 'option1',
                        ],
                    ]
                ],
            ]
        ];
        $request = $this->mock_request('POST','/v3/courses',[], $body);
        $controller = new course_controller();
        $response = $controller->create_courses($request);
        $this->assertInstanceOf(JsonResponse::class, $response);

        $response = $response->getPayload()->jsonSerialize();
        $this->assertArrayHasKey('_embedded', $response);
        $this->assertArrayHasKey('courses', $response['_embedded']);

        /** @var \local_lfapi\v3\response\resources\entities\course_resource */
        foreach ($response['_embedded']['courses'] as $index => $course_resource) {
            $course = $DB->get_record('course',['id' => $course_resource->get_attribute('id')]);
            $this->assertNotEmpty($course);
            self::load_custom_fields($course);
            $this->assertEquals($body['courses'][$index]['fullname'], $course->fullname);
            $this->assertEquals($body['courses'][$index]['shortname'], $course->shortname);

            $this->assertEquals(
                $body['courses'][$index]['customfields'][0]['value'],
                $course->customfields->selectfield->export_value()
            );
        }
    }

    /**
     * @group !current
     */
    public function test_update_courses(){
        global $DB;

        $course1 = reset($this->courses['group_c']);
        $course2 = end($this->courses['group_c']);

        $body = [
            'courses' => [
                [
                    'id'        => $course1->id,
                    'fullname'  => 'Updated Course 1',
                    'shortname' => 'updated-course-1',
                    'customfields' => [
                        [
                            'shortname' => 'checkboxfield',
                            'value' => 0,
                        ],
                        [
                            'shortname' => 'datefield',
                            'value' => time(),
                        ],
                        [
                            'shortname' => 'selectfield',
                            'value' => 'option2',
                        ],
                    ],
                ],
                [
                    'id'        => $course2->id,
                    'fullname'  => 'Updated Course 2',
                    'shortname' => 'updated-course-2',
                    'customfields' => [
                        [
                            'shortname' => 'checkboxfield',
                            'value' => 1,
                        ],
                        [
                            'shortname' => 'datefield',
                            'value' => time(),
                        ],
                        [
                            'shortname' => 'selectfield',
                            'value' => 'option3',
                        ],
                    ],
                ]
            ],
        ];
        $request = $this->mock_request('PATCH','/v3/courses',[], $body);
        $controller = new course_controller();
        $response = $controller->update_courses($request);

        $this->assertInstanceOf(EmptyResponse::class, $response);


        foreach ($body['courses'] as $request_course) {
            $course = $DB->get_record('course',['id' => $request_course['id']]);
            $this->assertNotEmpty($course);
            self::load_custom_fields($course);

            $this->assertEquals($request_course['fullname'], $course->fullname);
            $this->assertEquals($request_course['shortname'], $course->shortname);

            $this->assertEquals(
                $request_course['customfields'][0]['value'],
                $course->customfields->checkboxfield->get_value()
            );
            $this->assertEquals(
                $request_course['customfields'][1]['value'],
                $course->customfields->datefield->get_value()
            );
            $this->assertEquals(
                $request_course['customfields'][2]['value'],
                $course->customfields->selectfield->export_value()
            );
        }
    }

    /**
     * @group !current
     */
    public function test_update_courses_error(){
        global $DB;

        $body = [
            'courses' => [
                [
                    'fullname'  => 'Updated Course 1',
                    'shortname' => 'updated-course-1',
                    'customfields' => [
                        [
                            'shortname' => 'checkboxfield',
                            'value' => 0,
                        ],
                        [
                            'shortname' => 'datefield',
                            'value' => time(),
                        ],
                        [
                            'shortname' => 'selectfield',
                            'value' => 'option2',
                        ],
                    ],
                ],
            ]
        ];

        $this->expectException(validation_exception::class);
        $request = $this->mock_request('PATCH','/v3/courses',[], $body);
        $controller = new course_controller();
        $controller->update_courses($request);
    }

    /**
     * @group !current
     */
    public function test_update_courses_warning(){
        global $DB;

        $course = reset($this->courses['group_c']);

        $body = [
            'courses' => [
                [
                    'id' => $course->id,
                    'fullname'  => 'Updated Course 1',
                    'shortname' => '', // Should raise an warning
                    'customfields' => [
                        [
                            'shortname' => 'checkboxfield',
                            'value' => 0,
                        ],
                    ],
                ],
            ]
        ];
        $request = $this->mock_request('PATCH','/v3/courses',[], $body);
        $controller = new course_controller();
        $response = $controller->update_courses($request)->getPayload()->jsonSerialize();

        $this->assertArrayHasKey('_embedded', $response);
        $this->assertArrayHasKey('errors', $response['_embedded']);
        $this->assertNotEmpty($response['_embedded']['errors']);
    }

    /**
     * @group !current
     */
    public function test_update_course(){
        global $DB,$CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        $course = reset($this->courses['group_d']);
        $body = [
            'course' => [
                'fullname'  => 'Updated course',
                'shortname' => 'course_updated',
                'customfields' => [
                    [
                        'shortname' => 'selectfield',
                        'value' => 'option1',
                    ],
                ],
            ],
        ];
        $request = $this->mock_request('PATCH','/v3/courses/'.$course->id,[], $body);
        $controller = new course_controller();
        $response = $controller->update_course($request,['courseid' => $course->id]);
        $this->assertInstanceOf(EmptyResponse::class,$response);

        $updated_course = $DB->get_record('course', ['id' => $course->id]);
        self::load_custom_fields($updated_course);

        $this->assertEquals($body['course']['fullname'],$updated_course->fullname);
        $this->assertEquals($body['course']['shortname'],$updated_course->shortname);

        $this->assertEquals(
            $body['course']['customfields'][0]['value'],
            $updated_course->customfields->selectfield->export_value()
        );
    }

    /**
     * @group !current
     */
    public function test_delete_course(){
        global $DB,$CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        $course = reset($this->courses['group_d']);

        $request = $this->mock_request('DELETE','/v3/courses/'.$course->id);
        $controller = new course_controller();
        
        set_debugging(DEBUG_NONE, false); // workaround for local_customfields
        $response = $controller->delete_course($request, ['courseid' => $course->id]);
        set_debugging(DEBUG_DEVELOPER, true);

        $this->assertInstanceOf(EmptyResponse::class, $response);

        $deleted_course = $DB->get_record('course', ['id' => $course->id]);
        $this->assertEmpty($deleted_course);
    }
}
