<?php namespace local_lfapi\batch;

defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once(__DIR__ . '/../traits/mocked_request_trait.php');

use \advanced_testcase;
use \local_lfapi\traits\mocked_request_trait;
use \local_lfapi\v3\controllers\course_controller;
use \Laminas\Diactoros\Response\JsonResponse;
use \local_lfapi\v3\exceptions\http_exception;

class course_controller_batch_test extends advanced_testcase {

    use mocked_request_trait;

    protected $category;
    protected $customfields = [];

    protected function setUp(): void {
        $this->resetAfterTest();
        $this->setAdminUser();

        set_debugging(DEBUG_NONE, false);

        $this->create_custom_fields();

        set_debugging(DEBUG_DEVELOPER, true);
    }

    protected function create_custom_fields() {
        $handler = \core_course\customfield\course_handler::create();
        $categoryid = $handler->create_category("batchtestfields");
        $category_controller = \core_customfield\category_controller::create($categoryid);

        $fields = [
            ['shortname' => 'textfield',     'name' => 'textfield',     'type' => 'text'],
            ['shortname' => 'checkboxfield', 'name' => 'checkboxfield', 'type' => 'checkbox'],
            ['shortname' => 'datefield',     'name' => 'datefield',     'type' => 'date'],
            ['shortname' => 'selectfield',   'name' => 'selectfield',   'type' => 'select', 'configdata' => [
                'options' => implode("\r\n", ['option1', 'option2', 'option3'])
            ]],
        ];

        foreach ($fields as $fielddata) {
            $record = (object)[
                'shortname' => $fielddata['shortname'],
                'name' => $fielddata['name'],
                'type' => $fielddata['type'],
                'configdata' => isset($fielddata['configdata']) ? json_encode($fielddata['configdata']) : null,
            ];
            $controller = \core_customfield\field_controller::create(0, $record, $category_controller);
            $handler->save_field_configuration($controller, $controller->to_record());
            $this->customfields[] = $record->shortname;
        }
    }

    /**
     * @group batchtest
     */
    public function test_create_single_course_with_custom_fields() {
        $this->create_courses_with_custom_fields(1);
    }

    /**
     * @group batchtest
     */
    public function test_create_ten_courses_with_custom_fields() {
        $this->create_courses_with_custom_fields(10);
    }

    /**
     * @group batchtest
     */
    public function test_create_a_hundred_courses_with_custom_fields() {
        $this->create_courses_with_custom_fields(100);
    }

    /**
     * @group batchtest
     */
    public function test_create_a_thousand_courses_with_custom_fields() {
        $this->expectException(http_exception::class);
        $this->create_courses_with_custom_fields(1000);
    }

    protected function create_courses_with_custom_fields(int $number_of_courses){
        global $DB;

        $body = ['courses' => []];
        $now = time();

        for ($i = 1; $i <= $number_of_courses; $i++) {
            $body['courses'][] = [
                'fullname'   => "Batch Test Course $i",
                'shortname'  => "batch_test_course_$i",
                'idnumber'   => "batch_test_course_$i",
                'categoryid' => 1,
                'summary'    => "Course $i created during Batch Test",
                'visible'    => 1,
                'customfields' => [
                    ['shortname' => 'textfield',     'value' => "text_$i"],
                    ['shortname' => 'checkboxfield', 'value' => ($i % 2)],
                    ['shortname' => 'datefield',     'value' => $now + ($i * 3600)],
                    ['shortname' => 'selectfield',   'value' => 'option' . (($i % 3) + 1)],
                ],
            ];
        }

        $request = $this->mock_request('POST', '/v3/courses', [], $body);
        $controller = new course_controller();

        $start = microtime(true);
        $response = $controller->create_courses($request);
        $duration = microtime(true) - $start;

        $this->assertInstanceOf(JsonResponse::class, $response);

        $payload = $response->getPayload()->jsonSerialize();
        $this->assertArrayHasKey('_embedded', $payload);
        $this->assertArrayHasKey('courses', $payload['_embedded']);
        $this->assertCount($number_of_courses, $payload['_embedded']['courses']);

        // Checks them on the database
        $count = $DB->count_records_select('course', $DB->sql_like('shortname', ':pattern'), ['pattern' => 'batch_test_course_*']);
        $this->assertEquals($number_of_courses, $count);

        fwrite(STDOUT, "\n⏱ Execution time for creating $number_of_courses courses: " . round($duration, 2) . " seconds\n");
    }
}
