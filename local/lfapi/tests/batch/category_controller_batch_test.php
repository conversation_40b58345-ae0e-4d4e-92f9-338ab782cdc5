<?php namespace local_lfapi\batch;

defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once(__DIR__ . '/../traits/mocked_request_trait.php');

use \advanced_testcase;
use \local_lfapi\traits\mocked_request_trait;
use \local_lfapi\v3\controllers\category_controller;
use \Laminas\Diactoros\Response\JsonResponse;
use \local_lfapi\v3\exceptions\http_exception;

class category_controller_batch_test extends advanced_testcase {

    use mocked_request_trait;

    protected $category;
    protected $customfields = [];

    protected function setUp(): void {
        $this->resetAfterTest();
        $this->setAdminUser();
    }

    /**
     * @group batchtest
     */
    public function test_create_single_category() {
        $this->create_categories(1);
    }

    /**
     * @group batchtest
     */
    public function test_create_ten_categories() {
        $this->create_categories(10);
    }

    /**
     * @group batchtest
     */
    public function test_create_a_hundred_categories() {
        $this->create_categories(100);
    }

    /**
     * @group batchtest
     */
    public function test_create_a_thousand_categories() {
        $this->expectException(http_exception::class);
        $this->create_categories(1000);
    }

    protected function create_categories(int $count){
        global $DB;

        $body = ['categories' => []];
        for ($i = 1; $i <= $count; $i++) {
            $body['categories'][] = [
                'name'       => "Batch Test Category $i",
                'idnumber'   => "batch_cat_$i",
                'parent'     => 0,
                'description' => "Created during batch test $i"
            ];
        }

        $request = $this->mock_request('POST', '/v3/categories', [], $body);
        $controller = new category_controller();

        $start = microtime(true);
        $response = $controller->create_category($request);
        $duration = microtime(true) - $start;

        $this->assertInstanceOf(JsonResponse::class, $response);

        $payload = $response->getPayload()->jsonSerialize();
        $this->assertArrayHasKey('_embedded', $payload);
        $this->assertArrayHasKey('categories', $payload['_embedded']);
        $this->assertCount($count, $payload['_embedded']['categories']);

        $created = $DB->get_records_select(
            'course_categories',
            $DB->sql_like('idnumber', ':pattern'),
            ['pattern' => 'batch_cat_*']
        );

        $this->assertCount($count, $created);
        fwrite(STDOUT, "\n⏱ Criadas $count categorias em " . round($duration, 2) . " segundos.\n");
    }
}
