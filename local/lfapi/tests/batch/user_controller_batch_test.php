<?php namespace local_lfapi\batch;

defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once(__DIR__ . '/../traits/mocked_request_trait.php');

use \advanced_testcase;
use \local_lfapi\traits\mocked_request_trait;
use \local_lfapi\v3\controllers\user_controller;
use \Laminas\Diactoros\Response\JsonResponse;
use \local_lfapi\v3\exceptions\http_exception;

class user_controller_batch_test extends advanced_testcase {

    use mocked_request_trait;

    protected function setUp(): void {
        $this->resetAfterTest();
        $this->setAdminUser();
        $this->create_custom_fields();
    }

    protected function create_custom_fields() {
        global $DB;

        $category = (object)[
            'name' => 'Batch Test Profile Fields',
            'visible' => 1,
            'sortorder' => 1,
            'description' => 'Category for Batch Test custom fields.',
        ];
        $category->id = $DB->insert_record('user_info_category', $category);

        $fields = [
            [
                'shortname'   => 'textfield',
                'name'        => 'Text Field',
                'datatype'    => 'text',
                'categoryid'  => $category->id,
            ],
            [
                'shortname'   => 'datetimefield',
                'name'        => 'Datetime Field',
                'datatype'    => 'datetime',
                'categoryid'  => $category->id,
            ],
            [
                'shortname'   => 'menufield',
                'name'        => 'Menu Field',
                'datatype'    => 'menu',
                'categoryid'  => $category->id,
                'param1'      => "option1\noption2\noption3",
            ],
        ];

        foreach ($fields as $field) {
            $DB->insert_record('user_info_field', $field);
        }
    }

    /**
     * @group batchtest
     */
    public function test_create_a_single_user_with_custom_fields() {
        $this->create_users_with_custom_fields(1);
    }

    /**
     * @group batchtest
     */
    public function test_create_ten_users_with_custom_fields() {
        $this->create_users_with_custom_fields(10);
    }

    /**
     * @group batchtest
     */
    public function test_create_a_hundred_users_with_custom_fields() {
        $this->create_users_with_custom_fields(100);
    }

    /**
     * @group batchtest
     */
    public function test_create_a_thousand_users_with_custom_fields() {
        $this->expectException(http_exception::class);
        $this->create_users_with_custom_fields(1000);
    }

    protected function create_users_with_custom_fields(int $count) {
        global $DB;

        $body = ['users' => []];

        for ($i = 1; $i <= $count; $i++) {
            $body['users'][] = [
                'username'    => "batchtest_user_$i",
                'idnumber'    => "ltuser_$i",
                'firstname'   => "Load",
                'lastname'    => "User $i",
                'email'       => "batchtest_user_$<EMAIL>",
                'password'    => "Senha@123",
                'customfields' => [
                    ['type' => 'textfield',      'value' => "Test $i"],
                    ['type' => 'datetimefield',  'value' => time()],
                    ['type' => 'menufield',      'value' => 'option' . (($i % 3) + 1)],
                ]
            ];
        }

        $request = $this->mock_request('POST', '/v3/users', [], $body);
        $controller = new user_controller();

        $start = microtime(true);
        $response = $controller->create_users($request);
        $duration = microtime(true) - $start;

        $this->assertInstanceOf(JsonResponse::class, $response);
        $payload = $response->getPayload()->jsonSerialize();

        $this->assertArrayHasKey('_embedded', $payload);
        $this->assertArrayHasKey('users', $payload['_embedded']);
        $this->assertCount($count, $payload['_embedded']['users']);

        $created = $DB->count_records_select('user', $DB->sql_like('username', ':pattern'), ['pattern' => 'batchtest_user_*']);
        $this->assertEquals($count, $created);

        fwrite(STDOUT, "\n⏱ Execution time for creating $count users: " . round($duration, 2) . " seconds\n");
    }
}
