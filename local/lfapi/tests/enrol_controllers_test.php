<?php namespace local_lfapi;

defined('MOODLE_INTERNAL') || die();

global $CFG;

require_once(__DIR__ . '/traits/mocked_request_trait.php');

use \advanced_testcase;
use \Laminas\Diactoros\Response\JsonResponse;
use \Laminas\Diactoros\Response\EmptyResponse;
use \local_lfapi\traits\mocked_request_trait;
use local_lfapi\v3\controllers\enrol_instance_controller;
use local_lfapi\v3\controllers\user_enrolment_controller;
use local_lfapi\v3\exceptions\http_exception;
use webservice_api\exceptions\api_exception;

class enrol_controllers_test extends advanced_testcase {

    use mocked_request_trait;

    protected object $course;
    // protected object $teacher;
    protected array $students;

    protected int $student_role;
    protected int $teacher_role;

    protected array $instances;

    protected function setUp(): void{
        $this->resetAfterTest();
        $this->setAdminUser();

        set_debugging(DEBUG_NONE, false);
        $this->create_test_course();
        $this->create_test_enrol_instances();
        $this->create_test_users_and_enrolments();
        set_debugging(DEBUG_DEVELOPER, true);
    }

    protected function load_roles(){
        global $DB;

        $this->student_role = $DB->get_field('role', 'id', ['shortname' => 'student']);
        $this->teacher_role = $DB->get_field('role', 'id', ['shortname' => 'teacher']);
    }

    protected function create_test_course(){
        $this->course = $this->getDataGenerator()->create_course();
    }

    protected function create_test_enrol_instances(){
        global $DB;

        $this->instances = [];

        $manual = $DB->get_field('enrol', 'id', ['courseid' => $this->course->id, 'enrol' => 'manual']);
        $self = $DB->get_field('enrol', 'id', ['courseid' => $this->course->id, 'enrol' => 'self']);
        $guest = $DB->get_field('enrol', 'id', ['courseid' => $this->course->id, 'enrol' => 'guest']);

        // Changing manual instance
        $DB->update_record('enrol', ['id' => $manual, 'name' => 'manual-enrol-instance-01']);
        $this->instances['manual'] = $DB->get_record('enrol', ['id' => $manual]);

        // Changing self instance
        $DB->update_record('enrol', ['id' => $self, 'status' => ENROL_INSTANCE_ENABLED, 'enrolperiod' => 10 * DAYSECS]);
        $this->instances['self'] = $DB->get_record('enrol', ['id' => $self]);

        // Changing guest instance
        $DB->update_record('enrol', ['id' => $guest, 'enrolstartdate' => strtotime('-1 day')]);
        $this->instances['guest'] = $DB->get_record('enrol', ['id' => $guest]);
    }

    protected function create_test_users_and_enrolments(){
        global $DB;

        // GROUP A: 10 manual enrolled users 
        // future timeend
        // ENROL_USER_ACTIVE
        $this->students['group_a'] = [];
        for($i=1;$i<=10;$i++){
            $user = $this->getDataGenerator()->create_user();
            $this->getDataGenerator()->enrol_user(
                $user->id,
                $this->course->id,
                'student',
                'manual',
                0,
                strtotime("+1 year"),
                ENROL_USER_ACTIVE
            );
            $this->students['group_a'] = $user;
        }

        // GROUP B: 5 self enrolled users 
        // no timeend
        // ENROL_USER_ACTIVE
        $this->students['group_b'] = [];
        for($i=6;$i<=15;$i++){
            $user = $this->getDataGenerator()->create_user();
            $this->getDataGenerator()->enrol_user(
                $user->id,
                $this->course->id,
                'student',
                'self',
                0,
                0,
                ENROL_USER_ACTIVE
            );
            $this->students['group_b'] = $user;
        }

        // GROUP C: 5 manual enrolled users 
        // no timeend
        // ENROL_USER_SUSPENDED
        $this->students['group_c'] = [];
        for($i=21;$i<=25;$i++){
            $user = $this->getDataGenerator()->create_user();
            $this->getDataGenerator()->enrol_user(
                $user->id,
                $this->course->id,
                'student',
                'manual',
                0,
                0,
                ENROL_USER_SUSPENDED
            );
            $this->students['group_c'] = $user;
        }

        // GROUP C: 5 manual enrolled users 
        // past timeend
        // ENROL_USER_ACTIVE
        $this->students['group_c'] = [];
        for($i=31;$i<=35;$i++){
            $user = $this->getDataGenerator()->create_user();
            $this->getDataGenerator()->enrol_user(
                $user->id,
                $this->course->id,
                'student',
                'manual',
                0,
                0,
                ENROL_USER_ACTIVE
            );
            $this->students['group_c'] = $user;
        }

        // Enrolling teacher
        // $this->teacher = $this->getDataGenerator()->create_user();
        // $this->getDataGenerator()->enrol_user(
        //     $this->teacher->id,
        //     $this->course->id,
        //     'teacher',
        //     'manual',
        //     0,
        //     0,
        //     ENROL_USER_ACTIVE
        // );        
    }

    /**
     * @group !current
     */
    public function test_list_instances(){
        parse_str("status[eq]=0&name[like]=manual-*", $params);
        $params['_limit'] = 50;

        $courseid = $this->course->id;
        $request = $this->mock_request('GET',"/v3/courses/$courseid/enrol-instances", $params);
        $controller = new enrol_instance_controller();
        $response = $controller->list_instances($request, ['courseid' => $courseid])->jsonSerialize();
        
        $this->assertArrayHasKey('_embedded',$response);
        $this->assertArrayHasKey('instances',$response['_embedded']);
        $this->assertCount(1,$response['_embedded']['instances']);

        $instance = reset($response['_embedded']['instances']);
        $this->assertEquals($this->instances['manual']->id, $instance->get_attribute('id'));
    }

    /**
     * @group !current
     */
    public function test_get_instance(){
        $courseid = $this->course->id;
        $instance = $this->instances['self'];
        $request = $this->mock_request('GET',"/v3/courses/$courseid/enrol-instances/$instance->id");
        $controller = new enrol_instance_controller();

        $args = ['courseid' => $courseid, 'instanceid' => $instance->id];
        $response = $controller->get_instance($request, $args)->jsonSerialize();

        $this->assertEquals($instance->id, $response['id']);
        $this->assertEquals($instance->enrol, $response['enrol']);

        $this->assertArrayHasKey('_links',$response);
        $this->assertArrayHasKey('self',$response['_links']);
        $this->assertArrayHasKey('enrolments',$response['_links']);
        $this->assertArrayHasKey('_embedded',$response);
        $this->assertArrayHasKey('course',$response['_embedded']);
    }

    /**
     * @group !current
     */
    public function test_get_instance_not_found(){
        $courseid = $this->course->id;
        $request = $this->mock_request('GET',"/v3/courses/$courseid/enrol-instances/0");
        $controller = new enrol_instance_controller();

        $this->expectException(http_exception::class);
        $this->expectExceptionMessage(get_string('exception:enrol_instance_not_found', 'local_lfapi'));
        $args = ['courseid' => $courseid, 'instanceid' => 0];
        $controller->get_instance($request, $args)->jsonSerialize();
    }

    /**
     * @group !current
     */
    public function test_list_enrolments_single_page(){
        parse_str("enrol[eq]=manual&timeend[lte]=" . time(), $params);
        $params['_limit'] = 50;

        $courseid = $this->course->id;
        $request = $this->mock_request('GET',"/v3/courses/$courseid/user-enrolments", $params);
        $controller = new user_enrolment_controller();
        $response = $controller->list_enrolments($request, ['courseid' => $courseid])->jsonSerialize();
        
        $this->assertArrayHasKey('_embedded',$response);
        $this->assertArrayHasKey('enrolments',$response['_embedded']);
        $this->assertCount(10, $response['_embedded']['enrolments']);

        foreach ($response['_embedded']['enrolments'] as $enrolment) {
            $this->assertEquals($this->instances['manual']->id, $enrolment->get_attribute('enrolid'));
        }
        
    }

    /**
     * @group !current
     */
    public function test_list_enrolments_specific_enrol(){
        $enrolid = $this->instances['manual']->id;
        $status = ENROL_USER_SUSPENDED;
        parse_str("status[eq]=$status&enrolid[eq]=$enrolid", $params);
        $params['_limit'] = 50;

        $courseid = $this->course->id;
        $request = $this->mock_request('GET',"/v3/courses/$courseid/user-enrolments", $params);
        $controller = new user_enrolment_controller();

        $args =  ['courseid' => $courseid];
        $response = $controller->list_enrolments($request, $args)->jsonSerialize();
        
        $this->assertArrayHasKey('_embedded',$response);
        $this->assertArrayHasKey('enrolments',$response['_embedded']);
        $this->assertCount(5, $response['_embedded']['enrolments']);

        foreach ($response['_embedded']['enrolments'] as $enrolment) {
            $this->assertEquals($enrolid, $enrolment->get_attribute('enrolid'));
            $this->assertEquals($status, $enrolment->get_attribute('status'));
        }
    }

    /**
     * @group !current
     */
    public function test_get_enrolment(){
        global $DB;

        $courseid = $this->course->id;
        $enrolid = $this->instances['manual']->id;
        $enrolmentid = $DB->get_field('user_enrolments', 'id', ['enrolid' => $enrolid], IGNORE_MULTIPLE);

        $request = $this->mock_request('GET',"/v3/courses/$courseid/user-enrolments/$enrolmentid");
        $controller = new user_enrolment_controller();

        $args = ['courseid' => $courseid, 'enrolmentid' => $enrolmentid];
        $response = $controller->get_enrolment($request, $args)->jsonSerialize();

        $this->assertEquals($enrolmentid, $response['id']);
        $this->assertEquals($enrolid, $response['enrolid']);
        $this->assertEquals($courseid, $response['courseid']);

        $this->assertArrayHasKey('_links',$response);
        $this->assertArrayHasKey('self',$response['_links']);
        $this->assertArrayHasKey('_embedded',$response);
        $this->assertArrayHasKey('course',$response['_embedded']);
        $this->assertArrayHasKey('user',$response['_embedded']);
    }

    /**
     * @group !current
     */
    public function test_create_enrolment(){
        global $DB;

        $courseid = $this->course->id;
        $user = $this->getDataGenerator()->create_user();

        $body = [
            'enrolment' => [
                'enrol' => 'manual',
                'userid' => $user->id,
                'timeend' => strtotime('+20 days'),
            ],
        ];

        $request = $this->mock_request('POST', "/v3/courses/$courseid/user-enrolments", [], $body);
        $controller = new user_enrolment_controller();
        $args = ['courseid' => $courseid];
        $response = $controller->create_enrolment($request, $args)->getPayload()->jsonSerialize();

        $enrolment = $DB->get_record('user_enrolments', ['id' => $response['id']], '*', MUST_EXIST);

        $this->assertEquals(ENROL_USER_ACTIVE, $response['status']);
        $this->assertEquals(ENROL_USER_ACTIVE, $enrolment->status);
        $this->assertEquals($user->id, $response['userid']);
        $this->assertEquals($user->id, $enrolment->userid);
        $this->assertEquals($body['enrolment']['timeend'], $response['timeend']);
        $this->assertEquals($body['enrolment']['timeend'], $enrolment->timeend);
    }

    /**
     * @group !current
     */
    public function test_create_enrolment_error(){
        global $DB;

        $courseid = $this->course->id;

        $body = [
            'enrolment' => [
                'timeend' => strtotime('+20 days'),
            ],
        ];

        $request = $this->mock_request('POST', "/v3/courses/$courseid/user-enrolments", [], $body);
        $controller = new user_enrolment_controller();
        $args = ['courseid' => $courseid];

        $this->expectException(api_exception::class);
        $controller->create_enrolment($request, $args)->getPayload()->jsonSerialize();
    }

    /**
     * @group !current
     */
    public function test_update_enrolment(){
        global $DB;

        $courseid = $this->course->id;
        $enrolid = $this->instances['manual']->id;
        $enrolmentid = $DB->get_field('user_enrolments', 'id', ['enrolid' => $enrolid, 'status' => ENROL_USER_ACTIVE], IGNORE_MULTIPLE);

        $body = [
            'enrolment' => [
                'status' => ENROL_USER_SUSPENDED,
                'timeend' => 0,
            ],
        ];

        $request = $this->mock_request('PATCH',"/v3/courses/$courseid/user-enrolments/$enrolmentid", [], $body);
        $controller = new user_enrolment_controller();

        $args = ['courseid' => $courseid, 'enrolmentid' => $enrolmentid];
        $response = $controller->update_enrolment($request, $args)->jsonSerialize();

        $enrolment = $DB->get_record('user_enrolments', ['id' => $response['id']], '*', MUST_EXIST);

        $this->assertEquals(ENROL_USER_SUSPENDED, $response['status']);
        $this->assertEquals(ENROL_USER_SUSPENDED, $enrolment->status);
        $this->assertEquals($enrolid, $response['enrolid']);
        $this->assertEquals($enrolid, $enrolment->enrolid);
        $this->assertEquals($body['enrolment']['timeend'], $response['timeend']);
        $this->assertEquals($body['enrolment']['timeend'], $enrolment->timeend);
    }


    /**
     * @group !current
     */
    public function test_delete_enrolment(){
        global $DB;

        $courseid = $this->course->id;
        $enrolid = $this->instances['manual']->id;
        $enrolmentid = $DB->get_field('user_enrolments', 'id', ['enrolid' => $enrolid, 'status' => ENROL_USER_ACTIVE], IGNORE_MULTIPLE);

        $request = $this->mock_request('DELETE',"/v3/courses/$courseid/user-enrolments/$enrolmentid");
        $controller = new user_enrolment_controller();
        $args = ['courseid' => $courseid, 'enrolmentid' => $enrolmentid];
        $response = $controller->delete_enrolment($request, $args);

        $this->assertInstanceOf(EmptyResponse::class, $response);
        $this->assertFalse($DB->record_exists('user_enrolments', ['id' => $enrolmentid]));
    }
}
