<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8"/>
        <title>Test Documentation</title>
        <style>
            body {
                text-rendering: optimizeLegibility;
                font-variant-ligatures: common-ligatures;
                font-kerning: normal;
                margin-left: 2em;
                background-color: #ffffff;
                color: #000000;
            }

            body > ul > li {
                font-family: Source Serif Pro, PT Sans, Trebuchet MS, Helvetica, Arial;
                font-size: 2em;
            }

            h2 {
                font-family: Tahoma, Helvetica, Arial;
                font-size: 3em;
            }

            ul {
                list-style: none;
                margin-bottom: 1em;
            }
        </style>
    </head>
    <body>
        <h2 id="local_lfapi\category_controller_test">category_controller_test (local_lfapi\category_controller_test)</h2>
        <ul>
            <li style="color: #555753;">✓ List categories single page</li>
            <li style="color: #555753;">✓ List categories single page depth two</li>
            <li style="color: #555753;">✓ List categories paginated iteration</li>
            <li style="color: #555753;">✓ Get category</li>
            <li style="color: #555753;">✓ Get category not found</li>
            <li style="color: #555753;">✓ Create category</li>
            <li style="color: #555753;">✓ Create categories</li>
            <li style="color: #555753;">✓ Update category</li>
            <li style="color: #555753;">✓ Delete category</li>
        </ul>
        <h2 id="local_lfapi\course_completion_controller_test">course_completion_controller_test (local_lfapi\course_completion_controller_test)</h2>
        <ul>
            <li style="color: #555753;">✓ List completions single page</li>
            <li style="color: #555753;">✓ List completions single page</li>
            <li style="color: #555753;">✓ List completions specific course single page</li>
            <li style="color: #555753;">✓ List completions nonexistent course single page</li>
            <li style="color: #555753;">✓ Get completion</li>
            <li style="color: #555753;">✓ Get completion not found</li>
        </ul>
        <h2 id="local_lfapi\course_controller_test">course_controller_test (local_lfapi\course_controller_test)</h2>
        <ul>
            <li style="color: #555753;">✓ List courses single page</li>
            <li style="color: #555753;">✓ List courses paginated iteration</li>
            <li style="color: #555753;">✓ List courses paginated iteration</li>
            <li style="color: #555753;">✓ Get course</li>
            <li style="color: #555753;">✓ Get course not found</li>
            <li style="color: #555753;">✓ Create course</li>
            <li style="color: #555753;">✓ Create courses</li>
            <li style="color: #555753;">✓ Update courses</li>
            <li style="color: #555753;">✓ Update courses error</li>
            <li style="color: #555753;">✓ Update courses warning</li>
            <li style="color: #555753;">✓ Update course</li>
            <li style="color: #555753;">✓ Delete course</li>
        </ul>
        <h2 id="local_lfapi\enrol_controllers_test">enrol_controllers_test (local_lfapi\enrol_controllers_test)</h2>
        <ul>
            <li style="color: #555753;">✓ List instances</li>
            <li style="color: #555753;">✓ Get instance</li>
            <li style="color: #555753;">✓ Get instance not found</li>
            <li style="color: #555753;">✓ List enrolments single page</li>
            <li style="color: #555753;">✓ List enrolments specific enrol</li>
            <li style="color: #555753;">✓ Get enrolment</li>
            <li style="color: #555753;">✓ Create enrolment</li>
            <li style="color: #555753;">✓ Create enrolment error</li>
            <li style="color: #555753;">✓ Update enrolment</li>
            <li style="color: #555753;">✓ Delete enrolment</li>
        </ul>
        <h2 id="local_lfapi\setup_test">setup_test (local_lfapi\setup_test)</h2>
        <ul>
            <li style="color: #ef2929;">❌ Create default api users task</li>
            <li style="color: #555753;">✓ Create credentials for default api users</li>
        </ul>
        <h2 id="local_lfapi\user_controller_test">user_controller_test (local_lfapi\user_controller_test)</h2>
        <ul>
            <li style="color: #555753;">✓ List users single page</li>
            <li style="color: #555753;">✓ List users single page filter by custom fields</li>
            <li style="color: #555753;">✓ List categories paginated iteration</li>
            <li style="color: #555753;">✓ Get user</li>
            <li style="color: #555753;">✓ Get user not found</li>
            <li style="color: #555753;">✓ Create user</li>
            <li style="color: #555753;">✓ Create users</li>
            <li style="color: #555753;">✓ Update users</li>
            <li style="color: #555753;">✓ Update user</li>
            <li style="color: #555753;">✓ Delete user</li>
        </ul>
    </body>
</html>