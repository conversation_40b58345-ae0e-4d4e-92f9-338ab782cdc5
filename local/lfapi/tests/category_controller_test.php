<?php namespace local_lfapi;


defined('MOODLE_INTERNAL') || die();

global $CFG;

require_once(__DIR__ . '/traits/mocked_request_trait.php');

use \advanced_testcase;
use \Laminas\Diactoros\Response\EmptyResponse;
use \local_lfapi\traits\mocked_request_trait;
use \local_lfapi\v3\controllers\category_controller;
use local_lfapi\v3\exceptions\http_exception;

class category_controller_test extends advanced_testcase {

    use mocked_request_trait;

    /**
     * This property holds all the created categories separated in groups.
     *
     * Groups:
     * - group_a: 20 visible, top-level with idnumber prefix "ABC-"
     * - group_b: 10 non-visible, top-level with idnumber prefix "ABC-"
     * - group_c: 10 visible subcategories (depth 2) with idnumber prefix "ABC-"
     * - group_d: 10 visible, top-level without idnumber prefix (e.g., "ID-")
     *
     * @var array
     */
    protected $categories = [];
    
    protected function setUp(): void{
        $this->resetAfterTest();
        $this->setAdminUser();

        // Group A: 20 visible, top-level categories with idnumber prefix "ABC-".
        $this->categories['group_a'] = [];
        for ($i = 1; $i <= 20; $i++) {
            $cat = $this->getDataGenerator()->create_category([
                'name'         => 'Group A Category ' . $i,
                'idnumber'     => 'ABC-' . $i,
                'parent'       => 0,
                'coursecount'  => rand(0, 10),
                'depth'        => 1,
                'visible'      => 1,
                'timemodified' => time() - ($i * 60)
            ]);
            $this->categories['group_a'][$cat->id] = $cat;
        }

        // Group B: 10 non-visible, top-level categories with idnumber prefix "ABC-".
        $this->categories['group_b'] = [];
        for ($i = 21; $i <= 30; $i++) {
            $cat = $this->getDataGenerator()->create_category([
                'name'         => 'Group B Category ' . $i,
                'idnumber'     => 'ABC-' . $i,
                'parent'       => 0,
                'coursecount'  => rand(0, 10),
                'depth'        => 1,
                'visible'      => 0,
                'timemodified' => time() - ($i * 60)
            ]);
            $this->categories['group_b'][$cat->id] = $cat;
        }

        // Group C: 10 visible subcategories.
        // They will have a parent chosen from Group A and a depth of 2.
        $this->categories['group_c'] = [];
        // Get all Group A category ids.
        $group_a_ids = array_map(function($cat) {
            return $cat->id;
        }, $this->categories['group_a']);

        for ($i = 31; $i <= 40; $i++) {
            // Randomly select a parent from group_a.
            $parent = $group_a_ids[array_rand($group_a_ids)];
            $cat = $this->getDataGenerator()->create_category([
                'name'         => 'Group C Subcategory ' . $i,
                'idnumber'     => 'ABC-' . $i,
                'parent'       => $parent,
                'coursecount'  => rand(0, 10),
                'visible'      => 1,
                'timemodified' => time() - ($i * 60)
            ]);
            $this->categories['group_c'][$cat->id] = $cat;
        }

        // Group D: 10 visible, top-level categories without idnumber prefix.
        $this->categories['group_d'] = [];
        for ($i = 41; $i <= 50; $i++) {
            $cat = $this->getDataGenerator()->create_category([
                'name'         => 'Group D Category ' . $i,
                'idnumber'     => 'ID-' . $i, // No "ABC-" prefix.
                'parent'       => 0,
                'coursecount'  => rand(0, 10),
                'depth'        => 1,
                'visible'      => 1,
                'timemodified' => time() - ($i * 60)
            ]);
            $this->categories['group_d'][$cat->id] = $cat;
        }
    }

    /**
     * @group !current
     */
    public function test_list_categories_single_page() {
        // Build initial query parameters for filtering:
        //   - idnumber like "ABC-*"
        //   - visible equals 1.
        parse_str("idnumber[like]=ABC-*&visible[eq]=1", $params);
        // Add pagination limit parameter.
        $params['_limit'] = 50;
        
        // Create the initial request using the mocked request trait.
        $request = $this->mock_request('GET', '/v3/categories', $params);

        $controller = new category_controller();
        $response = $controller->list_categories($request)->jsonSerialize();

        $this->assertCount(30, $response['_embedded']['categories'], "Expected 30 categories matching the filter criteria.");
    }

    /**
     * @group !current
     */
    public function test_list_categories_single_page_depth_two() {
        // Build initial query parameters for filtering:
        //   - Depth > 1
        parse_str("depth[gt]=1", $params);
        // Add pagination limit parameter.
        $params['_limit'] = 50;
        
        // Create the initial request using the mocked request trait.
        $request = $this->mock_request('GET', '/v3/categories', $params);

        $controller = new category_controller();
        $response = $controller->list_categories($request)->jsonSerialize();

        $categories = [];
        foreach ($response['_embedded']['categories'] as $cat){
            /** @var \local_lfapi\v3\response\resources\entities\category_resource */
            $categories[$cat->get_attribute('id')] = $cat;
        }

        $this->assertCount(10, $response['_embedded']['categories'], "Expected 10 categories with depth > 1");
        $this->assertEmpty(array_diff(array_keys($categories), array_keys($this->categories['group_c'])));
        $this->assertEmpty(array_diff(array_keys($this->categories['group_c']), array_keys($categories)));
    }

    /**
     * @group !current
     */
    public function test_list_categories_paginated_iteration(){
        // Build initial query parameters for filtering:
        //   - idnumber like "ABC-*"
        //   - only visible categories.
        //   - 5 per page
        parse_str("idnumber[like]=ABC-*&visible[eq]=1", $params);
        $params['_limit'] = 5;

        $request = $this->mock_request('GET', '/v3/categories', $params);

        $controller = new category_controller();
        $all_categories = [];

        do {
            $response = $controller->list_categories($request)->jsonSerialize();

            // Assert that the response
            $this->assertArrayHasKey('_embedded', $response, 'Response must have _embedded data.');
            $this->assertArrayHasKey('categories', $response['_embedded'], 'Embedded data must contain categories.');

            foreach ($response['_embedded']['categories'] as $cat){
                /** @var \local_lfapi\v3\response\resources\entities\category_resource */
                $all_categories[$cat->get_attribute('id')] = $cat;
            }

            // Check if a next page is available.
            if (isset($response['_links']['next']['href'])) {
                $next_url = $response['_links']['next']['href'];
                $parsed_url = parse_url($next_url);

                $path = isset($parsed_url['path']) ? $parsed_url['path'] : '/v3/categories';
                $next_params = [];
                if (isset($parsed_url['query'])) {
                    parse_str($parsed_url['query'], $next_params);
                }

                $request = $this->mock_request('GET', $path, $next_params);
            } else {
                $request = null;
            }
        } while ($request !== null);

        $this->assertCount(30, $all_categories, "Expected 30 categories matching the filter criteria. Group A + Group C");
    }


    /**
     * @group !current
     */
    public function test_get_category(){
        $category = reset($this->categories['group_a']);
        $request = $this->mock_request('GET', "/v3/categories/$category->id");
        $controller = new category_controller();

        $response = $controller->get_category($request, ['categoryid' => $category->id])->jsonSerialize();
        $this->assertEquals($category->id, $response['id']);
        $this->assertArrayHasKey('_links', $response);
        $this->assertArrayHasKey('self', $response['_links']);
    }

    /**
     * @group !current
     */
    public function test_get_category_not_found(){
        $request = $this->mock_request('GET', "/v3/categories/0");
        $controller = new category_controller();

        $this->expectException(http_exception::class);
        $this->expectExceptionMessage(get_string('exception:category_not_found', 'local_lfapi'));
        $controller->get_category($request, ['categoryid' => 0])->jsonSerialize();
    }

    /**
     * @group !current
     */
    public function test_create_category(){
        global $DB;

        $body = [
            'category' => [
                'name' => 'Test category',
                'idnumber' => 'test-cat',
                'description' => "Lorem ipsum...",
            ],
        ];
        $request = $this->mock_request('POST', '/v3/categories', [], $body);
        $controller = new category_controller();
        $response = $controller->create_category($request)->getPayload()->jsonSerialize();

        $this->assertArrayHasKey('id', $response);
        $this->assertArrayHasKey('name', $response);
        $this->assertArrayHasKey('_links', $response);
        $this->assertArrayHasKey('self', $response['_links']);

        $category = $DB->get_record('course_categories', ['id' => $response['id']]);
        $this->assertNotEmpty($category);
        $this->assertEquals($body['category']['name'], $category->name);
    }

    /**
     * @group !current
     */
    public function test_create_categories(){
        global $DB;

        $parent = reset($this->categories['group_a']);

        $body = [
            'categories' => [
                [
                    'name' => 'Test category',
                    'idnumber' => 'test-cat',
                    'parent' => 0,
                    'description' => "Lorem ipsum...",
                ],
                [
                    'name' => 'Test category 2',
                    'idnumber' => 'test-cat-2',
                    'parent' => $parent->id,
                    'description' => "Lorem ipsum...",
                ]
            ],
        ];
        $request = $this->mock_request('POST', '/v3/categories', [], $body);
        $controller = new category_controller();
        $response = $controller->create_category($request)->getPayload()->jsonSerialize();

        $this->assertArrayHasKey('_embedded', $response);
        $this->assertArrayHasKey('categories', $response['_embedded']);

        /** @var \local_lfapi\v3\response\resources\entities\category_resource */
        foreach ($response['_embedded']['categories'] as $index => $category) {
            $category = $DB->get_record('course_categories', ['id' => $category->get_attribute('id')]);
            $this->assertNotEmpty($category);
            $this->assertEquals($body['categories'][$index]['name'], $category->name);
            $this->assertEquals($body['categories'][$index]['idnumber'], $category->idnumber);
            $this->assertEquals($body['categories'][$index]['parent'], $category->parent);
        }
    }

    /**
     * @group !current
     */
    public function test_update_category(){
        global $DB;

        $parent = reset($this->categories['group_d']);
        $category = reset($this->categories['group_c']);

        $body = [
            'category' => [
                'name' => 'Edited',
                'idnumber' => 'edited',
                'parent' => $parent->id,
                'description' => "Ipsum Lorem",
            ],
        ];
        $request = $this->mock_request('PATCH', '/v3/categories/' . $category->id, [], $body);
        $controller = new category_controller();
        $response = $controller->update_category($request, ['categoryid' => $category->id]);

        $this->assertInstanceOf(EmptyResponse::class, $response);

        $category = $DB->get_record('course_categories', ['id' => $category->id]);
        $this->assertNotEmpty($category);
        $this->assertEquals($body['category']['name'], $category->name);
        $this->assertEquals($body['category']['idnumber'], $category->idnumber);
        $this->assertEquals($body['category']['parent'], $category->parent);
    }

    /**
     * @group !current
     */
    public function test_delete_category(){
        global $DB;

        $category = reset($this->categories['group_c']);

        $request = $this->mock_request('DELETE', '/v3/categories/' . $category->id);
        $controller = new category_controller();
        $response = $controller->delete_category($request, ['categoryid' => $category->id]);

        $this->assertInstanceOf(EmptyResponse::class, $response);

        $category = $DB->get_record('course_categories', ['id' => $category->id]);
        $this->assertEmpty($category);
    }
}