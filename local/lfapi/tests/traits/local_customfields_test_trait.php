<?php namespace local_lfapi\traits;

/**
 * To be deleted
 */
trait local_customfields_test_trait {

    /**
     * Updates all tables, creating columns to represent.
     * This function calls the function that runs during the
     * plugin install.
     *
     * @return void
     */
    protected function update_local_customfields_columns(){
        global $CFG;
        require_once($CFG->dirroot . '/local/customfields/db/install.php');

        xmldb_local_customfields_install_custom_fields(false);
    }

    /**
     * Simulates an user_created_event for local_customfields
     *
     * @param integer $userid
     * @return void
     */
    protected function create_user_local_customfields(int $userid){
        $event = \core\event\user_created::create_from_userid($userid);
        \local_customfields\observer\user::created($event, $wait = false);
    }

    /**
     * Simulates an course_created for local_customfields
     *
     * @param integer $userid
     * @return void
     */
    protected static function create_course_local_customfields(object $course){
        $event = \core\event\course_created::create([
            'objectid' => $course->id,
            'context' => \context_course::instance($course->id),
            'other' => [
                'shortname' => $course->shortname,
                'fullname' => $course->fullname
            ],
        ]);

        \local_customfields\observer\course::created($event, $wait = false);
    }
}

