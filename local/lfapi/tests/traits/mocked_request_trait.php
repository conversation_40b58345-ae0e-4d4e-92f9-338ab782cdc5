<?php namespace local_lfapi\traits;

use \moodle_url;
// use \GuzzleHttp\Psr7\ServerRequest;
use \Laminas\Diactoros\ServerRequest;
use \Psr\Http\Message\ServerRequestInterface;

trait mocked_request_trait {

    protected function mock_request(string $method, string $relative_url, array $query_params = [], array $body = [], array $headers = []) : ServerRequestInterface {
        global $CFG;

        $uri = "$CFG->wwwroot/webservice/api/" . trim($relative_url, '/');
        return new ServerRequest([], [], $uri, $method, 'php://input', $headers, [], $query_params, $body);
    }
}

