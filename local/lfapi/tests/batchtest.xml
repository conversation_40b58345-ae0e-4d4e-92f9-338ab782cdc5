<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="" tests="12" assertions="48" errors="0" warnings="0" failures="0" skipped="0" time="25.584702">
    <testsuite name="local_lfapi_testsuite" tests="12" assertions="48" errors="0" warnings="0" failures="0" skipped="0" time="25.584702">
      <testsuite name="local_lfapi\batch\category_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\category_controller_batch_test.php" tests="4" assertions="16" errors="0" warnings="0" failures="0" skipped="0" time="4.143253">
        <testcase name="test_create_single_category" class="local_lfapi\batch\category_controller_batch_test" classname="local_lfapi.batch.category_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\category_controller_batch_test.php" line="29" assertions="5" time="0.717167"/>
        <testcase name="test_create_ten_categories" class="local_lfapi\batch\category_controller_batch_test" classname="local_lfapi.batch.category_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\category_controller_batch_test.php" line="36" assertions="5" time="0.846051"/>
        <testcase name="test_create_a_hundred_categories" class="local_lfapi\batch\category_controller_batch_test" classname="local_lfapi.batch.category_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\category_controller_batch_test.php" line="43" assertions="5" time="2.283585"/>
        <testcase name="test_create_a_thousand_categories" class="local_lfapi\batch\category_controller_batch_test" classname="local_lfapi.batch.category_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\category_controller_batch_test.php" line="50" assertions="1" time="0.296450"/>
      </testsuite>
      <testsuite name="local_lfapi\batch\course_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\course_controller_batch_test.php" tests="4" assertions="16" errors="0" warnings="0" failures="0" skipped="0" time="8.275803">
        <testcase name="test_create_single_course_with_custom_fields" class="local_lfapi\batch\course_controller_batch_test" classname="local_lfapi.batch.course_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\course_controller_batch_test.php" line="62" assertions="5" time="0.968700"/>
        <testcase name="test_create_ten_courses_with_custom_fields" class="local_lfapi\batch\course_controller_batch_test" classname="local_lfapi.batch.course_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\course_controller_batch_test.php" line="69" assertions="5" time="1.368301"/>
        <testcase name="test_create_a_hundred_courses_with_custom_fields" class="local_lfapi\batch\course_controller_batch_test" classname="local_lfapi.batch.course_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\course_controller_batch_test.php" line="76" assertions="5" time="5.319369"/>
        <testcase name="test_create_a_thousand_courses_with_custom_fields" class="local_lfapi\batch\course_controller_batch_test" classname="local_lfapi.batch.course_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\course_controller_batch_test.php" line="83" assertions="1" time="0.619434"/>
      </testsuite>
      <testsuite name="local_lfapi\batch\user_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\user_controller_batch_test.php" tests="4" assertions="16" errors="0" warnings="0" failures="0" skipped="0" time="13.165646">
        <testcase name="test_create_a_single_user_with_custom_fields" class="local_lfapi\batch\user_controller_batch_test" classname="local_lfapi.batch.user_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\user_controller_batch_test.php" line="65" assertions="5" time="1.111046"/>
        <testcase name="test_create_ten_users_with_custom_fields" class="local_lfapi\batch\user_controller_batch_test" classname="local_lfapi.batch.user_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\user_controller_batch_test.php" line="72" assertions="5" time="2.279871"/>
        <testcase name="test_create_a_hundred_users_with_custom_fields" class="local_lfapi\batch\user_controller_batch_test" classname="local_lfapi.batch.user_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\user_controller_batch_test.php" line="79" assertions="5" time="9.271682"/>
        <testcase name="test_create_a_thousand_users_with_custom_fields" class="local_lfapi\batch\user_controller_batch_test" classname="local_lfapi.batch.user_controller_batch_test" file="C:\wamp64\www\moodle420\local\lfapi\tests\batch\user_controller_batch_test.php" line="86" assertions="1" time="0.503047"/>
      </testsuite>
    </testsuite>
  </testsuite>
</testsuites>
