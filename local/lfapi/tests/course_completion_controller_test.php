<?php namespace local_lfapi;

defined('MOODLE_INTERNAL') || die();

global $CFG;

require_once(__DIR__ . '/traits/mocked_request_trait.php');

use \advanced_testcase;
use \Laminas\Diactoros\Response\EmptyResponse;
use \local_lfapi\traits\mocked_request_trait;
use \local_lfapi\v3\controllers\course_completion_controller;
use \completion_completion;
use local_lfapi\v3\exceptions\http_exception;

class course_completion_controller_test extends advanced_testcase {

    use mocked_request_trait;

    protected object $course;
    protected array $users;

    protected function setUp(): void{
        $this->resetAfterTest();
        $this->setAdminUser();

        set_debugging(DEBUG_NONE, false);
        $this->create_test_course();
        $this->create_test_users_and_completions();
        set_debugging(DEBUG_DEVELOPER, true);
    }

    protected function create_test_course(){
        $this->course = $this->getDataGenerator()->create_course(['enablecompletion' => 1]);
    }

    protected function create_test_users_and_completions(){
        global $DB;

        // GROUP A: 10 users that completed the course
        // enrolled a month ago
        // started yesterday
        // completed today
        $this->users['group_a'] = [];
        for($i=1;$i<=10;$i++){
            $user = $this->getDataGenerator()->create_user();

            $ccompletion = new completion_completion(['course' => $this->course->id, 'userid' => $user->id]);
            $ccompletion->mark_enrolled(strtotime('-1 month'));
            $ccompletion->mark_inprogress(strtotime('-10 day'));
            $ccompletion->mark_complete();

            $this->users['group_a'] = $user;
        }

        // GROUP B: 10 users that started the course
        // enrolled yesterday
        // started today
        $this->users['group_b'] = [];
        for($i=11;$i<=20;$i++){
            $user = $this->getDataGenerator()->create_user();

            $ccompletion = new completion_completion(['course' => $this->course->id, 'userid' => $user->id]);
            $ccompletion->mark_enrolled(strtotime('-1 day'));
            $ccompletion->mark_inprogress(time());

            $this->users['group_b'] = $user;
        }

        // GROUP C: 5 users that are not even enrolled
        $this->users['group_c'] = [];
        for($i=21;$i<=25;$i++){
            $user = $this->getDataGenerator()->create_user();
            $this->users['group_c'] = $user;
        }
    }

    /**
     * @group current
     */
    public function test_list_completions_single_page(){
        $today = strtotime('today');
        parse_str("timestarted[gte]=$today", $params);
        $params['_limit'] = 50;

        $request = $this->mock_request('GET',"/v3/course-completions", $params);
        $controller = new course_completion_controller();
        $response = $controller->list_completions($request, [])->jsonSerialize();

        $this->assertArrayHasKey('_embedded', $response);
        $this->assertArrayHasKey('completions', $response['_embedded']);
        $this->assertCount(10,$response['_embedded']['completions'], 'Expected 10 completions started today');
    }

    /**
     * @group current
     */
    public function test_list_completions_single_page2(){
        parse_str("timecompleted[gt]=0", $params);
        $params['_limit'] = 50;

        $request = $this->mock_request('GET',"/v3/course-completions", $params);
        $controller = new course_completion_controller();
        $response = $controller->list_completions($request, [])->jsonSerialize();

        $this->assertArrayHasKey('_embedded', $response);
        $this->assertArrayHasKey('completions', $response['_embedded']);
        $this->assertCount(10,$response['_embedded']['completions'], 'Expected 10 completed completions');
    }

    /**
     * @group current
     */
    public function test_list_completions_specific_course_single_page(){
        parse_str("timecompleted[gt]=0&courseid=" . $this->course->id, $params);
        $params['_limit'] = 50;

        $request = $this->mock_request('GET',"/v3/course-completions", $params);
        $controller = new course_completion_controller();
        $response = $controller->list_completions($request, [])->jsonSerialize();

        $this->assertArrayHasKey('_embedded', $response);
        $this->assertArrayHasKey('completions', $response['_embedded']);
        $this->assertCount(10,$response['_embedded']['completions'], 'Expected 10 completed completions');
    }

    /**
     * @group current
     */
    public function test_list_completions_nonexistent_course_single_page(){
        parse_str("timecompleted[gt]=0&courseid=-10", $params);
        $params['_limit'] = 50;
        
        $request = $this->mock_request('GET',"/v3/course-completions", $params);
        $controller = new course_completion_controller();
        $response = $controller->list_completions($request, [])->jsonSerialize();

        $this->assertArrayHasKey('_embedded', $response);
        $this->assertArrayHasKey('completions', $response['_embedded']);
        $this->assertCount(0,$response['_embedded']['completions'], 'Expected 0 completions');
    }

    /**
     * @group current
     */
    public function test_get_completion(){
        global $DB;

        $courseid = $this->course->id;
        $completionid = $DB->get_field('course_completions', 'id', ['course' => $courseid], IGNORE_MULTIPLE);

        $request = $this->mock_request('GET',"/v3/course-completions/$completionid");
        $controller = new course_completion_controller();

        $args = ['completionid' => $completionid];
        $response = $controller->get_completion($request, $args)->jsonSerialize();

        $completion = $DB->get_record('course_completions', ['id' => $completionid], '*', MUST_EXIST);

        $this->assertEquals($completion->userid, $response['userid']);
        $this->assertEquals($completion->timeenrolled, $response['timeenrolled']);
        $this->assertEquals($completion->timestarted, $response['timestarted']);
        $this->assertEquals($completion->timecompleted, $response['timecompleted']);
        $this->assertEquals($completion->course, $response['courseid']);
    }

    /**
     * @group current
     */
    public function test_get_completion_not_found(){
        global $DB;

        $courseid = $this->course->id;
        $completionid = 0;

        $request = $this->mock_request('GET',"/v3/course-completions/$completionid");
        $controller = new course_completion_controller();

        $this->expectException(http_exception::class);
        $args = ['courseid' => $courseid, 'completionid' => $completionid];
        $controller->get_completion($request, $args)->jsonSerialize();
    }
}
