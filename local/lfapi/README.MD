# API do LearningFlix

## Tokens de acesso:
Após o cron executar a tarefa adhoc `\local_lfapi\task\create_default_api_users_task`, dois usuário serão criados.
Os tokens destes usuários serão gerados automaticamente e serão exibidos na página:
> Administração do site -> Plugins -> Plugins Locais -> Gerenciar API do LearningFlix

## Documentação (V1)

https://epicbrasil.atlassian.net/wiki/spaces/LP1/pages/3627450394/API+do+LearningFlix

### Endpoint de cursos
https://epicbrasil.atlassian.net/wiki/spaces/LP1/pages/3627122741/Endpoint+de+Cursos
### Endpoint de matrículas manuais
https://epicbrasil.atlassian.net/wiki/spaces/LP1/pages/3627253806/Endpoint+de+Matr+culas+Manuais
### Endpoint de usuários
https://epicbrasil.atlassian.net/wiki/spaces/LP1/pages/3627155506/Endpoint+de+Usu+rios

## Documentação (V3)
`WWW_ROOT/webservice/api/docs` (Swagger UI)