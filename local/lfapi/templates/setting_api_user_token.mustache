<div class="form-group d-flex align-items-center mb-3">
    <input type="text"
           name="{{name}}"
           id="{{id}}"
           {{#value}} value="********************************"{{/value}}
           {{^value}} value="{{default}}"{{/value}}
           size="{{size}}"
           class="form-control flex-grow-1 mr-2"
           disabled>
    <button id="{{id}}_copybutton"
            type="button"
            class="btn btn-primary"
            data-toggle="tooltip"
            data-placement="right"
            title="{{#str}}copy{{/str}}">
        {{#str}}copy{{/str}}
    </button>
</div>

{{#js}}
require(['jquery'], function($, notification) {
    let copyButton = $('#{{id}}_copybutton');
    let value = "{{value}}";

    if(copyButton.tooltip){
        copyButton.tooltip();
    }

    copyButton.click(function(event) {
        event.preventDefault();

        if (!navigator.clipboard || !value) {
            return;
        }
        
        navigator.clipboard.writeText(value)
            .then(function() {
                copyButton.attr('data-original-title', "{{#str}} copied, local_lfapi{{/str}}").tooltip('show');

                setTimeout(function(){
                    copyButton.tooltip('hide');
                }, 2000);
            })
    });
});
{{/js}}