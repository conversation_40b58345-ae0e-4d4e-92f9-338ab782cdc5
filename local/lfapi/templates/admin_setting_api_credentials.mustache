<div class="form-group d-flex align-items-center mb-3">
    <label for="{{id}}_client_id"
           class="form-label mb-0 me-2"
           style="min-width: 8rem;">client_id</label>
    <input type="text"
           name="{{name}}_client_id"
           id="{{id}}_client_id"
           {{#has_credentials}} value="{{client_id}}"{{/has_credentials}}
           {{^has_credentials}} value="{{#str}}settings:client_credentials_empty, local_lfapi{{/str}}"{{/has_credentials}}
           size="{{size}}"
           class="form-control flex-grow-1"
           disabled>
</div>

<div class="form-group d-flex align-items-center mb-3">
    <label for="{{id}}_client_secret"
           class="form-label mb-0 me-2"
           style="min-width: 8rem;">client_secret</label>
    <input type="text"
           id="{{id}}_client_secret"
           name="{{name}}_client_secret"
           value="{{#str}}settings:client_credentials_secret_empty, local_lfapi{{/str}}"
           size="{{size}}"
           class="form-control flex-grow-1 me-2 mr-2"
           disabled>
    <button type="button"
            id="{{id}}_regenerate_button"
            class="btn btn-warning"
            data-toggle="tooltip"
            {{#has_credentials}}
            title="{{#str}}settings:regenerate_credentials, local_lfapi{{/str}}"
            data-has-credentials="true"
            {{/has_credentials}}
            {{^has_credentials}}
            title="{{#str}}settings:generate_credentials, local_lfapi{{/str}}"
            data-has-credentials="false"
            {{/has_credentials}}
            data-user="{{user}}"
            ">
        {{#has_credentials}}{{#str}}settings:regenerate_credentials, local_lfapi{{/str}}{{/has_credentials}}
        {{^has_credentials}}{{#str}}settings:generate_credentials, local_lfapi{{/str}}{{/has_credentials}}
    </button>
</div>


{{#js}}
require(['jquery', 'core/ajax', 'core/notification'], function($, Ajax, Notification) {
    $(document).ready(function() {
        const regenerate_button = $('#{{id}}_regenerate_button');

        if(regenerate_button.tooltip){
            regenerate_button.tooltip();
        }

        regenerate_button.on('click', function(event) {
            event.preventDefault();

            const user = $(this).data('user');
            $(this).prop('disabled', true);

            Ajax.call([{
                methodname: 'local_lfapi_regenerate_credentials',
                args: {
                    user: user
                }
            }])[0].done(function(response) {
                const client_id = response.client_id;
                const client_secret = response.client_secret;

                Notification.addNotification({
                    message: '{{#str}}settings:regenerate_credentials_success, local_lfapi{{/str}}',
                    type: 'success'
                });

                $('#{{id}}_client_id').val(client_id);
                $('#{{id}}_client_secret').val(client_secret);

            }).fail(function(error) {
                Notification.addNotification({
                    message: '{{#str}}settings:regenerate_credentials_error, local_lfapi{{/str}}',
                    type: 'error'
                });
            });
        });
    });
});
{{/js}}
