<?php

function local_lfapi_webservice_api_register_routes($router){
    global $CFG;
    include($CFG->dirroot . '/local/lfapi/routes.php');
}

/**
 * Returns an array of paths (to files or directories) that
 * contain OpenAI annotations or attributes that must be
 * included in the automatically generated documentation.
 * 
 * @return string[]
 */
function local_lfapi_webservice_api_openapi_definitions(){
    global $CFG;

    return [
       "$CFG->dirroot/local/lfapi/classes/v3/openapi",
       "$CFG->dirroot/local/lfapi/classes/v3/response",
    ];
}