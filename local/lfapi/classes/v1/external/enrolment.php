<?php namespace local_lfapi\v1\external;

require_once($CFG->dirroot . '/course/externallib.php');
require_once($CFG->dirroot . '/enrol/manual/externallib.php');
require_once($CFG->libdir . "/externallib.php");

defined('MOODLE_INTERNAL') || die();

use \core_external\external_api;
use \core_external\external_warnings;
use \core_external\external_value;
use \core_external\external_single_structure;
use \core_external\external_function_parameters;
use \core_external\external_multiple_structure;

\local_lfapi\v1\features\debug_info::force(); // Not cool, but the version 1 is deprecated anyways

class enrolment extends \enrol_manual_external {
    public static function enrol_users($enrolments){
        global $DB, $CFG;

        require_once($CFG->libdir . '/enrollib.php');

        $params = self::validate_parameters(self::enrol_users_parameters(),
                array('enrolments' => $enrolments));

        $transaction = $DB->start_delegated_transaction();

        $responses = [];

        // Retrieve the manual enrolment plugin.
        $enrol = enrol_get_plugin('manual');
        if (empty($enrol)) {
            throw new \moodle_exception('manualpluginnotinstalled', 'enrol_manual');
        }

        foreach ($params['enrolments'] as $enrolment) {

            $current_response = [
                'userid' => $enrolment['userid'],
                'courseid' => $enrolment['courseid'],
                'roleid' => $enrolment['roleid']
            ];

            try {
                // Ensure the current user is allowed to run this function in the enrolment context.
                $context = \context_course::instance($enrolment['courseid'], IGNORE_MISSING);
                self::validate_context($context);

                // Check that the user has the permission to manual enrol.
                require_capability('enrol/manual:enrol', $context);

                // Throw an exception if user is not able to assign the role.
                $roles = get_assignable_roles($context);
                if (!array_key_exists($enrolment['roleid'], $roles)) {
                    $errorparams = new \stdClass();
                    $errorparams->roleid = $enrolment['roleid'];
                    $errorparams->courseid = $enrolment['courseid'];
                    $errorparams->userid = $enrolment['userid'];
                    throw new \moodle_exception('wsusercannotassign', 'enrol_manual', '', $errorparams);
                }

                // Check manual enrolment plugin instance is enabled/exist.
                $instance = null;
                $enrolinstances = enrol_get_instances($enrolment['courseid'], true);
                foreach ($enrolinstances as $courseenrolinstance) {
                    if ($courseenrolinstance->enrol == "manual") {
                        $instance = $courseenrolinstance;
                        break;
                    }
                }
                if (empty($instance)) {
                    $errorparams = new \stdClass();
                    $errorparams->courseid = $enrolment['courseid'];
                    throw new \moodle_exception('wsnoinstance', 'enrol_manual', $errorparams);
                }

                // Check that the plugin accept enrolment (it should always the case, it's hard coded in the plugin).
                if (!$enrol->allow_enrol($instance)) {
                    $errorparams = new \stdClass();
                    $errorparams->roleid = $enrolment['roleid'];
                    $errorparams->courseid = $enrolment['courseid'];
                    $errorparams->userid = $enrolment['userid'];
                    throw new \moodle_exception('wscannotenrol', 'enrol_manual', '', $errorparams);
                }

                // Finally proceed the enrolment.
                $enrolment['timestart'] = isset($enrolment['timestart']) ? $enrolment['timestart'] : 0;
                $enrolment['timeend'] = isset($enrolment['timeend']) ? $enrolment['timeend'] : 0;
                $enrolment['status'] = (isset($enrolment['suspend']) && !empty($enrolment['suspend'])) ?
                        ENROL_USER_SUSPENDED : ENROL_USER_ACTIVE;

                $enrol->enrol_user($instance, $enrolment['userid'], $enrolment['roleid'],
                        $enrolment['timestart'], $enrolment['timeend'], $enrolment['status']);

                $current_response['error'] = false;

            } catch (\moodle_exception $me) {
                $current_response['error'] = $me->getMessage();
            } finally {
                $responses[] = $current_response;
            }
        }

        $transaction->allow_commit();

        return $responses;
    }





    public static function unenrol_users($enrolments) {
        global $CFG, $DB;
        $params = self::validate_parameters(self::unenrol_users_parameters(), array('enrolments' => $enrolments));
        require_once($CFG->libdir . '/enrollib.php');
        $transaction = $DB->start_delegated_transaction(); // Rollback all enrolment if an error occurs.
        $enrol = enrol_get_plugin('manual');
        if (empty($enrol)) {
            throw new \moodle_exception('manualpluginnotinstalled', 'enrol_manual');
        }

        $responses = [];

        foreach ($params['enrolments'] as $enrolment) {

            $current_response = [
                'userid' => $enrolment['userid'],
                'courseid' => $enrolment['courseid']
            ];


            try {
                $context = \context_course::instance($enrolment['courseid']);
                self::validate_context($context);
                require_capability('enrol/manual:unenrol', $context);
                $instance = $DB->get_record('enrol', array('courseid' => $enrolment['courseid'], 'enrol' => 'manual'));
                if (!$instance) {
                    throw new \moodle_exception('wsnoinstance', 'enrol_manual', $enrolment);
                }
                $user = $DB->get_record('user', array('id' => $enrolment['userid']));
                if (!$user) {
                    throw new \invalid_parameter_exception('User id not exist: '.$enrolment['userid']);
                }
                if (!$enrol->allow_unenrol($instance)) {
                    throw new \moodle_exception('wscannotunenrol', 'enrol_manual', '', $enrolment);
                }
                $enrol->unenrol_user($instance, $enrolment['userid']);

                $current_response['error'] = false;

            } catch (\moodle_exception $me) {
                $current_response['error'] = $me->getMessage();
            } finally {
                $responses[] = $current_response;
            }
        }

        $transaction->allow_commit();

        return $responses;
    }




    /**
     * Returns description of method result value.
     *
     * @return \external_description
     */
    public static function enrol_users_returns() {
        return new \external_multiple_structure(
            new \external_single_structure(
                array(
                    'userid' => new \external_value(PARAM_INT, 'id of the user'),
                    'courseid' => new \external_value(PARAM_INT, 'id of the course'),
                    'roleid' => new \external_value(PARAM_INT, "id of the user's role on the course"),
                    'error' => new \external_value(PARAM_RAW, 'error description, if any, during enrolment', VALUE_OPTIONAL)
                )
            )
        );
    }




    /**
     * Returns description of method result value.
     *
     * @return \external_description
     */
    public static function unenrol_users_returns() {
        return new \external_multiple_structure(
            new \external_single_structure(
                array(
                    'userid' => new \external_value(PARAM_INT, 'id of the user'),
                    'courseid' => new \external_value(PARAM_INT, 'id of the course'),
                    'error' => new \external_value(PARAM_RAW, 'error description, if any, during enrolment', VALUE_OPTIONAL)
                )
            )
        );
    }


    /**
     * Returns description of method parameters.
     *
     * @return \external_function_parameters
     * @since Moodle 2.2
     */
    public static function get_enrolment_details_parameters() {
        return new \external_function_parameters(
                array(
                    'enrolments' => new \external_multiple_structure(
                            new \external_single_structure(
                                    array(
                                        'roleid' => new \external_value(PARAM_INT, 'Role to assign to the user', VALUE_OPTIONAL),
                                        'userid' => new \external_value(PARAM_INT, 'The user that is going to be enrolled'),
                                        'courseid' => new \external_value(PARAM_INT, 'The course to enrol the user role in'),
                                    )
                            )
                    )
                )
        );
    }

    /**
     * Returns information about a users enrolment.
     *
     * Function throw an exception at the first error encountered.
     * @param array $enrolments  An array of user enrolment
     * @since Moodle 2.2
     */
    public static function get_enrolment_details($enrolments) {
        // global $DB, $CFG;

        $params = self::validate_parameters(self::get_enrolment_details_parameters(),
                array('enrolments' => $enrolments));

        $responses = [];

        foreach ($params['enrolments'] as $enrolment) {

            $context = \context_course::instance($enrolment['courseid'], IGNORE_MISSING);
            require_capability('enrol/manual:enrol', $context);

            $tempDetails = null;

            try {
                $tempDetails = (array) self::get_user_enrolment_details($enrolment['userid'], $enrolment['courseid']);

            } catch (\Throwable $th) {
                $tempDetails = [
                    'userid' => $enrolment['userid'],
                    'courseid' => $enrolment['courseid'],
                    'error' => $th->getMessage()
                ];

            }

            //Adds user first access to the course:
            $tempDetails['timefirstaccess'] = self::get_user_oldest_access_to_course($enrolment['userid'], $enrolment['courseid']);
            
            $responses[] = $tempDetails;
        }

        return $responses;
    }





    /**
     * Returns description of method result value.
     *
     * @return \external_description
     */
    public static function get_enrolment_details_returns() {
        return new \external_multiple_structure(
            new \external_single_structure(
                array(
                    'userid' => new \external_value(PARAM_INT, 'id of the user'),
                    'courseid' => new \external_value(PARAM_INT, 'id of the course'),
                    'roleid' => new \external_value(PARAM_INT, 'Role to assign to the user', VALUE_OPTIONAL),
                    'error' => new \external_value(PARAM_RAW, 'error description, if any, during enrolment', VALUE_OPTIONAL),
                    'roleshortname' => new \external_value(PARAM_INT, 'Role to assign to the user', VALUE_OPTIONAL),
                    'timefirstaccess' => new \external_value(PARAM_INT, 'Timestamp of the first access of the user to the course', VALUE_OPTIONAL),
                    'timecreated' => new \external_value(PARAM_INT, 'Timestamp of the date when the enrol as first created', VALUE_OPTIONAL),
                    'timestart' => new \external_value(PARAM_INT, 'Timestamp of the when the enrolment will start / has started', VALUE_OPTIONAL),
                    'timeend' => new \external_value(PARAM_INT, 'Timestamp of the when the enrolment will end / has ended', VALUE_OPTIONAL),
                    'status' => new \external_value(PARAM_RAW, 'Abstraction of the current status of the enrolment', VALUE_OPTIONAL),
                )
            )
        );
    }

    

    protected static function get_user_enrolment_details($userid, $courseid, $roleid = null){

        global $DB;

        $statuses = [
            ENROL_USER_ACTIVE => 'USER_ACTIVE',
            ENROL_USER_SUSPENDED => 'USER_SUSPENDED',
        ];

        $sql = "SELECT 
                    ue.userid AS userid,
                    c.id AS courseid,
                    r.id AS roleid, 
                    ue.status,
                    ue.timecreated,
                    ue.timestart,
                    ue.timeend
                FROM {user_enrolments} ue
                    JOIN {enrol} e ON e.id = ue.enrolid
                    JOIN {role_assignments} ra ON ra.userid = ue.userid
                    JOIN {context} ct ON ct.id = ra.contextid AND ct.contextlevel = 50
                    JOIN {course} c ON c.id = ct.instanceid AND e.courseid = c.id
                    JOIN {role} r ON r.id = ra.roleid
                WHERE ue.userid = :userid AND c.id = :courseid";

        $params = [
            'courseid' => $courseid,
            'userid' => $userid
        ];

        if(!empty($roleid)){
            $sql .= " AND ra.roleid = :roleid";
            $params['roleid'] = $roleid;
        }

        $sql .= " ORDER BY r.sortorder LIMIT 1";

        $details = $DB->get_record_sql($sql, $params);

        if(empty($details)){
            throw new \Exception("Enrolment not found!");
        }

        //Converting the status to something more readable;
        $details->status = isset($statuses[$details->status]) ? $statuses[$details->status] : 'UNKNOWN'; 

        return $details;
    }



    protected static function get_user_oldest_access_to_course($userid, $courseid)
    {
        // \core\event\user_list_viewed
        global $DB;

        $sql = "SELECT timecreated AS timefirstaccess
                FROM {logstore_standard_log}
                WHERE courseid = :courseid
                    AND userid = :userid
                    AND `action` = 'viewed'
                ORDER BY timecreated ASC
                LIMIT 1";
        
        $params = [
            'courseid' => $courseid,
            'userid' => $userid
        ];

        $record = $DB->get_record_sql($sql, $params);

        if(empty($record->timefirstaccess)){
            return null;
        }

        return $record->timefirstaccess;
    }

}