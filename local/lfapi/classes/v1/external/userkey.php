<?php namespace local_lfapi\v1\external;

require_once($CFG->libdir . "/externallib.php");

defined('MOODLE_INTERNAL') || die();

use \core_external\external_api;
use local_lfapi\v1\external\userkey\userkey_fallback_api;

\local_lfapi\v1\features\debug_info::force(); // Not cool, but the version 1 is deprecated anyways

class userkey extends external_api {

    /** @var external_api */
    private static $api;

    private static function get_external_api() : external_api {
        global $CFG;

        if(!isset(static::$api)){
            $userkey_service_path = "$CFG->dirroot/auth/userkey/externallib.php";
            if(file_exists($userkey_service_path)){
                require_once($userkey_service_path);
                static::$api = new \auth_userkey_external();
            }else{
                static::$api = new userkey_fallback_api();
            }
        }

        return static::$api;
    }

    public static function request_login_url_parameters() {
        $api = static::get_external_api();
        return $api::request_login_url_parameters();
    }

    public static function request_login_url($user) {
        $api = static::get_external_api();
        return $api::request_login_url($user);
    }

    public static function request_login_url_returns() {
        $api = static::get_external_api();
        return $api::request_login_url_returns();
    }
    
}