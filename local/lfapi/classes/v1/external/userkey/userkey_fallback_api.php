<?php namespace local_lfapi\v1\external\userkey;


require_once($CFG->libdir . "/externallib.php");

defined('MOODLE_INTERNAL') || die();

use \core_external\external_api;
use \core_external\external_warnings;
use \core_external\external_value;
use \core_external\external_single_structure;
use \core_external\external_function_parameters;
use \core_external\external_multiple_structure;
use \webservice_access_exception;

class userkey_fallback_api extends external_api {

    public static function request_login_url_parameters() {
        return new external_function_parameters([
            'user' => new external_single_structure([
                'username' => new external_value(
                    PARAM_USERNAME,
                    'Username'
                ),
                'email' => new external_value(
                    PARAM_EMAIL,
                    'A valid email address'
                ),
                'idnumber' => new external_value(
                    PARAM_RAW,
                    'An arbitrary ID code number perhaps from the institution'
                ),
            ])
        ]);
    }

    public function request_login_url($user) {
        throw new webservice_access_exception(get_string('v1:auth_userkey_not_available', 'local_lfapi'));
    }

    public static function request_login_url_returns() {
        return new external_single_structure(
            array(
                'loginurl' => new external_value(PARAM_RAW, 'Login URL for a user to log in'),
            )
        );
    }
}
