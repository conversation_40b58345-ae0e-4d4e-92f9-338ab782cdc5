<?php namespace local_lfapi\v1\features;

use \local_lfapi\config;

/**
 * This class helps to display debug information on lfapi
 * web service calls, on web service errors
 */
class debug_info{

    protected static function is_web_service_call() : bool {
        return defined('WS_SERVER') && (bool) WS_SERVER;
    }

    protected static function is_calling_lfapi_service() : bool {
        $wsfunction = optional_param('wsfunction', null, PARAM_RAW);
        return empty($wsfunction) || config::has_external_function($wsfunction);
    }

    /**
     * Forces debug on web service calls, simulating the DEBUG_DEVELOPER
     * - Only if calling a lfapi service
     *
     * @return void
     */
    public static function force(){
        global $CFG;

        if(!self::is_web_service_call() || !self::is_calling_lfapi_service()){
            return;
        }

        if(!config::is_debug_info_enabled()){
            $CFG->debug = DEBUG_NONE;
            $CFG->debugdisplay = 0;
            return;
        }

        @ini_set('display_errors', 1);
        @error_reporting(DEBUG_DEVELOPER);
        $CFG->debug = DEBUG_DEVELOPER;
        $CFG->debugdisplay = 1;
    }
}