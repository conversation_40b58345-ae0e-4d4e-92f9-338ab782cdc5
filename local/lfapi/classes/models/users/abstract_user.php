<?php namespace local_lfapi\models\users;

use \local_lfapi\config;
use \moodle_exception;
use \coding_exception;
use \webservice_api\services\oauth2_credentials_service;
use \webservice_api\models\auth\oauth2_credentials;

abstract class abstract_user{

    protected object $user;
    protected oauth2_credentials_service $credentials_service;

    protected function __construct(object $user) {
        $this->user = $user;
        $this->credentials_service = new oauth2_credentials_service();
    }


    /**
     * Defines the default data for this user
     *
     * @return array [
     *   "username" => "", // Required
     *   "firstname" => "",
     *   "lastname" => "",
     *   "email" => "",
     *   "description" => "",
     * ]
     */
    protected abstract static function define_user() : array;

    
    /**
     * Returns the archetype for the role that will be
     * assigned to the user
     *
     * @return string
     */
    protected abstract static function define_archetype() : string;


    protected static function get_username() : string {
        $definition = static::define_user();
        if(!empty($definition['username'])){
            return $definition['username'];
        }

        throw new coding_exception('exception:api_user_username_not_defined', 'local_lfapi');
    }

    function __get($property){
        return isset($this->user->$property) ? $this->user->$property : null;
    }

    function __set($property, $value){
        $this->user->$property = $value;
    }

    public static function get() : object {
        global $DB;

        if($user = $DB->get_record('user', ['username' => self::get_username()])){
            return new static($user);
        }

        throw new moodle_exception('exception:api_user_not_found', 'local_lfapi');
    }

    public static function exists() : bool {
        global $DB;
        return $DB->record_exists('user', ['username' => self::get_username()]);
    }

    public static function create() : object {
        global $DB, $CFG;

        require_once($CFG->dirroot."/user/lib.php");

        if(self::exists()){
            throw new moodle_exception('exception:api_user_already_exists', 'local_lfapi');
        }

        // Assembling user data
        $raw_user = static::define_user();
        $raw_user['auth'] = "manual";
        $raw_user['password'] = generate_password(20);
        $raw_user['confirmed'] = true;

        // Creating User
        $userid = user_create_user($raw_user, true, true);

        //Updating the policy agreed
        $DB->update_record('user', ['id' => $userid, 'policyagreed' => 1]);

        // Assigning role
        self::assign_roles($userid);

        return self::get();
    }

    public function delete() : bool {
        global $DB, $CFG;
        require_once($CFG->dirroot."/user/lib.php");

        user_delete_user($this->user);
        $DB->delete_records('user', ['id' => $this->user->id]);
        return true;
    }

    public function generate_token() : string {
        global $CFG;
        require_once($CFG->libdir . '/externallib.php');

        return external_generate_token(
            EXTERNAL_TOKEN_PERMANENT,
            config::SERVICE_NAME,
            $this->user->id,
            \context_system::instance()
        );
    }

    public function get_token() : ?object {
        global $DB;

        $params = [
            'serviceshortname' => config::SERVICE_SHORTNAME,
            'pluginname' => config::PLUGINNAME,
            'userid' => $this->user->id,
        ];

        $sql = "SELECT et.*
                FROM {external_services} es
                    JOIN {external_tokens} et ON (
                        es.id = et.externalserviceid
                    )
                WHERE es.shortname = :serviceshortname
                    AND es.component = :pluginname
                    AND et.userid = :userid";

        return $DB->get_record_sql($sql, $params, IGNORE_MULTIPLE) ?: null;
    }

    protected static function assign_roles(int $userid){
        global $DB;

        $archetype = static::define_archetype();
        $roles = $DB->get_records('role', ['archetype' => $archetype]);
        $context = \context_system::instance();

        foreach ($roles as $role) {
            role_assign($role->id, $userid, $context->id, config::PLUGINNAME);
        }
    }



    public function has_client_credentials() : bool {
        return $this->credentials_service->has_credentials($this->user->id);
    }

    public function get_client_credentials() : ?oauth2_credentials {
        return $this->credentials_service->get_user_credentials($this->user->id);
    }

    public function generate_client_credentials() : oauth2_credentials {
        if($credentials = $this->get_client_credentials()){
            return $this->credentials_service->regenerate_credentials($credentials->get('client_id'), 0);
        }

        return $this->credentials_service->generate_credentials($this->user->id, 0);
    }

    public function revoke_client_credentials(){
        $credentials = $this->get_client_credentials();
        $this->credentials_service->revoke_credentials($credentials->get('client_id'));
    }

}