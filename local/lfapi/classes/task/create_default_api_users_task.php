<?php namespace local_lfapi\task;

use \core\task\adhoc_task;
use \moodle_exception;
use \local_lfapi\models\users\admin_user;
use \local_lfapi\models\users\client_user;
use \local_lfapi\config;

class create_default_api_users_task extends adhoc_task {

    public function execute() {
        mtrace('Creating default LearningFlix API users...');

        // Getting or creating users
        $users = [
            admin_user::exists() ? admin_user::get() : admin_user::create(),
            client_user::exists() ? client_user::get() : client_user::create(),
        ];

        foreach ($users as $user) {
            try {
                if(!$user->get_token()){
                    $user->generate_token();
                }
                
            } catch (moodle_exception $th) {
                mtrace_exception($th);
                throw $th;
            }

            // API 3.0
            try {
                if(!$user->has_client_credentials()){
                    $user->generate_client_credentials();
                }
            } catch (\Throwable $th) {
                mtrace_exception($th);
                throw $th;
            }
        }

        config::set(config::CONFIG_API_USERS_CREATED, true);
    }


    protected static function create_task() : adhoc_task {
        $task = new static();
        $task->set_component('local_lfapi');
        $task->set_blocking(true);
        return $task;
    }

    public static function create_and_enqueue() : adhoc_task {
        $task = self::create_task();
        \core\task\manager::queue_adhoc_task($task, true);
        return $task;
    }
}