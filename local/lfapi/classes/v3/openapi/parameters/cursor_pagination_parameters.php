<?php namespace local_lfapi\v3\openapi\parameters;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Parameter(
    name: "_limit",
    in: "query",
    required: false,
    description: new lfapi_string('docs:pagination:_limit'),
    schema: new OA\Schema(type: "integer", default : 50)
)]
#[OA\Parameter(
    name: "_after",
    in: "query",
    required: false,
    description: new lfapi_string('docs:pagination:_after'),
    schema: new OA\Schema(type: "string")
)]
class cursor_pagination_parameters{}