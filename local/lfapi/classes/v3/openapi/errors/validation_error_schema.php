<?php namespace local_lfapi\v3\openapi\errors;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "validation_error",
    type: "object",
    title: new lfapi_string('docs:validation_error:title'),
    description: new lfapi_string('docs:validation_error:description'),
    required: ["message", "status"],
    properties: [
        new OA\Property(property: "message", type: "string"),
        new OA\Property(property: "status", type: "integer", example: 422)
    ]
)]
class validation_error_schema {}
