<?php namespace local_lfapi\v3\openapi\errors;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "generic_error",
    title: new lfapi_string('docs:generic_error:title'),
    description: new lfapi_string('docs:generic_error:description'),
    type: "object",
    required: ["message", "status"],
    properties: [
        new OA\Property(property: "message", type: "string"),
        new OA\Property(property: "status", type: "integer")
    ]
)]
class generic_error_schema {}
