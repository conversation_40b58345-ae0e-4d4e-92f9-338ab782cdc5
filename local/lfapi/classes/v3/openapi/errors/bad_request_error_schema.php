<?php namespace local_lfapi\v3\openapi\errors;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "bad_request_error",
    type: "object",
    title: new lfapi_string('docs:bad_request_error:title'),
    description: new lfapi_string('docs:bad_request_error:description'),
    required: ["message", "status"],
    properties: [
        new OA\Property(property: "message", type: "string"),
        new OA\Property(property: "status", type: "integer", example: 400)
    ]
)]
class bad_request_error_schema {}
