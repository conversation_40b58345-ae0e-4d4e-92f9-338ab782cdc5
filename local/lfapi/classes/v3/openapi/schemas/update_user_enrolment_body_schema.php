<?php namespace local_lfapi\v3\openapi\schemas;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'update_user_enrolment_body',
    title: new lfapi_string('docs:update_user_enrolment_body:title'),
    type: 'object',
    required: ['userid'],
    properties: [
        new OA\Property(
            property: 'userid',
            type: 'integer',
            description: new lfapi_string('docs:user_enrolment:userid')
        ),
        new OA\Property(
            property: 'roleid',
            type: 'integer',
            description: new lfapi_string('docs:user_enrolment:roleid')
        ),
        new OA\Property(
            property: 'enrolid',
            type: 'integer',
            description: new lfapi_string('docs:user_enrolment:enrolid')
        ),
        new OA\Property(
            property: 'status',
            type: 'integer',
            description: new lfapi_string('docs:user_enrolment:status')
        ),
        new OA\Property(
            property: 'timestart',
            type: 'integer', format: 'unix-timestamp',
            description: new lfapi_string('docs:user_enrolment:timestart')
        ),
        new OA\Property(
            property: 'timeend',
            type: 'integer', format: 'unix-timestamp',
            description: new lfapi_string('docs:user_enrolment:timeend')
        ),
    ]
)]
class update_user_enrolment_body_schema {}
