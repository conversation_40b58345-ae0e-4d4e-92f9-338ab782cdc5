<?php namespace local_lfapi\v3\openapi\schemas;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'update_category_body',
    title: new lfapi_string('docs:update_category_body:title'),
    type: 'object',
    properties: [
        new OA\Property(property: 'name', type: 'string', description: new lfapi_string('docs:category:name')),
        new OA\Property(property: 'parent', type: 'integer', description: new lfapi_string('docs:category:parent')),
        new OA\Property(property: 'idnumber', type: 'string', description: new lfapi_string('docs:category:idnumber')),
        new OA\Property(property: 'description', type: 'string', description: new lfapi_string('docs:category:description')),
        new OA\Property(property: 'descriptionformat', type: 'integer', description: new lfapi_string('docs:category:descriptionformat')),
        new OA\Property(property: 'theme', type: 'string', description: new lfapi_string('docs:category:theme'))
    ]
)]
class update_category_body_schema{}