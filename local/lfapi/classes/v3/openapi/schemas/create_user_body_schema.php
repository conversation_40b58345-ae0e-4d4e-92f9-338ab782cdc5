<?php namespace local_lfapi\v3\openapi\schemas;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'create_user_body',
    title: new lfapi_string('docs:create_user_body:title'),
    type: 'object',
    required: ['username', 'firstname', 'lastname', 'email'],
    properties: [
        new OA\Property(property: 'createpassword', type: 'boolean', description: new lfapi_string('docs:user:createpassword')),
        new OA\Property(property: 'username', type: 'string', description: new lfapi_string('docs:user:username')),
        new OA\Property(property: 'auth', type: 'string', description: new lfapi_string('docs:user:auth')),
        new OA\Property(property: 'password', type: 'string', description: new lfapi_string('docs:user:password')),
        new OA\Property(property: 'firstname', type: 'string', description: new lfapi_string('docs:user:firstname')),
        new OA\Property(property: 'lastname', type: 'string', description: new lfapi_string('docs:user:lastname')),
        new OA\Property(property: 'email', type: 'string', description: new lfapi_string('docs:user:email')),
        new OA\Property(property: 'maildisplay', type: 'integer', description: new lfapi_string('docs:user:maildisplay')),
        new OA\Property(property: 'city', type: 'string', description: new lfapi_string('docs:user:city')),
        new OA\Property(property: 'country', type: 'string', description: new lfapi_string('docs:user:country')),
        new OA\Property(property: 'timezone', type: 'string', description: new lfapi_string('docs:user:timezone')),
        new OA\Property(property: 'description', type: 'string', description: new lfapi_string('docs:user:description')),
        new OA\Property(property: 'firstnamephonetic', type: 'string', description: new lfapi_string('docs:user:firstnamephonetic')),
        new OA\Property(property: 'lastnamephonetic', type: 'string', description: new lfapi_string('docs:user:lastnamephonetic')),
        new OA\Property(property: 'middlename', type: 'string', description: new lfapi_string('docs:user:middlename')),
        new OA\Property(property: 'alternatename', type: 'string', description: new lfapi_string('docs:user:alternatename')),
        new OA\Property(property: 'interests', type: 'string', description: new lfapi_string('docs:user:interests')),
        new OA\Property(property: 'idnumber', type: 'string', description: new lfapi_string('docs:user:idnumber')),
        new OA\Property(property: 'institution', type: 'string', description: new lfapi_string('docs:user:institution')),
        new OA\Property(property: 'department', type: 'string', description: new lfapi_string('docs:user:department')),
        new OA\Property(property: 'phone1', type: 'string', description: new lfapi_string('docs:user:phone1')),
        new OA\Property(property: 'phone2', type: 'string', description: new lfapi_string('docs:user:phone2')),
        new OA\Property(property: 'address', type: 'string', description: new lfapi_string('docs:user:address')),
        new OA\Property(property: 'lang', type: 'string', description: new lfapi_string('docs:user:lang')),
        new OA\Property(property: 'calendartype', type: 'string', description: new lfapi_string('docs:user:calendartype')),
        new OA\Property(property: 'theme', type: 'string', description: new lfapi_string('docs:user:theme')),
        new OA\Property(property: 'mailformat', type: 'integer', description: new lfapi_string('docs:user:mailformat')),

        new OA\Property(
            property: 'customfields',
            type: 'array',
            description: new lfapi_string('docs:user:customfields'),
            items: new OA\Items(
                type: 'object',
                required: ['type', 'value'],
                properties: [
                    new OA\Property(property: 'type', type: 'string', description: new lfapi_string('docs:user:customfields:type')),
                    new OA\Property(property: 'value', type: 'string', description: new lfapi_string('docs:user:customfields:value'))
                ]
            )
        ),

        new OA\Property(
            property: 'preferences',
            type: 'array',
            description: new lfapi_string('docs:user:preferences'),
            items: new OA\Items(
                type: 'object',
                required: ['type', 'value'],
                properties: [
                    new OA\Property(property: 'type', type: 'string', description: new lfapi_string('docs:user:preferences:type')),
                    new OA\Property(property: 'value', type: 'string', description: new lfapi_string('docs:user:preferences:value'))
                ]
            )
        )
    ]
)]
class create_user_body_schema{}
