<?php namespace local_lfapi\v3\openapi\schemas;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'update_course_body',
    title: new lfapi_string('docs:update_course_body:title'),
    type: 'object',
    required: [],
    properties: [
        new OA\Property(
            property: 'id',
            description: new lfapi_string('docs:course:id'),
            type: 'integer'
        ),
        new OA\Property(
            property: 'fullname',
            description: new lfapi_string('docs:course:fullname'),
            type: 'string'
        ),
        new OA\Property(
            property: 'shortname',
            description: new lfapi_string('docs:course:shortname'),
            type: 'string'
        ),
        new OA\Property(
            property: 'categoryid',
            description: new lfapi_string('docs:course:categoryid'),
            type: 'integer'
        ),
        new OA\Property(property: 'idnumber', description: new lfapi_string('docs:course:idnumber'), type: 'string'),
        new OA\Property(property: 'summary', description: new lfapi_string('docs:course:summary'), type: 'string'),
        new OA\Property(property: 'summaryformat', description: new lfapi_string('docs:course:summaryformat'), type: 'integer'),
        new OA\Property(property: 'format', description: new lfapi_string('docs:course:format'), type: 'string'),
        new OA\Property(property: 'showgrades', description: new lfapi_string('docs:course:showgrades'), type: 'integer'),
        new OA\Property(property: 'newsitems', description: new lfapi_string('docs:course:newsitems'), type: 'integer'),
        new OA\Property(property: 'startdate', description: new lfapi_string('docs:course:startdate'), type: 'integer', format: 'unix-timestamp'),
        new OA\Property(property: 'enddate', description: new lfapi_string('docs:course:enddate'), type: 'integer', format: 'unix-timestamp'),
        new OA\Property(property: 'numsections', description: new lfapi_string('docs:course:numsections'), type: 'integer'),
        new OA\Property(property: 'maxbytes', description: new lfapi_string('docs:course:maxbytes'), type: 'integer'),
        new OA\Property(property: 'showreports', description: new lfapi_string('docs:course:showreports'), type: 'integer'),
        new OA\Property(property: 'visible', description: new lfapi_string('docs:course:visible'), type: 'integer'),
        new OA\Property(property: 'hiddensections', description: new lfapi_string('docs:course:hiddensections'), type: 'integer'),
        new OA\Property(property: 'groupmode', description: new lfapi_string('docs:course:groupmode'), type: 'integer'),
        new OA\Property(property: 'groupmodeforce', description: new lfapi_string('docs:course:groupmodeforce'), type: 'integer'),
        new OA\Property(property: 'defaultgroupingid', description: new lfapi_string('docs:course:defaultgroupingid'), type: 'integer'),
        new OA\Property(property: 'enablecompletion', description: new lfapi_string('docs:course:enablecompletion'), type: 'integer'),
        new OA\Property(property: 'completionnotify', description: new lfapi_string('docs:course:completionnotify'), type: 'integer'),
        new OA\Property(property: 'lang', description: new lfapi_string('docs:course:lang'), type: 'string'),
        new OA\Property(property: 'forcetheme', description: new lfapi_string('docs:course:forcetheme'), type: 'string'),

        new OA\Property(
            property: 'courseformatoptions',
            description: new lfapi_string('docs:course:courseformatoptions'),
            type: 'array',
            items: new OA\Items(
                type: 'object',
                required: ['name', 'value'],
                properties: [
                    new OA\Property(
                        property: 'name',
                        description: new lfapi_string('docs:course:courseformatoptions:name'),
                        type: 'string'
                    ),
                    new OA\Property(
                        property: 'value',
                        description: new lfapi_string('docs:course:courseformatoptions:value'),
                        type: 'string'
                    )
                ]
            )
        ),

        new OA\Property(
            property: 'customfields',
            description: new lfapi_string('docs:course:customfields'),
            type: 'array',
            items: new OA\Items(
                type: 'object',
                required: ['shortname', 'value'],
                properties: [
                    new OA\Property(
                        property: 'shortname',
                        description: new lfapi_string('docs:course:customfields:shortname'),
                        type: 'string'
                    ),
                    new OA\Property(
                        property: 'value',
                        description: new lfapi_string('docs:course:customfields:value'),
                        type: 'string'
                    )
                ]
            )
        )
    ]
)]
class update_course_body_schema{}
