<?php namespace local_lfapi\v3\services\enrolment;

require_once($CFG->libdir . '/enrollib.php');

use \context_course;
use local_lfapi\v3\exceptions\http_exception;
use \webservice_api\traits\request_params_trait;

class manual_enrol_service implements enrol_service_interface {

    use request_params_trait;

    protected static int $student_role;

    protected function get_student_role() : int {
        global $DB;

        if(!isset(self::$student_role)){
            self::$student_role = $DB->get_field('role', 'id', ['archetype' => 'student'], IGNORE_MULTIPLE) ?: 5;
        }

        return self::$student_role;
    }
    
    public function get_user_enrolment(int $id) : object {
        global $DB;
        if($enrol = $DB->get_record('user_enrolments', ['id' => $id])){
            return $enrol;
        }

        throw http_exception::fromString('exception:user_enrol_not_found', 'local_lfapi')->setStatusCode(404);
    }

    public function get_enrolment_by_instance_and_user(int $instanceid, int $userid) : object {
        global $DB;
        if($enrol = $DB->get_record('user_enrolments', ['enrolid' => $instanceid, 'userid' => $userid])){
            return $enrol;
        }

        throw http_exception::fromString('exception:user_enrol_not_found', 'local_lfapi')->setStatusCode(404);
    }

    public function get_enrol_instance(int $id) : object {
        global $DB;

        if($instance = $DB->get_record('enrol', ['id' => $id, 'enrol' => 'manual'], '*', IGNORE_MULTIPLE)){
            return $instance;
        }

        throw http_exception::fromString('exception:enrol_instance_not_found', 'local_lfapi')->setStatusCode(404);
    }

    public function find_available_enrol_instance(int $courseid) : object {
        global $DB;

        $conditions = ['courseid' => $courseid, 'status' => ENROL_INSTANCE_ENABLED, 'enrol' => 'manual'];
        if($instance = $DB->get_record('enrol', $conditions)){
            return $instance;
        }

        $ctx = (object)['courseid' => $courseid];
        throw http_exception::fromString('wsnoinstance', 'enrol_manual', $ctx)->setStatusCode(404);
    }

    public function enrol_user(int $courseid, array $enrolment = []) : object {
        global $DB;

        // Checking permissions
        $context = context_course::instance($courseid, IGNORE_MISSING);
        require_capability('enrol/manual:enrol', $context);

        // Validating parameters
        $userid = $this->required_param($enrolment, 'userid', PARAM_INT);
        $roleid = $this->optional_param($enrolment, 'roleid', PARAM_INT, $this->get_student_role());
        $enrolid = $this->optional_param($enrolment, 'enrolid', PARAM_INT, 0);
        $status = $this->optional_param($enrolment, 'status', PARAM_INT, ENROL_USER_ACTIVE);
        $timestart = $this->optional_param($enrolment, 'timestart', PARAM_INT, 0);
        $timeend = $this->optional_param($enrolment, 'timeend', PARAM_INT, 0);

        // Checking if user can assign roles
        if(!has_capability('moodle/role:assign', $context)){
            $ctx = (object)[
                'roleid' => $roleid,
                'userid' => $userid,
                'courseid' => $courseid,
            ];
            throw http_exception::fromString('wsusercannotassign', 'enrol_manual', $ctx)->setStatusCode(403);
        }

        // Checking enrol plugin
        $enrol = enrol_get_plugin('manual');
        if(empty($enrol)){
            throw http_exception::fromString('manualpluginnotinstalled', 'enrol_manual')->setStatusCode(500);
        }

        // Getting enrol instances
        if($enrolid !== 0){
            $instance = $this->get_enrol_instance($enrolid);
        }else{
            $instance = $this->find_available_enrol_instance($courseid);
        }

        if(!$enrol->allow_enrol($instance)){
            $ctx = (object)[
                'roleid' => $roleid,
                'userid' => $userid,
                'courseid' => $courseid,
            ];
            throw http_exception::fromString('wscannotenrol', 'enrol_manual', $ctx)->setStatusCode(403);
        }

        // Enrolling
        try {
            $transaction = $DB->start_delegated_transaction();
            $enrol->enrol_user($instance, $userid, $roleid, $timestart, $timeend, $status);
            $transaction->allow_commit();

            return $this->get_enrolment_by_instance_and_user($instance->id, $userid);

        } catch (\Exception $e) {
            /** @throws moodle_exception */
            $transaction->rollback($e);
        }        
    }

    public function update_user_enrolment(array $enrolment) : object {
        global $DB;

        // Retrieving instance
        $instance = $this->get_enrol_instance($enrolment['enrolid']);

        // Validating new data
        $userid = $this->required_param($enrolment, 'userid', PARAM_INT);
        $status = $this->optional_param($enrolment, 'status', PARAM_INT, null);
        $timestart = $this->optional_param($enrolment, 'timestart', PARAM_INT, null);
        $timeend = $this->optional_param($enrolment, 'timeend', PARAM_INT, null);

        // Checking permissions
        $context = context_course::instance($instance->courseid, IGNORE_MISSING);
        require_capability('enrol/manual:enrol', $context);

        // Checking enrol plugin
        $enrol = enrol_get_plugin('manual');
        if(empty($enrol)){
            throw http_exception::fromString('manualpluginnotinstalled', 'enrol_manual')->setStatusCode(500);
        }

        // Updating enrolment
        try {
            $transaction = $DB->start_delegated_transaction();
            $enrol->update_user_enrol($instance, $userid, $status, $timestart, $timeend);
            $transaction->allow_commit();

            return $this->get_enrolment_by_instance_and_user($instance->id, $userid);

        } catch (\Exception $e) {
            /** @throws moodle_exception */
            $transaction->rollback($e);
        }
    }

    public function unenrol_user(array $enrolment){
        global $DB;

        $instance = $this->get_enrol_instance($enrolment['enrolid']);
        $userid = $this->required_param($enrolment, 'userid', PARAM_INT);

        // Checking permissions
        $context = context_course::instance($instance->courseid, IGNORE_MISSING);
        require_capability('enrol/manual:unenrol', $context);

        // Checking enrol plugin
        $enrol = enrol_get_plugin('manual');
        if(empty($enrol)){
            throw http_exception::fromString('manualpluginnotinstalled', 'enrol_manual')->setStatusCode(500);
        }

        // Unenrolling user
        try {
            $transaction = $DB->start_delegated_transaction();
            $enrol->unenrol_user($instance, $userid);
            $transaction->allow_commit();

        } catch (\Exception $e) {
            /** @throws moodle_exception */
            $transaction->rollback($e);
        }
    }

}
