<?php namespace local_lfapi\v3\services\enrolment;

interface enrol_service_interface {
    public function get_user_enrolment(int $id) : object;

    public function get_enrol_instance(int $id) : object;

    public function enrol_user(int $courseid, array $enrolment = []) : object;

    public function update_user_enrolment(array $enrolment) : object;

    public function unenrol_user(array $enrolment);
}
