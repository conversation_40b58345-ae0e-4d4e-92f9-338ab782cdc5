<?php namespace local_lfapi\v3\filters;

use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\like_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\notlike_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\in_sql_condition;

class course_sql_filter extends lfapi_lhs_filter {

    public function __construct(array $query_params = []){
        if(isset($query_params['categoryid'])){
            $query_params['category'] = $query_params['categoryid'];
            unset($query_params['categoryid']);
        }

        parent::__construct($query_params);
    }

    /**
     * Defines the accepted fields
     *
     * @return array
     */
    protected function define_fields() : array {
        return [
            'id' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'category' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'idnumber' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    like_sql_condition::get_alias(),
                    notlike_sql_condition::get_alias(),
                ],
            ],
            'shortname' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    like_sql_condition::get_alias(),
                    notlike_sql_condition::get_alias(),
                ],
            ],
            'fullname' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    like_sql_condition::get_alias(),
                    notlike_sql_condition::get_alias(),
                ],
            ],
            'format' => [
                'type' => PARAM_PLUGIN,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'startdate' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'enddate' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'visible' => [
                'type' => PARAM_BOOL,
            ],
            'timecreated' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'timemodified' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'enablecompletion' => [
                'type' => PARAM_BOOL,
            ],
            'completionnotify' => [
                'type' => PARAM_BOOL,
            ],
        ];
    }
}
