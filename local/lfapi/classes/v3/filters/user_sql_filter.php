<?php namespace local_lfapi\v3\filters;

use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\isnull_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\notnull_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\like_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\in_sql_condition;

class user_sql_filter extends lfapi_lhs_filter {

    /**
     * Defines the accepted fields
     *
     * @return array
     */
    protected function define_fields() : array {
        return [
            'id' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'auth' => [
                'type' => PARAM_AUTH,
            ],
            'confirmed' => [
                'type' => PARAM_BOOL,
            ],
            'suspended' => [
                'type' => PARAM_BOOL,
            ],
            'deleted' => [
                'type' => PARAM_BOOL,
            ],
            'policyagreed' => [
                'type' => PARAM_BOOL,
            ],
            'username' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    like_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'idnumber' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    like_sql_condition::get_alias(),
                    isnull_sql_condition::get_alias(),
                    notnull_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'firstname' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    like_sql_condition::get_alias(),
                ],
            ],
            'email' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    like_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'timecreated' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'timemodified' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'firstaccess' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'lastaccess' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'lastlogin' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
        ];
    }
}
