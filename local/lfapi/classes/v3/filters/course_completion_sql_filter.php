<?php namespace local_lfapi\v3\filters;

use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\neq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\isnull_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\notnull_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\in_sql_condition;

class course_completion_sql_filter extends lfapi_lhs_filter {

    public function __construct(array $query_params = []){
        if(isset($query_params['courseid'])){
            $query_params['course'] = $query_params['courseid'];
            unset($query_params['courseid']);
        }
        
        parent::__construct($query_params);
    }

    /**
     * Defines the accepted fields
     *
     * @return array
     */
    protected function define_fields() : array {
        return [
            'course' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'userid' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'timeenrolled' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    neq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'timestarted' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    neq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'timecompleted' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    neq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                    isnull_sql_condition::get_alias(),
                    notnull_sql_condition::get_alias(),
                ],
            ],
        ];
    }
}
