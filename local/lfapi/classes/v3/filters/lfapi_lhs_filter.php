<?php namespace local_lfapi\v3\filters;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \Exception;
use \webservice_api\exceptions\validation_exception;

use \moodle_dev_utils\http\filters\lhs\lhs_filter;
use \moodle_dev_utils\http\filters\exceptions\forbidden_operator_exception;
use \moodle_dev_utils\http\filters\exceptions\invalid_operator_exception;
use \moodle_dev_utils\http\filters\exceptions\missing_required_field_exception;
use \moodle_dev_utils\http\filters\exceptions\invalid_condition_choice_exception;
use \moodle_dev_utils\http\filters\exceptions\invalid_condition_value_exception;

class lfapi_lhs_filter extends lhs_filter {

    public function set_condition(string $operator_alias, string $field, mixed $value = null) : static {
        try {
            return parent::set_condition($operator_alias, $field, $value);
        } catch (Exception $ex) {
            $this->throw_as_validation_exception($ex);
        }
    }

    protected function throw_as_validation_exception(Exception $ex){
        $plugin = 'local_lfapi';

        if($ex instanceof forbidden_operator_exception){
            $ctx = $ex->get_context();
            throw validation_exception::fromString('exception:operator_not_accepted', $plugin, $ctx);
        }

        if($ex instanceof invalid_operator_exception){
            $ctx = $ex->get_context();
            throw validation_exception::fromString('exception:invalid_filter_operator', $plugin, $ctx);
        }

        if($ex instanceof invalid_condition_value_exception){
            $ctx = $ex->get_context();
            throw validation_exception::fromString('exception:invalid_filter_value', $plugin, $ctx);
        }

        if($ex instanceof invalid_condition_choice_exception){
            $ctx = $ex->get_context();
            throw validation_exception::fromString('exception:filter_invalid_choice', $plugin, $ctx);
        }

        if($ex instanceof missing_required_field_exception){
            $ctx = $ex->get_context();
            throw validation_exception::fromString('exception:required_filter', $plugin, $ctx);
        }

        throw new validation_exception($ex->getMessage());
    }
}
