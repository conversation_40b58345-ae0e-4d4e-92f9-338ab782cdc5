<?php namespace local_lfapi\v3\filters;

use moodle_dev_utils\database\query\moodle_query;
use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\neq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\isnull_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\notnull_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\like_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\in_sql_condition;

class profile_field_sql_filter extends joinable_lhs_filter {

    public function __construct(array $raw_query_params = []){
        $prefix = 'profile_field_';
        $prefix_length = strlen($prefix);

        $query_params = [];
        foreach ($raw_query_params as $key => $value) {
            if (str_starts_with($key, $prefix)) {
                $query_params[substr($key, $prefix_length)] = $value;
            }
        }
        parent::__construct($query_params);
    }

    /**
     * Defines the accepted fields
     *
     * @return array
     */
    protected function define_fields() : array {
        global $DB;

        $definitions = [];

        foreach ($DB->get_records('user_info_field', null, '', 'id, shortname, datatype') as $field) {
            $definition = [
                'id' => $field->id,
            ];

            switch ($field->datatype) {
                case 'text':
                    $definition['type'] = PARAM_RAW;
                    $definition['operators'] = [
                        eq_sql_condition::get_alias(),
                        neq_sql_condition::get_alias(),
                        like_sql_condition::get_alias(),
                        isnull_sql_condition::get_alias(),
                        notnull_sql_condition::get_alias(),
                        in_sql_condition::get_alias(),
                    ];
                    break;
                case 'datetime':
                    $definition['type'] = PARAM_INT;
                    $definition['operators'] = [
                        eq_sql_condition::get_alias(),
                        gt_sql_condition::get_alias(),
                        gte_sql_condition::get_alias(),
                        lt_sql_condition::get_alias(),
                        lte_sql_condition::get_alias(),
                    ];
                    break;
                
                default:
                    $definition['type'] = PARAM_RAW;
                    $definition['operators'] = [
                        eq_sql_condition::get_alias(),
                    ];
                    break;
            }

            $definitions[$field->shortname] = $definition;
        }

        return $definitions;
    }

    public function apply_joins(moodle_query $query, string $userid_column){
        $definitions = $this->get_fields_definition();
        $joins = [];

        // Aggregating conditions per join
        foreach ($this->conditions as $condition) {
            $field = $condition->get_field();
            $fieldid = $definitions[$field]['id'];
            $alias = "pf_$field";

            if(!isset($joins[$field])){
                $joins[$field] = (object)[
                    'type' => 'INNER',
                    'on_condition' => [
                        "$alias.userid = $userid_column",
                        "$alias.fieldid = $fieldid",
                    ],
                    'table' => 'user_info_data',
                    'alias' => $alias,
                ];
            }

            $sql = str_replace("$alias.$field", "$alias.data", $condition->to_sql($alias));
            $joins[$field]->on_condition[] = $sql;
        }

        // Appying joins to the query
        foreach ($joins as $join) {
            $on_condition = implode(' AND ', $join->on_condition);
            $query->join($join->type, $join->table, $on_condition, $join->alias);
        }

        $query->set_params($this->get_parameters());
    }
}
