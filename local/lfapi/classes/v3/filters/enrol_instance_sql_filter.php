<?php namespace local_lfapi\v3\filters;

use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\like_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\in_sql_condition;

class enrol_instance_sql_filter extends basic_enrol_instance_sql_filter {

    /**
     * Defines the accepted fields
     *
     * @return array
     */
    protected function define_fields() : array {
        return array_merge(parent::define_fields(), [
            'enrolperiod' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ]
            ],
            'enrolstartdate' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ]
            ],
            'enrolenddate' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ]
            ],
            'name' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    like_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'status' => [
                'type' => PARAM_INT,
            ],
        ]);
    }
}
