<?php namespace local_lfapi\v3\filters;

use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\in_sql_condition;

class basic_enrol_instance_sql_filter extends lfapi_lhs_filter {

    /**
     * Defines the accepted fields
     *
     * @return array
     */
    protected function define_fields() : array {
        return [
            'courseid' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'enrol' => [
                'type' => PARAM_PLUGIN,
            ],
        ];
    }
}
