<?php namespace local_lfapi\v3\filters;

use \moodle_dev_utils\database\query\moodle_query;
use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\neq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\notlike_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\like_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\in_sql_condition;

use \local_lfapi\v3\traits\custom_course_field_trait;

class custom_course_fields_sql_filter extends joinable_lhs_filter {

    use custom_course_field_trait;

    protected array $original_params = [];

    public static function reset_cache(){
        unset(self::$definition_cache[static::class]);
    }

    public function __construct(array $raw_query_params = []){
        $prefix = 'custom_field_';
        $prefix_length = strlen($prefix);

        $query_params = [];
        foreach ($raw_query_params as $key => $value) {
            if (str_starts_with($key, $prefix)) {
                $query_params[substr($key, $prefix_length)] = $value;
            }
        }
       
        parent::__construct($query_params);
    }

    protected function define_fields() : array {
        $definitions = [];

        $fields = $this->get_custom_fields();
        
        foreach ($fields as $field) {
            $definition = [
                'id' => $field->get('id'),
                'controller' => $field,
                'datafield' => 'value',
            ];

            $data_controller = '\\customfield_' . $field->get('type') . '\\data_controller';
            if(class_exists($data_controller)){
                $data_controller = $data_controller::create(0, null, $field);
                $data_controller->set('id', 1);

                $definition['datafield'] = $data_controller->datafield();
            }

            switch ($field->get('type')) {
                case 'date':
                    $definition['type'] = PARAM_RAW;
                    $definition['operators'] = [
                        eq_sql_condition::get_alias(),
                        neq_sql_condition::get_alias(),
                        gt_sql_condition::get_alias(),
                        gte_sql_condition::get_alias(),
                        lt_sql_condition::get_alias(),
                        lte_sql_condition::get_alias(),
                        in_sql_condition::get_alias(),
                    ];
                    break;
                case 'text':
                    $definition['type'] = PARAM_RAW;
                    $definition['operators'] = [
                        eq_sql_condition::get_alias(),
                        neq_sql_condition::get_alias(),
                        like_sql_condition::get_alias(),
                        notlike_sql_condition::get_alias(),
                        in_sql_condition::get_alias(),
                    ];
                    break;
                case 'checkbox':
                    $definition['type'] = PARAM_BOOL;
                    $definition['operators'] = [
                        eq_sql_condition::get_alias(),
                    ];
                    break;
                case 'select':
                    $definition['choices'] = $definition['controller']->get_options();
                default:
                    $definition['type'] = PARAM_RAW;
                    $definition['operators'] = [
                        eq_sql_condition::get_alias(),
                        neq_sql_condition::get_alias(),
                    ];
                    break;
            }

            $definitions[$field->get('shortname')] = $definition;
        }

        return $definitions;
    }

    protected function get_custom_fields() : array {
        $course_handler = \core_course\customfield\course_handler::create();
        return $course_handler->get_fields();
    }

    protected function field_to_alias(string $field) : string {
        return "cf_$field";
    }

    protected function alias_to_field(string $alias) : string {
        return strpos($alias, 'cf_') === 0 ? substr($alias, 3) : $alias;
    }

    public function apply_joins(moodle_query $query, string $courseid_column){
        $definitions = $this->get_fields_definition();
        $joins = [];
       
        // Aggregating conditions per join
        foreach ($this->conditions as $condition) {
            $field = $condition->get_field();
            $fieldid = $definitions[$field]['id'];
            $alias = $this->field_to_alias($field);

            if(!isset($joins[$field])){
                $joins[$field] = (object)[
                    'type' => 'INNER',
                    'on_condition' => [
                        "$alias.instanceid = $courseid_column",
                        "$alias.fieldid = $fieldid",
                    ],
                    'table' => 'customfield_data',
                    'alias' => $alias,
                ];
            }

            $column = $definitions[$field]['datafield'];
            $sql = str_replace("$alias.$field", "$alias.$column", $condition->to_sql($alias));
            $joins[$field]->on_condition[] = $sql;
        }

        // Appying joins to the query
        foreach ($joins as $join) {
            $on_condition = implode(' AND ', $join->on_condition);
            $query->join($join->type, $join->table, $on_condition, $join->alias);
        }

        $query->set_params($this->get_parameters());
    }


    public function get_parameters() : array {
        $definitions = $this->get_fields_definition();
        $agg_params = [];

        foreach ($this->conditions as $condition) {
            if(!isset($agg_params[$condition->get_field()])){
                $agg_params[$condition->get_field()] = [];
            }

            $condition->append_param($agg_params[$condition->get_field()]);
        }

        $params = [];
        foreach ($agg_params as $field => $params) {
            foreach ($params as $alias => $value) {    
                if(empty($definitions[$field])){
                    $params[$alias] = $value;
                    continue; // Should not happen
                }
    
                $params[$alias] = $this->parse_custom_field_value($definitions[$field]['controller'], $value);
            }
        }

        return $params;
    }

}
