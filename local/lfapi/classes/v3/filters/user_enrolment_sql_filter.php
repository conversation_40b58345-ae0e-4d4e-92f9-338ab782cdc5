<?php namespace local_lfapi\v3\filters;

use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\in_sql_condition;

class user_enrolment_sql_filter extends lfapi_lhs_filter {

    /**
     * Defines the accepted fields
     *
     * @return array
     */
    protected function define_fields() : array {
        return [
            'userid' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'timestart' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'timeend' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'timecreated' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'timemodified' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'status' => [
                'type' => PARAM_INT,
            ],
            'enrolid' => [
                'type' => PARAM_INT,
            ],
        ];
    }
}
