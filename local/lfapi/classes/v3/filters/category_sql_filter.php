<?php namespace local_lfapi\v3\filters;

use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\neq_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\gte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lt_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\lte_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\like_sql_condition;
use \moodle_dev_utils\http\filters\lhs\conditions\in_sql_condition;

class category_sql_filter extends lfapi_lhs_filter {

    /**
     * Defines the accepted fields
     *
     * @return array
     */
    protected function define_fields() : array {
        return [
            'id' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'name' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    like_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'idnumber' => [
                'type' => PARAM_RAW,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    like_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'parent' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    neq_sql_condition::get_alias(),
                    in_sql_condition::get_alias(),
                ],
            ],
            'coursecount' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'depth' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
            'visible' => [
                'type' => PARAM_BOOL,
            ],
            'timemodified' => [
                'type' => PARAM_INT,
                'operators' => [
                    eq_sql_condition::get_alias(),
                    gt_sql_condition::get_alias(),
                    gte_sql_condition::get_alias(),
                    lt_sql_condition::get_alias(),
                    lte_sql_condition::get_alias(),
                ],
            ],
        ];
    }
}
