<?php namespace local_lfapi\v3\filters;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \moodle_dev_utils\database\query\moodle_query;

/**
 * This kind of LHS filter does not return conditions
 * to be appended to the WHERE clause.
 * 
 * It represents data that needs a join for its filtering
 * like custom profile fields.
 */
abstract class joinable_lhs_filter extends lfapi_lhs_filter {

    public function get_conditions(string $table = '') : string {
        throw new \coding_exception("This filter doesn't support get_conditions(). Use apply_joins()");
    }

    /**
     * Appends all necessary joins to a moodle_query
     * Also appends its parameters.
     *
     * @param moodle_query $query
     * @param string $join_column (The column to used in the ON condition)
     * @return void
     */
    abstract public function apply_joins(moodle_query $query, string $join_column);
}
