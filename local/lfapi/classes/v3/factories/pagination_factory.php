<?php namespace local_lfapi\v3\factories;

use Psr\Http\Message\ServerRequestInterface;
use \local_lfapi\config;
use \local_lfapi\v3\helpers\custom_moodle_url;
use \webservice_api\helpers\pagination\cursor_pagination_helper;

class pagination_factory {

    /**
     * Instanciates a cursor pagination helper
     * that is compliant with LHS Brackets.
     *
     * @param ServerRequestInterface $request
     * @return cursor_pagination_helper
     */
    public static function make_cursor_pagination_helper(ServerRequestInterface $request) : cursor_pagination_helper {
        $helper = new cursor_pagination_helper($request, config::get_page_size());
        $helper->set_max_limit(config::get_max_page_size(), true);
        $helper->override_url_class(custom_moodle_url::class);
        return $helper;
    }
}
