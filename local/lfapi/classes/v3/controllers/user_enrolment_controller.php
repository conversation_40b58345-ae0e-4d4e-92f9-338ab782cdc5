<?php namespace local_lfapi\v3\controllers;

use \webservice_api\controllers\abstract_controller;
use \local_lfapi\v3\factories\pagination_factory;
use \Psr\Http\Message\ServerRequestInterface;
use \Laminas\Diactoros\Response\JsonResponse;
use \Laminas\Diactoros\Response\EmptyResponse;
use \local_lfapi\v3\exceptions\http_exception;
use \local_lfapi\v3\filters\basic_enrol_instance_sql_filter;
use \local_lfapi\v3\filters\user_enrolment_sql_filter;
use \local_lfapi\v3\queries\get_course_query;
use local_lfapi\v3\queries\get_user_enrol_query;
use local_lfapi\v3\queries\get_user_query;
use local_lfapi\v3\queries\list_user_enrols_query;
use \local_lfapi\v3\response\resources\entities\course_resource;
use local_lfapi\v3\response\resources\entities\user_enrolment_resource;
use local_lfapi\v3\response\resources\entities\user_resource;
use \local_lfapi\v3\response\resources\paginated_hal_resource;
use local_lfapi\v3\services\enrolment\manual_enrol_service;
use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;
use \webservice_api\exceptions\validation_exception;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Tag(name: "Course Enrolments")]
class user_enrolment_controller extends abstract_controller {

    #[OA\Get(
        path: "/v3/courses/{courseid}/user-enrolments",
        summary: new lfapi_string('docs:list_user_enrolments'),
        description: new lfapi_string('docs:list_user_enrolments:description'),
        tags: ['Course Enrolments'],
        parameters: [
            new OA\Parameter(
                name: "courseid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:course:id'),
                schema: new OA\Schema(type: "integer")
            ),
            new OA\Parameter(
                name : 'userid',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_enrolment_sql_filter:userid', '[eq|in]'),
                schema : new OA\Schema(type: 'integer'),
            ),
            new OA\Parameter(
                name : 'timestart',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_enrolment_sql_filter:timestart', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name : 'timeend',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_enrolment_sql_filter:timeend', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name : 'timecreated',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_enrolment_sql_filter:timecreated', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name : 'timemodified',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_enrolment_sql_filter:timemodified', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name : 'status',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_enrolment_sql_filter:status', '[eq]'),
                schema : new OA\Schema(type: 'integer'),
            ),
            new OA\Parameter(
                name : 'enrolid',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_enrolment_sql_filter:enrolid', '[eq]'),
                schema : new OA\Schema(type: 'integer'),
            ),
            new OA\Parameter(
                name : 'enrol',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:basic_enrol_instance_sql_filter:enrol', '[eq]'),
                schema : new OA\Schema(type: 'string'),
            ),
            new OA\Parameter(ref: "#/components/parameters/_limit"),
            new OA\Parameter(ref: "#/components/parameters/_after")
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:list_user_enrolments:successful_response'),
                content: new OA\JsonContent(
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "_embedded",
                            type: "object",
                            required: ["enrolments"],
                            properties: [
                                new OA\Property(
                                    property: "enrolments",
                                    type: "array",
                                    items: new OA\Items(ref: "#/components/schemas/user_enrolment_resource")
                                )
                            ]
                        ),
                        new OA\Property(
                            property: "_links",
                            type: "object",
                            properties: [
                                new OA\Property(
                                    property: "self",
                                    type: "object",
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/courses/{courseid}/user-enrolments"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                ),
                                new OA\Property(
                                    property: "next",
                                    type: "object",
                                    nullable: true,
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/courses/{courseid}/user-enrolments?_after=50"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
        ]
    )]
    public function list_enrolments(ServerRequestInterface $request, array $args){
        $pagination_helper = pagination_factory::make_cursor_pagination_helper($request);
        $courseid = $this->required_param($args, 'courseid', PARAM_INT);

        // Assemblying filters
        $user_enrol_filter = user_enrolment_sql_filter::from_request($request);
        $instance_filter = basic_enrol_instance_sql_filter::from_request($request);
        $instance_filter->set_condition(eq_sql_condition::get_alias(), 'courseid', $courseid); // Forcing course

        // Get paginated list of enrolments
        $query = new list_user_enrols_query($this->db, $user_enrol_filter, $instance_filter);
        $enrolments_paginator = $query->get_paginator();
        $enrolments_paginator->set_limit($pagination_helper->get_limit());
        $enrolments_paginator->set_cursor($pagination_helper->get_cursor());

        // Retrieving and formatting enrolments
        $enrolments = [];
        foreach ($enrolments_paginator->get_generator() as $enrolment) {
            $enrolments[] = new user_enrolment_resource($enrolment);
        }

        // Building response
        $response = new paginated_hal_resource();
        $response->embed('enrolments', $enrolments);
        $response->add_self_link($pagination_helper->get_current_page_url());

        if($next_cursor = $enrolments_paginator->get_next_cursor()){
            $response->add_next_link($pagination_helper->make_next_page_url($next_cursor));
        }

        return $response;
    }

    #[OA\Get(
        path: "/v3/courses/{courseid}/user-enrolments/{enrolmentid}",
        summary: new lfapi_string('docs:get_user_enrolment'),
        tags: ['Course Enrolments'],
        parameters: [
            new OA\Parameter(
                name: "courseid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:course:id'),
                schema: new OA\Schema(type: "integer")
            ),
            new OA\Parameter(
                name: "enrolmentid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:user_enrolment:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:get_user_enrolment:successful_response'),
                content: new OA\JsonContent(ref: "#/components/schemas/user_enrolment_resource")
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:user_enrol_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
        ]
    )]
    public function get_enrolment(ServerRequestInterface $request, array $args = []){
        $enrolmentid = $this->required_param($args, 'enrolmentid', PARAM_INT);
        $courseid = $this->required_param($args, 'courseid', PARAM_INT);

        // Retrieving instance
        $query = new get_user_enrol_query($this->db, $enrolmentid, $courseid);
        if(!$enrol = $query->execute()){
            throw http_exception::fromString('exception:user_enrol_not_found', 'local_lfapi')->setStatusCode(404);
        }

        // Building response
        $response = new user_enrolment_resource($enrol);

        // Embedding course
        $course_query = new get_course_query($this->db, $enrol->courseid);
        if($course = $course_query->execute()){
            $course_resource = new course_resource($course);
            $response->embed('course', $course_resource);
        }

        // Embedding user
        $user_query = new get_user_query($this->db, $enrol->userid);
        if($user = $user_query->execute()){
            $user_resource = new user_resource($user);
            $response->embed('user', $user_resource);
        }

        return $response;
    }

    #[OA\Post(
        path: '/v3/courses/{courseid}/user-enrolments',
        summary: new lfapi_string('docs:create_enrolment'),
        tags: ['Course Enrolments'],
        parameters: [
            new OA\Parameter(
                name: "courseid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:course:id'),
                schema: new OA\Schema(type: "integer")
            ),
        ],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'enrolment',
                            ref: '#/components/schemas/create_user_enrolment_body'
                        )
                    ]
                ),
            )
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: new lfapi_string('docs:create_enrolment:successful_response'),
                
                content: new OA\JsonContent(ref: '#/components/schemas/user_enrolment_resource'),
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
        ]
    )]
    public function create_enrolment(ServerRequestInterface $request, array $args = []){
        $courseid = $this->required_param($args, 'courseid', PARAM_INT);

        // Validating params
        $body = $request->getParsedBody();

        if(empty($body['enrolment'])){
            $error = get_string('validation:missing_key', 'local_lfapi', (object) ['key' => 'enrolment']);
            throw new validation_exception($error, 400);
        }

        // Enrolling user
        $enrol = $this->optional_param($body['enrolment'], 'enrol', PARAM_PLUGIN, 'manual');

        switch ($enrol) {
            case 'manual':
                $enrolment_service = new manual_enrol_service();
                $enrolment = $enrolment_service->enrol_user($courseid, $body['enrolment']);
                break;
            
            default:
                throw validation_exception::fromString('exception:only_manual_enrol_supported', 'local_lfapi');
        }
        
        // Assembling response
        $response = new user_enrolment_resource($enrolment);
        return new JsonResponse($response, 201);
    }

    #[OA\Patch(
        path: '/v3/courses/{courseid}/user-enrolments/{enrolmentid}',
        summary: new lfapi_string('docs:update_enrolment'),
        tags: ['Course Enrolments'],
        parameters: [
            new OA\Parameter(
                name: "courseid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:course:id'),
                schema: new OA\Schema(type: "integer")
            ),
            new OA\Parameter(
                name: "enrolmentid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:user_enrolment:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'enrolment',
                            ref: '#/components/schemas/update_user_enrolment_body'
                        )
                    ]
                ),
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:update_enrolment:successful_response'),
                content: new OA\JsonContent(ref: '#/components/schemas/user_enrolment_resource'),
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:user_enrol_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function update_enrolment(ServerRequestInterface $request, array $args = []){
        global $CFG;
        require_once($CFG->libdir . '/enrollib.php');

        $enrolmentid = $this->required_param($args, 'enrolmentid', PARAM_INT);
        $courseid = $this->required_param($args, 'courseid', PARAM_INT);

        $query = new get_user_enrol_query($this->db, $enrolmentid, $courseid);
        if(!$existing_enrolment = $query->execute()){
            throw http_exception::fromString('exception:user_enrol_not_found', 'local_lfapi')->setStatusCode(404);
        }

        // Validating params
        $body = $request->getParsedBody();

        if(empty($body['enrolment'])){
            $error = get_string('validation:missing_key', 'local_lfapi', (object) ['key' => 'enrolment']);
            throw new validation_exception($error, 400);
        }

        // Updating enrolment
        $enrolment = $body['enrolment'];
        $enrolment['id'] = $existing_enrolment->id;
        $enrolment['userid'] = $existing_enrolment->userid;
        $enrolment['enrolid'] = $existing_enrolment->enrolid;

        switch ($enrolment['enrol'] ?? 'manual') {
            case 'manual':
                $enrolment_service = new manual_enrol_service();
                $enrolment = $enrolment_service->update_user_enrolment($enrolment);
                break;
            
            default:
                throw validation_exception::fromString('exception:only_manual_enrol_supported', 'local_lfapi');
        }

        // Assembling response
        $response = new user_enrolment_resource($enrolment);
        return $response;
    }

    #[OA\Delete(
        path: '/v3/courses/{courseid}/user-enrolments/{enrolmentid}',
        summary: new lfapi_string('docs:delete_enrolment'),
        tags: ['Course Enrolments'],
        parameters: [
            new OA\Parameter(
                name: "courseid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:course:id'),
                schema: new OA\Schema(type: "integer")
            ),
            new OA\Parameter(
                name: "enrolmentid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:user_enrolment:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        responses: [
            new OA\Response(
                response: 204,
                description: new lfapi_string('docs:delete_enrolment:successful_response'),
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:user_enrol_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
        ]
    )]
    public function delete_enrolment(ServerRequestInterface $request, array $args = []){
        global $CFG;
        require_once($CFG->libdir . '/enrollib.php');

        $enrolmentid = $this->required_param($args, 'enrolmentid', PARAM_INT);
        $courseid = $this->required_param($args, 'courseid', PARAM_INT);

        $query = new get_user_enrol_query($this->db, $enrolmentid, $courseid);
        if(!$enrolment = $query->execute()){
            throw http_exception::fromString('exception:user_enrol_not_found', 'local_lfapi')->setStatusCode(404);
        }

        switch ($enrolment->enrol) {
            case 'manual':
                $enrolment_service = new manual_enrol_service();
                $enrolment_service->unenrol_user((array) $enrolment);
                break;
            
            default:
                throw validation_exception::fromString('exception:only_manual_enrol_supported', 'local_lfapi');
        }

        return new EmptyResponse();
    }
}
