<?php namespace local_lfapi\v3\controllers;

use \webservice_api\controllers\abstract_controller;
use \webservice_api\http\response\resources\hal_resource;
use \local_lfapi\v3\factories\pagination_factory;
use \local_lfapi\v3\filters\category_sql_filter;

use \local_lfapi\lfapi_string;
use \context_system;
use \invalid_parameter_exception;
use \Psr\Http\Message\ServerRequestInterface;
use \Laminas\Diactoros\Response\JsonResponse;
use \Laminas\Diactoros\Response\EmptyResponse;
use \local_lfapi\v3\exceptions\http_exception;
use \local_lfapi\v3\queries\get_category_query;
use \local_lfapi\v3\queries\list_categories_query;
use \local_lfapi\v3\response\resources\entities\category_resource;
use \local_lfapi\v3\response\resources\paginated_hal_resource;
use \webservice_api\exceptions\validation_exception;
use \local_lfapi\v3\traits\batch_request_trait;

use \OpenApi\Attributes as OA;

#[OA\Tag(name: "Categories")]
class category_controller extends abstract_controller{

    use batch_request_trait;

    const MAX_BATCH_ITEMS = 100;

    public function __construct(){
        parent::__construct();
    }

    #[OA\Get(
        path: "/v3/categories",
        summary: new lfapi_string('docs:list_categories'),
        tags: ['Categories'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:category_sql_filter:id', '[eq|gt|gte|lt|lte|in]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'name',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:category_sql_filter:name', '[eq|like|in]'),
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'idnumber',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:category_sql_filter:idnumber', '[eq|like|in]'),
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'parent',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:category_sql_filter:parent', '[eq|neq|in]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'coursecount',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:category_sql_filter:coursecount', '[eq|gt|gte|lt|lte]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'depth',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:category_sql_filter:depth', '[eq|gt|gte|lt|lte]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'visible',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:category_sql_filter:visible', '[eq]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'timemodified',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:category_sql_filter:timemodified', '[eq|gt|gte|lt|lte]'),
                schema: new OA\Schema(type: 'integer', format: 'unix-timestamp')
            ),
            new OA\Parameter(ref: "#/components/parameters/_limit"),
            new OA\Parameter(ref: "#/components/parameters/_after")
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:list_categories:response:description'),
                content: new OA\JsonContent(
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "_embedded",
                            type: "object",
                            required: ["categories"],
                            properties: [
                                new OA\Property(
                                    property: "categories",
                                    type: "array",
                                    items: new OA\Items(ref: "#/components/schemas/category_resource")
                                )
                            ]
                        ),
                        new OA\Property(
                            property: "_links",
                            type: "object",
                            properties: [
                                new OA\Property(
                                    property: "self",
                                    type: "object",
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/categories"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                ),
                                new OA\Property(
                                    property: "next",
                                    type: "object",
                                    nullable: true,
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/categories?_after=50"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
        ]
    )]
    public function list_categories(ServerRequestInterface $request){
        $pagination_helper = pagination_factory::make_cursor_pagination_helper($request);

        // Assemblying filters
        $category_filter = category_sql_filter::from_request($request);

        // Get paginated list of categories
        $query = new list_categories_query($this->db, $category_filter);
        $categories_paginator = $query->get_paginator();
        $categories_paginator->set_limit($pagination_helper->get_limit());
        $categories_paginator->set_cursor($pagination_helper->get_cursor());

        // Retrieving and formatting categories
        $categories = [];
        foreach ($categories_paginator->get_generator() as $category) {
            $categories[] = new category_resource($category);
        }

        // Building response
        $response = new paginated_hal_resource();
        $response->embed('categories', $categories);
        $response->add_self_link($pagination_helper->get_current_page_url());

        if($next_cursor = $categories_paginator->get_next_cursor()){
            $response->add_next_link($pagination_helper->make_next_page_url($next_cursor));
        }

        return $response;
    }

    #[OA\Get(
        path: "/v3/categories/{categoryid}",
        summary: new lfapi_string('docs:get_category'),
        tags: ['Categories'],
        parameters: [
            new OA\Parameter(
                name: "categoryid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:category:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:get_category:successful_response'),
                content: new OA\JsonContent(ref: "#/components/schemas/category_resource")
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:category_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function get_category(ServerRequestInterface $request, array $args = []){
        $categoryid = $this->required_param($args, 'categoryid', PARAM_INT);

        // Retrieving user
        $query = new get_category_query($this->db, $categoryid);
        if(!$category = $query->execute()){
            throw http_exception::fromString('exception:category_not_found', 'local_lfapi')->setStatusCode(404);
        }

        // Checking for permissions
        if(!$category->visible){
            $context = context_system::instance();
            require_capability('moodle/category:viewhiddencategories', $context);
        }

        // Building response
        $response = new category_resource($category);
        return $response;
    }


    #[OA\Post(
        path: '/v3/categories',
        summary: new lfapi_string('docs:create_category'),      
        tags: ['Categories'],
        requestBody: new OA\RequestBody(
            required: true,
            description: new lfapi_string('docs:create_category:request_body_description'),
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    oneOf: [
                        new OA\Schema(
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'category',
                                    ref: '#/components/schemas/create_category_body'
                                )
                            ]
                        ),
                        new OA\Schema(
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'categories',
                                    type: 'array',
                                    items: new OA\Items(ref: '#/components/schemas/create_category_body')
                                )
                            ]
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: new lfapi_string('docs:create_category:successful_response'),
                content: new OA\JsonContent(
                    oneOf: [
                        new OA\JsonContent(ref: '#/components/schemas/category_resource'),
                        new OA\JsonContent(
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: '_embedded',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(
                                            property: 'categories',
                                            type: 'array',
                                            items: new OA\Items(ref: '#/components/schemas/category_resource')
                                        )
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
        ]
    )]
    public function create_category(ServerRequestInterface $request){
        global $CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        try {
            $body = $request->getParsedBody();

            $multiple = true;
            if(isset($body['category'])){
                $categories = [$body['category']];
                $multiple = false;
            }else{
                $this->validate_batch_items_limit($body, 'categories', self::MAX_BATCH_ITEMS);
                $categories = $body['categories'] ?? null;
            }

            $created = \core_course_external::create_categories($categories);
            if($multiple){
                $response = new hal_resource();
                $response->embed('categories', category_resource::from_collection($created));
                return new JsonResponse($response, 201);
            }

            // Building response
            $response = new category_resource(reset($created));
            return new JsonResponse($response, 201);

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe);
        }
    }

    #[OA\Patch(
        path: '/v3/categories/{categoryid}',
        summary: new lfapi_string('docs:update_category'),
        tags: ['Categories'],
        parameters: [
            new OA\Parameter(
                name: "categoryid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:category:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'category',
                            ref: '#/components/schemas/update_category_body'
                        )
                    ]
                ),
            )
        ),
        responses: [
            new OA\Response(
                response: 204,
                description: new lfapi_string('docs:update_category:successful_response'),
                content: new OA\JsonContent(ref: '#/components/schemas/category_resource'),
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:category_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function update_category(ServerRequestInterface $request, array $args = []){
        global $CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        $categoryid = $this->required_param($args, 'categoryid', PARAM_INT);

        try {
            $body = $request->getParsedBody();

            if(empty($body['category'])){
                $error = get_string('validation:missing_key', 'local_lfapi', (object) ['key' => 'category']);
                throw new validation_exception($error, 400);
            }

            $body['category']['id'] = $categoryid;
            \core_course_external::update_categories([$body['category']]);
            
            return new EmptyResponse();

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe);
        }
    }

    #[OA\Delete(
        path: '/v3/categories/{categoryid}',
        summary: new lfapi_string('docs:delete_category'),
        tags: ['Categories'],
        parameters: [
            new OA\Parameter(
                name: 'categoryid',
                in: 'path',
                required: true,
                description: new lfapi_string('docs:category:id'),
                schema: new OA\Schema(type: 'integer')
            )
        ],
        responses: [
            new OA\Response(
                response: 204,
                description: new lfapi_string('docs:delete_category:successful_response')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function delete_category(ServerRequestInterface $request, array $args = []){
        global $CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        $categoryid = $this->required_param($args, 'categoryid', PARAM_INT);
        $recursive = $this->optional_param($request->getQueryParams(), 'recursive', PARAM_BOOL, false);
        $newparent = $this->optional_param($request->getQueryParams(), 'newparent', PARAM_INT, 0);

        try {
            $category = [
                'id' => $categoryid,
                'recursive' => $recursive,
                'newparent' => $newparent,
            ];
            \core_course_external::delete_categories([$category]);           
            return new EmptyResponse();

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe, true);
        }
    }
}

new paginated_hal_resource();