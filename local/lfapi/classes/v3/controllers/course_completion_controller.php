<?php namespace local_lfapi\v3\controllers;

use \context_system;
use \local_lfapi\v3\exceptions\http_exception;
use \webservice_api\controllers\abstract_controller;
use \local_lfapi\v3\factories\pagination_factory;
use \local_lfapi\v3\filters\course_completion_sql_filter;
use \local_lfapi\v3\queries\get_course_completion_query;
use \local_lfapi\v3\queries\list_course_completions_query;
use \local_lfapi\v3\response\resources\entities\course_completion_resource;
use \local_lfapi\v3\response\resources\paginated_hal_resource;
use \Psr\Http\Message\ServerRequestInterface;


use \local_lfapi\lfapi_string;
use \local_lfapi\v3\response\resources\entities\detailed_course_completion_resource;
use \OpenApi\Attributes as OA;

#[OA\Tag(name: "Courses Completions")]
class course_completion_controller extends abstract_controller{

    #[OA\Get(
        path: "/v3/course-completions",
        summary: new lfapi_string('docs:list_completions'),
        description: new lfapi_string('docs:list_completions:description'),
        tags: ['Course Completions'],
        parameters: [
            new OA\Parameter(
                name: "courseid",
                in: "query",
                required: false,
                description: new lfapi_string('docs:course_completion_sql_filter:course', '[eq|in]'),
                schema: new OA\Schema(type: "integer")
            ),
            new OA\Parameter(
                name: 'userid',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_completion_sql_filter:userid', '[eq|in]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'timeenrolled',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_completion_sql_filter:timeenrolled', '[eq|neq|gt|gte|lt|lte]'),
                schema: new OA\Schema(type: 'integer', format: "unix-timestamp")
            ),
            new OA\Parameter(
                name: 'timestarted',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_completion_sql_filter:timestarted', '[eq|neq|gt|gte|lt|lte]'),
                schema: new OA\Schema(type: 'integer', format: "unix-timestamp")
            ),
            new OA\Parameter(
                name: 'timecompleted',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_completion_sql_filter:timecompleted', '[eq|neq|gt|gte|lt|lte|in|isnull|notnull]'),
                schema: new OA\Schema(type: 'integer', format: "unix-timestamp")
            ),
            new OA\Parameter(ref: "#/components/parameters/_limit"),
            new OA\Parameter(ref: "#/components/parameters/_after")
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:list_completions_by_course:successful_response'),
                content: new OA\JsonContent(
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "_embedded",
                            type: "object",
                            required: ["completions"],
                            properties: [
                                new OA\Property(
                                    property: "completions",
                                    type: "array",
                                    items: new OA\Items(ref: "#/components/schemas/course_completion_resource")
                                )
                            ]
                        ),
                        new OA\Property(
                            property: "_links",
                            type: "object",
                            properties: [
                                new OA\Property(
                                    property: "self",
                                    type: "object",
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/course-completions"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                ),
                                new OA\Property(
                                    property: "next",
                                    type: "object",
                                    nullable: true,
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/course-completions?_after=50"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                )
                            ]
                        )
                    ]
                )
            )
        ]
    )]
    public function list_completions(ServerRequestInterface $request, array $args){
        $pagination_helper = pagination_factory::make_cursor_pagination_helper($request);

        // Assemblying filters
        $completion_filter = course_completion_sql_filter::from_request($request);

        // Preparing query
        $query = new list_course_completions_query($this->db, $completion_filter);

        $completions_paginator = $query->get_paginator();
        $completions_paginator->set_limit($pagination_helper->get_limit());
        $completions_paginator->set_cursor($pagination_helper->get_cursor());

        // Retrieving and formatting completions
        $completions = [];
        foreach ($completions_paginator->get_generator() as $completion) {
            $completions[] = new course_completion_resource($completion);
        }

        // Building response
        $response = new paginated_hal_resource();
        $response->embed('completions', $completions);
        $response->add_self_link($pagination_helper->get_current_page_url());

        if($next_cursor = $completions_paginator->get_next_cursor()){
            $response->add_next_link($pagination_helper->make_next_page_url($next_cursor));
        }

        return $response;
    }

    #[OA\Get(
        path: "/v3/course-completions/{completionid}",
        summary: new lfapi_string('docs:get_completion'),
        tags: ['Course Completions'],
        parameters: [
            new OA\Parameter(
                name: "completionid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:completion:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:get_completion:successful_response'),
                content: new OA\JsonContent(ref: "#/components/schemas/course_completion_resource")
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:course_completion_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
        ]
    )]
    public function get_completion(ServerRequestInterface $request, array $args = []){
        $completionid = $this->required_param($args, 'completionid', PARAM_INT);

        // Retrieving user
        $query = new get_course_completion_query($this->db, $completionid);
        if(!$completion = $query->execute()){
            throw http_exception::fromString('exception:course_completion_not_found', 'local_lfapi')->setStatusCode(404);
        }
        
        // Building response
        $response = new detailed_course_completion_resource($completion);
        return $response;
    }
}
