<?php namespace local_lfapi\v3\controllers;

use \webservice_api\controllers\abstract_controller;
use \Psr\Http\Message\ServerRequestInterface;
use \local_lfapi\v3\factories\pagination_factory;
use \local_lfapi\v3\filters\user_sql_filter;
use \local_lfapi\v3\filters\profile_field_sql_filter;
use \local_lfapi\v3\response\resources\entities\user_resource;

use \local_lfapi\lfapi_string;
use \context_user;
use \local_lfapi\v3\exceptions\http_exception;
use \local_lfapi\v3\response\resources\entities\detailed_user_resource;
use \local_lfapi\v3\response\resources\paginated_hal_resource;
use \invalid_parameter_exception;
use \webservice_api\exceptions\validation_exception;
use \webservice_api\http\response\resources\hal_resource;
use \Laminas\Diactoros\Response\JsonResponse;
use \Laminas\Diactoros\Response\EmptyResponse;

use \local_lfapi\v3\queries\get_user_query;
use \local_lfapi\v3\queries\list_users_query;
use \local_lfapi\v3\response\resources\update_warning_resource;
use \local_lfapi\v3\traits\batch_request_trait;

use \OpenApi\Attributes as OA;

#[OA\Tag(name: "Users")]
class user_controller extends abstract_controller {

    use batch_request_trait;

    const MAX_BATCH_ITEMS = 100;

    public function __construct(){
        parent::__construct();
    }

    #[OA\Get(
        path: "/v3/users",
        summary: new lfapi_string('docs:list_users'),
        description: new lfapi_string('docs:list_users:description'),
        tags: ['Users'],
        parameters: [
            new OA\Parameter(
                name : 'id',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:id', '[eq|gt|gte|lt|lte|in]'),
                schema : new OA\Schema(type: 'integer'),
            ),
            new OA\Parameter(
                name : 'auth',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:auth', '[eq]'),
                schema : new OA\Schema(type: 'string'),
            ),
            new OA\Parameter(
                name : 'confirmed',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:confirmed', '[eq]'),
                schema : new OA\Schema(type: 'integer'),
            ),
            new OA\Parameter(
                name : 'suspended',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:suspended', '[eq]'),
                schema : new OA\Schema(type: 'integer'),
            ),
            new OA\Parameter(
                name : 'deleted',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:deleted', '[eq]'),
                schema : new OA\Schema(type: 'integer'),
            ),
            new OA\Parameter(
                name : 'policyagreed',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:policyagreed', '[eq]'),
                schema : new OA\Schema(type: 'integer'),
            ),
            new OA\Parameter(
                name : 'username',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:username', '[eq|like|in]'),
                schema : new OA\Schema(type: 'string'),
            ),
            new OA\Parameter(
                name : 'idnumber',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:idnumber', '[eq|like|isnull|notnull|in]'),
                schema : new OA\Schema(type: 'string'),
            ),
            new OA\Parameter(
                name : 'firstname',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:firstname', '[eq|like]'),
                schema : new OA\Schema(type: 'string'),
            ),
            new OA\Parameter(
                name : 'email',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:email', '[eq|like|in]'),
                schema : new OA\Schema(type: 'string'),
            ),
            new OA\Parameter(
                name : 'timecreated',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:timecreated', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name : 'timemodified',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:timemodified', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name : 'firstaccess',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:firstaccess', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name : 'lastaccess',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:lastaccess', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name : 'lastlogin',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:user_sql_filter:lastlogin', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name: 'profile_field_{name}',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:profile_field_sql_filter'),
                allowReserved: true,
                example: 'profile_field_department[eq]=TI',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(ref: "#/components/parameters/_limit"),
            new OA\Parameter(ref: "#/components/parameters/_after")
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:list_users:successful_response'),
                content: new OA\JsonContent(
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "_embedded",
                            type: "object",
                            required: ["users"],
                            properties: [
                                new OA\Property(
                                    property: "users",
                                    type: "array",
                                    items: new OA\Items(ref: "#/components/schemas/user_resource")
                                )
                            ]
                        ),
                        new OA\Property(
                            property: "_links",
                            type: "object",
                            properties: [
                                new OA\Property(
                                    property: "self",
                                    type: "object",
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/users"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                ),
                                new OA\Property(
                                    property: "next",
                                    type: "object",
                                    nullable: true,
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/users?_after=50"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
        ]
    )]
    public function list_users(ServerRequestInterface $request){
        $pagination_helper = pagination_factory::make_cursor_pagination_helper($request);

        // Assemblying filters
        $user_filter = user_sql_filter::from_request($request);
        $profile_filter = profile_field_sql_filter::from_request($request);
        
        // Get paginated list of users
        $query = new list_users_query($this->db, $user_filter, $profile_filter);
        $users_paginator = $query->get_paginator();
        $users_paginator->set_limit($pagination_helper->get_limit());
        $users_paginator->set_cursor($pagination_helper->get_cursor());

        // Retrieving and formatting users
        $users = [];
        foreach ($users_paginator->get_generator() as $user) {
            $users[] = new user_resource($user);
        }
        
        // Building response
        $response = new paginated_hal_resource();
        $response->embed('users', $users);
        $response->add_self_link($pagination_helper->get_current_page_url());

        if($next_cursor = $users_paginator->get_next_cursor()){
            $response->add_next_link($pagination_helper->make_next_page_url($next_cursor));
        }

        return $response;
    }

    #[OA\Get(
        path: "/v3/users/{userid}",
        summary: new lfapi_string('docs:get_user'),
        tags: ['Users'],
        parameters: [
            new OA\Parameter(
                name: "userid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:user:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:get_user:successful_response'),
                content: new OA\JsonContent(ref: "#/components/schemas/detailed_user_resource")
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:user_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function get_user(ServerRequestInterface $request, array $args = []){
        $userid = $this->required_param($args, 'userid', PARAM_INT);

        // Checking permissions
        if($context = context_user::instance($userid, IGNORE_MISSING)){
            require_capability('moodle/user:viewdetails', $context);
        }

        // Retrieving user
        $query = new get_user_query($this->db, $userid);
        if(!$user = $query->execute()){
            throw http_exception::fromString('exception:user_not_found', 'local_lfapi')->setStatusCode(404);
        }

        // Building response
        $response = new detailed_user_resource($user);
        return $response;
    }

    #[OA\Post(
        path: '/v3/users',
        summary: new lfapi_string('docs:create_users'),
        tags: ['Users'],
        requestBody: new OA\RequestBody(
            required: true,
            description: new lfapi_string('docs:create_users:request_body_description'),
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    oneOf: [
                        new OA\Schema(
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'user',
                                    ref: '#/components/schemas/create_user_body'
                                )
                            ]
                        ),
                        new OA\Schema(
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'users',
                                    type: 'array',
                                    items: new OA\Items(ref: '#/components/schemas/create_user_body')
                                )
                            ]
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: new lfapi_string('docs:create_users:successful_response'),
                content: new OA\JsonContent(
                    oneOf: [
                        new OA\JsonContent(ref: '#/components/schemas/user_resource'),
                        new OA\JsonContent(
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: '_embedded',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(
                                            property: 'users',
                                            type: 'array',
                                            items: new OA\Items(ref: '#/components/schemas/user_resource')
                                        )
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
        ]
    )]
    public function create_users(ServerRequestInterface $request){
        global $CFG;
        require_once($CFG->dirroot . '/user/externallib.php');

        try {
            $body = $request->getParsedBody();

            $multiple = true;
            if(isset($body['user'])){
                $users = [$body['user']];
                $multiple = false;
            }else{
                $this->validate_batch_items_limit($body, 'users', self::MAX_BATCH_ITEMS);
                $users = $body['users'] ?? null;
            }

            $users = \core_user_external::create_users($users);
            if($multiple){
                $response = new hal_resource();
                $response->embed('users', user_resource::from_collection($users));
                return new JsonResponse($response, 201);
            }

            $response = new user_resource(reset($users));
            return new JsonResponse($response, 201);

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe);
        }
    }

    #[OA\Patch(
        path: '/v3/users',
        summary: new lfapi_string('docs:update_users'),
        tags: ['Users'],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'users',
                            type: 'array',
                            items: new OA\Items(ref: '#/components/schemas/update_user_body', required: ['id'])
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 204,
                description: new lfapi_string('docs:update_users:successful_response'),
            ),
            new OA\Response(
                response: 207,
                description: new lfapi_string('docs:update_users:partial_successful_response'),
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/update_warning_resource')
                )
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function update_users(ServerRequestInterface $request){
        global $CFG;
        require_once($CFG->dirroot . '/user/externallib.php');
        
        try {
            $body = $request->getParsedBody();
            $this->validate_batch_items_limit($body, 'users', self::MAX_BATCH_ITEMS);
            $warnings = (\core_user_external::update_users($body['users'] ?? []))['warnings'];
            
            if(empty($warnings)){
                return new EmptyResponse();
            }
            
            $response = new hal_resource();
            $response->embed('errors', update_warning_resource::from_collection($warnings));
            return new JsonResponse($response, 207);

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe);
        }
    }

    #[OA\Patch(
        path: '/v3/users/{userid}',
        summary: new lfapi_string('docs:update_user'),
        tags: ['Users'],
        parameters: [
            new OA\Parameter(
                name: "userid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:user:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'user',
                            type: 'array',
                            items: new OA\Items(ref: '#/components/schemas/update_user_body')
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 204,
                description: new lfapi_string('docs:update_user:succesful_response'),
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:user_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function update_user(ServerRequestInterface $request, array $args = []){
        global $CFG;
        require_once($CFG->dirroot . '/user/externallib.php');

        $userid = $this->required_param($args, 'userid', PARAM_INT);

        try {
            $body = $request->getParsedBody();

            if(empty($body['user'])){
                $error = get_string('validation:missing_key', 'local_lfapi', (object) ['key' => 'user']);
                throw new validation_exception($error, 400);
            }

            $user = array_merge(['id' => $userid], $body['user']);
            $warnings = (\core_user_external::update_users([$user]))['warnings'];

            if(!empty($warnings)){
                throw new validation_exception(reset($warnings)['message'], 422);
            }
            
            return new EmptyResponse();

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe);
        }
    }

    #[OA\Delete(
        path: '/v3/users/{userid}',
        summary: new lfapi_string('docs:delete_user'),
        tags: ['Users'],
        parameters: [
            new OA\Parameter(
                name: 'userid',
                in: 'path',
                required: true,
                description: new lfapi_string('docs:user:id'),
                schema: new OA\Schema(type: 'integer')
            )
        ],
        responses: [
            new OA\Response(
                response: 204,
                description: new lfapi_string('docs:delete_user:successful_response'),
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:user_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function delete_user(ServerRequestInterface $request, array $args = []){
        global $CFG;
        require_once($CFG->dirroot . '/user/externallib.php');

        $userid = $this->required_param($args, 'userid', PARAM_INT);

        try {
            \core_user_external::delete_users([$userid]);           
            return new EmptyResponse();

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe);
        }
    }
}
