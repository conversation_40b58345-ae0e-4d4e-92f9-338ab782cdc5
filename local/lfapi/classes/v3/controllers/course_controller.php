<?php namespace local_lfapi\v3\controllers;

use \webservice_api\controllers\abstract_controller;
use \webservice_api\exceptions\api_exception;
use \webservice_api\factories\resources\hal_resource_factory;
use \webservice_api\http\response\resources\hal_resource;
use \local_lfapi\v3\factories\pagination_factory;
use \local_lfapi\v3\filters\course_sql_filter;

use \local_lfapi\lfapi_string;
use \context_system;
use \invalid_parameter_exception;
use \Psr\Http\Message\ServerRequestInterface;
use \Laminas\Diactoros\Response\JsonResponse;
use \Laminas\Diactoros\Response\EmptyResponse;
use \local_lfapi\v3\exceptions\http_exception;
use \local_lfapi\v3\filters\custom_course_fields_sql_filter;
use local_lfapi\v3\queries\get_course_query;
use \local_lfapi\v3\queries\list_courses_query;
use \local_lfapi\v3\response\resources\entities\course_resource;
use \local_lfapi\v3\response\resources\paginated_hal_resource;
use \local_lfapi\v3\response\resources\update_warning_resource;
use \webservice_api\exceptions\validation_exception;

use \local_lfapi\v3\traits\custom_course_field_trait;
use \local_lfapi\v3\traits\batch_request_trait;

use \OpenApi\Attributes as OA;

#[OA\Tag(name: "Courses")]
class course_controller extends abstract_controller{
    use custom_course_field_trait, batch_request_trait;
    
    const MAX_BATCH_ITEMS = 100;

    #[OA\Get(
        path: "/v3/courses",
        summary: new lfapi_string('docs:list_courses'),
        description: new lfapi_string('docs:list_courses:description'),
        tags: ['Courses'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:id', '[eq|in]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'categoryid',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:categoryid', '[eq|in]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'idnumber',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:idnumber', '[eq|like|notlike]'),
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'shortname',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:shortname', '[eq|like|notlike]'),
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'fullname',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:fullname', '[eq|like|notlike]'),
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'format',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:format', '[eq|in]'),
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(
                name: 'startdate',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:startdate', '[eq|gt|gte|lt|lte|in]'),
                schema: new OA\Schema(type: 'integer', format: 'unix-timestamp')
            ),
            new OA\Parameter(
                name: 'enddate',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:enddate', '[eq|gt|gte|lt|lte|in]'),
                schema: new OA\Schema(type: 'integer', format: 'unix-timestamp')
            ),
            new OA\Parameter(
                name: 'visible',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:visible', '[eq]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'timecreated',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:timecreated', '[eq|gt|gte|lt|lte|in]'),
                schema: new OA\Schema(type: 'integer', format: 'unix-timestamp')
            ),
            new OA\Parameter(
                name: 'timemodified',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:timemodified', '[eq|gt|gte|lt|lte|in]'),
                schema: new OA\Schema(type: 'integer', format: 'unix-timestamp')
            ),
            new OA\Parameter(
                name: 'enablecompletion',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:enablecompletion', '[eq]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'completionnotify',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:course_sql_filter:completionnotify', '[eq]'),
                schema: new OA\Schema(type: 'integer')
            ),
            new OA\Parameter(
                name: 'custom_field_{name}',
                in: 'query',
                required: false,
                description: new lfapi_string('docs:custom_course_fields_sql_filter', '[eq]'),
                allowReserved: true,
                example: 'custom_field_audience[eq]=TI',
                schema: new OA\Schema(type: 'string')
            ),
            new OA\Parameter(ref: "#/components/parameters/_limit"),
            new OA\Parameter(ref: "#/components/parameters/_after")
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:list_courses:successful_response'),
                content: new OA\JsonContent(
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "_embedded",
                            type: "object",
                            required: ["courses"],
                            properties: [
                                new OA\Property(
                                    property: "courses",
                                    type: "array",
                                    items: new OA\Items(ref: "#/components/schemas/course_resource")
                                )
                            ]
                        ),
                        new OA\Property(
                            property: "_links",
                            type: "object",
                            properties: [
                                new OA\Property(
                                    property: "self",
                                    type: "object",
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/courses"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                ),
                                new OA\Property(
                                    property: "next",
                                    type: "object",
                                    nullable: true,
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/courses?_after=50"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
        ]
    )]
    public function list_courses(ServerRequestInterface $request){
        $pagination_helper = pagination_factory::make_cursor_pagination_helper($request);

        // Assemblying filters
        $course_filter = course_sql_filter::from_request($request);
        $custom_fields_filter = custom_course_fields_sql_filter::from_request($request);

        // Get paginated list of courses
        $query = new list_courses_query($this->db, $course_filter, $custom_fields_filter);
        $courses_paginator = $query->get_paginator();
        $courses_paginator->set_limit($pagination_helper->get_limit());
        $courses_paginator->set_cursor($pagination_helper->get_cursor());

        // Retrieving and formatting courses
        $courses = [];
        foreach ($courses_paginator->get_generator() as $category) {
            $courses[] = new course_resource($category);
        }

        // Building response
        $response = new paginated_hal_resource();
        $response->embed('courses', $courses);
        $response->add_self_link($pagination_helper->get_current_page_url());

        if($next_cursor = $courses_paginator->get_next_cursor()){
            $response->add_next_link($pagination_helper->make_next_page_url($next_cursor));
        }

        return $response;
    }

    #[OA\Get(
        path: "/v3/courses/{courseid}",
        summary: new lfapi_string('docs:get_course'),
        tags: ['Courses'],
        parameters: [
            new OA\Parameter(
                name: "courseid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:course:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:get_course:successful_response'),
                content: new OA\JsonContent(ref: "#/components/schemas/course_resource")
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:course_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function get_course(ServerRequestInterface $request, array $args = []){
        $courseid = $this->required_param($args, 'courseid', PARAM_INT);
        $context = context_system::instance();

        // Checking permissions
        if($courseid!= SITEID){
            require_capability('moodle/course:view', $context);
        }

        // Retrieving course
        $query = new get_course_query($this->db, $courseid);
        if(!$course = $query->execute()){
            throw http_exception::fromString('exception:course_not_found', 'local_lfapi')->setStatusCode(404);
        }

        if(!$course->visible){
            require_capability('moodle/course:viewhiddencourses', $context);
        }

        // Building response
        $response = new course_resource($course);
        return $response;
    }

    #[OA\Post(
        path: '/v3/courses',
        summary: new lfapi_string('docs:create_courses'),
        tags: ['Courses'],
        requestBody: new OA\RequestBody(
            required: true,
            description: new lfapi_string('docs:create_courses:request_body_description'),
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    oneOf: [
                        new OA\Schema(
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'course',
                                    ref: '#/components/schemas/create_course_body'
                                )
                            ]
                        ),
                        new OA\Schema(
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: 'courses',
                                    type: 'array',
                                    items: new OA\Items(ref: '#/components/schemas/create_course_body')
                                )
                            ]
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: new lfapi_string('docs:create_courses:successful_response'),
                content: new OA\JsonContent(
                    oneOf: [
                        new OA\JsonContent(ref: '#/components/schemas/course_resource'),
                        new OA\JsonContent(
                            type: 'object',
                            properties: [
                                new OA\Property(
                                    property: '_embedded',
                                    type: 'object',
                                    properties: [
                                        new OA\Property(
                                            property: 'courses',
                                            type: 'array',
                                            items: new OA\Items(ref: '#/components/schemas/course_resource')
                                        )
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
        ]
    )]
    public function create_courses(ServerRequestInterface $request){
        global $CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        try {
            $body = $request->getParsedBody();

            $multiple = true;
            if(isset($body['course'])){
                if(!empty($body['course']['customfields'])){
                    $this->core_course_external_parse_custom_fields($body['course']['customfields']);
                }
                $courses = [$body['course']];
                $multiple = false;
            }else{
                $this->validate_batch_items_limit($body, 'courses', self::MAX_BATCH_ITEMS);
                $courses = $body['courses'] ?? [];
                foreach ($courses as &$course) {
                    if(isset($course['customfields'])){
                        $this->core_course_external_parse_custom_fields($course['customfields']);
                    }
                }
            }

            $created = \core_course_external::create_courses($courses);
            if($multiple){
                $response = new hal_resource();
                $response->embed('courses', course_resource::from_collection($created));
                return new JsonResponse($response, 201);
            }

            // Building response
            $response = new course_resource(reset($created));
            return new JsonResponse($response, 201);

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe);
        }
    }

    #[OA\Patch(
        path: '/v3/courses',
        summary: new lfapi_string('docs:update_courses'),
        tags: ['Courses'],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'courses',
                            type: 'array',
                            items: new OA\Items(ref: '#/components/schemas/update_course_body', required: ['id', 'fullname', 'shortname', 'categoryid'])
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 204,
                description: new lfapi_string('docs:update_courses:successful_response'),
            ),
            new OA\Response(
                response: 207,
                description: new lfapi_string('docs:update_courses:partial_successful_response'),
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/update_warning_resource')
                )
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function update_courses(ServerRequestInterface $request, array $args = []){
        global $CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        try {
            $body = $request->getParsedBody();
            $this->validate_batch_items_limit($body, 'courses', self::MAX_BATCH_ITEMS);
            $courses = $body['courses'] ?? [];
            foreach ($courses as &$course) {
                if(!empty($course['customfields'])){
                    $this->core_course_external_parse_custom_fields($course['customfields']);
                }
            }

            $warnings = (\core_course_external::update_courses($courses))['warnings'];
            
            if(empty($warnings)){
                return new EmptyResponse();
            }

            $response = new hal_resource();
            $response->embed('errors', update_warning_resource::from_collection($warnings));
            return new JsonResponse($response, 207);

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe);
        }
    }

    #[OA\Patch(
        path: '/v3/courses/{courseid}',
        summary: new lfapi_string('docs:update_course'),
        tags: ['Courses'],
        parameters: [
            new OA\Parameter(
                name: "courseid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:course:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'application/json',
                schema: new OA\Schema(
                    type: 'object',
                    properties: [
                        new OA\Property(
                            property: 'course',
                            type: 'array',
                            items: new OA\Items(ref: '#/components/schemas/update_course_body')
                        )
                    ]
                )
            )
        ),
        responses: [
            new OA\Response(
                response: 204,
                description: new lfapi_string('docs:update_course:successful_response'),
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:course_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function update_course(ServerRequestInterface $request, array $args = []){
        global $CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        $courseid = $this->required_param($args, 'courseid', PARAM_INT);

        try {
            $body = $request->getParsedBody();

            if(empty($body['course'])){
                $error = get_string('validation:missing_key', 'local_lfapi', (object) ['key' => 'course']);
                throw new validation_exception($error, 400);
            }

            if(!empty($body['course']['customfields'])){
                $this->core_course_external_parse_custom_fields($body['course']['customfields']);
            }

            $body['course']['id'] = $courseid;
            \core_course_external::update_courses([$body['course']]);
            
            return new EmptyResponse();

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe);
        }
    }

    #[OA\Delete(
        path: '/v3/courses/{courseid}',
        summary: new lfapi_string('docs:delete_course'),
        tags: ['Courses'],
        parameters: [
            new OA\Parameter(
                name: 'courseid',
                in: 'path',
                required: true,
                description: new lfapi_string('docs:course:id'),
                schema: new OA\Schema(type: 'integer')
            )
        ],
        responses: [
            new OA\Response(
                response: 204,
                description: new lfapi_string('docs:delete_course:successful_response'),
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:course_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
        ]
    )]
    public function delete_course(ServerRequestInterface $request, array $args = []){
        global $CFG;
        require_once($CFG->dirroot . '/course/externallib.php');

        $courseid = $this->required_param($args, 'courseid', PARAM_INT);

        try {
            \core_course_external::delete_courses([$courseid]);           
            return new EmptyResponse();

        } catch (invalid_parameter_exception $ipe) {
            throw validation_exception::fromException($ipe);
        }
    }
}
