<?php namespace local_lfapi\v3\controllers;

use \webservice_api\controllers\abstract_controller;
use \local_lfapi\v3\factories\pagination_factory;
use \Psr\Http\Message\ServerRequestInterface;
use \local_lfapi\v3\exceptions\http_exception;
use local_lfapi\v3\filters\enrol_instance_sql_filter;
use \local_lfapi\v3\queries\get_course_query;
use local_lfapi\v3\queries\get_enrol_instance_query;
use \local_lfapi\v3\queries\list_enrol_instances_query;
use \local_lfapi\v3\response\resources\entities\course_resource;
use local_lfapi\v3\response\resources\entities\enrol_instance_resource;
use \local_lfapi\v3\response\resources\paginated_hal_resource;
use \moodle_dev_utils\http\filters\lhs\conditions\eq_sql_condition;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Tag(name: "Course Enrolments")]
class enrol_instance_controller extends abstract_controller{

    #[OA\Get(
        path: "/v3/courses/{courseid}/enrol-instances",
        summary: new lfapi_string('docs:list_enrol_instances'),
        description: new lfapi_string('docs:list_enrol_instances:description'),
        tags: ['Course Enrolments'],
        parameters: [
            new OA\Parameter(
                name: "courseid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:course:id'),
                schema: new OA\Schema(type: "integer")
            ),
            new OA\Parameter(
                name : 'enrol',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:enrol_instance_sql_filter:enrol', '[eq]'),
                schema : new OA\Schema(type: 'string'),
            ),
            new OA\Parameter(
                name : 'enrolperiod',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:enrol_instance_sql_filter:enrolperiod', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer'),
            ),
            new OA\Parameter(
                name : 'enrolstartdate',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:enrol_instance_sql_filter:enrolstartdate', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name : 'enrolenddate',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:enrol_instance_sql_filter:enrolenddate', '[eq|gt|gte|lt|lte]'),
                schema : new OA\Schema(type: 'integer', format: 'unix-timestamp'),
            ),
            new OA\Parameter(
                name : 'name',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:enrol_instance_sql_filter:name', '[eq|like|in]'),
                schema : new OA\Schema(type: 'string'),
            ),
            new OA\Parameter(
                name : 'status',
                in : 'query',
                required : false,
                description : new lfapi_string('docs:enrol_instance_sql_filter:status', '[eq]'),
                schema : new OA\Schema(type: 'integer'),
            ),
            new OA\Parameter(ref: "#/components/parameters/_limit"),
            new OA\Parameter(ref: "#/components/parameters/_after")
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:list_enrol_instances:successful_response'),
                content: new OA\JsonContent(
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "_embedded",
                            type: "object",
                            required: ["instances"],
                            properties: [
                                new OA\Property(
                                    property: "instances",
                                    type: "array",
                                    items: new OA\Items(ref: "#/components/schemas/enrol_instance_resource")
                                )
                            ]
                        ),
                        new OA\Property(
                            property: "_links",
                            type: "object",
                            properties: [
                                new OA\Property(
                                    property: "self",
                                    type: "object",
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/courses/{courseid}/enrol-instances"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                ),
                                new OA\Property(
                                    property: "next",
                                    type: "object",
                                    nullable: true,
                                    required: ["href", "method"],
                                    properties: [
                                        new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/courses/{courseid}/enrol-instances?_after=50"),
                                        new OA\Property(property: "method", type: "string", example: "GET")
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ),
            new OA\Response(
                response: 400,
                description: new lfapi_string('docs:bad_request_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/bad_request_error')
            ),
            new OA\Response(
                response: 401,
                description: new lfapi_string('docs:unauthorized_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/unauthorized_error')
            ),
            new OA\Response(
                response: 403,
                description: new lfapi_string('docs:forbidden_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/forbidden_error')
            ),
            new OA\Response(
                response: 422,
                description: new lfapi_string('docs:validation_error:description'),
                content: new OA\JsonContent(ref: '#/components/schemas/validation_error')
            ),
        ]
    )]
    public function list_instances(ServerRequestInterface $request, array $args){
        $pagination_helper = pagination_factory::make_cursor_pagination_helper($request);
        $courseid = $this->required_param($args, 'courseid', PARAM_INT);

        // Assemblying filters
        $instance_filter = enrol_instance_sql_filter::from_request($request);
        $instance_filter->set_condition(eq_sql_condition::get_alias(), 'courseid', $courseid); // Forcing course

        // Get paginated list of instances
        $query = new list_enrol_instances_query($this->db, $instance_filter);
        $instances_paginator = $query->get_paginator();
        $instances_paginator->set_limit($pagination_helper->get_limit());
        $instances_paginator->set_cursor($pagination_helper->get_cursor());

        // Retrieving and formatting instances
        $instances = [];
        foreach ($instances_paginator->get_generator() as $category) {
            $instances[] = new enrol_instance_resource($category);
        }

        // Building response
        $response = new paginated_hal_resource();
        $response->embed('instances', $instances);
        $response->add_self_link($pagination_helper->get_current_page_url());

        if($next_cursor = $instances_paginator->get_next_cursor()){
            $response->add_next_link($pagination_helper->make_next_page_url($next_cursor));
        }

        return $response;
    }

    #[OA\Get(
        path: "/v3/courses/{courseid}/enrol-instances/{instanceid}",
        summary: new lfapi_string('docs:get_enrol_instance'),
        tags: ['Course Enrolments'],
        parameters: [
            new OA\Parameter(
                name: "courseid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:course:id'),
                schema: new OA\Schema(type: "integer")
            ),
            new OA\Parameter(
                name: "instanceid",
                in: "path",
                required: true,
                description: new lfapi_string('docs:enrol_instance:id'),
                schema: new OA\Schema(type: "integer")
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: new lfapi_string('docs:get_enrol_instance:successful_response'),
                content: new OA\JsonContent(ref: "#/components/schemas/enrol_instance_resource")
            ),
            new OA\Response(
                response: 404,
                description: new lfapi_string('exception:enrol_instance_not_found'),
                content: new OA\JsonContent(ref: '#/components/schemas/not_found_error')
            ),
        ]
    )]
    public function get_instance(ServerRequestInterface $request, array $args = []){
        $instanceid = $this->required_param($args, 'instanceid', PARAM_INT);

        // Retrieving instance
        $query = new get_enrol_instance_query($this->db, $instanceid);
        if(!$instance = $query->execute()){
            throw http_exception::fromString('exception:enrol_instance_not_found', 'local_lfapi')->setStatusCode(404);
        }

        // Building response
        $response = new enrol_instance_resource($instance);

        // Embedding course
        $course_query = new get_course_query($this->db, $instance->courseid);
        if($course = $course_query->execute()){
            $course_resource = new course_resource($course);
            $response->embed('course', $course_resource);
        }

        return $response;
    }
}
