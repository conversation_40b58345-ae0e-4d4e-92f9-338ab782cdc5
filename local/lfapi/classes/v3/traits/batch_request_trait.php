<?php namespace local_lfapi\v3\traits;

use \Psr\Http\Message\ServerRequestInterface;
use \local_lfapi\v3\exceptions\http_exception;

/**
 * Trait batch_request_trait
 *
 * Provides validation for batch requests to ensure the number of submitted items
 * does not exceed the allowed limit.
 *
 * This trait is typically used in controller classes that support creating,
 * updating, or deleting multiple entities in a single request.
 */
trait batch_request_trait {
    
    /**
     * Validates that the number of items in a batch request does not exceed the specified limit.
     *
     * @param array|ServerRequestInterface|null $source  The request body or parsed data source.
     * @param string                            $key     The array key in the body that contains the batch items.
     * @param int                               $limit   The maximum allowed number of items.
     * @param string                            $message Optional. A custom error message. If not provided,
     *                                                  a default exception with localized string will be thrown.
     *
     * @throws http_exception If the batch size exceeds the allowed limit.
     */
    public function validate_batch_items_limit(array|ServerRequestInterface|null $source, string $key, int $limit, string $message = ''){
        $body = ($source instanceof ServerRequestInterface) ? $source->getParsedBody() : $source;

        if(!array_key_exists($key, $body) || !is_array($body[$key]) || count($body[$key]) <= $limit){
            return;
        }

        if(empty($message)){
            throw http_exception::fromString('exception:batch_limit_violation', 'local_lfapi', $limit)->setStatusCode(400);
        }

        throw new http_exception($message, 400);
    }
}
