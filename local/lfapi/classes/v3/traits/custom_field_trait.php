<?php namespace local_lfapi\v3\traits;

use \core_customfield\field_controller;
use \core_customfield\handler;

trait custom_field_trait {

    abstract function get_handler() : handler;

    /**
     * @return \core_customfield\field_controller[]
     */
    public function get_editable_custom_fields() : array {
        $fields = [];
        foreach ($this->get_handler()->get_fields() as $field) {
            $fields[$field->get('shortname')] = $field;
        }
        return $fields;
    }

    public function parse_custom_field_value(field_controller $field, mixed $value) : mixed {
        return is_string($value) && !is_numeric($value) ? $field->parse_value($value) : $value;
    }

    /**
     * Parses the customfields for a external core_course call.
     *
     * @param array $customfields [['shortname' => "FIELDSHORTNAME', 'value' => "SOMEVALUE"], ...] BY REFERENCE
     * @return void
     */
    public function core_course_external_parse_custom_fields(array &$customfields){
        $fields = $this->get_editable_custom_fields();

        foreach ($customfields as $index => $customfield){
            if(is_object($customfield)){
                if(isset($fields[$customfield->shortname])){
                    $customfields[$index]->value = $this->parse_custom_field_value(
                        $fields[$customfield->shortname],
                        $customfields[$index]?->value
                    );
                }
                continue;
            }
            
            if(is_array($customfield)){
                if(isset($fields[$customfield['shortname']])){
                    $customfields[$index]['value'] = $this->parse_custom_field_value(
                        $fields[$customfield['shortname']],
                        $customfields[$index]['value'] ?? null
                    );
                }
                continue;
            }
        }
    }
}
