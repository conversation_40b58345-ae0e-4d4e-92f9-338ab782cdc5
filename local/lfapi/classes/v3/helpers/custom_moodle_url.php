<?php namespace local_lfapi\v3\helpers;

use \moodle_url;
use \coding_exception;

/**
 * Custom implementation of moodle_url
 * that allows for array params
 */
class custom_moodle_url extends moodle_url {

    public function params(?array $params = null) {
        $params = (array)$params;

        foreach ($params as $key => $value) {
            if (is_int($key)) {
                throw new coding_exception('Url parameters can not have numeric keys!');
            }
            if (is_array($value)) {
                $this->params[$key] = $value;
            } else {
                if (is_object($value) && !method_exists($value, '__toString')) {
                    throw new coding_exception('Url parameters values can not be objects, unless __toString() is defined!');
                }
                $this->params[$key] = (string)$value;
            }
        }
        return $this->params;
    }

    protected function merge_overrideparams(?array $overrideparams = null) {
        $overrideparams = (array)$overrideparams;
        $params = $this->params;

        foreach ($overrideparams as $key => $value) {
            if (is_int($key)) {
                throw new coding_exception('Overridden parameters can not have numeric keys!');
            }
            if (is_array($value)) {
                $params[$key] = $value;
            } else {
                if (is_object($value) && !method_exists($value, '__toString')) {
                    throw new coding_exception('Overridden parameters values can not be objects, unless __toString() is defined!');
                }
                $params[$key] = (string)$value;
            }
        }

        return $params;
    }

    public function get_query_string($escaped = true, ?array $overrideparams = null) {
        $arr = [];
        $params = $overrideparams !== null ? $this->merge_overrideparams($overrideparams) : $this->params;

        foreach ($params as $key => $val) {
            $key = urlencode($key);
            if (is_array($val)) {
                foreach ($val as $subkey => $subval) {
                    $subkey = urlencode($subkey);
                    $arr[] = "{$key}[{$subkey}]=" . rawurlencode($subval);
                }
            } else {
                if (isset($val) && $val !== '') {
                    $arr[] = "{$key}=" . rawurlencode($val);
                } else {
                    $arr[] = "{$key}";
                }
            }
        }

        return $escaped ? implode('&amp;', $arr) : implode('&', $arr);
    }
}
