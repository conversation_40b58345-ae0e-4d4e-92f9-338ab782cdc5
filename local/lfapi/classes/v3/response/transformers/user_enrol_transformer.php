<?php namespace local_lfapi\v3\response\transformers;

use \local_lfapi\lfapi_string;
use \webservice_api\http\response\transformers\abstract_defined_transformer;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "user_enrol_transformer",
    title: new lfapi_string('docs:user_enrol_transformer:title'),
    required: ["id", "userid", "courseid", "enrolid"],
    properties: [
        new OA\Property(property: "id", type: "integer"),
        new OA\Property(property: "enrol", type: "string"),
        new OA\Property(property: "enrolid", type: "integer"),
        new OA\Property(property: "courseid", type: "integer"),
        new OA\Property(property: "userid", type: "integer"),
        new OA\Property(property: "status", type: "integer"),
        new OA\Property(property: "timestart", type: "integer"),
        new OA\Property(property: "timeend", type: "integer"),
        new OA\Property(property: "timecreated", type: "integer"),
        new OA\Property(property: "timemodified", type: "integer")
    ]
)]
class user_enrol_transformer extends abstract_defined_transformer {

    protected function define_properties(): array {
        return [
            'id' => PARAM_INT,
            'enrol' => PARAM_PLUGIN,
            'enrolid' => PARAM_INT,
            'courseid' => PARAM_INT,
            'userid' => PARAM_INT,
            'status' => PARAM_INT,
            'timestart' => PARAM_INT,
            'timeend' => PARAM_INT,
            'timecreated' => PARAM_INT,
            'timemodified' => PARAM_INT,
        ];
    }
}
