<?php namespace local_lfapi\v3\response\transformers;

require_once($CFG->dirroot.'/user/lib.php');

use \webservice_api\http\response\transformers\abstract_defined_transformer;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "user_transformer",
    title: new lfapi_string('docs:user_transformer:title'),
    required: ["id", "username", "firstname", "lastname", "email"],
    properties: [
        new OA\Property(property: "id", type: "integer"),
        new OA\Property(property: "auth", type: "string"),
        new OA\Property(property: "confirmed", type: "boolean"),
        new OA\Property(property: "policyagreed", type: "boolean"),
        new OA\Property(property: "deleted", type: "boolean"),
        new OA\Property(property: "suspended", type: "boolean"),
        new OA\Property(property: "username", type: "string"),
        new OA\Property(property: "idnumber", type: "string"),
        new OA\Property(property: "firstname", type: "string"),
        new OA\Property(property: "lastname", type: "string"),
        new OA\Property(property: "surname", type: "string"),
        new OA\Property(property: "email", type: "string"),
        new OA\Property(property: "phone1", type: "string"),
        new OA\Property(property: "phone2", type: "string"),
        new OA\Property(property: "institution", type: "string"),
        new OA\Property(property: "department", type: "string"),
        new OA\Property(property: "address", type: "string"),
        new OA\Property(property: "city", type: "string"),
        new OA\Property(property: "country", type: "string"),
        new OA\Property(property: "lang", type: "string"),
        new OA\Property(property: "theme", type: "string"),
        new OA\Property(property: "timezone", type: "string"),
        new OA\Property(property: "firstaccess", type: "integer"),
        new OA\Property(property: "lastaccess", type: "integer"),
        new OA\Property(property: "lastlogin", type: "integer"),
        new OA\Property(property: "currentlogin", type: "integer"),
        new OA\Property(property: "lastip", type: "string"),
        new OA\Property(property: "description", type: "string"),
        new OA\Property(property: "descriptionformat", type: "integer"),
        new OA\Property(property: "mailformat", type: "integer"),
        new OA\Property(property: "maildigest", type: "integer"),
        new OA\Property(property: "maildisplay", type: "integer"),
        new OA\Property(property: "autosubscribe", type: "integer"),
        new OA\Property(property: "trackforums", type: "integer"),
        new OA\Property(property: "timecreated", type: "integer"),
        new OA\Property(property: "timemodified", type: "integer"),
        new OA\Property(property: "imagealt", type: "string"),
        new OA\Property(property: "lastnamephonetic", type: "string"),
        new OA\Property(property: "firstnamephonetic", type: "string"),
        new OA\Property(property: "middlename", type: "string"),
        new OA\Property(property: "alternatename", type: "string"),
        new OA\Property(property: "lastcourseaccess", type: "integer"),
        new OA\Property(property: "fullname", type: "string"),
        new OA\Property(property: "profileimageurl", type: "string", format: "uri"),
        new OA\Property(property: "profileimageurlsmall", type: "string", format: "uri"),

        new OA\Property(
            property: "customfields",
            type: "array",
            items: new OA\Items(
                type: "object",
                properties: [
                    new OA\Property(property: "shortname", type: "string"),
                    new OA\Property(property: "value", type: "string")
                ]
            )
        ),
        new OA\Property(property: "interests", type: "string"),
    ]
)]
class user_transformer extends abstract_defined_transformer {

    protected array $extra_user_details = [];

    protected function define_properties() : array {
        return [
            'id' => PARAM_INT,
            'auth' => PARAM_AUTH,
            'confirmed' => PARAM_BOOL,
            'policyagreed' => PARAM_BOOL,
            'deleted' => PARAM_BOOL,
            'suspended' => PARAM_BOOL,
            'username' => PARAM_USERNAME,
            'idnumber' => PARAM_RAW,
            'firstname' => PARAM_NOTAGS,
            'lastname' => PARAM_NOTAGS,
            'surname' => PARAM_NOTAGS,
            'email' => PARAM_RAW_TRIMMED,
            'phone1' => PARAM_NOTAGS,
            'phone2' => PARAM_NOTAGS,
            'institution' => PARAM_TEXT,
            'department' => PARAM_TEXT,
            'address' => PARAM_TEXT,
            'city' => PARAM_TEXT,
            'country' => PARAM_ALPHA,
            'lang' => PARAM_LANG,
            'theme' => PARAM_THEME,
            'timezone' => PARAM_TIMEZONE,
            'firstaccess' => PARAM_INT,
            'lastaccess' => PARAM_INT,
            'lastlogin' => PARAM_INT,
            'currentlogin' => PARAM_INT,
            'lastip' => PARAM_NOTAGS,
            'description' => PARAM_RAW,
            'descriptionformat' => PARAM_INT,
            'mailformat' => PARAM_INT,
            'maildigest' => PARAM_INT,
            'maildisplay' => PARAM_INT,
            'autosubscribe' => PARAM_INT,
            'trackforums' => PARAM_INT,
            'timecreated' => PARAM_INT,
            'timemodified' => PARAM_INT,
            'imagealt' => PARAM_TEXT,
            'lastnamephonetic' => PARAM_NOTAGS,
            'firstnamephonetic' => PARAM_NOTAGS,
            'middlename' => PARAM_NOTAGS,
            'alternatename' => PARAM_NOTAGS,
            'lastcourseaccess' => PARAM_INT,

            // Extra fields
            'fullname' => PARAM_RAW,
            'profileimageurl' => PARAM_URL,
            'profileimageurlsmall' => PARAM_URL,
            'customfields' => PARAM_RAW,
            'interests' => PARAM_TEXT,
            'enrolledcourses' => PARAM_RAW,
            'preferences' => PARAM_RAW,
            'roles' => PARAM_RAW
        ];
    }

    public function include_preferences() : static {
        $this->extra_user_details[] = 'preferences';
        return $this;
    }

    public function include_roles() : static {
        $this->extra_user_details[] = 'roles';
        return $this;
    }

    public function include_enrolled_courses() : static {
        $this->extra_user_details[] = 'enrolledcourses';
        return $this;
    }

    public function include_last_course_access() : static {
        $this->extra_user_details[] = 'lastcourseaccess';
        return $this;
    }

    public function include_profile_images() : static {
        $this->extra_user_details[] = 'profileimageurlsmall';
        $this->extra_user_details[] = 'profileimageurl';
        return $this;
    }

    public function include_interests() : static {
        $this->extra_user_details[] = 'interests';
        return $this;
    }

    public function include_custom_fields() : static {
        $this->extra_user_details[] = 'customfields';
        return $this;
    }

    public function include_all_details() : static {
        $this->include_preferences();
        $this->include_roles();
        $this->include_enrolled_courses();
        $this->include_last_course_access();
        $this->include_profile_images();
        $this->include_interests();
        $this->include_custom_fields();
        return $this;
    }

    protected function get_user_details_fields() : array {
        return $this->extra_user_details;
    }

    public function transform(mixed $data): array {
        if(!empty($this->extra_user_details)){
            $user_details = user_get_user_details_courses((object) $data, $this->get_user_details_fields());
            $user = array_merge((array) $data, $user_details);
        }else{
            $user = $data;
        }
        
        $user = parent::transform($user);
        return $user;
    }
}