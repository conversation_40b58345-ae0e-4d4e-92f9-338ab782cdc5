<?php namespace local_lfapi\v3\response\transformers;

use \webservice_api\http\response\transformers\abstract_defined_transformer;
use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "enrol_instance_transformer",
    title: new lfapi_string('docs:enrol_instance_transformer:title'),
    required: ["id", "courseid", "enrol"],
    properties: [
        new OA\Property(property: "id", type: "integer"),
        new OA\Property(property: "enrol", type: "string"),
        new OA\Property(property: "courseid", type: "integer"),
        new OA\Property(property: "enrolperiod", type: "integer"),
        new OA\Property(property: "enrolstartdate", type: "integer"),
        new OA\Property(property: "enrolenddate", type: "integer"),
        new OA\Property(property: "status", type: "integer"),
        new OA\Property(property: "name", type: "string"),
        new OA\Property(property: "expirynotify", type: "boolean"),
        new OA\Property(property: "expirythreshold", type: "integer"),
        new OA\Property(property: "notifyall", type: "boolean"),
        new OA\Property(property: "timecreated", type: "integer"),
        new OA\Property(property: "timemodified", type: "integer"),
    ]
)]
class enrol_instance_transformer extends abstract_defined_transformer {

    protected function define_properties() : array {
        return [
            'id' => PARAM_INT,
            'enrol' => PARAM_PLUGIN,
            'courseid' => PARAM_INT,
            'enrolperiod' => PARAM_INT,
            'enrolstartdate' => PARAM_INT,
            'enrolenddate' => PARAM_INT,
            'status' => PARAM_INT,
            'name' => PARAM_RAW,
            'expirynotify' => PARAM_BOOL,
            'expirythreshold' => PARAM_INT,
            'notifyall' => PARAM_BOOL,
            'timecreated' => PARAM_INT,
            'timemodified' => PARAM_INT,
        ];
    }
}