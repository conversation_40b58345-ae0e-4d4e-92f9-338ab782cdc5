<?php namespace local_lfapi\v3\response\transformers;


require_once($CFG->dirroot.'/course/format/lib.php');

use \context_course;
use \webservice_api\http\response\transformers\abstract_defined_transformer;
use \core_external\util;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "course_transformer",
    title: new lfapi_string('docs:course_transformer:title'),
    required: ["id", "shortname"],
    properties: [
        new OA\Property(property: "id", type: "integer"),
        new OA\Property(property: "fullname", type: "string"),
        new OA\Property(property: "shortname", type: "string"),
        new OA\Property(property: "displayname", type: "string"),
        new OA\Property(property: "categoryid", type: "integer"),
        new OA\Property(property: "summary", type: "string"),
        new OA\Property(property: "format", type: "string"),
        new OA\Property(property: "startdate", type: "integer"),
        new OA\Property(property: "enddate", type: "integer"),
        new OA\Property(property: "showactivitydates", type: "boolean"),
        new OA\Property(property: "showcompletionconditions", type: "boolean"),
        new OA\Property(property: "pdfexportfont", type: "string"),
        new OA\Property(property: "idnumber", type: "string"),
        new OA\Property(property: "showgrades", type: "boolean"),
        new OA\Property(property: "showreports", type: "boolean"),
        new OA\Property(property: "newsitems", type: "integer"),
        new OA\Property(property: "visible", type: "boolean"),
        new OA\Property(property: "maxbytes", type: "integer"),
        new OA\Property(property: "groupmode", type: "integer"),
        new OA\Property(property: "groupmodeforce", type: "integer"),
        new OA\Property(property: "defaultgroupingid", type: "integer"),
        new OA\Property(property: "lang", type: "string"),
        new OA\Property(property: "timecreated", type: "integer"),
        new OA\Property(property: "timemodified", type: "integer"),
        new OA\Property(property: "forcetheme", type: "string"),
        new OA\Property(property: "enablecompletion", type: "boolean"),
        new OA\Property(property: "completionnotify", type: "boolean"),
        new OA\Property(
            property: "customfields",
            type: "array",
            items: new OA\Items(
                type: "object",
                properties: [
                    new OA\Property(property: "shortname", type: "string"),
                    new OA\Property(property: "value", type: "string")
                ]
            )
        ),
        new OA\Property(
            property: "courseformatoptions",
            type: "array",
            items: new OA\Items(
                type: "object",
                properties: [
                    new OA\Property(property: "name", type: "string"),
                    new OA\Property(property: "value", type: "string")
                ]
            )
        )
    ]
)]
class course_transformer extends abstract_defined_transformer {

    protected context_course $context;

    protected function define_properties() : array {

        $can_edit = isset($this->context) ? has_capability('moodle/course:update', $this->context) : false;

        $definition = [
            'id' => PARAM_INT,
            'fullname' => PARAM_RAW,
            'shortname' => PARAM_RAW,
            'displayname' => PARAM_RAW,
            'categoryid' => PARAM_INT,
            'summary' => PARAM_RAW,
            'format' => PARAM_PLUGIN,
            'startdate' => PARAM_INT,
            'enddate' => PARAM_INT,
            'showactivitydates' => PARAM_BOOL,
            'showcompletionconditions' => PARAM_BOOL,
            'pdfexportfont' => PARAM_RAW,

            // Extra fields
            'customfields' => PARAM_RAW,
            'courseformatoptions' => PARAM_RAW,
        ];

        if($can_edit){
            $definition = $definition + [
                'idnumber' => PARAM_RAW,
                'showgrades' => PARAM_BOOL,
                'showreports' => PARAM_BOOL,
                'newsitems' => PARAM_INT,
                'visible' => PARAM_BOOL,
                'maxbytes' => PARAM_INT,
                'groupmode' => PARAM_INT,
                'groupmodeforce' => PARAM_INT,
                'defaultgroupingid' => PARAM_INT,
                'lang' => PARAM_LANG,
                'timecreated' => PARAM_INT,
                'timemodified' => PARAM_INT,
                'forcetheme' => PARAM_THEME,
                'enablecompletion' => PARAM_BOOL,
                'completionnotify' => PARAM_BOOL,
            ];
        }

        return $definition;
    }

    public function transform(mixed $data): array {
        if(!is_object($data) && !is_array($data)){
            throw new \InvalidArgumentException("Expected an array or object");
        }

        $course = (object) $data;
        $this->context = context_course::instance($course->id, IGNORE_MISSING);
        $course = $this->enhance_course_data($course);

        $definition = $this->define_properties(); // Must come after context_course::instance

        $transformed_data = [];
        foreach ($course as $key => $value) {
            if(isset($definition[$key])){
                $transformed_data[$key] = $this->clean_param($value, $definition[$key]);
            }
        }

        return $transformed_data;
    }

    protected function enhance_course_data(object $course) : array {
        if(isset($course->category)){
            $course->categoryid = $course->category;
            unset($course->category);
        }
        
        if(isset($course->format)){
            $course_format = course_get_format($course);
            $course_format_options = $course_format->get_format_options();
        }else{
            $course_format_options = [];
        }
            
        if (isset($course->fullname)) {
            $course->fullname = util::format_string($course->fullname, $this->context);
            $course->displayname = util::format_string(get_course_display_name_for_list($course), $this->context);
        }
    
        if (isset($course->shortname)) {
            $course->shortname = util::format_string($course->shortname, $this->context);
        }
    
    
        if (!empty($course->summary)) {
            [$course->summary, $course->summaryformat] = util::format_text(
                $course->summary,
                $course->summaryformat ?? 0,
                $this->context,
                'course',
                'summary',
                0
            );
        }
    
        if(!empty($course_format_options)){
            $course->courseformatoptions = [];
            foreach ($course_format_options as $key => $value) {
                $course->courseformatoptions[] = (object)[
                    'name'  => $key,
                    'value' => $value
                ];
            }
        }

        $handler = \core_course\customfield\course_handler::create();
        if ($customfields = $handler->export_instance_data($course->id)) {
            $course->customfields = [];
            foreach ($customfields as $data) {
                $course->customfields[] = [
                    'type' => $data->get_type(),
                    'value' => $data->get_value(),
                    'valueraw' => $data->get_data_controller()->get_value(),
                    'name' => $data->get_name(),
                    'shortname' => $data->get_shortname()
                ];
            }
        }
        
        return (array)$course;
    }
}
    