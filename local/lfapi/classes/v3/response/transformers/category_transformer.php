<?php namespace local_lfapi\v3\response\transformers;

use \webservice_api\http\response\transformers\abstract_defined_transformer;
use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "category_transformer",
    title: new lfapi_string('docs:category_transformer:title'),
    required: ["id", "name"],
    properties: [
        new OA\Property(property: "id", type: "integer", example: 3),
        new OA\Property(property: "name", type: "string", example: "Categoria 03"),
        new OA\Property(property: "idnumber", type: "string", example: "cat03"),
        new OA\Property(property: "description", type: "string", example: "Lorem ipsum..."),
        new OA\Property(property: "descriptionformat", type: "integer", example: 0),
        new OA\Property(property: "parent", type: "integer", example: 1),
        new OA\Property(property: "coursecount", type: "integer", example: 10),
        new OA\Property(property: "visible", type: "boolean", example: true),
        new OA\Property(property: "timemodified", type: "integer", example: 1609459200),
        new OA\Property(property: "depth", type: "integer", example: 2),
        new OA\Property(property: "path", type: "string", example: "/1/3"),
        new OA\Property(property: "theme", type: "string", example: "smart")
    ]
)]
class category_transformer extends abstract_defined_transformer {

    protected function define_properties() : array {
        return [
            'id' => PARAM_INT,
            'name' => PARAM_RAW,
            'idnumber' => PARAM_RAW,
            'description' => PARAM_RAW,
            'descriptionformat' => PARAM_INT,
            'parent' => PARAM_INT,
            'coursecount' => PARAM_INT,
            'visible' => PARAM_BOOL,
            'timemodified' => PARAM_INT,
            'depth' => PARAM_INT,
            'path' => PARAM_TEXT,
            'theme' => PARAM_THEME,
        ];
    }
}
    