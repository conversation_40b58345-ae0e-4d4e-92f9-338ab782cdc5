<?php namespace local_lfapi\v3\response\transformers;

require_once($CFG->dirroot.'/user/lib.php');

use \context_system;
use \core_external\util;
use \webservice_api\http\response\transformers\abstract_defined_transformer;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "grade_transformer",
    title: new lfapi_string('docs:grade_transformer:title'),
    description: new lfapi_string('docs:grade_transformer:description'),
    required: ["id", "rawgrade", "finalgrade"],
    properties: [
        new OA\Property(property: "id", type: "integer", example: 123),
        new OA\Property(property: "feedback", type: "string", nullable: true, example: "A.O.K"),
        new OA\Property(property: "information", type: "string", nullable: true),
        new OA\Property(property: "rawgrade", type: "number", format: "float", example: 75.5),
        new OA\Property(property: "rawgrademax", type: "number", format: "float", example: 100.0),
        new OA\Property(property: "rawgrademin", type: "number", format: "float", example: 0.0),
        new OA\Property(property: "finalgrade", type: "number", format: "float", example: 80.0)
    ]
)]
class grade_transformer extends abstract_defined_transformer {

    protected function define_properties() : array {
        return [
            'id' => PARAM_INT,
            'feedback' => PARAM_RAW,
            'information' => PARAM_RAW,
            'rawgrade' => PARAM_FLOAT,
            'rawgrademax' => PARAM_FLOAT,
            'rawgrademin' => PARAM_FLOAT,
            'finalgrade' => PARAM_FLOAT,
        ];
    }

    public function transform(mixed $data): array {
        if(!is_object($data) && !is_array($data)){
            throw new \InvalidArgumentException("Expected an array or object");
        }

        $grade = (array) $data;
        $context = context_system::instance();

        if(!empty($grade['feedback'])){
            $grade['feedback'] = util::format_text($grade['feedback'], $grade['feedbackformat'], $context);
        }

        if(!empty($grade['information'])){
            $grade['information'] = util::format_text($grade['information'], $grade['informationformat'], $context);
        }

        return parent::transform($grade);
    }
}
    