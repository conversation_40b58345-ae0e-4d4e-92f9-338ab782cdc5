<?php namespace local_lfapi\v3\response\transformers;

use \local_lfapi\v3\queries\get_course_grade_query;
use \local_lfapi\v3\queries\get_course_query;
use \local_lfapi\v3\queries\get_user_query;
use \local_lfapi\v3\response\resources\entities\course_resource;
use \local_lfapi\v3\response\resources\entities\grade_resource;
use \local_lfapi\v3\response\resources\entities\user_resource;
use \webservice_api\http\response\transformers\abstract_defined_transformer;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "course_completion_transformer",
    title: new lfapi_string('docs:course_completion_transformer:title'),
    required: ["id", "courseid", "userid"],
    properties: [
        new OA\Property(property: "id", type: "integer", example: 1),
        new OA\Property(property: "courseid", type: "integer", example: 12),
        new OA\Property(property: "userid", type: "integer", example: 45),
        new OA\Property(property: "timeenrolled", type: "integer", example: 1672531200),
        new OA\Property(property: "timestarted", type: "integer", example: 1672617600),
        new OA\Property(property: "timecompleted", type: "integer", example: 1675209600),
        new OA\Property(property: "lastaccess", type: "integer", example: 1675209600),
        new OA\Property(property: "reaggregate", type: "integer", example: 0),
    ]
)]
class course_completion_transformer extends abstract_defined_transformer {

    protected bool $include_course = false;
    protected bool $include_user = false;
    protected bool $include_grade = false;
    protected bool $include_course_lastaccess = false;

    protected function define_properties() : array {
        return [
            'id' => PARAM_INT,
            'courseid' => PARAM_INT,
            'userid' => PARAM_INT,
            'timeenrolled' => PARAM_INT,
            'timestarted' => PARAM_INT,
            'timecompleted' => PARAM_INT,
            'reaggregate' => PARAM_INT,

            // Extra
            'course' => PARAM_RAW,
            'user' => PARAM_RAW,
            'grade' => PARAM_RAW,
            'lastaccess' => PARAM_INT,
        ];
    }

    public function include_course() : static {
        $this->include_course = true;
        return $this;
    }

    public function include_user() : static {
        $this->include_user = true;
        return $this;
    }

    public function include_grade() : static {
        $this->include_grade = true;
        return $this;
    }

    public function include_course_lastaccess() : static {
        $this->include_course_lastaccess = true;
        return $this;
    }

    public function transform(mixed $data): array {
        global $DB;

        if(!is_object($data) && !is_array($data)){
            throw new \InvalidArgumentException("Expected an array or object");
        }
        $modified_data = (array) $data;
        $modified_data['courseid'] = $modified_data['course'];
        unset($modified_data['course']);

        if($this->include_course){
            $query = new get_course_query($DB, $modified_data['courseid']);
            $modified_data['course'] = new course_resource($query->execute());
        }

        if($this->include_user){
            $query = new get_user_query($DB, $modified_data['userid']);
            $modified_data['user'] = new user_resource($query->execute());
        }

        if($this->include_grade){
            $query = new get_course_grade_query($DB, $modified_data['courseid'], $modified_data['userid']);
            if($grade = $query->execute()){
                $modified_data['grade'] = new grade_resource($query->execute());
            }
        }

        if($this->include_course_lastaccess){
            $modified_data['lastaccess'] = $this->get_last_course_last_access($modified_data);
        }

        return parent::transform($modified_data);
    }

    protected function get_last_course_last_access(array $data) : int {
        global $DB;

        return $DB->get_field('user_lastaccess', 'timeaccess', [
            'userid' => $data['userid'],
            'courseid' => $data['courseid'],
        ]) ?: 0;
    }
}
    