<?php namespace local_lfapi\v3\response\resources\entities;

use \webservice_api\helpers\routing\api_route_helper;
use \local_lfapi\v3\response\resources\abstract_transformable_resource;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "user_resource",
    title: new lfapi_string('docs:user_resource:title'),
    description: new lfapi_string('docs:user_resource:description'),
    allOf: [
        new OA\Schema(ref: "#/components/schemas/user_transformer"),
        new OA\Schema(
            type: "object",
            properties: [
                new OA\Property(
                    property: "_links",
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "self",
                            type: "object",
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/users/{userid}"),
                                new OA\Property(property: "method", type: "string", example: "GET")
                            ]
                        ),
                        new OA\Property(
                            property: "update",
                            type: "object",
                            nullable: true,
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/users/{userid}"),
                                new OA\Property(property: "method", type: "string", example: "PATCH")
                            ]
                        ),
                        new OA\Property(
                            property: "delete",
                            type: "object",
                            nullable: true,
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/users/{userid}"),
                                new OA\Property(property: "method", type: "string", example: "DELETE")
                            ]
                        )
                    ]
                )
            ]
        )
    ]
)]
class user_resource extends abstract_transformable_resource {

    protected array $embeddable_properties = [];

    protected function transform_attributes(array|object $user) : array {
        $transformer = new \local_lfapi\v3\response\transformers\user_transformer();
        return $transformer($user);
    }

    public function add_default_links() : static {
        $uri = api_route_helper::get_api_absolute_uri('/v3/users/' . $this->get_attribute('id'));

        $this->add_link('self', $uri, 'GET');
        $this->add_link('update', $uri, 'PATCH');
        $this->add_link('delete', $uri, 'DELETE');

        return $this;
    }
}
