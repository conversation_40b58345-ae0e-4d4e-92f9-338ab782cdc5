<?php namespace local_lfapi\v3\response\resources\entities;

use \webservice_api\helpers\routing\api_route_helper;
use \local_lfapi\v3\response\resources\abstract_transformable_resource;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "grade_resource",
    title: new lfapi_string('docs:grade_resource:title'),
    description: new lfapi_string('docs:grade_resource:description'),
    required: ["id", "userid", "courseid"],
    properties: [
        new OA\Property(property: "id", type: "integer", example: 103),
        new OA\Property(property: "userid", type: "integer", example: 42),
        new OA\Property(property: "courseid", type: "integer", example: 15),
        new OA\Property(property: "rawgrade", type: "number", format: "float", example: 87.5),
        new OA\Property(property: "finalgrade", type: "number", format: "float", example: 90.0),
        new OA\Property(property: "percentage", type: "number", format: "float", example: 90.0),
        new OA\Property(property: "feedback", type: "string", example: "Excelente participação."),
        new OA\Property(property: "hidden", type: "boolean", example: false),
        new OA\Property(property: "locked", type: "boolean", example: false),
        new OA\Property(property: "overridden", type: "boolean", example: false),
        new OA\Property(property: "timecreated", type: "integer", example: 1704040200),
        new OA\Property(property: "timemodified", type: "integer", example: 1705040300)
    ]
)]
class grade_resource extends abstract_transformable_resource {

    protected array $embeddable_properties = [];

    protected function transform_attributes(array|object $grade) : array {
        $transformer = new \local_lfapi\v3\response\transformers\grade_transformer();
        return $transformer($grade);
    }

    public function add_default_links() : static {
        return $this;
    }
}
