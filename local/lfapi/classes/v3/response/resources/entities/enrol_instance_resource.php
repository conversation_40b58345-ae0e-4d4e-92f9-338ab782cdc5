<?php namespace local_lfapi\v3\response\resources\entities;

use \webservice_api\helpers\routing\api_route_helper;
use \local_lfapi\v3\response\resources\abstract_transformable_resource;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "enrol_instance_resource",
    title: new lfapi_string('docs:enrol_instance_resource:title'),
    description: new lfapi_string('docs:enrol_instance_resource:description'),
    allOf: [
        new OA\Schema(ref: "#/components/schemas/enrol_instance_transformer"),
        new OA\Schema(
            type: "object",
            properties: [
                new OA\Property(
                    property: "_links",
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "self",
                            type: "object",
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/courses/{courseid}/enrol-instances/{instanceid}"),
                                new OA\Property(property: "method", type: "string", example: "GET")
                            ]
                        ),
                        new OA\Property(
                            property: "update",
                            type: "object",
                            nullable: true,
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/courses/{courseid}/enrol-instances/{instanceid}"),
                                new OA\Property(property: "method", type: "string", example: "PATCH")
                            ]
                        ),
                        new OA\Property(
                            property: "delete",
                            type: "object",
                            nullable: true,
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/courses/{courseid}/enrol-instances/{instanceid}"),
                                new OA\Property(property: "method", type: "string", example: "DELETE")
                            ]
                        ),
                        new OA\Property(
                            property: "enrolments",
                            type: "object",
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/courses/{courseid}/user-enrolments?enrolid={instanceid}"),
                                new OA\Property(property: "method", type: "string", example: "GET")
                            ]
                        )
                    ]
                ),
                new OA\Property(
                    property: "_embedded",
                    type: "object",
                    nullable: true,
                    properties: [
                        new OA\Property(
                            property: "course",
                            ref: "#/components/schemas/course_resource"
                        )
                    ]
                )
            ]
        )
    ]
)]
class enrol_instance_resource extends abstract_transformable_resource {

    protected array $embeddable_properties = [];

    protected function transform_attributes(array|object $data) : array {
        $transformer = new \local_lfapi\v3\response\transformers\enrol_instance_transformer();
        return $transformer($data);
    }

    public function add_default_links() : static {
        $id = $this->get_attribute('id');
        $courseid = $this->get_attribute('courseid');
        $uri = api_route_helper::get_api_absolute_uri("/v3/courses/$courseid/enrol-instances/$id");

        $this->add_link('self', $uri, 'GET');
        // $this->add_link('update', $uri, 'PATCH');
        // $this->add_link('delete', $uri, 'DELETE');

        $ue_uri = api_route_helper::get_api_absolute_uri("/v3/courses/$courseid/user-enrolments", ['enrolid' => $id]);
        $this->add_link('enrolments', $ue_uri, 'GET');
        return $this;
    }
}
