<?php namespace local_lfapi\v3\response\resources\entities;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "detailed_user_resource",
    title: new lfapi_string('docs:detailed_user_resource:title'),
    description: new lfapi_string('docs:detailed_user_resource:description'),
    allOf: [
        new OA\Schema(ref: "#/components/schemas/user_transformer"),
        new OA\Schema(
            type: "object",
            properties: [
                new OA\Property(
                    property: "_embedded",
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "roles",
                            type: "array",
                            items: new OA\Items(
                                type: "object",
                                properties: [
                                    new OA\Property(property: "roleid", type: "integer"),
                                    new OA\Property(property: "name", type: "string"),
                                    new OA\Property(property: "shortname", type: "string"),
                                    new OA\Property(property: "sortorder", type: "integer")
                                ]
                            )
                        ),
                        new OA\Property(
                            property: "enrolledcourses",
                            type: "array",
                            items: new OA\Items(
                                type: "object",
                                properties: [
                                    new OA\Property(property: "id", type: "integer"),
                                    new OA\Property(property: "fullname", type: "string"),
                                    new OA\Property(property: "shortname", type: "string")
                                ]
                            )
                        ),
                        new OA\Property(
                            property: "preferences",
                            type: "array",
                            items: new OA\Items(
                                type: "object",
                                properties: [
                                    new OA\Property(property: "name", type: "string"),
                                    new OA\Property(property: "value", type: "string")
                                ]
                            )
                        )
                    ]
                ),
            ]
        ),
        new OA\Schema(
            type: "object",
            properties: [
                new OA\Property(
                    property: "_links",
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "self",
                            type: "object",
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/users/{userid}"),
                                new OA\Property(property: "method", type: "string", example: "GET")
                            ]
                        ),
                        new OA\Property(
                            property: "update",
                            type: "object",
                            nullable: true,
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/users/{userid}"),
                                new OA\Property(property: "method", type: "string", example: "PATCH")
                            ]
                        ),
                        new OA\Property(
                            property: "delete",
                            type: "object",
                            nullable: true,
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/users/{userid}"),
                                new OA\Property(property: "method", type: "string", example: "DELETE")
                            ]
                        )
                    ]
                )
            ]
        )
    ]
)]
class detailed_user_resource extends user_resource {

    protected array $embeddable_properties = [
        'preferences', 'roles', 'enrolledcourses',
    ];

    protected function transform_attributes(array|object $data) : array {
        $transformer = new \local_lfapi\v3\response\transformers\user_transformer();
        $transformer->include_all_details();
        return $transformer($data);
    }
}
