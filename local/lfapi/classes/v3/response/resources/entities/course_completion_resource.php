<?php namespace local_lfapi\v3\response\resources\entities;

use \webservice_api\helpers\routing\api_route_helper;
use \local_lfapi\v3\response\resources\abstract_transformable_resource;
use \OpenApi\Attributes as OA;
use \local_lfapi\lfapi_string;

#[OA\Schema(
    schema: "course_completion_resource",
    title: new lfapi_string('docs:course_completion_resource:title'),
    description: new lfapi_string('docs:course_completion_resource:description'),
    allOf: [
        new OA\Schema(ref: "#/components/schemas/course_completion_transformer"),
        new OA\Schema(
            type: "object",
            properties: [
                new OA\Property(
                    property: "_links",
                    type: "object",
                    properties: [
                        new OA\Property(
                            property: "self",
                            type: "object",
                            required: ["href", "method"],
                            properties: [
                                new OA\Property(property: "href", type: "string", format: "uri", example: "https://example.com/webservice/api/v3/course-completions/{completionid}"),
                                new OA\Property(property: "method", type: "string", example: "GET")
                            ]
                        ),
                    ],
                ),
            ]
        )
    ]
)]
class course_completion_resource extends abstract_transformable_resource {

    protected array $embeddable_properties = ['course', 'user', 'grade'];

    protected function transform_attributes(array|object $data) : array {
        $transformer = new \local_lfapi\v3\response\transformers\course_completion_transformer();
        $transformer->include_grade();
        $transformer->include_course_lastaccess();        
        return $transformer($data);
    }

    public function add_default_links() : static {
        $id = $this->get_attribute('id');
        $uri = api_route_helper::get_api_absolute_uri("/v3/course-completions/$id");

        $this->add_link('self', $uri, 'GET');
        return $this;
    }
}
