<?php namespace local_lfapi\v3\response\resources;

use \webservice_api\http\response\resources\hal_resource;
use \moodle_url;

class paginated_hal_resource extends hal_resource {
    
    public function set_limit(int $limit) : static {
        $this->add_attribute('limit', $limit);
        return $this;
    }

    public function add_next_link(string|moodle_url $href, $method = 'GET', array $attributes = []) : static {
        $this->add_link('next', $href, $method, $attributes);
        return $this;
    }

    public function add_previous_link(string|moodle_url $href, $method = 'GET', array $attributes = []) : static {
        $this->add_link('prev', $href, $method, $attributes);
        return $this;
    }

    public function add_self_link(string|moodle_url $href, $method = 'GET', array $attributes = []) : static {
        $this->add_link('self', $href, $method, $attributes);
        return $this;
    }
}
