<?php namespace local_lfapi\v3\response\resources;

use \webservice_api\http\response\resources\hal_resource;

abstract class abstract_transformable_resource extends hal_resource {

    protected array $embeddable_properties = [];

    public function __construct(array|object $properties = []){
        parent::__construct($this->transform_attributes($properties));
        
        // Embeddings
        $this->embed_embeddable_properties();

        // Adding links
        $this->add_default_links();
    }

    protected function embed_embeddable_properties(){
        foreach ($this->embeddable_properties as $property) {
            $value = $this->get_attribute($property);

            if(is_array($value) || is_object($value)){
                $this->embed($property, $value);
                $this->remove_attribute($property);
            }
        }
    }

    abstract protected function transform_attributes(array|object $attributes) : array;

    abstract public function add_default_links() : static;
}
