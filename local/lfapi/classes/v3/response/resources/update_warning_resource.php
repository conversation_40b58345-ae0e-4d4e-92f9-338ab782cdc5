<?php namespace local_lfapi\v3\response\resources;

use \webservice_api\http\response\resources\hal_resource;

use \local_lfapi\lfapi_string;
use \OpenApi\Attributes as OA;

#[OA\Schema(
    schema: "update_warning_resource",
    title: new lfapi_string('docs:update_warning_resource:title'),
    description: new lfapi_string('docs:update_warning_resource:description'),
    type: "object",
    required: ["id", "resource", "message"],
    properties: [
        new OA\Property(property: "id", type: "integer"),
        new OA\Property(property: "resource", type: "string"),
        new OA\Property(property: "message", type: "string")
    ]
)]
class update_warning_resource extends hal_resource {

    public function __construct(array|object $attributes = []){
        $attributes = (array) $attributes;
        $this->attributes = [
            'id' => (int) $attributes['itemid'],
            'resource' => $attributes['item'],
            'message' => $attributes['message'],
        ];
    }

}
