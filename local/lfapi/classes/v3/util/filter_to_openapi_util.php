<?php namespace local_lfapi\v3\util;

use \local_lfapi\v3\filters\lfapi_lhs_filter;
use \core_external\external_single_structure;
use \local_lfapi\lfapi_string;

/**
 * Reads a filter and writes a OA\Parameter
 * definition for it.
 */
class filter_to_openapi_util {
    protected external_single_structure $external_description;

    public function __construct(protected lfapi_lhs_filter $filter){}

    public function add_context_from_external_description(external_single_structure $description) : static {
        $this->external_description = $description;
        return $this;
    }

    public function export() : string {
        $reflection = new \ReflectionClass($this->filter);
        $class_shortname = $reflection->getShortName();
        $method = $reflection->getMethod('get_fields_definition');
        $method->setAccessible(true);
        $fields = $method->invoke($this->filter);

        $definitions = [];
        $strings = '';

        foreach ($fields as $field => $definition) {
            $operators = $this->translate_operators($definition);
            $str_name = "docs:$class_shortname:$field";

            $strings .= "\n" . '$string[\'' . $str_name . '\'] = \'xxxxxxxxxx\';';

            $description = "new OA\Parameter(";
            $description .= "\n\tname : '$field',";
            $description .= "\n\tin : 'query',";
            $description .= "\n\trequired : " . (empty($definition['required']) ? 'false' : 'true') . ',';
            $description .= "\n\tdescription : new lfapi_string('$str_name', '$operators'),";
            $description .= "\n\tschema : " . $this->translate_schema($definition) . ',';
            $description .= "\n)";

            $definitions[] = $description;
        }

        return implode(",\n", $definitions) . "\n\n\n$strings\n";
    }

    protected function translate_operators(array $definition) : string {
        return '[' . implode('|', $definition['operators']) . ']';
    }

    protected function translate_schema($definition) : string {
        $type =  match ($definition['type']) {
            PARAM_INT, PARAM_BOOL => 'integer',
            PARAM_FLOAT => 'number',
            default => 'string',
        };

        return "new OA\Schema(type: '$type')";
    }
}
