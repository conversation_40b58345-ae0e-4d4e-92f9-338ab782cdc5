<?php namespace local_lfapi\v3\util;

use core_external\external_description;
use core_external\external_single_structure;
use core_external\external_multiple_structure;
use core_external\external_value;

class external_to_openapi_util {

    protected array $strings = [];

    public function __construct(
        protected external_single_structure $external_description,
        protected string $prefix
    ) {}

    public function export_strings() : string {
        $strings = '';
        foreach ($this->strings as $key => $value) {
            $strings .= "\n" . '$string[\'' . $key . '\'] = \''.$value.'\';';
        }
        return $strings;
    }

    public function export_to_single_structure(string $propertyName): string {
        $schemaInside = $this->translate_schema($this->external_description, $propertyName, 4);

        $schema = <<<EOD
new OA\Schema(
    type: 'object',
    properties: [
        new OA\Property(
            property: '$propertyName',
            required: true,
            schema: $schemaInside
        )
    ]
)
EOD;

        return <<<EOD
new OA\RequestBody(
    required: true,
    content: new OA\MediaType(
        mediaType: "application/json",
        schema: $schema
    )
)
EOD;
    }

    public function export_to_multiple_structure(string $propertyName): string {
        $itemSchema = $this->translate_schema($this->external_description, $propertyName, 5);

        $schema = <<<EOD
new OA\Schema(
    type: 'object',
    properties: [
        new OA\Property(
            property: '$propertyName',
            required: true,
            schema: new OA\Schema(
                type: 'array',
                items: $itemSchema
            )
        )
    ]
)
EOD;

        return <<<EOD
new OA\RequestBody(
    required: true,
    content: new OA\MediaType(
        mediaType: "application/json",
        schema: $schema
    )
)
EOD;
    }

    protected function translate_schema(external_description $definition, string $key, int $indentLevel = 0): string {
        if ($definition instanceof external_value) {
            $type = match ($definition->type) {
                PARAM_INT, PARAM_BOOL => 'integer',
                PARAM_FLOAT => 'number',
                default => 'string',
            };

            return "new OA\Schema(type: '$type')";
        }

        if ($definition instanceof external_multiple_structure) {
            $itemSchema = $this->translate_schema($definition->content, $key, $indentLevel + 1);
            return <<<EOD
new OA\Schema(
{$this->indent("type: 'array',\nitems: $itemSchema", $indentLevel + 1)}
)
EOD;
        }

        if ($definition instanceof external_single_structure) {
            $properties = [];
            foreach ($definition->keys as $childkey => $child) {
                $childSchema = $this->translate_schema($child, $childkey, $indentLevel + 2);

                $strname = "docs:$this->prefix:$key:$childkey";
                $childDesc = "new lfapi_string('$strname')";
                $this->strings[$strname] = $child->desc ?? '';
                $required = ($child->required === VALUE_REQUIRED) ? 'true' : 'false';

                $prop = <<<EOD
new OA\Property(
    property: '$childkey',
    description: $childDesc,
    required: $required,
    schema: $childSchema
)
EOD;
                $properties[] = $this->indent($prop, $indentLevel + 2);
            }

            $propertiesStr = implode(",\n\n", $properties);

            return <<<EOD
new OA\Schema(
{$this->indent("type: 'object',\nproperties: [\n$propertiesStr\n" . str_repeat('    ', $indentLevel + 1) . "]", $indentLevel + 1)}
)
EOD;
        }

        return "new OA\Schema(type: 'string')";
    }

    private function indent(string $str, int $level): string {
        $pad = str_repeat('    ', $level);
        return implode("\n", array_map(fn($line) => $pad . $line, explode("\n", $str)));
    }

    public function get_strings(): array {
        return $this->strings;
    }
}
