<?php namespace local_lfapi\v3\queries;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \moodle_dev_utils\database\query\moodle_query;
use \moodle_dev_utils\database\query\interfaces\query_interface;
use \moodle_dev_utils\database\query\interfaces\paginator_interface;
use \moodle_dev_utils\database\query\interfaces\paginated_query_interface;
use \moodle_dev_utils\database\query\pagination\query_cursor_paginator;

use \moodle_database;
use \local_lfapi\v3\filters\category_sql_filter;

class list_categories_query implements query_interface, paginated_query_interface {

    const CUSTOM_FIELDS_TABLE = 'local_custom_fields_user';
    protected moodle_query $query;

    public function __construct( moodle_database $db, category_sql_filter $filter){
        $this->query = new moodle_query($db);
        $this->query->select('c.*')->from('course_categories', 'c');

        if($filter->has_conditions()){
            $this->query->where($filter->get_conditions('c'), $filter->get_parameters());
        }
    }

    public function execute(): array {
        $categories = [];
        foreach ($this->query->get_recordset() as $category) {
            $categories[] = $category;
        }
        return $categories;
    }

    /**
     * @return moodle_dev_utils\database\query\pagination\query_cursor_paginator
     */
    public function get_paginator(): paginator_interface {
        return new query_cursor_paginator($this->query, 'c.id');
    }
}
