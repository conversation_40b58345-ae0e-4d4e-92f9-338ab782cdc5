<?php namespace local_lfapi\v3\queries;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \moodle_dev_utils\database\query\moodle_query;
use \moodle_dev_utils\database\query\interfaces\query_interface;
use \moodle_dev_utils\database\query\interfaces\paginator_interface;
use \moodle_dev_utils\database\query\interfaces\paginated_query_interface;
use \moodle_dev_utils\database\query\pagination\query_cursor_paginator;

use \moodle_database;
use \local_lfapi\v3\filters\course_completion_sql_filter;

class list_course_completions_query implements query_interface, paginated_query_interface {

    protected moodle_query $query;

    public function __construct(
        moodle_database $db,
        course_completion_sql_filter $filter
    ) {
        $this->query = new moodle_query($db);
        $this->query->select('cc.*')->from('course_completions', 'cc');

        if($filter->has_conditions()){
            $this->query->where($filter->get_conditions('cc'), $filter->get_parameters());
        }
    }

    public function restrict_to_course(int $courseid) : static {
        $this->query->where('cc.course = :courseid', ['courseid' => $courseid]);
        return $this;
    }

    public function execute(): array {
        $completions = [];
        foreach ($this->query->get_recordset() as $completion) {
            $completions[] = $completion;
        }
        return $completions;
    }

    /**
     * @return moodle_dev_utils\database\query\pagination\query_cursor_paginator
     */
    public function get_paginator(): paginator_interface {
        return new query_cursor_paginator($this->query, 'cc.id');
    }
}
