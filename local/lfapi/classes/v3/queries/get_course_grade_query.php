<?php namespace local_lfapi\v3\queries;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \moodle_dev_utils\database\query\interfaces\query_interface;

use \moodle_database;
use \moodle_dev_utils\database\query\moodle_query;

class get_course_grade_query implements query_interface {
    protected moodle_database $db;
    protected moodle_query $query;

    public function __construct(moodle_database $db, int $courseid, int $userid) {
        $this->db = $db;

        $this->query = new moodle_query($db);
        $this->query->from('grade_items', 'gi');
        $this->query->select('gg.*')->inner_join('grade_grades', 'gi.id = gg.itemid', 'gg');
        $this->query->where('gi.courseid = :courseid', ['courseid' => $courseid]);
        $this->query->where('gi.itemtype = :itemtype', ['itemtype' => 'course']);
        $this->query->where('gg.userid = :userid', ['userid' => $userid]);
        $this->query->where('gg.finalgrade IS NOT NULL');
    }

    public function execute(): ?object {
        return $this->query->first();
    }
}
