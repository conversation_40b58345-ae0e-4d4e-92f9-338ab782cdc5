<?php namespace local_lfapi\v3\queries;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \moodle_dev_utils\database\query\moodle_query;
use \moodle_dev_utils\database\query\interfaces\query_interface;
use \moodle_dev_utils\database\query\interfaces\paginator_interface;
use \moodle_dev_utils\database\query\interfaces\paginated_query_interface;
use \moodle_dev_utils\database\query\pagination\query_cursor_paginator;

use \moodle_database;
use \local_lfapi\v3\filters\basic_enrol_instance_sql_filter;
use \local_lfapi\v3\filters\user_enrolment_sql_filter;

class list_user_enrols_query implements query_interface, paginated_query_interface {
    protected moodle_query $query;

    public function __construct(
        moodle_database $db,
        user_enrolment_sql_filter $filter,
        basic_enrol_instance_sql_filter $instance_filter
    ){
        $this->query = new moodle_query($db);
        $this->query->select('e.courseid, e.enrol')->from('enrol', 'e');
        $this->query->select('ue.*')->inner_join('user_enrolments', 'e.id = ue.enrolid', 'ue');

        if($instance_filter->has_conditions()){
            $this->query->where($instance_filter->get_conditions('e'), $instance_filter->get_parameters());
        }

        if($filter->has_conditions()){
            $this->query->where($filter->get_conditions('ue'), $filter->get_parameters());
        }
    }

    public function execute(): array {
        $enrolments = [];
        foreach ($this->query->get_recordset() as $enrolment) {
            $enrolments[] = $enrolment;
        }
        return $enrolments;
    }

    /**
     * @return moodle_dev_utils\database\query\pagination\query_cursor_paginator
     */
    public function get_paginator(): paginator_interface {
        return new query_cursor_paginator($this->query, 'e.id');
    }
}
