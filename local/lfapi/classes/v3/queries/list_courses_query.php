<?php namespace local_lfapi\v3\queries;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \moodle_dev_utils\database\query\moodle_query;
use \moodle_dev_utils\database\query\interfaces\query_interface;
use \moodle_dev_utils\database\query\interfaces\paginator_interface;
use \moodle_dev_utils\database\query\interfaces\paginated_query_interface;
use \moodle_dev_utils\database\query\pagination\query_cursor_paginator;

use \moodle_database;
use \local_lfapi\v3\filters\course_sql_filter;
use \local_lfapi\v3\filters\custom_course_fields_sql_filter;

class list_courses_query implements query_interface, paginated_query_interface {

    const CUSTOM_FIELDS_TABLE = 'local_custom_fields_course';
    protected moodle_query $query;

    public function __construct(
        moodle_database $db,
        course_sql_filter $course_filter,
        ?custom_course_fields_sql_filter $custom_fields_filter = null
    ) {

        $this->query = new moodle_query($db);
        $this->query->select('c.*')->from('course', 'c');
        $this->query->where('c.id != ' . SITEID);

        if($course_filter->has_conditions()){
            $this->query->where($course_filter->get_conditions('c'), $course_filter->get_parameters());
        }

        if($custom_fields_filter->has_conditions()){
            $custom_fields_filter->apply_joins($this->query, 'c.id');
        }
    }

    public function execute(): array {
        $courses = [];
        foreach ($this->query->get_recordset() as $course) {
            $courses[] = $course;
        }
        return $courses;
    }

    /**
     * @return moodle_dev_utils\database\query\pagination\query_cursor_paginator
     */
    public function get_paginator(): paginator_interface {
        return new query_cursor_paginator($this->query, 'c.id');
    }
}
