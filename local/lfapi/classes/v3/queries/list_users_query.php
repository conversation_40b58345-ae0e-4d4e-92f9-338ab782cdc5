<?php namespace local_lfapi\v3\queries;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \moodle_dev_utils\database\query\moodle_query;
use \moodle_dev_utils\database\query\interfaces\query_interface;
use \moodle_dev_utils\database\query\interfaces\paginator_interface;
use \moodle_dev_utils\database\query\interfaces\paginated_query_interface;
use \moodle_dev_utils\database\query\pagination\query_cursor_paginator;

use \moodle_database;
use \local_lfapi\v3\filters\user_sql_filter;
use \local_lfapi\v3\filters\profile_field_sql_filter;

class list_users_query implements query_interface, paginated_query_interface {

    protected moodle_query $query;

    public function __construct(
        moodle_database $db,
        user_sql_filter $user_filter,
        profile_field_sql_filter $profile_filter
    ) {

        $this->query = new moodle_query($db);
        $this->query->select('u.*')->from('user', 'u');
        $this->query->where('u.id != 1'); // Excluding Guest

        if($user_filter->has_conditions()){
            $this->query->where($user_filter->get_conditions('u'), $user_filter->get_parameters());
        }

        if($profile_filter->has_conditions()){
            $profile_filter->apply_joins($this->query, 'u.id');
        }
    }

    public function execute(): array {
        $users = [];
        foreach ($this->query->get_recordset() as $user) {
            $users[] = $user;
        }
        return $users;
    }

    /**
     * @return moodle_dev_utils\database\query\pagination\query_cursor_paginator
     */
    public function get_paginator(): paginator_interface {
        return new query_cursor_paginator($this->query, 'u.id');
    }
}
