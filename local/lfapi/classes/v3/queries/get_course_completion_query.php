<?php namespace local_lfapi\v3\queries;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \moodle_dev_utils\database\query\interfaces\query_interface;

use \moodle_database;

class get_course_completion_query implements query_interface {
    protected moodle_database $db;
    protected array $conditions;

    public function __construct(moodle_database $db, int $completionid) {
        $this->db = $db;
        $this->conditions = ['id' => $completionid];
    }

    public function execute(): ?object {
        return $this->db->get_record('course_completions', $this->conditions) ?: null;
    }
}
