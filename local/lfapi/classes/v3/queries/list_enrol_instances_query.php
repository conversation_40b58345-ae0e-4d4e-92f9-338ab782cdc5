<?php namespace local_lfapi\v3\queries;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \moodle_dev_utils\database\query\moodle_query;
use \moodle_dev_utils\database\query\interfaces\query_interface;
use \moodle_dev_utils\database\query\interfaces\paginator_interface;
use \moodle_dev_utils\database\query\interfaces\paginated_query_interface;
use \moodle_dev_utils\database\query\pagination\query_cursor_paginator;

use \moodle_database;
use \local_lfapi\v3\filters\basic_enrol_instance_sql_filter;

class list_enrol_instances_query implements query_interface, paginated_query_interface {
    protected moodle_query $query;

    public function __construct( moodle_database $db, basic_enrol_instance_sql_filter $filter){
        $this->query = new moodle_query($db);
        $this->query->select('e.*')->from('enrol', 'e');

        if($filter->has_conditions()){
            $this->query->where($filter->get_conditions('e'), $filter->get_parameters());
        }
    }

    public function execute(): array {
        $instances = [];
        foreach ($this->query->get_recordset() as $instance) {
            $instances[] = $instance;
        }
        return $instances;
    }

    /**
     * @return moodle_dev_utils\database\query\pagination\query_cursor_paginator
     */
    public function get_paginator(): paginator_interface {
        return new query_cursor_paginator($this->query, 'e.id');
    }
}
