<?php namespace local_lfapi\v3\queries;

require_once($CFG->dirroot . '/local/lfapi/vendor/autoload.php');

use \moodle_dev_utils\database\query\interfaces\query_interface;

use \moodle_database;
use \moodle_dev_utils\database\query\moodle_query;

class get_user_enrol_query implements query_interface {
    protected moodle_database $db;
    protected array $conditions;
    protected moodle_query $query;

    public function __construct(moodle_database $db, int $enrolmentid, ?int $courseid = null) {
        $this->db = $db;
    
        $this->query = new moodle_query($db);
        $this->query->select('e.courseid, e.enrol')->from('enrol', 'e');
        $this->query->select('ue.*')->inner_join('user_enrolments', 'e.id = ue.enrolid', 'ue');
        $this->query->where('ue.id = :enrolmentid', ['enrolmentid' => $enrolmentid]);

        if($courseid){
            $this->query->where('e.courseid = :courseid', ['courseid' => $courseid]);
        }
    }

    public function execute(): ?object {
        return $this->query->first();
    }
}
