<?php namespace local_lfapi;

class config{
    const SERVICE_SHORTNAME = 'lfapi';
    const SERVICE_NAME = 'LearningFlix API';
    const PLUGINNAME = 'local_lfapi';

    const CONFIG_DEBUG_ENABLED = 'enable_debug';
    const CONFIG_API_USERS_CREATED = 'api_users_created';

    protected static bool $services_loaded = false;
    protected static array $functions = [];
    protected static array $services = [];

    protected static function load_services(){
        global $CFG;

        $path = "$CFG->dirroot/local/lfapi/db/services.php";
        if(!file_exists($path = "$CFG->dirroot/local/lfapi/db/services.php")){
            return;
        }

        $functions = array();
        $services = array();
        include($path);

        self::$functions = $functions;
        self::$services = $services;
        self::$services_loaded = true;
    }

    public static function is_debug_info_enabled() : bool {
        return (bool) self::get(self::CONFIG_DEBUG_ENABLED, false);
    }

    public static function get_external_functions() : array {
        if(!self::$services_loaded){
            self::load_services();
        }

        return self::$functions;
    }

    public static function get_external_services() : array {
        if(!self::$services_loaded){
            self::load_services();
        }

        return self::$services;
    }

    public static function has_external_function(string $identified) : bool {
        return isset(self::get_external_functions()[$identified]);
    }


    /**
     * Return a plugin config by key
     *
     * @param string $key
     * @return mixed|null
     */
    public static function get($key, $default = null){
        return get_config(self::PLUGINNAME, $key) ?: $default;
    }

    /**
     * Sets a value to a config
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public static function set(string $key, $value){
        set_config($key, $value, self::PLUGINNAME);
    }

    /**
     * If the plugin is ready to be used
     *
     * @return boolean
     */
    public static function is_ready() : bool {
        return (bool) self::get(self::CONFIG_API_USERS_CREATED);
    }

    public static function get_page_size() : int {
        return 50;
    }

    public static function get_max_page_size() : int {
        return 100;
    }
}