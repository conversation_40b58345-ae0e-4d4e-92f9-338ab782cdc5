<?php namespace local_lfapi\external;

require_once($CFG->libdir . "/externallib.php");

defined('MOODLE_INTERNAL') || die();

use \coding_exception;
use \core_external\external_api;
use \core_external\external_warnings;
use \core_external\external_value;
use \core_external\external_single_structure;
use \core_external\external_function_parameters;
use \core_external\external_multiple_structure;
use \local_lfapi\models\users\abstract_user;

class external_client_credentials extends external_api {

    public static function generate_parameters(){
        return new external_function_parameters([
            'user' => new external_value(PARAM_RAW, 'User class'),
        ]);
    }

    public static function generate($user){
        $class = $user;
        if(!class_exists($class) || !is_subclass_of($class, abstract_user::class)){
            throw new coding_exception("Invalid user class");
        }

        // Getting or creating user
        $user = $class::exists() ? $class::get() : $class::create();
        $credentials = $user->generate_client_credentials();

        return [
            'client_id' => $credentials->get('client_id'),
            'client_secret' => $credentials->get_secret(),
        ];
    }

    public static function generate_returns() {
        return new external_single_structure([
            'client_id'      => new external_value(PARAM_RAW),
            'client_secret'  => new external_value(PARAM_RAW),
        ]);
    }
}
