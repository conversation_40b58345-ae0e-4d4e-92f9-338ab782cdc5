<?php namespace local_lfapi\admin;

class admin_setting_api_user_token extends \admin_setting {

    protected $class;
    protected $emptymessage;
    protected $size;

    /**
     * Config text constructor
     *
     * @param string $name unique ascii name, either 'mysetting' for settings that in config,
     *                     or 'myplugin/mysetting' for ones in config_plugins.
     * @param string $visiblename localised
     * @param string $description long localised info
     * @param string $defaultsetting
     * @param string $emptymessage message displayed if there is not value to be outputed
     */
    public function __construct($class, $name, $visiblename, $description, $emptymessage = '',  $size = 60) {
        $this->nosave = true;
        $this->class = $class;
        $this->emptymessage = $emptymessage;
        $this->size = $size;

        $defaultsetting = null;
        parent::__construct($name, $visiblename, $description, $defaultsetting);
    }

    /**
     * Get whether this should be displayed in LTR mode.
     *
     * Try to guess from the PARAM type unless specifically set.
     */
    public function get_force_ltr() {
        $forceltr = parent::get_force_ltr();
        return (bool) $forceltr;
    }

    public function write_setting($data) {
        return '';
    }

    /**
     * Return the setting
     *
     * @return mixed returns config if successful else null
     */
    public function get_setting() {
        $class = $this->class;

        if($class::exists()){
            $user = $class::get();
            $token = $user->get_token();
            return $token ? $token->token : null;
        }

        return null;
    }

    /**
     * Validate data before storage
     *
     * @param string $data data
     * @return mixed true if ok string if error found
     */
    public function validate($data) {
        return true;
    }

    /**
     * Return an XHTML string for the setting
     * @return string Returns an XHTML string
     */
    public function output_html($data, $query='') {
        global $OUTPUT;

        $default = $this->get_defaultsetting();
        $context = (object) [
            'size' => $this->size,
            'id' => $this->get_id(),
            'name' => $this->get_full_name(),
            'value' => $data,
            'forceltr' => $this->get_force_ltr(),
            'readonly' => true,
            'default' => $this->emptymessage
        ];
        $element = $OUTPUT->render_from_template('local_lfapi/setting_api_user_token', $context);

        return format_admin_setting($this, $this->visiblename, $element, $this->description, true, '', $default, $query);
    }
}