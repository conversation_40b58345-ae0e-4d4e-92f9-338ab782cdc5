<?php
namespace local_lfapi\admin;

use webservice_api\services\oauth2_credentials_service;

class admin_setting_api_credentials extends \admin_setting {

    protected $class;
    protected $empty_message;
    protected $size;

    /**
     * Constructor.
     *
     * @param string $class Name of the service class to be used (e.g. oauth2_credentials_service)
     * @param string $name Unique setting name (for example, 'api_credentials')
     * @param string $visible_name Localized label for the setting.
     * @param string $description Long localized description.
     * @param int $size Size of the input field.
     */
    public function __construct($class, $name, $visible_name, $description, $size = 60) {
        $this->nosave = true;
        $this->class = $class;
        $this->size = $size;

        $default_setting = null;
        parent::__construct($name, $visible_name, $description, $default_setting);
    }

    /**
     * Retrieve the API credentials.
     *
     * Uses the oauth2_credentials_service to obtain the credentials for the current user.
     *
     * @return array|null Associative array with keys 'client_id', and 'client_secret'
     *                    or null if no credentials exist.
     */
    public function get_setting(){
        $setting = [
            'client_id'     => null,
            'client_secret' => null
        ];

        $class = $this->class;
        if(!$class::exists()){
            return $setting;
        }
        
        $user = $class::get();
        $credentials = $user->get_client_credentials() ?? $user->generate_client_credentials();
        if(!$credentials){
            return $setting;
        }

        $setting['client_id'] = $credentials->get('client_id');
        $setting['client_secret'] = $credentials->get_secret();

        return $setting;
    }

    /**
     * This setting is display-only; it does not write any value.
     *
     * @param mixed $data
     * @return string Always returns an empty string.
     */
    public function write_setting($data) {
        return '';
    }

    /**
     * Render the HTML for the setting using a Mustache template.
     *
     * The output shows three fields: Client ID, and Client Secret.
     * The Client Secret field is masked (if a secret exists) and includes a "Regenerate"
     * button that triggers an AJAX call to regenerate the secret.
     *
     * @param mixed $data Associative array with keys 'client_id', and 'client_secret'.
     * @param string $query Search query for highlighting (if any).
     * @return string Rendered HTML.
     */
    public function output_html($data, $query = '') {
        global $OUTPUT;

        $context = (object)[
            'size'           => $this->size,
            'id'             => $this->get_id(),
            'name'           => $this->get_full_name(),
            'client_id'      => $data['client_id'],
            'client_secret'  => $data['client_secret'],
            'user'  => $this->class,
            'has_credentials' => !empty($data['client_id']),
        ];

        $element = $OUTPUT->render_from_template('local_lfapi/admin_setting_api_credentials', $context);
        return format_admin_setting($this, $this->visiblename, $element, $this->description, true, '', null, $query);
    }
}
