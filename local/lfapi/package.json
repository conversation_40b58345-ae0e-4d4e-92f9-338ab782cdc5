{"name": "local_lfapi", "version": "1.0.0", "description": "", "main": "", "directories": {"test": "tests"}, "scripts": {"testall": "cd ../../ && php ./vendor/bin/phpunit --testsuite local_lfapi_testsuite --testdox-html local/lfapi/tests/tests.html", "batchtest": "cd ../../ && php ./vendor/bin/phpunit --testsuite local_lfapi_testsuite --group batchtest --log-junit local/lfapi/tests/batchtest.xml", "testcurrent": "cd ../../ && php ./vendor/bin/phpunit --testsuite local_lfapi_testsuite --group current --testdox-html local/lfapi/tests/currents.html", "test": "cd ../../ && php ./vendor/bin/phpunit --testsuite local_lfapi_testsuite --exclude skip --exclude-group batchtest --testdox-html local/lfapi/tests/tests.html", "initphpunit": "cd ../../ && php admin/tool/phpunit/cli/init.php", "upgrade": "cd ../../admin/cli/ && php upgrade.php --non-interactive", "testcoverage": "cd ../../ && php ./vendor/bin/phpunit --testsuite local_lfapi_testsuite --coverage-html local/lfapi/tests/coverage.html"}, "author": "", "license": "", "devDependencies": {}}