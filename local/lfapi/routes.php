<?php

global $CFG;
require_once($CFG->dirroot . '/webservice/api/vendor/autoload.php');

use \League\Route\RouteGroup;
use \webservice_api\http\middlewares\auth\oauth2_token_auth;
use \webservice_api\http\middlewares\log\request_logger;

use \local_lfapi\v3\controllers\user_controller;
use \local_lfapi\v3\controllers\category_controller;
use \local_lfapi\v3\controllers\course_controller;
use \local_lfapi\v3\controllers\enrol_instance_controller;
use \local_lfapi\v3\controllers\user_enrolment_controller;
use \local_lfapi\v3\controllers\course_completion_controller;

/**
 * Routes definition file for API 3.0
 */

// Authenticated routes
$router->group('/v3', function (RouteGroup $route){
    // User routes
    $route->get('/users', [user_controller::class, 'list_users']);
    $route->get('/users/{userid}', [user_controller::class, 'get_user']);
    $route->post('/users', [user_controller::class, 'create_users']);
    $route->patch('/users', [user_controller::class, 'update_users']);
    $route->patch('/users/{userid}', [user_controller::class, 'update_user']);
    $route->delete('/users/{userid}', [user_controller::class, 'delete_user']);

    // Category routes
    $route->get('/categories', [category_controller::class, 'list_categories']);
    $route->get('/categories/{categoryid}', [category_controller::class, 'get_category']);
    $route->post('/categories', [category_controller::class, 'create_category']);
    $route->patch('/categories/{categoryid}', [category_controller::class, 'update_category']);
    $route->delete('/categories/{categoryid}', [category_controller::class, 'delete_category']);

    // Course routes
    $route->get('/courses', [course_controller::class, 'list_courses']);
    $route->get('/courses/{courseid}', [course_controller::class, 'get_course']);
    $route->post('/courses', [course_controller::class, 'create_courses']);
    $route->patch('/courses', [course_controller::class, 'update_courses']);
    $route->patch('/courses/{courseid}', [course_controller::class, 'update_course']);
    $route->delete('/courses/{courseid}', [course_controller::class, 'delete_course']);

    // Enrolment instance routes
    $route->get('/courses/{courseid}/enrol-instances', [enrol_instance_controller::class, 'list_instances']);
    $route->get(
        '/courses/{courseid}/enrol-instances/{instanceid}',
        [enrol_instance_controller::class, 'get_instance']
    );

    // User Enrolment routes
    $route->get('/courses/{courseid}/user-enrolments', [user_enrolment_controller::class, 'list_enrolments']);
    $route->get(
        '/courses/{courseid}/user-enrolments/{enrolmentid}',
        [user_enrolment_controller::class, 'get_enrolment']
    );
    $route->post('/courses/{courseid}/user-enrolments', [user_enrolment_controller::class, 'create_enrolment']);
    $route->patch('/courses/{courseid}/user-enrolments/{enrolmentid}', [user_enrolment_controller::class, 'update_enrolment']);
    $route->delete(
        '/courses/{courseid}/user-enrolments/{enrolmentid}',
        [user_enrolment_controller::class, 'delete_enrolment']
    );

    // Completion routes
    $route->get('/course-completions', [course_completion_controller::class, 'list_completions']);
    $route->get('/course-completions/{completionid}', [course_completion_controller::class, 'get_completion']);
    
})->middleware(new oauth2_token_auth())
    ->middleware(new request_logger());
