<?php
define('CLI_SCRIPT', true);
require(__DIR__ . '/../../../config.php');

use \local_lfapi\v3\util\filter_to_openapi_util;

$filters = [
    \local_lfapi\v3\filters\basic_enrol_instance_sql_filter::class,
    \local_lfapi\v3\filters\category_sql_filter::class,
    \local_lfapi\v3\filters\course_completion_sql_filter::class,
    \local_lfapi\v3\filters\course_sql_filter::class,
    \local_lfapi\v3\filters\custom_course_fields_sql_filter::class,
    \local_lfapi\v3\filters\enrol_instance_sql_filter::class,
    \local_lfapi\v3\filters\profile_field_sql_filter::class,
    \local_lfapi\v3\filters\user_enrolment_sql_filter::class,
    \local_lfapi\v3\filters\user_sql_filter::class,
    \local_lfapi\v3\filters\category_sql_filter::class,
    \local_lfapi\v3\filters\category_sql_filter::class,
    \local_lfapi\v3\filters\category_sql_filter::class,
    \local_lfapi\v3\filters\category_sql_filter::class,
    \local_lfapi\v3\filters\category_sql_filter::class,
];

foreach ($filters as $class) {
    echo "\n\n$class\n";
    $util = new filter_to_openapi_util(new $class());
    echo "\n" . $util->export();
}