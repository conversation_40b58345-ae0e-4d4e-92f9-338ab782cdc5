<?php
define('CLI_SCRIPT', true);
require(__DIR__ . '/../../../config.php');

global $CFG;
require_once($CFG->dirroot . '/course/externallib.php');
require_once($CFG->dirroot . '/user/externallib.php');


use \local_lfapi\v3\util\external_to_openapi_util;

$external_definitions = [
    // 'create_category' => \core_course_external::create_categories_parameters()->keys['categories']->content,
    // 'create_course' => \core_course_external::create_courses_parameters()->keys['courses']->content,
    'create_user' => \core_user_external::create_users_parameters()->keys['users']->content
];

foreach ($external_definitions as $prefix => $external_definition) {
    echo "\n\n".$prefix."\n";
    $util = new external_to_openapi_util($external_definition, $prefix);
    echo "\n" . $util->export_to_single_structure($prefix);
    echo "\n" . $util->export_strings();
}