<?php return array(
    'root' => array(
        'name' => 'local/lfapi',
        'pretty_version' => 'dev-develop',
        'version' => 'dev-develop',
        'reference' => '4cd96f4d83e6c1d6d031b097aadc806b2ce9b069',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'linkisensei/moodle-dev-utils' => array(
            'pretty_version' => '0.0.3',
            'version' => '0.0.3.0',
            'reference' => 'c69a29df852ba2e83ccde20f1784bc3c6668c3cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../linkisensei/moodle-dev-utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'local/lfapi' => array(
            'pretty_version' => 'dev-develop',
            'version' => 'dev-develop',
            'reference' => '4cd96f4d83e6c1d6d031b097aadc806b2ce9b069',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
