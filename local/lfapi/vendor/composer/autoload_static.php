<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitec2c88977232a0f4ca9ee9323555bfbc
{
    public static $prefixLengthsPsr4 = array (
        'm' => 
        array (
            'moodle_dev_utils\\' => 17,
        ),
        'P' => 
        array (
            'Psr\\Http\\Message\\' => 17,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'moodle_dev_utils\\' => 
        array (
            0 => __DIR__ . '/..' . '/linkisensei/moodle-dev-utils/src',
        ),
        'Psr\\Http\\Message\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/http-message/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitec2c88977232a0f4ca9ee9323555bfbc::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitec2c88977232a0f4ca9ee9323555bfbc::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitec2c88977232a0f4ca9ee9323555bfbc::$classMap;

        }, null, ClassLoader::class);
    }
}
