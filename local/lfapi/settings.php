<?php

/**
 * Plugin administration pages are defined here.
 *
 * @package     local_lfapi
 * @category    local
 * @copyright   2024 Revvo <<EMAIL>>
 */

if ($hassiteconfig) {
    $settingspage = new admin_settingpage(
        'managelfapi',
        new \local_lfapi\lfapi_string('manage'),
        'local/lfapi:config'
    );

    if ($ADMIN->fulltree) {
        $config = new \local_lfapi\config;
        $setting_name = $config::CONFIG_DEBUG_ENABLED;
        $setting = new admin_setting_configcheckbox(
            "local_lfapi/$setting_name",
            get_string("settings:$setting_name", 'local_lfapi'),
            get_string("settings:$setting_name" . '_desc', 'local_lfapi'),
            0
        );
        $settingspage->add($setting);

        // V1
        $setting_name = 'api_users_config_header';
        $setting = new admin_setting_heading(
            "local_lfapi/$setting_name",
            get_string("settings:$setting_name", 'local_lfapi'),
            get_string("settings:$setting_name" . '_desc', 'local_lfapi'),
        );
        $settingspage->add($setting);

        if($config->is_ready()){
            $setting_name = 'admin_api_user_token';
            $setting = new \local_lfapi\admin\admin_setting_api_user_token(
                \local_lfapi\models\users\admin_user::class,
                "local_lfapi/$setting_name",
                get_string("settings:$setting_name", 'local_lfapi'),
                get_string("settings:$setting_name" . '_desc', 'local_lfapi'),
                get_string("settings:api_user_token_empty", 'local_lfapi'),
            );
            $settingspage->add($setting);
    
            $setting_name = 'client_api_user_token';
            $setting = new \local_lfapi\admin\admin_setting_api_user_token(
                \local_lfapi\models\users\client_user::class,
                "local_lfapi/$setting_name",
                get_string("settings:$setting_name", 'local_lfapi'),
                get_string("settings:$setting_name" . '_desc', 'local_lfapi'),
                get_string("settings:api_user_token_empty", 'local_lfapi'),
            );
            $settingspage->add($setting);
        }

        // V3
        $setting_name = 'api_credentials_config_header';
        $setting = new admin_setting_heading(
            "local_lfapi/$setting_name",
            get_string("settings:$setting_name", 'local_lfapi'),
            get_string("settings:$setting_name" . '_desc', 'local_lfapi'),
        );
        $settingspage->add($setting);

        if($config->is_ready()){
            $setting_name = 'admin_credentials';
            $setting = new \local_lfapi\admin\admin_setting_api_credentials(
                \local_lfapi\models\users\admin_user::class,
                "local_lfapi/$setting_name",
                get_string("settings:$setting_name", 'local_lfapi'),
                get_string("settings:$setting_name" . '_desc', 'local_lfapi'),
            );
            $settingspage->add($setting);
    
            $setting_name = 'client_credentials';
            $setting = new \local_lfapi\admin\admin_setting_api_credentials(
                \local_lfapi\models\users\client_user::class,
                "local_lfapi/$setting_name",
                get_string("settings:$setting_name", 'local_lfapi'),
                get_string("settings:$setting_name" . '_desc', 'local_lfapi'),
            );
            $settingspage->add($setting);
        }

    }

    $ADMIN->add('localplugins', $settingspage);
}