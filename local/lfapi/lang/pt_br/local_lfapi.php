<?php

$string['manage'] = 'Gerenciar API LearningFlix';
$string['pluginname'] = 'API LearningFlix';
$string['local/lfapi:config'] = 'Configurar API LearningFlix';

$string['copied'] = 'Copiado para a área de transferência!';
$string['copyerror'] = 'Erro ao copiar para a área de transferência';
$string['setup:admin_user_description'] = 'Usuário administrador gerado automaticamente para a API LearningFlix';
$string['setup:client_user_description'] = 'Usuário cliente gerado automaticamente para a API LearningFlix';

/* Exceptions */

$string['exception:api_user_already_exists'] = 'Usuário da API já existe!';
$string['exception:api_user_not_found'] = 'Usuário da API não encontrado!';
$string['exception:category_not_found'] = 'Categoria não encontrada!';
$string['exception:course_completion_not_found'] = 'Conclusão de curso não encontrada!';
$string['exception:course_not_found'] = 'Curso não encontrado!';
$string['exception:enrol_instance_not_found'] = 'Instância de matrícula não encontrada!';
$string['exception:filter_invalid_choice'] = 'Valor inválido para "{$a->field}". Apenas aceita: {$a->choices}';
$string['exception:invalid_filter_operator'] = 'Operador inválido "{$a->operator}"';
$string['exception:invalid_filter_value'] = 'Valor inválido para "{$a->field}"';
$string['exception:operator_not_accepted'] = 'O campo "{$a->field}" aceita apenas os operadores [{$a->accepts}]';
$string['exception:required_filter'] = 'Campo obrigatório "{$a->field}" ausente';
$string['exception:user_enrol_not_found'] = 'Matrícula de usuário não encontrada';
$string['exception:user_not_found'] = 'Usuário não encontrado!';
$string['validation:invalid_array'] = 'Apenas arrays são aceitos. Valor inválido "{$a->value}"';
$string['validation:invalid_param_value'] = '{$a->message}';
$string['validation:key_error'] = '"{$a->key}": {$a->message}';
$string['validation:missing_key'] = 'Chave obrigatória "{$a->key}" ausente.';
$string['validation:type_mismatch'] = '{$a->expected_type}: Valor de parâmetro inválido "{$a->value}"';
$string['validation:unexpected_key'] = 'Chave inesperada "{$a->key}" detectada.';
$string['exception:batch_limit_violation'] = 'O número máximo de itens que podem ser processados de uma vez é {$a}.';
$string['v1:auth_userkey_not_available'] = "Esta funcionalidade não está disponível para esta plataforma.";

/* Settings */

$string['settings:admin_api_user_token'] = 'Token do administrador';
$string['settings:admin_api_user_token_desc'] = 'Token de usuário criado automaticamente para acesso de administrador às APIs';
$string['settings:admin_credentials'] = 'Credenciais de administrador';
$string['settings:admin_credentials_desc'] = '';
$string['settings:api_credentials_config_header'] = 'Credenciais do cliente';
$string['settings:api_credentials_config_header_desc'] = '';
$string['settings:api_users_config_header'] = 'Tokens de acesso (obsoleto)';
$string['settings:api_users_config_header_desc'] = '';
$string['settings:api_user_token_empty'] = 'Token ainda não foi gerado';
$string['settings:client_api_user_token'] = 'Token do cliente';
$string['settings:client_api_user_token_desc'] = 'Token de usuário criado automaticamente para acesso do cliente às APIs';
$string['settings:client_credentials'] = 'Credenciais do cliente';
$string['settings:client_credentials_desc'] = '';
$string['settings:client_credentials_empty'] = 'As credenciais não foram geradas';
$string['settings:client_credentials_secret_empty'] = 'Gere outro segredo para visualizá-lo';
$string['settings:enable_debug'] = 'Ativar debug';
$string['settings:enable_debug_desc'] = 'Exibe mais informações sobre erros ao realizar chamadas à API.';
$string['settings:generate_credentials'] = 'Gerar';
$string['settings:regenerate_credentials'] = 'Regerar';
$string['settings:regenerate_credentials_error'] = 'Ocorreu um erro ao gerar as credenciais da API.';
$string['settings:regenerate_credentials_success'] = 'Novas credenciais da API foram geradas! Guarde-as em um local seguro, pois não será possível visualizá-las novamente.';

/* Documentation */

$string['docs:bad_request_error:description'] = 'Requisição inválida. Os parâmetros enviados estão malformados, ausentes ou incompatíveis com o esperado.';
$string['docs:bad_request_error:example'] = 'Chave inesperada detectada';
$string['docs:bad_request_error:title'] = 'Requisição inválida';
$string['docs:category:description'] = 'Descrição da categoria';
$string['docs:category:descriptionformat'] = 'Formato da descrição (1 = HTML, 0 = DEFAULT, 2 = TEXTO, ou 4 = MARKDOWN)';
$string['docs:category:id'] = 'ID da categoria';
$string['docs:category:idnumber'] = 'Idnumber da categoria';
$string['docs:category:name'] = 'Nome da categoria';
$string['docs:category:parent'] = 'ID da categoria pai. Use 0 para uma categoria raiz';
$string['docs:category:theme'] = 'Tema da categoria. Esta opção precisa estar habilitada na plataforma.';
$string['docs:category_resource:description'] = 'Representa uma categoria com seus atributos e links.';
$string['docs:category_resource:title'] = 'Categoria (HAL)';
$string['docs:category_transformer:title'] = 'Categoria';
$string['docs:completion:id'] = 'ID da conclusão de curso';
$string['docs:course:categoryid'] = 'ID da categoria do curso';
$string['docs:course:completionnotify'] = 'Notificar usuários ao concluírem este curso. 1 para sim, 0 para não';
$string['docs:course:courseformatoptions'] = 'Opções adicionais para um formato específico de curso';
$string['docs:course:courseformatoptions:name'] = 'Nome da opção de formato de curso';
$string['docs:course:courseformatoptions:value'] = 'Valor da opção de formato de curso';
$string['docs:course:customfields'] = 'Campos personalizados do curso';
$string['docs:course:customfields:shortname'] = 'Shortname do campo personalizado';
$string['docs:course:customfields:value'] = 'Valor do campo personalizado';
$string['docs:course:defaultgroupingid'] = 'ID de agrupamento padrão';
$string['docs:course:enablecompletion'] = '1 = Ativar acompanhamento de conclusão do curso. 0 = Desativar.';
$string['docs:course:enddate'] = 'Data de término do curso.';
$string['docs:course:forcetheme'] = 'Nome do tema forçado';
$string['docs:course:format'] = 'Formato do curso: learningflix, seasons, topics, site...';
$string['docs:course:fullname'] = 'Nome completo do curso';
$string['docs:course:groupmode'] = 'Sem grupos, separados, visíveis';
$string['docs:course:groupmodeforce'] = '1: sim, 0: não';
$string['docs:course:hiddensections'] = '(Obsoleto, use courseformatoptions)';
$string['docs:course:id'] = 'ID do curso';
$string['docs:course:idnumber'] = 'Idnumber do curso';
$string['docs:course:lang'] = 'Idioma forçado do curso';
$string['docs:course:maxbytes'] = 'Tamanho máximo de arquivo permitido para upload no curso';
$string['docs:course:newsitems'] = 'Número de notícias recentes exibidas na página do curso';
$string['docs:course:numsections'] = '(Obsoleto, use courseformatoptions)';
$string['docs:course:shortname'] = 'Nome abreviado do curso';
$string['docs:course:showgrades'] = '1 se as notas são exibidas, senão 0';
$string['docs:course:showreports'] = 'Relatórios de atividades visíveis (sim = 1, não = 0)';
$string['docs:course:startdate'] = 'Data de início do curso';
$string['docs:course:summary'] = 'Resumo';
$string['docs:course:summaryformat'] = 'Formato do resumo (1 = HTML, 0 = DEFAULT, 2 = TEXTO, ou 4 = MARKDOWN)';
$string['docs:course:visible'] = '1: disponível para os alunos, 0: indisponível';
$string['docs:course_completion_resource:description'] = 'Representa uma conclusão de curso com seus atributos e links.';
$string['docs:course_completion_resource:title'] = 'Conclusão de curso (HAL)';
$string['docs:course_completion_transformer:title'] = 'Conclusão de curso';
$string['docs:course_resource:description'] = 'Representa um curso com seus atributos e links.';
$string['docs:course_resource:title'] = 'Curso (HAL)';
$string['docs:course_transformer:title'] = 'Curso';
$string['docs:create_category'] = 'Criar uma ou mais categorias';
$string['docs:create_category:successful_response'] = 'Categoria(s) criada(s)';
$string['docs:create_category_body:title'] = 'Objeto de criação de categoria';
$string['docs:create_user_enrolment_body:title'] = 'Objeto de criação de matrícula de usuário em curso';
$string['docs:create_courses'] = 'Criar um ou mais cursos';
$string['docs:create_courses:successful_response'] = 'Curso(s) criado(s)';
$string['docs:create_course_body:title'] = 'Objeto de criação de curso';
$string['docs:create_enrolment'] = 'Criar uma matrícula de um usuário em um curso';
$string['docs:create_enrolment:successful_response'] = 'Matrícula criada com sucesso';
$string['docs:create_users'] = 'Criar um ou mais usuários';
$string['docs:create_users:successful_response'] = 'Usuário(s) criado(s)';
$string['docs:create_user_body:title'] = 'Objeto de criação de usuário';
$string['docs:delete_category'] = 'Remover uma categoria';
$string['docs:delete_category:successful_response'] = 'Categoria removida com sucesso';
$string['docs:delete_course'] = 'Remover um curso';
$string['docs:delete_course:successful_response'] = 'Curso removido com sucesso';
$string['docs:delete_enrolment'] = 'Remover uma matrícula de um usuário em um curso';
$string['docs:delete_enrolment:successful_response'] = 'Matrícula removida com sucesso';
$string['docs:delete_user'] = 'Remover um usuário';
$string['docs:delete_user:successful_response'] = 'Usuário removido com sucesso';
$string['docs:detailed_course_completion_resource:description'] = 'Representa uma conclusão de curso com seus atributos, links e recursos embutidos.';
$string['docs:detailed_course_completion_resource:title'] = 'Conclusão de curso detalhada (HAL)';
$string['docs:detailed_user_resource:description'] = 'Representa um usuário com seus atributos e links.';
$string['docs:detailed_user_resource:title'] = 'Usuário detalhado (HAL)';
$string['docs:enrol_instance:enrol'] = 'Nome do plugin de matrícula. Exemplo: "manual"';
$string['docs:enrol_instance:id'] = 'ID da instância de matrícula';
$string['docs:enrol_instance_resource:description'] = 'Representa uma instância de matrícula em curso com seus atributos, links e recursos embutidos';
$string['docs:enrol_instance_resource:title'] = 'Instância de matrícula em curso (HAL)';
$string['docs:enrol_instance_transformer:title'] = 'Instância de matrícula em curso';
$string['docs:forbidden_error:description'] = 'A autenticação falhou ou não foi fornecida. O token de acesso pode estar ausente, inválido ou expirado.';
$string['docs:forbidden_error:example'] = 'O token de acesso pode estar ausente ou inválido.';
$string['docs:forbidden_error:title'] = 'Acesso negado';
$string['docs:generic_error:description'] = 'Formato padrão para mensagens de erro';
$string['docs:generic_error:title'] = 'Erro genérico';
$string['docs:get_category'] = 'Obter uma categoria';
$string['docs:get_category:successful_response'] = 'Retorna os dados da categoria';
$string['docs:get_completion'] = 'Obter conclusão de curso';
$string['docs:get_completion:successful_response'] = 'Sucesso';
$string['docs:get_course'] = 'Obter curso';
$string['docs:get_course:successful_response'] = 'Sucesso';
$string['docs:get_enrol_instance'] = 'Obter instância de matrícula';
$string['docs:get_enrol_instance:successful_response'] = 'Sucesso';
$string['docs:get_user'] = 'Obter usuário';
$string['docs:get_user:successful_response'] = 'Sucesso';
$string['docs:get_user_enrolment'] = 'Obter matrícula de usuário em curso';
$string['docs:get_user_enrolment:successful_response'] = 'Sucesso';
$string['docs:grade_resource:description'] = 'Contém informações sobre a nota do usuário em um curso.';
$string['docs:grade_resource:title'] = 'Nota do usuário no curso (HAL)';
$string['docs:grade_transformer:description'] = 'Contém informações sobre a nota atribuída ao usuário em um curso';
$string['docs:grade_transformer:title'] = 'Nota do usuário no curso';
$string['docs:list_categories'] = 'Listar categorias';
$string['docs:list_categories:description'] = "Retorna uma lista paginada de categorias. **Suporta filtragem por LHS Brackets.**";
$string['docs:list_categories:response:description'] = 'Lista paginada de categorias';
$string['docs:list_completions_by_course'] = 'Listar conclusões de um curso específico';
$string['docs:list_completions_by_course:description'] = "Retorna uma lista paginada de conclusões de um curso. **Suporta filtragem por LHS Brackets.**";
$string['docs:list_completions'] = 'Listar conclusões de curso';
$string['docs:list_completions:description'] = "Retorna uma lista paginada de conclusões de curso. **Suporta filtragem por LHS Brackets.**";
$string['docs:list_completions_by_course:successful_response'] = 'Sucesso';
$string['docs:list_courses'] = 'Listar cursos';
$string['docs:list_courses:description'] = 'Lista cursos';
$string['docs:list_courses:successful_response'] = 'Sucesso';
$string['docs:list_enrol_instances'] = 'Listar instâncias de matrícula';
$string['docs:list_enrol_instances:description'] = "Retorna uma lista paginada de instâncias de matrícula. **Suporta filtragem por LHS Brackets.**";
$string['docs:list_enrol_instances:successful_response'] = 'Sucesso';
$string['docs:list_users'] = 'Listar usuários';
$string['docs:list_users:description'] = "Retorna uma lista paginada de usuários. **Suporta filtragem por LHS Brackets.**";
$string['docs:list_users:successful_response'] = 'Sucesso';
$string['docs:list_user_enrolments'] = 'Listar matrículas de usuários em cursos';
$string['docs:list_user_enrolments:description'] = "Retorna uma lista paginada de matrículas de usuários. **Suporta filtragem por LHS Brackets.**";
$string['docs:list_user_enrolments:successful_response'] = 'Sucesso';
$string['docs:not_found_error:description'] = 'O recurso solicitado não foi encontrado. Ele pode ter sido removido ou nunca ter existido.';
$string['docs:not_found_error:example'] = 'Recurso não encontrado';
$string['docs:not_found_error:title'] = 'Recurso não encontrado';
$string['docs:pagination:_after'] = 'Cursor usado para paginação.';
$string['docs:pagination:_limit'] = 'Número de itens por página.';
$string['docs:unauthorized_error:description'] = 'A autenticação falhou ou não foi fornecida. O token de acesso pode estar ausente, inválido ou expirado.';
$string['docs:unauthorized_error:example'] = 'Token de acesso inválido!';
$string['docs:unauthorized_error:title'] = 'Erro de autenticação';
$string['docs:update_category'] = 'Atualizar uma categoria';
$string['docs:update_category:successful_response'] = 'Categoria atualizada com sucesso';
$string['docs:update_category_body:title'] = 'Objeto de atualização de categoria';
$string['docs:update_course'] = 'Atualizar um curso específico';
$string['docs:update_course:successful_response'] = 'Curso atualizado com sucesso';
$string['docs:update_courses'] = 'Atualizar múltiplos cursos';
$string['docs:update_courses:partial_successful_response'] = 'Alguns cursos foram atualizados com sucesso, mas outros retornaram erros';
$string['docs:update_courses:successful_response'] = 'Cursos atualizados com sucesso';
$string['docs:update_course_body:title'] = 'Objeto de atualização de curso';
$string['docs:update_enrolment'] = 'Atualizar a matrícula de um usuário em um curso';
$string['docs:update_enrolment:successful_response'] = 'Matrícula do usuário atualizada';
$string['docs:update_user'] = 'Atualizar um usuário específico';
$string['docs:update_user:succesful_response'] = 'Usuário atualizado com sucesso';
$string['docs:update_users'] = 'Atualizar múltiplos usuários';
$string['docs:update_users:partial_successful_response'] = 'Alguns usuários foram atualizados com sucesso, mas outros retornaram erros';
$string['docs:update_users:successful_response'] = 'Usuários atualizados com sucesso';
$string['docs:update_user_body:title'] = 'Objeto de atualização de usuário';
$string['docs:update_user_enrolment_body:title'] = 'Objeto de atualização da matrícula de usuário';
$string['docs:update_warning_resource:description'] = 'Contém os dados de um erro parcial ao atualizar um recurso';
$string['docs:update_warning_resource:title'] = 'Avisos de atualização parcial (HAL)';
$string['docs:user:address'] = 'Endereço postal';
$string['docs:user:alternatename'] = 'Nome alternativo do usuário';
$string['docs:user:auth'] = 'Plugins de autenticação incluem manual, ldap, etc';
$string['docs:user:calendartype'] = 'Tipo de calendário como "gregorian", deve existir no servidor';
$string['docs:user:city'] = 'Cidade de origem do usuário';
$string['docs:user:country'] = 'Código do país de origem do usuário, como BR ou PT';
$string['docs:user:createpassword'] = 'Verdadeiro se a senha deve ser criada e enviada ao usuário por e-mail.';
$string['docs:user:customfields'] = 'Campos personalizados do usuário (também conhecidos como campos de perfil)';
$string['docs:user:customfields:type'] = 'Nome do campo personalizado';
$string['docs:user:customfields:value'] = 'Valor do campo personalizado';
$string['docs:user:department'] = 'Departamento';
$string['docs:user:description'] = 'Descrição do perfil do usuário, sem HTML';
$string['docs:user:email'] = 'Um endereço de e-mail válido e único';
$string['docs:user:firstname'] = 'Primeiro(s) nome(s) do usuário';
$string['docs:user:firstnamephonetic'] = 'Versão fonética do(s) primeiro(s) nome(s) do usuário';
$string['docs:user:id'] = 'ID do usuário';
$string['docs:user:idnumber'] = 'Código de identificação arbitrário, possivelmente da instituição';
$string['docs:user:institution'] = 'Instituição';
$string['docs:user:interests'] = 'Interesses do usuário (separados por vírgulas)';
$string['docs:user:lang'] = 'Código de idioma, como "pt" ou "en", que deve existir no servidor';
$string['docs:user:lastname'] = 'Sobrenome do usuário';
$string['docs:user:lastnamephonetic'] = 'Versão fonética do sobrenome do usuário';
$string['docs:user:maildisplay'] = 'Visibilidade do e-mail';
$string['docs:user:mailformat'] = 'Código do formato de e-mail: 0 para texto simples, 1 para HTML, etc.';
$string['docs:user:middlename'] = 'Nome do meio do usuário';
$string['docs:user:password'] = 'Senha em texto plano composta por quaisquer caracteres';
$string['docs:user:phone1'] = 'Telefone 1';
$string['docs:user:phone2'] = 'Telefone 2';
$string['docs:user:preferences'] = 'Preferências do usuário';
$string['docs:user:preferences:type'] = 'Nome da preferência';
$string['docs:user:preferences:value'] = 'Valor da preferência';
$string['docs:user:theme'] = 'Nome do tema, como "standard", que deve existir no servidor';
$string['docs:user:timezone'] = 'Código do fuso horário, como Australia/Perth, ou 99 para padrão';
$string['docs:user:username'] = 'A política de nome de usuário é definida na configuração de segurança do Moodle.';
$string['docs:user_enrolment:enrolid'] = 'ID da instância de matrícula';
$string['docs:user_enrolment:id'] = 'ID da matrícula';
$string['docs:user_enrolment:roleid'] = 'ID do papel do usuário no curso';
$string['docs:user_enrolment:status'] = 'Status da matrícula. 0 para ativa, 1 para suspensa.';
$string['docs:user_enrolment:timeend'] = 'Data de término da matrícula.';
$string['docs:user_enrolment:timestart'] = 'Data de início da matrícula.';
$string['docs:user_enrolment:userid'] = 'ID do usuário';
$string['docs:user_enrolment_resource:description'] = 'Representa uma matrícula de usuário em um curso com seus atributos, links e recursos embutidos.';
$string['docs:user_enrolment_resource:title'] = 'Matrícula de usuário em curso (HAL)';
$string['docs:user_enrol_transformer:title'] = 'Matrícula de usuário em um curso';
$string['docs:user_resource:description'] = 'Representa um usuário com seus atributos e links.';
$string['docs:user_resource:title'] = 'Usuário (HAL)';
$string['docs:user_transformer:title'] = 'Usuário';
$string['docs:validation_error:description'] = 'A requisição foi compreendida, mas contém dados inválidos. Um ou mais campos falharam na validação.';
$string['docs:validation_error:example'] = 'Valor inválido detectado';
$string['docs:validation_error:title'] = 'Erro de validação';

// Filters
$str_accepts_operators = ' Aceita os operadores {$a}';

$string['docs:basic_enrol_instance_sql_filter:courseid'] = 'ID do curso.' . $str_accepts_operators;
$string['docs:basic_enrol_instance_sql_filter:enrol'] = 'Nome do plugin de matrícula.' . $str_accepts_operators;
$string['docs:category_sql_filter:coursecount'] = 'Número de cursos contidos na categoria.' . $str_accepts_operators;
$string['docs:category_sql_filter:depth'] = 'Profundidade da categoria.' . $str_accepts_operators;
$string['docs:category_sql_filter:id'] = 'ID da categoria.' . $str_accepts_operators;
$string['docs:category_sql_filter:idnumber'] = 'Idnumber da categoria.' . $str_accepts_operators;
$string['docs:category_sql_filter:name'] = 'Nome da categoria.' . $str_accepts_operators;
$string['docs:category_sql_filter:parent'] = 'ID da categoria pai.' . $str_accepts_operators;
$string['docs:category_sql_filter:timemodified'] = 'Data de modificação.' . $str_accepts_operators;
$string['docs:category_sql_filter:visible'] = 'Visibilidade da categoria. Use 1 para visível e 0 para invisível.' . $str_accepts_operators;
$string['docs:custom_course_fields_sql_filter'] = "Filtro por campo personalizado. Substitua `{name}` pelo shortname do campo.\nExemplo: `custom_field_audience=TI`.\nOs operadores suportados variam conforme o tipo do campo.";
$string['docs:course_sql_filter:categoryid'] = 'ID da categoria que contém o curso.' . $str_accepts_operators;
$string['docs:course_sql_filter:completionnotify'] = 'Notificar usuários ao concluírem o curso. 1 para sim, 0 para não.' . $str_accepts_operators;
$string['docs:course_sql_filter:enablecompletion'] = 'Se o acompanhamento de progresso está ativado. 1 para sim, 0 para não.' . $str_accepts_operators;
$string['docs:course_sql_filter:enddate'] = 'Data de término do curso.' . $str_accepts_operators;
$string['docs:course_sql_filter:format'] = 'Formato do curso: learningflix, seasons, topics, site...' . $str_accepts_operators;
$string['docs:course_sql_filter:fullname'] = 'Nome completo do curso.' . $str_accepts_operators;
$string['docs:course_sql_filter:id'] = 'ID do curso.' . $str_accepts_operators;
$string['docs:course_sql_filter:idnumber'] = 'Idnumber do curso.' . $str_accepts_operators;
$string['docs:course_sql_filter:shortname'] = 'Nome breve do curso.' . $str_accepts_operators;
$string['docs:course_sql_filter:startdate'] = 'Data de início do curso.' . $str_accepts_operators;
$string['docs:course_sql_filter:timecreated'] = 'Data de criação do curso.' . $str_accepts_operators;
$string['docs:course_sql_filter:timemodified'] = 'Data de modificação do curso.' . $str_accepts_operators;
$string['docs:course_sql_filter:visible'] = 'Visibilidade do curso. Use 1 para visível e 0 para invisível.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:courseid'] = 'ID do curso.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:enrol'] = 'Nome do plugin de matrícula.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:enrolenddate'] = 'Data de início da auto-matrícula.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:enrolperiod'] = 'Duração padrão da matrícula, em segundos.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:enrolstartdate'] = 'Data de término da matrícula.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:name'] = 'Nome da instância de matrícula.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:status'] = 'Status da instância de matrícula. 0 para ativa, 1 para inativa.' . $str_accepts_operators;
$string['docs:user_sql_filter:auth'] = 'Nome do plugin de autenticação.' . $str_accepts_operators;
$string['docs:user_sql_filter:confirmed'] = 'Se a conta do usuário foi confirmada.' . $str_accepts_operators;
$string['docs:user_sql_filter:deleted'] = 'Se o usuário foi excluído.' . $str_accepts_operators;
$string['docs:user_sql_filter:email'] = 'Endereço de e-mail do usuário.' . $str_accepts_operators;
$string['docs:user_sql_filter:firstaccess'] = 'Data do primeiro acesso.' . $str_accepts_operators;
$string['docs:user_sql_filter:firstname'] = 'Primeiro nome do usuário.' . $str_accepts_operators;
$string['docs:user_sql_filter:id'] = 'ID do usuário.' . $str_accepts_operators;
$string['docs:user_sql_filter:idnumber'] = 'Idnumber do usuário.' . $str_accepts_operators;
$string['docs:user_sql_filter:lastaccess'] = 'Data do último acesso.' . $str_accepts_operators;
$string['docs:user_sql_filter:lastlogin'] = 'Data do último login.' . $str_accepts_operators;
$string['docs:user_sql_filter:policyagreed'] = 'Se o usuário aceitou os termos do site.' . $str_accepts_operators;
$string['docs:user_sql_filter:suspended'] = 'Se o usuário está suspenso.' . $str_accepts_operators;
$string['docs:user_sql_filter:timecreated'] = 'Data de criação da conta do usuário.' . $str_accepts_operators;
$string['docs:user_sql_filter:timemodified'] = 'Data de modificação da conta do usuário.' . $str_accepts_operators;
$string['docs:user_sql_filter:username'] = 'Nome de usuário.' . $str_accepts_operators;
$string['docs:profile_field_sql_filter'] = "Filtro por campo de perfil personalizado. Substitua `{name}` pelo shortname do campo.\nExemplo: `profile_field_department=TI`.\nOs operadores suportados variam conforme o tipo do campo.";
$string['docs:user_enrolment_sql_filter:enrolid'] = 'ID da instância de matrícula.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:status'] = 'Status da matrícula. 0 para ativa, 1 para suspensa.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:timecreated'] = 'Data de criação da matrícula.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:timeend'] = 'Data de término da matrícula.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:timemodified'] = 'Data de modificação da matrícula.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:timestart'] = 'Data de início da matrícula.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:userid'] = 'ID do usuário.' . $str_accepts_operators;
$string['docs:course_completion_sql_filter:course'] = 'ID do curso.' . $str_accepts_operators;
$string['docs:course_completion_sql_filter:userid'] = 'ID do usuário.' . $str_accepts_operators;
$string['docs:course_completion_sql_filter:timeenrolled'] = 'Data de matrícula.' . $str_accepts_operators;
$string['docs:course_completion_sql_filter:timestarted'] = 'Data a partir da qual o usuário começou a cumprir os critérios de conclusão do curso.' . $str_accepts_operators;
$string['docs:course_completion_sql_filter:timecompleted'] = 'Data de conclusão do curso.' . $str_accepts_operators;
$string['docs:api:description'] = "### Filtros com LHS Brackets

As rotas que suportam filtragem permitem o uso da sintaxe de **LHS Brackets**, que permitem a criação de consultas mais dinâmicas.

A sintaxe é:
`<campo>[<operador>]=<valor>`

#### Exemplo:
```http
GET /api/resource?name[like]=test*&depth[gt]=1
```
- `name[like]=test*` aplica um filtro de correspondência parcial ao campo `name`;
- `depth[gt]=1` filtra resultados onde `depth` é maior que 1.

> **Observação:** os operadores disponíveis podem variar de acordo com o campo utilizado.
---
### Operadores Suportados

| Operador   | Descrição                                  | Exemplo                         |
|------------|--------------------------------------------|---------------------------------|
| `gt`       | Maior que (`greater than`)                 | timestarted[gt]=1743311107      |
| `gte`      | Maior ou igual a (`greater than or equal`) | timecompleted[gte]=1655300000   |
| `lt`       | Menor que (`less than`)                    | timecreated[lt]=1728805000      |
| `lte`      | Menor ou igual a (`less than or equal`)    | firstaccess[lte]=1738000000     |
| `eq`       | Igual a                                    | policyagreed[eq]=1              |
| `neq`      | Diferente de                               | auth[neq]=manual                |
| `isnull`   | Valor nulo (`IS NULL` SQL)                 | name[isnull]                    |
| `notnull`  | Valor não nulo (`NOT NULL` SQL)            | idnumber[notnull]               |
| `like`     | Corresponde a padrão (`LIKE` SQL)          | idnumber[like]=external*        |
| `notlike`  | Não corresponde a padrão (`NOT LIKE` SQL)  | email[notlike]=*@test.com       |        
| `in`       | O valor está na lista (separado por vírgula) | custom_field_level[in]=1,2,3    |

---";
$string['docs:create_courses:request_body_description'] = 'Você pode enviar um único curso usando `{ "course": { ... } }` ou múltiplos cursos com `{ "courses": [ ... ] }`.';
$string['docs:create_category:request_body_description'] = 'Você pode enviar uma única categoria usando `{ "category": { ... } }` ou múltiplas categorias com `{ "categories": [ ... ] }`.';
$string['docs:create_users:request_body_description'] = 'Você pode enviar um único usuário usando `{ "user": { ... } }` ou múltiplos usuários com `{ "users": [ ... ] }`.';