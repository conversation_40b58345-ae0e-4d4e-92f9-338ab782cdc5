<?php

$string['manage'] = 'Manage LearningFlix API';
$string['pluginname'] = 'LearningFlix API';
$string['local/lfapi:config'] = 'Configure LearningFlix API';

$string['copied'] = 'Copied to clipboard!';
$string['copyerror'] = 'Error while copying to clipboard';
$string['setup:admin_user_description'] = 'Automatically generated admin user for the LearningFlix API';
$string['setup:client_user_description'] = 'Automatically generated client user for the LearningFlix API';

/* Exceptions */

$string['exception:api_user_already_exists'] = 'API user already exists!';
$string['exception:api_user_not_found'] = 'API user not found!';
$string['exception:category_not_found'] = 'Category not found!';
$string['exception:course_completion_not_found'] = 'Course completion not found!';
$string['exception:course_not_found'] = 'Course not found!';
$string['exception:enrol_instance_not_found'] = 'Enrolment instance not found!';
$string['exception:filter_invalid_choice'] = 'Invalid value for "{$a->field}". Only accepts: {$a->choices}';
$string['exception:invalid_filter_operator'] = 'Invalid operator "{$a->operator}';
$string['exception:invalid_filter_value'] = 'Invalid value for "{$a->field}"';
$string['exception:operator_not_accepted'] = 'The field "{$a->field}" only accepts the operators [{$a->accepts}]';
$string['exception:required_filter'] = 'Missing required field "{$a->field}"';
$string['exception:user_enrol_not_found'] = 'User enrolment not found';
$string['exception:user_not_found'] = 'User not found!';
$string['validation:invalid_array'] = 'Only arrays accepted. Bad value "{$a->value}"';
$string['validation:invalid_param_value'] = '{$a->message}';
$string['validation:key_error'] = '"{$a->key}": {$a->message}';
$string['validation:missing_key'] = 'Missing required key "{$a->key}".';
$string['validation:type_mismatch'] = '{$a->expected_type}: Invalid parameter value "{$a->value}"';
$string['validation:unexpected_key'] = 'Unexpected key "{$a->key}" detected.';
$string['exception:batch_limit_violation'] = 'The maximum number of items that can be processed at once is {$a}.';
$string['v1:auth_userkey_not_available'] = "This functionality is unavailable for this platform.";

/* Settings */

$string['settings:admin_api_user_token'] = 'Administrator Token';
$string['settings:admin_api_user_token_desc'] = 'Automatically created user token for administrator access to APIs';
$string['settings:admin_credentials'] = 'Admin credentials';
$string['settings:admin_credentials_desc'] = '';
$string['settings:api_credentials_config_header'] = 'Client credentials';
$string['settings:api_credentials_config_header_desc'] = '';
$string['settings:api_users_config_header'] = 'Access tokens (Deprecated)';
$string['settings:api_users_config_header_desc'] = '';
$string['settings:api_user_token_empty'] = 'Token has not yet been generated';
$string['settings:client_api_user_token'] = 'Client Token';
$string['settings:client_api_user_token_desc'] = 'Automatically created user token for client access to APIs';
$string['settings:client_credentials'] = 'Client credentials';
$string['settings:client_credentials_desc'] = '';
$string['settings:client_credentials_empty'] = 'Credentials have not been generated';
$string['settings:client_credentials_secret_empty'] = 'Generate another secret to view it';
$string['settings:enable_debug'] = 'Enable debug';
$string['settings:enable_debug_desc'] = 'Displays more information about errors when making API calls.';
$string['settings:generate_credentials'] = 'Generate';
$string['settings:regenerate_credentials'] = 'Regenerate';
$string['settings:regenerate_credentials_error'] = 'An error occurred while generating the API credentials.';
$string['settings:regenerate_credentials_success'] = 'New API credentials have been generated! Please store them somewhere safe, as you will not be able to view them again.';

/* Documentation */

$string['docs:bad_request_error:description'] = 'Invalid request. The submitted parameters are malformed, missing, or incompatible with what is expected.';
$string['docs:bad_request_error:example'] = 'Unexpected key detected';
$string['docs:bad_request_error:title'] = 'Invalid request';
$string['docs:category:description'] = 'The category description';
$string['docs:category:descriptionformat'] = 'Description format (1 = HTML, 0 = DEFAULT, 2 = PLAIN, or 4 = MARKDOWN)';
$string['docs:category:id'] = 'Category ID';
$string['docs:category:idnumber'] = 'The category idnumber';
$string['docs:category:name'] = 'Category name';
$string['docs:category:parent'] = 'The parent category ID. Set to 0 for a root category';
$string['docs:category:theme'] = 'The category theme. This option must be enabled on the platform.';
$string['docs:category_resource:description'] = 'Represents a category with its attributes and links.';
$string['docs:category_resource:title'] = 'Category (HAL)';
$string['docs:category_transformer:title'] = 'Category';
$string['docs:completion:id'] = 'Course completion ID';
$string['docs:course:categoryid'] = 'Course category ID';
$string['docs:course:completionnotify'] = 'Notify users when they complete this course. 1 for yes, 0 for no';
$string['docs:course:courseformatoptions'] = 'Additional options for a specific course format';
$string['docs:course:courseformatoptions:name'] = 'Course format option name';
$string['docs:course:courseformatoptions:value'] = 'Course format option value';
$string['docs:course:customfields'] = 'Custom fields for the course';
$string['docs:course:customfields:shortname'] = 'The shortname of the custom field';
$string['docs:course:customfields:value'] = 'The value of the custom field';
$string['docs:course:defaultgroupingid'] = 'Default grouping ID';
$string['docs:course:enablecompletion'] = '1 = Enable course completion tracking. 0 = Disable course completion tracking.';
$string['docs:course:enddate'] = 'Course end date.';
$string['docs:course:forcetheme'] = 'Name of the forced theme';
$string['docs:course:format'] = 'Course format: learningflix, seasons, topics, site...';
$string['docs:course:fullname'] = 'Full course name';
$string['docs:course:groupmode'] = 'No groups, separate, visible';
$string['docs:course:groupmodeforce'] = '1: yes, 0: no';
$string['docs:course:hiddensections'] = '(Deprecated, use courseformatoptions)';
$string['docs:course:id'] = 'Course ID';
$string['docs:course:idnumber'] = 'Course idnumber';
$string['docs:course:lang'] = 'Forced course language';
$string['docs:course:maxbytes'] = 'Maximum file size allowed for uploads in this course';
$string['docs:course:newsitems'] = 'Number of recent news items shown on the course page';
$string['docs:course:numsections'] = '(Deprecated, use courseformatoptions)';
$string['docs:course:shortname'] = 'Short course name';
$string['docs:course:showgrades'] = '1 if grades are shown, otherwise 0';
$string['docs:course:showreports'] = 'Are activity reports shown (yes = 1, no = 0)';
$string['docs:course:startdate'] = 'Course start date';
$string['docs:course:summary'] = 'Summary';
$string['docs:course:summaryformat'] = 'Summary format (1 = HTML, 0 = DEFAULT, 2 = PLAIN, or 4 = MARKDOWN)';
$string['docs:course:visible'] = '1: available to students, 0: not available';
$string['docs:course_completion_resource:description'] = 'Represents a course completion with its attributes and links.';
$string['docs:course_completion_resource:title'] = 'Course completion (HAL)';
$string['docs:course_completion_transformer:title'] = 'Course completion';
$string['docs:course_resource:description'] = 'Represents a course with its attributes and links.';
$string['docs:course_resource:title'] = 'Course (HAL)';
$string['docs:course_transformer:title'] = 'Course';
$string['docs:create_category'] = 'Create one or more categories';
$string['docs:create_category:successful_response'] = 'Category(ies) created';
$string['docs:create_category_body:title'] = 'Category creation object';
$string['docs:create_user_enrolment_body:title'] = 'User enrolment creation object';
$string['docs:create_courses'] = 'Create one or more courses';
$string['docs:create_courses:successful_response'] = 'Course(s) created';
$string['docs:create_course_body:title'] = 'Course creation object';
$string['docs:create_enrolment'] = 'Create an enrolment for a user in a course';
$string['docs:create_enrolment:successful_response'] = 'User enrolment created';
$string['docs:create_users'] = 'Create one or more users';
$string['docs:create_users:successful_response'] = 'User(s) created';
$string['docs:create_user_body:title'] = 'User creation object';
$string['docs:delete_category'] = 'Delete a category';
$string['docs:delete_category:successful_response'] = 'Category deleted successfully';
$string['docs:delete_course'] = 'Delete a course';
$string['docs:delete_course:successful_response'] = 'Course deleted successfully';
$string['docs:delete_enrolment'] = 'Delete a user enrolment from a course';
$string['docs:delete_enrolment:successful_response'] = 'Enrolment deleted successfully';
$string['docs:delete_user'] = 'Delete a user';
$string['docs:delete_user:successful_response'] = 'User deleted successfully';
$string['docs:detailed_course_completion_resource:description'] = 'Represents a course completion with its attributes, links, and embedded resources.';
$string['docs:detailed_course_completion_resource:title'] = 'Detailed course completion (HAL)';
$string['docs:detailed_user_resource:description'] = 'Represents a user with their attributes and links.';
$string['docs:detailed_user_resource:title'] = 'Detailed user (HAL)';
$string['docs:enrol_instance:enrol'] = 'Enrolment plugin name. Example: "manual"';
$string['docs:enrol_instance:id'] = 'Enrolment instance ID';
$string['docs:enrol_instance_resource:description'] = 'Represents a course enrolment instance with its attributes, links, and embedded resources';
$string['docs:enrol_instance_resource:title'] = 'Course enrolment instance (HAL)';
$string['docs:enrol_instance_transformer:title'] = 'Course enrolment instance';
$string['docs:forbidden_error:description'] = 'Authentication failed or was not provided. The access token may be missing, invalid, or expired.';
$string['docs:forbidden_error:example'] = 'The access token may be missing or invalid.';
$string['docs:forbidden_error:title'] = 'Access denied';
$string['docs:generic_error:description'] = 'Default format for error messages';
$string['docs:generic_error:title'] = 'Generic error';
$string['docs:get_category'] = 'Retrieve a category';
$string['docs:get_category:successful_response'] = 'Returns category data';
$string['docs:get_completion'] = 'Get course completion';
$string['docs:get_completion:successful_response'] = 'Success';
$string['docs:get_course'] = 'Get course';
$string['docs:get_course:successful_response'] = 'Success';
$string['docs:get_enrol_instance'] = 'Get enrolment instance';
$string['docs:get_enrol_instance:successful_response'] = 'Success';
$string['docs:get_user'] = 'Get user';
$string['docs:get_user:successful_response'] = 'Success';
$string['docs:get_user_enrolment'] = 'Get user enrolment in course';
$string['docs:get_user_enrolment:successful_response'] = 'Success';
$string['docs:grade_resource:description'] = 'Contains information about the user\'s grade in a course.';
$string['docs:grade_resource:title'] = 'User grade in course (HAL)';
$string['docs:grade_transformer:description'] = 'Contains information about the grade assigned to a user in a course';
$string['docs:grade_transformer:title'] = 'User grade in course';
$string['docs:list_categories'] = 'List categories';
$string['docs:list_categories:description'] = "Returns a paginated list of categories. **Supports LHS Brackets filters**";
$string['docs:list_categories:response:description'] = 'Paginated list of categories';
$string['docs:list_completions_by_course'] = 'List completions for a specific course';
$string['docs:list_completions_by_course:description'] = "Returns a paginated list of course completions for a specific course. **Supports LHS Brackets filters**";
$string['docs:list_completions'] = 'List course completions';
$string['docs:list_completions:description'] = "Returns a paginated list of course completions. **Supports LHS Brackets filters**";
$string['docs:list_completions_by_course:successful_response'] = 'Success';
$string['docs:list_courses'] = 'List courses';
$string['docs:list_courses:description'] = 'List courses';
$string['docs:list_courses:successful_response'] = 'Success';
$string['docs:list_enrol_instances'] = 'List enrolment instances';
$string['docs:list_enrol_instances:description'] = "Returns a paginated list of enrolment instances. **Supports LHS Brackets filters**";
$string['docs:list_enrol_instances:successful_response'] = 'Success';
$string['docs:list_users'] = 'List users';
$string['docs:list_users:description'] = "Returns a paginated list of users. **Supports LHS Brackets filters**";
$string['docs:list_users:successful_response'] = 'Success';
$string['docs:list_user_enrolments'] = 'List user enrolments in courses';
$string['docs:list_user_enrolments:description'] = "Returns a paginated list of user enrolments. **Supports LHS Brackets filters**";
$string['docs:list_user_enrolments:successful_response'] = 'Success';
$string['docs:not_found_error:description'] = 'The requested resource was not found. It may have been removed or never existed.';
$string['docs:not_found_error:example'] = 'Resource not found';
$string['docs:not_found_error:title'] = 'Resource not found';
$string['docs:pagination:_after'] = 'Cursor used for pagination.';
$string['docs:pagination:_limit'] = 'Number of items per page.';
$string['docs:unauthorized_error:description'] = 'Authentication failed or was not provided. The access token may be missing, invalid, or expired.';
$string['docs:unauthorized_error:example'] = 'Invalid access token!';
$string['docs:unauthorized_error:title'] = 'Authentication error';
$string['docs:update_category'] = 'Update a category';
$string['docs:update_category:successful_response'] = 'Category updated successfully';
$string['docs:update_category_body:title'] = 'Category update object';
$string['docs:update_course'] = 'Update a specific course';
$string['docs:update_course:successful_response'] = 'Course updated successfully';
$string['docs:update_courses'] = 'Update multiple courses';
$string['docs:update_courses:partial_successful_response'] = 'Some courses were updated successfully, but others returned errors';
$string['docs:update_courses:successful_response'] = 'Courses updated successfully';
$string['docs:update_course_body:title'] = 'Course update object';
$string['docs:update_enrolment'] = 'Update a user\'s enrolment in a course';
$string['docs:update_enrolment:successful_response'] = 'User enrolment updated';
$string['docs:update_user'] = 'Update a specific user';
$string['docs:update_user:succesful_response'] = 'User updated successfully';
$string['docs:update_users'] = 'Update multiple users';
$string['docs:update_users:partial_successful_response'] = 'Some users were updated successfully, but others returned errors';
$string['docs:update_users:successful_response'] = 'Users updated successfully';
$string['docs:update_user_body:title'] = 'User update object';
$string['docs:update_user_enrolment_body:title'] = 'User enrolment update object';
$string['docs:update_warning_resource:description'] = 'Contains data about a partial resource update error';
$string['docs:update_warning_resource:title'] = 'Partial update warnings (HAL)';
$string['docs:user:address'] = 'Postal address';
$string['docs:user:alternatename'] = 'The alternate name of the user';
$string['docs:user:auth'] = 'Auth plugins include manual, ldap, etc';
$string['docs:user:calendartype'] = 'Calendar type such as "gregorian", must exist on server';
$string['docs:user:city'] = 'Home city of the user';
$string['docs:user:country'] = 'Home country code of the user, such as AU or CZ';
$string['docs:user:createpassword'] = 'True if password should be created and mailed to user.';
$string['docs:user:customfields'] = 'User custom fields (also known as user profile fields)';
$string['docs:user:customfields:type'] = 'The name of the custom field';
$string['docs:user:customfields:value'] = 'The value of the custom field';
$string['docs:user:department'] = 'Department';
$string['docs:user:description'] = 'User profile description, no HTML';
$string['docs:user:email'] = 'A valid and unique email address';
$string['docs:user:firstname'] = 'The first name(s) of the user';
$string['docs:user:firstnamephonetic'] = 'The phonetic version of the user\'s first name(s)';
$string['docs:user:id'] = 'User ID';
$string['docs:user:idnumber'] = 'An arbitrary ID code number, possibly from the institution';
$string['docs:user:institution'] = 'Institution';
$string['docs:user:interests'] = 'User interests (separated by commas)';
$string['docs:user:lang'] = 'Language code such as "en", must exist on server';
$string['docs:user:lastname'] = 'The family name of the user';
$string['docs:user:lastnamephonetic'] = 'The phonetic version of the user\'s family name';
$string['docs:user:maildisplay'] = 'Email visibility';
$string['docs:user:mailformat'] = 'Mail format code: 0 for plain text, 1 for HTML, etc.';
$string['docs:user:middlename'] = 'The middle name of the user';
$string['docs:user:password'] = 'Plain text password consisting of any characters';
$string['docs:user:phone1'] = 'Phone 1';
$string['docs:user:phone2'] = 'Phone 2';
$string['docs:user:preferences'] = 'User preferences';
$string['docs:user:preferences:type'] = 'The name of the preference';
$string['docs:user:preferences:value'] = 'The value of the preference';
$string['docs:user:theme'] = 'Theme name such as "standard", must exist on server';
$string['docs:user:timezone'] = 'Timezone code such as Australia/Perth, or 99 for default';
$string['docs:user:username'] = 'Username policy is defined in Moodle security config.';
$string['docs:user_enrolment:enrolid'] = 'Enrolment instance ID';
$string['docs:user_enrolment:id'] = 'Enrolment ID';
$string['docs:user_enrolment:roleid'] = 'User role ID in the course';
$string['docs:user_enrolment:status'] = 'Enrolment status. 0 for active, 1 for suspended.';
$string['docs:user_enrolment:timeend'] = 'Enrolment end date.';
$string['docs:user_enrolment:timestart'] = 'Enrolment start date.';
$string['docs:user_enrolment:userid'] = 'User ID';
$string['docs:user_enrolment_resource:description'] = 'Represents a user enrolment in a course with its attributes, links, and embedded resources.';
$string['docs:user_enrolment_resource:title'] = 'User enrolment in course (HAL)';
$string['docs:user_enrol_transformer:title'] = 'User enrolment in a course';
$string['docs:user_resource:description'] = 'Represents a user with their attributes and links.';
$string['docs:user_resource:title'] = 'User (HAL)';
$string['docs:user_transformer:title'] = 'User';
$string['docs:validation_error:description'] = 'The request was understood but contains invalid data. One or more fields failed validation.';
$string['docs:validation_error:example'] = 'Invalid value detected';
$string['docs:validation_error:title'] = 'Validation error';

// Filters
$str_accepts_operators = ' Accepts the operators {$a}';

$string['docs:basic_enrol_instance_sql_filter:courseid'] = 'Course ID.' . $str_accepts_operators;
$string['docs:basic_enrol_instance_sql_filter:enrol'] = 'Enrolment plugin name.' . $str_accepts_operators;
$string['docs:category_sql_filter:coursecount'] = 'Number of courses contained in the category.' . $str_accepts_operators;
$string['docs:category_sql_filter:depth'] = 'Category depth.' . $str_accepts_operators;
$string['docs:category_sql_filter:id'] = 'Category ID.' . $str_accepts_operators;
$string['docs:category_sql_filter:idnumber'] = 'Category idnumber.' . $str_accepts_operators;
$string['docs:category_sql_filter:name'] = 'Category name.' . $str_accepts_operators;
$string['docs:category_sql_filter:parent'] = 'Parent category ID.' . $str_accepts_operators;
$string['docs:category_sql_filter:timemodified'] = 'Modification date.' . $str_accepts_operators;
$string['docs:category_sql_filter:visible'] = 'Category visibility. Use 1 for visible and 0 for hidden.' . $str_accepts_operators;
$string['docs:custom_course_fields_sql_filter'] = "Filter by custom field. Replace `{name}` with the shortname of the custom field.\nExample: `custom_field_audience=IT`.\nSupported operators vary depending on the field type.";
$string['docs:course_sql_filter:categoryid'] = 'ID of the category that contains the course.' . $str_accepts_operators;
$string['docs:course_sql_filter:completionnotify'] = 'Notify users when they complete this course. 1 for yes, 0 for no.' . $str_accepts_operators;
$string['docs:course_sql_filter:enablecompletion'] = 'Whether progress tracking is enabled for the course. 1 for yes, 0 for no.' . $str_accepts_operators;
$string['docs:course_sql_filter:enddate'] = 'Course end date.' . $str_accepts_operators;
$string['docs:course_sql_filter:format'] = 'Course format: learningflix, seasons, topics, site...' . $str_accepts_operators;
$string['docs:course_sql_filter:fullname'] = 'Full course name.' . $str_accepts_operators;
$string['docs:course_sql_filter:id'] = 'Course ID.' . $str_accepts_operators;
$string['docs:course_sql_filter:idnumber'] = 'Course idnumber.' . $str_accepts_operators;
$string['docs:course_sql_filter:shortname'] = 'Short course name.' . $str_accepts_operators;
$string['docs:course_sql_filter:startdate'] = 'Course start date.' . $str_accepts_operators;
$string['docs:course_sql_filter:timecreated'] = 'Course creation date.' . $str_accepts_operators;
$string['docs:course_sql_filter:timemodified'] = 'Course modification date.' . $str_accepts_operators;
$string['docs:course_sql_filter:visible'] = 'Course visibility. Use 1 for visible and 0 for hidden.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:courseid'] = 'Course ID.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:enrol'] = 'Enrolment plugin name.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:enrolenddate'] = 'Self-enrolment start date.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:enrolperiod'] = 'Default enrolment duration, in seconds.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:enrolstartdate'] = 'Enrolment end date.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:name'] = 'Enrolment instance name.' . $str_accepts_operators;
$string['docs:enrol_instance_sql_filter:status'] = 'Enrolment instance status. 0 for active, 1 for inactive.' . $str_accepts_operators;
$string['docs:user_sql_filter:auth'] = 'Authentication plugin name.' . $str_accepts_operators;
$string['docs:user_sql_filter:confirmed'] = 'Whether the user account is confirmed.' . $str_accepts_operators;
$string['docs:user_sql_filter:deleted'] = 'Whether the user is deleted.' . $str_accepts_operators;
$string['docs:user_sql_filter:email'] = 'User email address.' . $str_accepts_operators;
$string['docs:user_sql_filter:firstaccess'] = 'Date of first access.' . $str_accepts_operators;
$string['docs:user_sql_filter:firstname'] = 'User first name.' . $str_accepts_operators;
$string['docs:user_sql_filter:id'] = 'User ID.' . $str_accepts_operators;
$string['docs:user_sql_filter:idnumber'] = 'User idnumber.' . $str_accepts_operators;
$string['docs:user_sql_filter:lastaccess'] = 'Date of last access.' . $str_accepts_operators;
$string['docs:user_sql_filter:lastlogin'] = 'Date of last login.' . $str_accepts_operators;
$string['docs:user_sql_filter:policyagreed'] = 'Whether the user agreed to the site policy.' . $str_accepts_operators;
$string['docs:user_sql_filter:suspended'] = 'Whether the user is suspended.' . $str_accepts_operators;
$string['docs:user_sql_filter:timecreated'] = 'User account creation date.' . $str_accepts_operators;
$string['docs:user_sql_filter:timemodified'] = 'User account modification date.' . $str_accepts_operators;
$string['docs:user_sql_filter:username'] = 'Username.' . $str_accepts_operators;
$string['docs:profile_field_sql_filter'] = "Filter by custom profile field. Replace `{name}` with the shortname of the custom field.\nExample: `profile_field_department=IT`.\nSupported operators vary depending on the field type.";
$string['docs:user_enrolment_sql_filter:enrolid'] = 'Enrolment instance ID.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:status'] = 'Enrolment status. 0 for active, 1 for suspended.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:timecreated'] = 'Enrolment creation date.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:timeend'] = 'Enrolment end date.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:timemodified'] = 'Enrolment modification date.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:timestart'] = 'Enrolment start date.' . $str_accepts_operators;
$string['docs:user_enrolment_sql_filter:userid'] = 'User ID.' . $str_accepts_operators;
$string['docs:course_completion_sql_filter:course'] = 'Course ID.' . $str_accepts_operators;
$string['docs:course_completion_sql_filter:userid'] = 'User ID.' . $str_accepts_operators;
$string['docs:course_completion_sql_filter:timeenrolled'] = 'Date enrolled.' . $str_accepts_operators;
$string['docs:course_completion_sql_filter:timestarted'] = 'Date the user started meeting the course completion criteria.' . $str_accepts_operators;
$string['docs:course_completion_sql_filter:timecompleted'] = 'Date the course was completed.' . $str_accepts_operators;
$string['docs:api:description'] = "### Filters with LHS Brackets

Routes that support filtering allow the use of **LHS Brackets** syntax, which allows the creation of more dynamic queries.

The syntax is:
`<field>[<operator>]=<value>`

#### Example:
```http
GET /api/resource?name[like]=test*&depth[gt]=1
```
- `name[like]=test*` applies a partial match filter to the `name` field;
- `depth[gt]=1` filters results where `depth` is greater than 1.

> **Note:** the available operators may vary according to the field used.
---
### Supported Operators

| Operator | Description | Example |
|------------|--------------------------------------------|---------------------------------|
| `gt` | Greater than | timestarted[gt]=1743311107 |
| `gte` | Greater than or equal to (`greater than or equal`) | timecompleted[gte]=1655300000 |
| `lt` | Less than (`less than`) | timecreated[lt]=1728805000 |
| `lte` | Less than or equal to (`less than or equal`) | firstaccess[lte]=1738000000 |
| `eq` | Equal to | policyagreed[eq]=1 |
| `neq` | Different from | auth[neq]=manual |
| `isnull` | Null value (`IS NULL` SQL) | name[isnull] |
| `notnull` | Non-null value (`NOT NULL` SQL) | idnumber[notnull] |
| `like` | Matches pattern (`LIKE` SQL) | idnumber[like]=external* |
| `notlike` | Does not match pattern (`NOT LIKE` SQL) | email[notlike]=*@test.com |
| `in` | Value is in list (comma-separated) | custom_field_level[in]=1,2,3 |

---";
$string['docs:create_courses:request_body_description'] = 'You can submit a single course using `{ "course": { ... } }` or multiple courses with `{ "courses": [ ... ] }`.';
$string['docs:create_category:request_body_description'] = 'You can submit a single category using `{ "category": { ... } }` or multiple categories with `{ "categories": [ ... ] }`.';
$string['docs:create_users:request_body_description'] = 'You can submit a single user using `{ "user": { ... } }` or multiple users with `{ "users": [ ... ] }`.';