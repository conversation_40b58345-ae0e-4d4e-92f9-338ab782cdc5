{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "14f14643541811aa9f5400f547a7be22", "packages": [{"name": "linkisensei/moodle-dev-utils", "version": "0.0.3", "source": {"type": "git", "url": "https://github.com/linkisensei/moodle-dev-utils.git", "reference": "c69a29df852ba2e83ccde20f1784bc3c6668c3cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/linkisensei/moodle-dev-utils/zipball/c69a29df852ba2e83ccde20f1784bc3c6668c3cb", "reference": "c69a29df852ba2e83ccde20f1784bc3c6668c3cb", "shasum": ""}, "require": {"php": ">=8.0", "psr/http-message": "^2.0"}, "require-dev": {"vlucas/phpdotenv": "^5.6"}, "suggest": {"moodle/moodle": "This package is intended to be used within a Moodle environment as it depends on classes provided by Moodle."}, "type": "library", "autoload": {"psr-4": {"moodle_dev_utils\\": "src/"}}, "scripts": {"test": ["@php tests/run.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/linkisensei"}], "description": "Collection of classes and functions to be used in Moodle plugins. This library does not follow the PSR-1 Basic Coding Standard so it better interacts with Moodle Coding Standards", "support": {"source": "https://github.com/linkisensei/moodle-dev-utils/tree/0.0.3", "issues": "https://github.com/linkisensei/moodle-dev-utils/issues"}, "time": "2025-04-08T10:42:18+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}