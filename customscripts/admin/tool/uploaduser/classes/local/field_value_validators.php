<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * File containing the field_value_validators class.
 *
 * @package    tool_uploaduser
 * @copyright  2019 Mathew May
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace tool_uploaduser\local;

defined('MOODLE_INTERNAL') || die();

/**
 * Field validator class.
 *
 * @package    tool_uploaduser
 * @copyright  2019 Mathew May
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class custom_field_value_validators
{

    /**
     * List of valid and compatible themes.
     *
     * @return array
     */
    protected static $themescache;

    /**
     * Validates the value provided for the theme field.
     *
     * @param string $value The value for the theme field.
     * @return array Contains the validation status and message.
     */
    public static function validate_theme($value)
    {
        global $CFG;

        $status = 'normal';
        $message = '';

        // Validate if user themes are allowed.
        if (!$CFG->allowuserthemes) {
            $status = 'warning';
            $message = get_string('userthemesnotallowed', 'tool_uploaduser');
        } else {
            // Cache list of themes if not yet set.
            if (!isset(self::$themescache)) {
                self::$themescache = get_list_of_themes();
            }

            // Check if we have a valid theme.
            if (empty($value)) {
                $status = 'warning';
                $message = get_string('notheme', 'tool_uploaduser');
            } else if (!isset(self::$themescache[$value])) {
                $status = 'warning';
                $message = get_string('invalidtheme', 'tool_uploaduser', s($value));
            }
        }

        return [$status, $message];
    }

    /**
     * Validate if position belongs to structure
     *
     * @param string $position Position name or ID
     * @param string $structure Structure name or ID
     * @return array with keys: status (normal, warning, error) and message (optional error message)
     */
    public static function validate_position_structure($position, $structure)
    {
        global $DB;

        // Se não tiver estrutura nem cargo, está ok
        if (empty($position) && empty($structure)) {
            return ['normal', ''];
        }

        // desabilitado temporariamente por falta do plugin local/hierarchy v2
        // Se tiver cargo mas não tiver estrutura, erro
        if (!empty($position) && empty($structure)) {
            return ['error', get_string('positionwithoutstructure', 'profilefield_position')];
        }

        // Se tiver estrutura mas não tiver cargo, está ok
        if (empty($position) && !empty($structure)) {
            return ['normal', ''];
        }

        // Ambos estão preenchidos, verificar se o cargo pertence à estrutura

        // Obter ID da estrutura
        $structureId = null;
        if (is_numeric($structure)) {
            // É um ID
            if ($DB->record_exists('local_hierarchy_structure', ['id' => $structure, 'deleted' => 0])) {
                $structureId = $structure;
            }
        } else {
            // É um nome
            $structureRecord = $DB->get_record(
                'local_hierarchy_structure',
                ['name' => $structure, 'deleted' => 0],
                'id'
            );
            if ($structureRecord) {
                $structureId = $structureRecord->id;
            }
        }

        if (!$structureId) {
            return;
        }

        // Obter ID do cargo
        $positionId = null;
        if (is_numeric($position)) {
            // É um ID
            if ($DB->record_exists('local_hierarchy_position', ['id' => $position, 'deleted' => 0])) {
                $positionId = $position;
            }
        } else {
            // É um nome
            $positionRecord = $DB->get_record(
                'local_hierarchy_position',
                ['name' => $position, 'deleted' => 0],
                'id'
            );
            if ($positionRecord) {
                $positionId = $positionRecord->id;
            }
        }

        if (!$positionId) {
            return;
        }

        // desabilitado temporariamente por falta do plugin local/hierarchy v2
        // Verificar se o cargo pertence à estrutura
        // Verificar na tabela de relacionamento muitos para muitos
        $exists = $DB->record_exists('local_hierarchy_pos_struct', [
            'positionid' => $positionId,
            'structureid' => $structureId
        ]);

        if ($exists) {
            return ['normal', ''];
        }

        return ['error', get_string(
            'positionnotinstructure',
            'profilefield_position',
            ['position' => $position, 'structure' => $structure]
        )];
    }
}
