language: php

notifications:
  email:
    recipients:
      - d<PERSON><PERSON><PERSON>@catalyst-au.net

sudo: false

cache:
  directories:
    - $HOME/.composer/cache

addons:
  postgresql: "9.6"

php:
  - 7.1
  - 7.2
  - 7.3

env:
  global:
    - DB=pgsql
  matrix:
    - MOODLE_BRANCH=MOODLE_33_STABLE
    - MOODLE_BRANCH=MOODLE_34_STABLE
    - MOODLE_BRANCH=MOODLE_35_STABLE
    - MOODLE_BRANCH=MOODLE_36_STABLE
    - MOODLE_BRANCH=MOODLE_37_STABLE
    - MOODLE_BRANCH=MOODLE_38_STABLE
    - MOODLE_BRANCH=master

matrix:
  exclude:
    - php: 7.1
      env: M<PERSON><PERSON><PERSON>_BRANCH=master
    - php: 7.2
      env: MOODLE_BRANCH=MOODLE_33_STABLE
    - php: 7.3
      env: MOODLE_BRANCH=MOODLE_33_STABLE
    - php: 7.3
      env: M<PERSON><PERSON>LE_BRANCH=MOODLE_34_STABLE
    - php: 7.3
      env: M<PERSON><PERSON>LE_BRANCH=MOODLE_35_STABLE

before_install:
  - cd ../..
  - composer selfupdate
  - composer create-project -n --no-dev moodlerooms/moodle-plugin-ci ci ^1
  - export PATH="$(cd ci/bin; pwd):$(cd ci/vendor/bin; pwd):$PATH"

install:
  - moodle-plugin-ci install -vvv

script:
  - moodle-plugin-ci phplint
  - moodle-plugin-ci phpmd
  - moodle-plugin-ci codechecker
  - moodle-plugin-ci csslint
  - moodle-plugin-ci shifter
  - moodle-plugin-ci jshint
  - moodle-plugin-ci phpunit
  - moodle-plugin-ci behat
