﻿<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.


/**
 * @package    auth_joomdle
 * @copyright  2009 Qontori Pte Ltd
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */


$string['auth_joomdletitle'] = 'Joomdle';
$string['auth_joomdledescription'] = 'diese Methode nutzt -Joomdle web services- um zu erkennen ob sich der User in einer gültigen Sitzung von Joomla.\nVersion 0.3 befindet';

$string['joomla_sp_name'] = 'Joomdle';
$string['joomla_sp_description'] = 'Service für die Joomla Integration<br>';
$string['auth_joomla_url'] = 'Joomla URL<br>';
$string['auth_joomla_url_desc'] = 'Joomla Server URL<br>';
$string['auth_joomla_connection_method'] = 'Verbindungsmethode<br>';
$string['auth_joomla_connection_method_description'] = 'Verbindungsmethode um Web Services zu nutzen<br>';
$string['auth_joomla_jomsocial_integration'] = 'Integration von Jomsocial<br>';
$string['auth_joomla_jomsocial_integration_description'] = 'Integration von Jomsocial in Joomla<br>';
$string['auth_joomla_jomsocial_activities'] = 'Jomsocial Aktivitäten<br>';
$string['auth_joomla_jomsocial_activities_description'] = 'Jomsocial Aktivitäten hinzufügen<br>';
$string['auth_joomla_jomsocial_groups'] = 'Jomsocial Gruppen<br>';
$string['auth_joomla_jomsocial_groups_description'] = 'Eine Jomsocial Gruppe für jeden Kurs erzeugen<br>';
$string['auth_joomla_group_discussion'] = 'Gruppendiskussion';
$string['auth_joomla_enrol_parents'] = 'Eltern in Kurse einschreiben';
$string['auth_joomla_enrol_parents_description'] = 'Automatisches Einschreiben der Eltern in Kinderkursen';
$string['auth_joomla_parent_role_id'] = 'Eltern Rolle ID';
$string['auth_joomla_parent_role_id_description'] = 'Rolle ID der Eltern Rolle';
