<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component Learningflix course format.
 *
 * @package   format_learningflix
 * @copyright 2023 Revvo
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['addsections'] = 'Add {$a}';
$string['currentsection'] = 'This season';
$string['deletesection'] = 'Delete {$a}';
$string['editsection'] = 'Edit {$a}';
$string['editsectionname'] = 'Edit {$a} name';
$string['hidefromothers'] = 'Hide {$a}';
$string['newsectionname'] = 'New name for {$a->sectionname} {$a->title}';
$string['page-course-view-learningflix'] = 'Any course main page in learningflix format';
$string['page-course-view-learningflix-x'] = 'Any course page in learningflix format';
$string['pluginname'] = 'LearningFlix format';
$string['privacy:metadata'] = 'The Learningflix format plugin does not store any personal data.';
$string['indentation'] = 'Allow indentation on course page';
$string['indentation_help'] = 'Allow teachers, and other users with the manage activities capability, to indent items on the course page.';
$string['section0name'] = 'General';
$string['sectionavailability_title'] = '{$a} availability';
$string['sectiondelete_title'] = 'Delete {$a}?';
$string['sectionmove_title'] = 'Move {$a}';
$string['sectionname'] = 'Season';
$string['sectionsavailability'] = '{$a} availability';
$string['sectionsavailability_title'] = '{$a} availability';
$string['sectionsdelete'] = 'Delete {$a}';
$string['sectiondelete_info'] = 'This will delete {$a->sectionname} {$a->name} and all activities within it';
$string['sectionsdelete_info'] = 'This will delete {$a->count} {$a->sectionname} and all the activities they contain.';
$string['sectionsdelete_title'] = 'Delete selected {$a}?';
$string['sectionsmove'] = 'Move {$a}';
$string['sectionsmove_info'] = 'Move {$a->count} {$a->sectionname} after';
$string['sectionsmove_title'] = 'Move selected {$a}';
$string['selectsection'] = 'Select {$a}';
$string['showfromothers'] = 'Show {$a}';
$string['noactivities'] = 'No content available yet.';
$string['intro'] = 'Introduction';
$string['resume'] = 'Resume';
$string['activity_unavailable'] = 'Activity not available';
$string['access_activity'] = 'Access activity';

$string['config:sectionname'] = 'Section name';
$string['config:pluralsectionname'] = 'Plural section name';
$string['config:sectionname_help'] = 'Customize the name of the sections on the course page, by default the name is "season(s)".';

$string['config:coverposition'] = 'Course cover position';
$string['config:coverposition_help'] = 'Select the alignment the course cover should display.';
$string['config:coverposition_center'] = 'Centered';
$string['config:coverposition_top_center'] = 'Top and center';
$string['config:coverposition_top_left'] = 'Top and left';
$string['config:coverposition_top_right'] = 'Top and right';
$string['config:coverposition_bottom_center'] = 'Bottom and center';
$string['config:coverposition_bottom_left'] = 'Bottom and left';
$string['config:coverposition_bottom_right'] = 'Bottom and right';