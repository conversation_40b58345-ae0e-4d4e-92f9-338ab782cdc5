{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content/cm

    Displays a course module instance inside a course section.

    Example context (json):
    {
        "cmname": {
            "displayvalue" : "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Activity example</span></a>"
        },
        "hasname": "true",
        "afterlink": "<span class=\"badge badge-primary\">30 unread messages</span>",
        "hasextras": true,
        "extras": ["<span class=\"badge badge-secondary\">[extras]</span>"],
        "activityinfo": {
            "hasmodavailability": true,
            "activityname": "Activity example",
            "hascompletion": true,
            "uservisible": true,
            "hasdates": true,
            "isautomatic": true,
            "istrackeduser": true,
            "activitydates": [
                {
                    "label": "Opens:",
                    "datestring": "6 April 2021, 6:46 PM"
                }
            ],
            "completiondetails": [
                {
                    "statuscomplete": 1,
                    "description": "Viewed"
                },
                {
                    "statusincomplete": 1,
                    "description": "Receive a grade"
                }
            ]
        },
        "modstealth": true,
        "indent": 1
    }
}}
{{#editing}}
    <div class="divider divider-plus bulk-hidden" data-action="insert-before-{{activityname}}">
        {{> core_course/activitychooserbuttonactivity}}
    </div>
{{/editing}}
<div class="activity-item bg-dark {{#modstealth}}hiddenactivity stealth{{/modstealth}}{{!
        }}{{#modhiddenfromstudents}}hiddenactivity{{/modhiddenfromstudents}}{{!
        }}{{#modinline}}activityinline{{/modinline}} position-relative" 
		data-activityname="{{activityname}}" 
		style="background-image:url('{{coverurl}}')" 
	>
	
    {{^hidelabel}}
	    <h6><span class="badge badge-dark rounded-sm">T{{season}}E{{episode}}</span></h6>
	{{/hidelabel}}

	
	{{^modavailability.hasmodavailability}}
	<a href="{{url}}" class="d-flex justify-content-center align-items-center position-absolute w-100 h-100" style="top:0; left:0" onclick="{{{onclick}}}">
    <!--{{#player}}
		<span class="btn btn-light btn-lg rounded-circle text-primary" style="cursor: pointer !important;"><i class="fa-solid fa-play pl-1"></i></span>
    {{/player}}-->
	</a>
	{{/modavailability.hasmodavailability}}

    {{#completionstatus.state}}
    <a href="{{url}}" class="d-flex justify-content-center align-items-center position-absolute w-100 h-100" style="top:0; left:0" onclick="{{{onclick}}}">
		<span class="btn btn-success btn-lg rounded-circle text-white" style="pointer-events:none"><i class="fa-solid fa-check"></i></span>
	</a>
    {{/completionstatus.state}}

	{{#modavailability.hasmodavailability}}
	<a href="{{#isadmin}}{{url}}{{/isadmin}}{{^isadmin}}#{{/isadmin}}" class="btn-show-availability-info d-flex justify-content-center align-items-center position-absolute w-100 h-100" style="top:0; left:0; z-index:10" onclick="{{{onclick}}}">
		 <span class="btn btn-danger btn-lg rounded-circle" style="pointer-events:none"><i class="fa-solid fa-sm fa-lock"></i></span>
	</a>
	{{/modavailability.hasmodavailability}}
		
    {{$ core_courseformat/local/content/cm/bulkselect }}
        {{> core_courseformat/local/content/cm/bulkselect }}
    {{/ core_courseformat/local/content/cm/bulkselect }}
    {{!
      Place the actual content of the activity-item in a separate template to make it easier for other formats to add
      additional content to the activity wrapper.
    }}
    {{$ core_courseformat/local/content/cm/activity }}
        {{> format_learningflix/local/content/cm/activity }}
    {{/ core_courseformat/local/content/cm/activity }}
</div>
