{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_courseformat/local/content

    Displays the complete course format.

    Example context (json):
    {
        "initialsection": {
                "num": 0,
                "id": 34,
                "cmlist": {
                    "cms": [
                        {
                            "cmitem": {
                                "cmformat": {
                                    "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Forum example</span></a>",
                                    "hasname": "true"
                                },
                                "id": 3,
                                "cmid": 3,
                                "module": "forum",
                                "extraclasses": "newmessages",
                                "anchor": "module-3"
                            }
                        }
                    ],
                    "hascms": true
                },
                "iscurrent": true,
                "summary": {
                    "summarytext": "Summary text!"
                }
            },
        "sections": [
            {
                "num": 1,
                "id": 35,
                "header": {
                    "name": "Section title",
                    "url": "#"
                },
                "cmlist": {
                    "cms": [
                        {
                            "cmitem": {
                                "cmformat": {
                                    "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Another forum</span></a>",
                                    "hasname": "true"
                                },
                                "id": 4,
                                "cmid": 4,
                                "module": "forum",
                                "extraclasses": "newmessages",
                                "anchor": "module-4"
                            }
                        }
                    ],
                    "hascms": true
                },
                "iscurrent": true,
                "summary": {
                    "summarytext": "Summary text!"
                }
            },
            {
                "num": 4,
                "id": 36,
                "header": {
                    "name": "Section 2 title",
                    "url": "#"
                },
                "cmlist": {
                    "cms": [
                        {
                            "cmitem": {
                                "cmformat": {
                                    "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Forum example</span></a>",
                                    "hasname": "true"
                                },
                                "id": 5,
                                "cmid": 5,
                                "module": "forum",
                                "extraclasses": "newmessages",
                                "anchor": "module-5"
                            }
                        }
                    ],
                    "hascms": true
                },
                "iscurrent": true,
                "summary": {
                    "summarytext": "Summary text!"
                }
            }
        ],
        "format": "topics",
        "title": "Course title example",
            "hasnavigation": true,
            "sectionnavigation": {
            "hasprevious": true,
            "previousurl": "#",
            "larrow": "&#x25C4;",
            "previousname": "Section 3",
            "hasnext": true,
            "rarrow": "&#x25BA;",
            "nexturl": "#",
            "nextname": "Section 5"
        },
        "sectionselector": {
            "hasprevious": true,
            "previousurl": "#",
            "larrow": "&#x25C4;",
            "previousname": "Section 3",
            "hasnext": true,
            "rarrow": "&#x25BA;",
            "nexturl": "#",
            "nextname": "Section 5",
            "selector": "<select><option>Section 4</option></select>"
        },
        "sectionreturn": 1,
        "singlesection": {
            "num": 5,
            "id": 37,
            "header": {
                "name": "Single Section Example",
                "url": "#"
            },
            "cmlist": {
                "cms": [
                    {
                        "cmitem": {
                            "cmformat": {
                                "cmname": "<a class=\"aalink\" href=\"#\"><span class=\"instancename\">Assign example</span></a>",
                                "hasname": "true"
                            },
                            "id": 6,
                            "cmid": 6,
                            "module": "assign",
                            "extraclasses": "",
                            "anchor": "module-6"
                        }
                    }
                ],
                "hascms": true
            },
            "iscurrent": true,
            "summary": {
                "summarytext": "Summary text!"
            }
        }
    }
}}

<div id="{{uniqid}}-course-format">
	{{#courseinfo}}
		{{> format_learningflix/local/course_info}}
	{{/courseinfo}}
	
    <h2 class="accesshide">{{{title}}}</h2>
    {{{completionhelp}}}
	
	<div class="flex-fill d-flex justify-content-md-end justify-content-center align-self-start mt-2 custom-link">
		<a
			id="collapsesections"
			class="section-collapsemenu"
			href="#"
			aria-expanded="true"
			role="button"
			data-toggle="toggleall"
		>
			<span class="collapseall text-nowrap">{{#str}}collapseall{{/str}}</span>
			<span class="expandall text-nowrap">{{#str}}expandall{{/str}}</span>
		</a>
    </div>
	
    <ul class="{{format}} p-0" data-for="course_sectionlist">
        {{^hideinitialsection}}
			{{#initialsection}}
				{{$ core_courseformat/local/content/section }}
					{{> format_learningflix/local/content/section }}
				{{/ core_courseformat/local/content/section }}
			{{/initialsection}}
		{{/hideinitialsection}}
		
        {{#sections}}
            {{$ core_courseformat/local/content/section }}
                {{> format_learningflix/local/content/section }}
            {{/ core_courseformat/local/content/section }}
        {{/sections}}
    </ul>
    {{#hasnavigation}}
    <div class="single-section">
        {{#sectionnavigation}}
            {{$ core_courseformat/local/content/sectionnavigation }}
                {{> core_courseformat/local/content/sectionnavigation }}
            {{/ core_courseformat/local/content/sectionnavigation }}
        {{/sectionnavigation}}
        <ul class="{{format}}">
        {{#singlesection}}
            {{$ core_courseformat/local/content/section }}
                {{> format_learningflix/local/content/section }}
            {{/ core_courseformat/local/content/section }}
        {{/singlesection}}
        </ul>
        {{#sectionselector}}
            {{$ core_courseformat/local/content/sectionselector }}
                {{> core_courseformat/local/content/sectionselector }}
            {{/ core_courseformat/local/content/sectionselector }}
        {{/sectionselector}}
    </div>
    {{/hasnavigation}}
    {{#numsections}}
        {{$ core_courseformat/local/content/addsection}}
            {{> core_courseformat/local/content/addsection}}
        {{/ core_courseformat/local/content/addsection}}
    {{/numsections}}
    {{#bulkedittools}}
        {{$ core_courseformat/local/content/bulkedittools}}
            {{> core_courseformat/local/content/bulkedittools}}
        {{/ core_courseformat/local/content/bulkedittools}}
    {{/bulkedittools}}
</div>

{{#js}}
require(['jquery', 'format_learningflix/local/content', 'local_courseblockapi/api', 'core/notification', 'format_learningflix/availability_more'], function($, component, API, Notification, AvailabilityMore) {
    component.init('{{uniqid}}-course-format', {}, {{sectionreturn}}, '{{sectionname}}', '{{pluralsectionname}}');
	
    AvailabilityMore.init();
	
	$(document).on("click", ".btn-show-availability-info", function(e){
		e.preventDefault();
		$(".activity-item .modal-availability-info").removeClass("d-flex").addClass("d-none");
		$(this).closest(".activity-item").find(".modal-availability-info").removeClass("d-none").addClass("d-flex");
	})
	.on("click", ".btn-hide-availability-info", function(e){
		e.preventDefault();
		$(this).closest(".activity-item").find(".modal-availability-info").removeClass("d-flex").addClass("d-none");
	});

	$("#page").on("click", ".toggle-favourite", function(e){
		e.preventDefault();
		let $icon = $(this).find(".theicon");
		let status = !$(this).data("status");
		
		let promise = API.set_favourite({
			courses: [
				{
					component: null, //null means it favourites in system context
					id: {{courseinfo.id}},
					favourite: status,
				},
			],
		});

		promise
			.then((result) => {
                let $this = $(this);
				$(this).data("status", status);
				
				if(status){
					$icon.removeClass("fa-regular").addClass("fa");
					$(this).find(".favtext").addClass("d-none");
					$(this).find(".unfavtext").removeClass("d-none");
                    $(this).find("[aria-live]").text("Favoritado");
				}else{
					$icon.removeClass("fa").addClass("fa-regular");
					$(this).find(".favtext").removeClass("d-none");
					$(this).find(".unfavtext").addClass("d-none");
                    $(this).find("[aria-live]").text("Desfavoritado");
				}

                setTimeout(function(){
                    $this.find("[aria-live]").text("");
                }, 500)
			})
			.fail(Notification.exception);
	});
});
{{/js}}
