<header class="header-incourse d-flex justify-content-between align-items-center px-4 py-3 bg-light border-bottom shadow">
	{{#activityinfo}}
		<div class="d-flex align-items-center">
			<div class="activityiconcontainer {{purpose}} courseicon mr-3">
				<img src="{{{icon}}}" class="activityicon {{iconclass}}" alt="{{{modname}}} icon">
			</div>
			<div>
				<h3 class="mb-0">{{activityname}}</h3>
				<p class="mb-0 small text-muted">{{{pluginname}}} {{#workload}}• {{workload}}{{/workload}}</p>
			</div>
		</div>
	{{/activityinfo}}
	
	<div class="d-flex align-items-center">
		<a href="{{courseurl}}" class="btn btn-dark default">
			<span class="d-md-inline d-none"><i class="fa-solid fa-door-open pr-1"></i> {{#str}}exit, contentbank{{/str}}</span>
			<span class="d-md-none"><i class="fa-solid fa-arrow-right-from-bracket fa-rotate-180 mb-2" title="{{#str}}exit, contentbank{{/str}}"></i></span>
		</a>
		
		{{#activitynavigation}}
		<a href="{{previous_activity_url}}" 
			class="btn btn-dark default {{^previous_activity_url}}disabled{{/previous_activity_url}} {{#previous_activity_has_availability}}has_availability{{/previous_activity_has_availability}}"
			title="Atividade anterior: {{previous_activity_name}}"
			data-id="{{previous_activity_id}}"
			{{#previous_onclick}}onclick="{{{previous_onclick}}}"{{/previous_onclick}}
		>
			<i class="fa-solid fa-angle-left"></i>
		</a>
		
		<a href="{{next_activity_url}}" 
			class="btn btn-dark default {{^next_activity_url}}disabled{{/next_activity_url}} {{#next_activity_has_availability}}has_availability{{/next_activity_has_availability}}"
			title="Próxima Atividade: {{next_activity_name}}"
			data-id="{{next_activity_id}}"
			{{#next_onclick}}onclick="{{{next_onclick}}}"{{/next_onclick}}
		>
			<i class="fa-solid fa-angle-right"></i>
		</a>
		{{/activitynavigation}}
		
		{{#secondarymoremenu}}
			<div class="ml-1">
				{{>theme_smart/course_menu}}
			</div>
		{{/secondarymoremenu}}
	</div>
</header>

{{#js}}
	require(['jquery'], function($){
		$(".header-incourse").on("click", ".has_availability", function(e){
			e.preventDefault();
			let id = $(this).data("id");
			let $modal = $(".courseindex-item[data-id="+id+"] .modal-availability-info").clone(true, true);
			
			$(this).next(".modal-availability-info").remove();

			$modal
				.removeClass("d-none")
				.addClass("d-flex")
				.css("z-index", 100001)
				.insertAfter($(this));
		})
		.on("click", ".btn-hide-availability-info", function(){
			$(this).closest(".modal-availability-info").remove();
		})
	})
{{/js}}