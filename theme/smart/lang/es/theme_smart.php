<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Language file.
 *
 * @package   theme_smart
 * @copyright 2023 Revvo
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['advancedsettings'] = 'Configuraciones avanzadas';
$string['backgroundimage'] = 'Imagen de fondo';
$string['backgroundimage_desc'] = 'La imagen que se mostrará como fondo del sitio. La imagen de fondo que cargues aquí reemplazará la imagen de fondo en tus archivos predefinidos del tema.';
$string['brandcolor'] = 'Color corporativo';
$string['brandcolor_desc'] = 'El color de acento.';
$string['bootswatch'] = 'Bootswatch';
$string['bootswatch_desc'] = 'Un bootswatch es un conjunto de variables de Bootstrap y CSS para estilizar Bootstrap.';
$string['choosereadme'] = 'El tema es un tema moderno y altamente personalizable. Este tema está diseñado para ser usado directamente o como tema padre al crear nuevos temas utilizando Bootstrap 4.';
$string['configtitle'] = 'Tema';
$string['event'] = 'Evento';
$string['generalsettings'] = 'Configuraciones generales';
$string['loginbackgroundimage'] = 'Imagen de fondo de la página de inicio de sesión';
$string['loginbackgroundimage_desc'] = 'La imagen que se mostrará como fondo en la página de inicio de sesión.';
$string['footerlogo'] = "Logo del pie de página";
$string['footerlogo_desc'] = "Agregar un logo para mostrar en el pie de página";
$string['nobootswatch'] = 'Ninguno';
$string['pluginname'] = 'Tema';
$string['presetfiles'] = 'Archivos predefinidos adicionales del tema';
$string['presetfiles_desc'] = 'Los archivos predefinidos pueden usarse para alterar drásticamente la apariencia del tema. Consulta <a href="https://docs.moodle.org/dev/Theme_Presets">Preajustes de temas</a> para obtener información sobre cómo crear y compartir tus propios archivos predefinidos, y consulta el <a href="https://archive.moodle.net/theme">Repositorio de preajustes</a> para ver los que otros han compartido.';
$string['preset'] = 'Preajuste del tema';
$string['preset_desc'] = 'Selecciona un preajuste para cambiar en general el aspecto del tema.';
$string['privacy:metadata'] = 'El tema no almacena ningún dato personal de ningún usuario.';
$string['rawscss'] = 'SCSS sin procesar';
$string['rawscss_desc'] = 'Utiliza este campo para proporcionar código SCSS o CSS que se inyectará al final de la hoja de estilos.';
$string['rawscsspre'] = 'SCSS inicial sin procesar';
$string['rawscsspre_desc'] = 'En este campo puedes proporcionar código SCSS de inicialización; se inyectará antes de todo lo demás. Generalmente se usa para definir variables.';
$string['region-side-pre'] = 'Derecha';

$string['coursesettings'] = 'Configuración del curso';
$string['activitynavigationmods'] = 'Habilitar navegación en módulos';
$string['activitynavigationmods_desc'] = 'Selecciona los módulos que tendrán navegación habilitada. Selecciona ninguno para deshabilitar.';

$string['social_title'] = 'Redes sociales';
$string['socialsettings'] = 'Configuración de redes sociales';
$string['companyaddress'] = 'Dirección de la empresa';
$string['social_facebook'] = 'Facebook';
$string['social_twitter'] = 'Twitter';
$string['social_instagram'] = 'Instagram';
$string['social_linkedin'] = 'LinkedIn';
$string['social_youtube'] = 'YouTube';
$string['social_whatsapp'] = 'WhatsApp';

$string['apps_title'] = 'Tiendas de apps';
$string['app_applestore'] = 'Apple Store';
$string['app_googleplay'] = 'Google Play';

$string['aisettings'] = 'Configuraciones de IA';
$string['ai_title'] = 'Configuración de integración con Judy';
$string['enable_ai_menu'] = 'Habilitar Judy en el menú';
$string['enable_ai_menu_desc'] = 'Habilitar o deshabilitar la integración de Judy en el menú.';
$string['ai_assistantid'] = 'ID del asistente';

$string['privacy'] = 'Privacidad';
$string['terms'] = 'Términos';
$string['credits'] = '© ' . date("Y") . ' Desarrollado por ';

$string['ranking'] = 'Clasificación';
$string['newpassword'] = "Nueva contraseña";
$string['newpassword_help'] = "La contraseña debe tener al menos 8 caracteres, contener al menos 1 dígito, al menos 1 letra minúscula, al menos 1 letra mayúscula y al menos 1 carácter no alfanumérico, como *, guion o almohadilla.";

$string['login:access'] = "Acceder";
$string['login:rememberme'] = "Recuérdame";
$string['login:forgotpassword'] = "¿Olvidaste tu contraseña?";
$string['login:loginwith'] = "Iniciar sesión con";
$string['login:backtologin'] = "Volver al inicio de sesión";
$string['login:lostpassword'] = "¿Perdiste tu contraseña?";
$string['loginlogo'] = "Logo de inicio de sesión";
$string['loginlogo_desc'] = "Agrega un logo que se mostrará en el formulario de inicio de sesión";
$string['login:showpassword'] = "Mostrar contraseña";
$string['login:hiddenpassword'] = "Ocultar contraseña";
$string['signup:username_arialabel'] = "Identificación del usuario. Este campo se usa para crear un nombre de usuario único en la plataforma. Introduce una combinación de letras, números y caracteres especiales. Evita usar espacios o caracteres acentuados. Ejemplos de nombres válidos: 'usuario123'";
$string['signup:password_arialabel'] = "Contraseña. ".$string['newpassword_help'];

// MENU BAR //
$string['adminmenu_name'] = 'Administración';
$string['admin_submenu_manageusers'] = 'Gestionar usuarios';
$string['admin_submenu_managecourses'] = 'Gestionar cursos';

// COURSE //
$string['exitcourse'] = 'Salir del curso';
$string['coursemenu'] = 'Menú del curso';

$string['showfooter'] = 'Mostrar pie de página';
$string['unaddableblocks'] = 'Bloques innecesarios';
$string['unaddableblocks_desc'] = 'Los bloques especificados no son necesarios con este tema y no aparecerán en el menú "Agregar un bloque".';
$string['privacy:metadata:preference:draweropenblock'] = 'Preferencia del usuario para mostrar u ocultar el panel con bloques.';
$string['privacy:metadata:preference:draweropenindex'] = 'Preferencia del usuario para mostrar u ocultar el índice del curso.';
$string['privacy:metadata:preference:draweropennav'] = 'Preferencia del usuario para mostrar u ocultar el menú de navegación.';
$string['privacy:drawerindexclosed'] = 'La preferencia actual para el índice está cerrada.';
$string['privacy:drawerindexopen'] = 'La preferencia actual para el índice está abierta.';
$string['privacy:drawerblockclosed'] = 'La preferencia actual para el panel de bloques está cerrada.';
$string['privacy:drawerblockopen'] = 'La preferencia actual para el panel de bloques está abierta.';

$string['footer:sitemap'] = "Mapa del sitio";
$string['footer:socialmedia'] = "Redes sociales";
$string['footer:apps'] = "Aplicaciones";

$string['breadcrumbs:home'] = "Inicio";
$string['breadcrumbs:catalog'] = "Catálogo";
$string['catalog'] = "Catálogo";

$string['placeholder:search'] = "Buscar...";

$string['message:plural'] = "Mensajes";
$string['message:seeall'] = "Ver todo";
$string['message:noncontacts'] = "Contactos";

$string['forum:format'] = "Formato";
$string['forum:discussion'] = "discusión";
$string['forum:parent'] = "padre";
$string['forum:userfullname'] = "nombre completo del usuario";
$string['forum:created'] = "creado";
$string['forum:modified'] = "modificado";
$string['forum:mailed'] = "enviado";
$string['forum:subject'] = "asunto";
$string['forum:message'] = "mensaje";
$string['forum:messageformat'] = "formato de mensaje";
$string['forum:messagetrust'] = "confianza del mensaje";
$string['forum:attachment'] = "archivo adjunto";
$string['forum:totalscore'] = "puntuación total";
$string['forum:mailnow'] = "enviar ahora";
$string['forum:deleted'] = "eliminado";
$string['forum:privatereplyto'] = "respuesta privada a";
$string['forum:privatereplytofullname'] = "nombre completo del destinatario privado";
$string['forum:wordcount'] = "conteo de palabras";
$string['forum:charcount'] = "conteo de caracteres";
$string['enable_mustaches_footer'] = 'Habilitar pie de página para administrador';
$string['enable_mustaches_footer_desc'] = 'Habilitar o deshabilitar la visualización para administrador.';
$string['footer_hiden_user'] = 'Habilitar pie de página para usuario';
$string['footer_hiden_user_desc'] = 'Habilitar o deshabilitar el pie de página para usuario.';
$string['enable_login_footer'] = 'Habilitar pie de página en inicio de sesión';
$string['enable_login_footer_desc'] = 'Habilitar o deshabilitar la visualización en la página de inicio de sesión.';

$string['btn_primary_bg_color'] = "Color primario";
$string['btn_primary_bg_color_desc'] = "Botón de color primario";
$string['btn_primary_text_color'] = "Color de texto primario";
$string['btn_primary_text_color_desc'] = "Texto del botón primario";

$string['required'] = 'Obligatorio';

$string['catalog'] = 'Catálogo';

$string['profile_index_alert'] = "Los campos personalizados marcados como 'Obligatorios' deben mostrarse exclusivamente en la sección 'Campos personalizados'.";
