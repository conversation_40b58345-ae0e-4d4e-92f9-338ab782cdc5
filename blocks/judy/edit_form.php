<?php

/**
 * Per-block settings
 *
 * @package    block_judy
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once($CFG->dirroot . '/blocks/judy/lib.php');
require_once($CFG->dirroot . '/repository/lib.php');
require_once($CFG->dirroot . '/local/judy/lib.php');

/**
 * Class block_judy_edit_form
 */
class block_judy_edit_form extends block_edit_form
{
    protected $error_message = "";

    /**
     * Override this to create any form fields specific to this type of block.
     * @param \MoodleQuickForm $mform the form being built.
     */
    protected function specific_definition($mform)
    {
        $mform->addElement('header', 'config_header', get_string('blocksettings', 'block'));
        
        if($assistants = $this->get_assistants()){
            $mform->addElement(
                'autocomplete',
                'config_assistant',
                get_string('config_assistant', 'block_judy'),
                $assistants,
                null
            );
            $mform->setType('config_assistant', PARAM_TEXT);
            $mform->addHelpButton('config_assistant', 'config_assistant', 'block_judy');

        }elseif($this->error_message){
            $message_html = '<div class="alert alert-danger" role="alert">'.$this->error_message.'</div>';
            $mform->addElement('static', 'error_message', '', $message_html);
        }
    }

    protected function get_assistants() : array {
        try {
            $assistants = [];
            foreach (local_judy_list_assistants() as $assistant) {
                $assistants[$assistant->uuid] = $assistant->name;
            }

            return $assistants;
        } catch (\Throwable $th) {
            $this->error_message = $th->getMessage();
            return [];
        }
    }
}
