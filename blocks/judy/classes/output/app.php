<?php

namespace block_judy\output;

use \local_judy\config;

defined('MOODLE_INTERNAL') || die();

/**
 * Class app
 * @package block_judy\output
 */
class app implements \renderable, \templatable
{   
    /**
     * @var string
     */
    protected $assistantId;

    /**
     * app constructor.
     * @param $assistantId
     */
    public function __construct($assistantId, $companyId)
    {
        $this->assistantId = $assistantId;
    }

    /**
     * Get user picture
     * @return string
     */
    protected function get_picture() 
    {
        global $USER, $PAGE;

        $pic = new \user_picture($USER);
        $pic->size = 1;

        return $pic->get_url($PAGE);
    }

    /**
     * Check if SSO is enabled
     * @return string
     */
    protected function isSsoEnabled()
    {
        return config::is_sso_enabled() ? 'true' : 'false';
    }

    /**
     * Export for template {Mustache}
     *
     * @param \renderer_base $output
     * @return array
     */
    public function export_for_template(\renderer_base $output)
    {
        global $PAGE;

        return [
            'baseUrl'       => config::get_api_url(),
            'assistantId'   => $this->assistantId,
            'companyId'     => config::get_api_user_company_id(),
            'hasConfig'     => !empty($this->assistantId),
            'isEditing'     => $PAGE->user_is_editing(),
            'userAvatarUrl' => $this->get_picture(),
            'ssoEnabled'    => $this->isSsoEnabled(),
            'ssoLoginUrl'   => config::get_embedded_sso_url(),
        ];
    }
}
