{{^isEditing}}
  <script>
    (function(J,u,d,y) {
      J.jdyOptions = J.jdyOptions || {};
      J.jdyOptions.assistantId = '{{ assistantId }}';
      J.jdyOptions.baseUrl = '{{ baseUrl }}';
      J.jdyOptions.userAvatarUrl = '{{ userAvatarUrl }}';
      J.jdyOptions.ssoConfig = {
        enabled: {{ ssoEnabled }},
        loginUrl: '{{ ssoLoginUrl }}',
        btnLabel: 'Entrar usando sua conta LearningFlix',
      };
    {{#companyId}}
      J.jdyOptions.companyId = '{{ companyId }}';
    {{/companyId}}
      var s = u.createElement(d), a = u.getElementsByTagName(d)[0];
      s.async = 1; s.src = y; a.parentNode.appendChild(s)
    })(window, document, 'script', '{{ baseUrl }}/judy/main.js?_=' + Date.now());
  </script>
{{/isEditing}}

<div class="mt-5 pt-4">
  <div class="card">
    <div class="card-body">
      <h5 class="card-title">Bloco Judy</h5>
      <p class="card-text">
        Este bloco é responsável por exibir a Judy.<br />
        {{#hasConfig}}
          Assistente: {{ assistantId }}
        {{/hasConfig}}
        {{^hasConfig}}
          Selecione a assistente na configuração do bloco.
        {{/hasConfig}}
      </p>
    </div>
  </div>
</div>