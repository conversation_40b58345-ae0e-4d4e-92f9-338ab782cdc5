<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Strings for component 'block_mnet_hosts', language 'en', branch 'MOODLE_20_STABLE'
 *
 * @package   block_mnet_hosts
 * @copyright 1999 onwards <PERSON>  {@link http://moodle.com}
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['error_authmnetneeded'] = 'El complemento de autenticación MNet debe estar habilitado para ver la lista de servidores de red MNet';
$string['error_localusersonly'] = 'Los usuarios remotos no pueden saltar a otros servidores de red MNet desde este host';
$string['error_roamcapabilityneeded'] = 'Los usuarios necesitan la capacidad \'Navegar a una aplicación remota vía MNet\' para ver la lista de servidores de red MNet';
$string['mnet_hosts:addinstance'] = 'Añadir un nuevo bloque de servidores de red';
$string['mnet_hosts:myaddinstance'] = 'Añadir un nuevo bloque de servidores de red al Panel de control';
$string['pluginname'] = 'Servidores de red';
$string['server'] = 'Servidor';
$string['privacy:metadata'] = 'El bloque de Servidores de red solo permite la interacción con servidores de red y no almacena ni exporta datos por sí mismo.';
