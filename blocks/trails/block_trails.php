<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Block definition class for the block_trails plugin.
 *
 * @package   block_trails
 * @copyright 2024, REVVO <www.somosrevvo.com.br>
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

if (!class_exists('block_base')) {
    // Faz o require do arquivo que contém a classe
    require_once($CFG->dirroot . '/blocks/moodleblock.class.php');
}

class block_trails extends block_base
{
    function init()
    {
        $this->title = get_string('pluginname', 'block_trails');;
    }

    function get_content($mobile = false, $idMasterElement = 'block_trails_root', $pathLoad = "", $isABlock = true)
    {
        global $OUTPUT, $PAGE;

        if ($this->content !== NULL) {
            return $this->content;
        }

        if (!isloggedin() or isguestuser()) {
            // Only real users can access block_trails block.
            return;
        }

        $PAGE->requires->css(new moodle_url('/local/trails/styles.css'));
        $PAGE->requires->css(new moodle_url('/local/trails/amd/build/client/style.css'));

        $PAGE->requires->js_call_amd('local_trails/client/app-lazy', 'init', ['#'.$idMasterElement, true]);

        $params['title'] = $this->title;
        $params['trails_url'] = $pathLoad;

        // $text = $OUTPUT->render_from_template('block_trails/block', $params);
        $text = html_writer::div('', '', ['id' => $idMasterElement]);
        if($mobile) $text = '<div id="'.$idMasterElement.'" class="block_trails_root"></div>';

        $this->content = new stdClass();
        $this->content->text = $text;
        $this->content->footer = '';

        if($mobile) {
            $this->content->otherdata = [
                "component" => '#'.$idMasterElement,
                "isBlock" => $isABlock,
                "params" => json_encode($params)
            ];
            $this->content->javascripts = $this->get_mobile_javascripts();
        }

        return $this->content;
    }

    /**
     * Defines in which pages this block can be added.
     *
     * @return array of the pages where the block can be added.
     */
    public function applicable_formats()
    {
        return [
            'site-index' => true,
            'my' => true,
        ];
    }

    /**
     * allow the block to have a configuration page
     *
     * @return boolean
     */
    public function has_config()
    {
        return false;
    }

    public function hide_header()
    {
        return true;
    }

    /**
     * allow more than one instance of the block on a page
     *
     * @return boolean
     */
    public function instance_allow_multiple()
    {
        //allow more than one instance on a page
        return false;
    }

    /**
     * allow instances to have their own configuration
     *
     * @return boolean
     */
    function instance_allow_config()
    {
        //allow instances to have their own configuration
        return false;
    }

    public function get_mobile_javascripts() {
        global $CFG;
        $jsNameFiles = [
            ['url' => $CFG->dirroot . '/local/trails/amd/src/client/mobile/loader.js', 'title' => 'loader'],
            ['url' => $CFG->dirroot . '/admin/tool/lfxp/amd/src/mobile/user.js', 'title' => 'user_tool_lfxp'],
            ['url' => $CFG->dirroot . '/admin/tool/lfxp/amd/src/mobile/ajax.js', 'title' => 'ajax_tool_lfxp'],
            ['url' => $CFG->dirroot . '/local/trails/amd/src/client/mobile/appLazy.js', 'title' => 'appLazy', 'call' => 'init']
        ];
        $jsContent = "";
        $runFinalCalls = "";
        foreach ($jsNameFiles as $jsFile) {
            if (file_exists($jsFile['url'])) {
                $jsContent .= file_get_contents($jsFile['url']) . "\n";
                if(isset($jsFile['call'])){
                    $jsFileName = explode('/', $jsFile['url']);
                    $jsFileName = $jsFileName[count($jsFileName)-1];
                    $jsFileName = explode('.', $jsFileName);
                    $jsFileName = $jsFileName[0].'NS';
                    $callInitFunction = $jsFile['call'];
                    $runFinalCalls .= <<<JS
                    console.log({$jsFileName});
                    {$jsFileName}.{$callInitFunction}();
                    JS;
                }
            } else {
                $titleFileErro = $jsFile['title'];
                $titleBlockErro = $this->name();
                $jsContent .= <<<JS
                (async function (t) {
                    const alert = await t.AlertController.create({
                    header: '{$titleFileErro}',
                    subHeader: 'For: {$titleBlockErro}',
                    message: 'Módulo do bloco não foi carregado!',
                    buttons: ['OK'],
                    });
                    await alert.present();
                })(this);
                JS;
            }
        }
        $jsContent .= $runFinalCalls;
        $jsContent = str_replace(["\r", "\n", "\t"], '', $jsContent);
        return $jsContent;
    }
}
