<?php

namespace block_trails\output;
require_once(__DIR__ . '/../../block_trails.php');
require_once(__DIR__ . '/../../../../config.php');

class mobile {
    public static function mobile_view_block_trails($args) {
        global $CFG;

        $bcbase = new \block_trails();
        $bcbase->init();
        $content = $bcbase->get_content(true);
        
        $css = file_get_contents($CFG->wwwroot . '/local/trails/style_mobile.css');
        $css .= file_get_contents($CFG->wwwroot . '/local/trails/amd/src/client/mobile/style.css');

        return [
            'templates' => [
                [
                    'id' => 'main-block_trails',
                    'html' => '<style>' . $css . '</style>'.$content->text,
                ],
            ],
            'javascript' => $content->javascripts,
            'otherdata' => $content->otherdata,
        ];
    }

    public static function mobile_view_navigate_trails($args) {
        global $CFG;

        $bcbase = new \block_trails();
        $bcbase->init();
        $path = $args['trails_url'] ?: '';
        $superId = $args['superId'] ?: '';
        if($superId != '') $superId = '_'.$superId;
        $content = $bcbase->get_content(true, 'block_trails_navigated_root'.$superId, $path, false);
        
        $css = file_get_contents($CFG->wwwroot . '/local/trails/style_mobile.css');
        $css .= file_get_contents($CFG->wwwroot . '/local/trails/amd/src/client/mobile/style.css');

        return [
            'templates' => [
                [
                    'id' => 'main-block_trails',
                    'html' => '<style>' . $css . '</style>'.$content->text,
                ],
            ],
            'javascript' => $content->javascripts,
            'otherdata' => $content->otherdata,
        ];
    }
}