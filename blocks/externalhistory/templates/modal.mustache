{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template block_externalhistory/modal

    TODO describe template modal

    Example context (json):
    {
    }
}}
<!-- Modal -->
<div class="modal fade" id="modalExternalhistory" tabindex="-1" role="dialog" aria-labelledby="modalExternalhistory" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLabel">{{#str}}headermodal, block_externalhistory{{/str}}</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="{{#str}}close, block_externalhistory{{/str}}">
            <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <h6 class="solutionname">{{#str}}solutionname, block_externalhistory{{/str}}</h6>
            <hr>
            <div>{{#str}}format, block_externalhistory{{/str}}: <span class="format"></span></div>
            <div>{{#str}}hourscompleted, block_externalhistory{{/str}}: <span class="hourscompleted"></span></div>
            <div>{{#str}}description, block_externalhistory{{/str}}</div>
            <div style="max-height: 160px; overflow-y: auto;" class="border p-3 mb-3 description"></div>
            <div>{{#str}}realizationdate, block_externalhistory{{/str}}: <span class="startdate"></span> - <span class="enddate"></span></div>
            <div>{{#str}}institution, block_externalhistory{{/str}}: <span class="institution"></span></div>
            <a class="linkpdf" target="_blank" href="#"><i class="fa-solid fa-file-pdf"></i></a>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">{{#str}}close, block_externalhistory{{/str}}</button>
            <a href="#" class="btn btn-primary btn-edit-solution" style="color:#FFF">{{#str}}edit, block_externalhistory{{/str}}</a>                    
        </div>
        </div>
    </div>
</div>