.imgcard{
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: -1;
    border-radius: 6px;
    border: solid 1px #FFF9;
}

.cards-ext{
    border: 1px solid rgba(255, 255, 255, .125);
    height: 160px;
    border-radius: 6px;
    padding: 20px;
    position: relative;
    margin: 8px 0;
    width: 100%;
    background-position: center;
    background-repeat: repeat;

}


.cards-ext .course-title{
    text-transform: uppercase;
    font-size: 18px;
}

.cards-ext .institution-name{
    text-transform: uppercase;
}
.cards-ext .institution-name,
.cards-ext .fa-circle-info::before,
.cards-ext .course-title,
.cards-ext .course-institution
{
    filter: drop-shadow(2px 2px 2px black);
}

.btn-infoext{
    position: absolute !important;
    bottom: 10px;
    right: 15px;
    z-index: 1;
    font-size: 24px;
}

.btn-infoext .fa-circle-info{
    color: #FFF !important;
}


#page-blocks-externalhistory-index #nocourses svg{
    width: 80px;
    height: auto;
    margin-bottom: 20px;

}

#page-blocks-externalhistory-index .headerindex{
    margin-bottom: 40px;
    display: flex;
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
}

#modalExternalhistory .modal-body a .fa-file-pdf{
    font-size: 32px;
    right: 15px;
    margin-top: 10px;
}

#block_externalhistory .card-courses .card-container .cards-ext.add a{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    cursor: pointer;    
    height: 100%;
    width: 100%;
    text-decoration: none;
}

#block_externalhistory .card-courses .card-container .cards-ext.add i{
    font-size: 48px;
}

#block_externalhistory .card-courses .card-container .cards-ext.add .txt-add{
    font-size: 14px;
    text-align: center;
    margin-top: 10px;
}

#block_externalhistory .blocktitle {
    font-size: 1.171875rem;
}

#block_externalhistory .card-showall{
    color: var(--theme-link-color-primary) !important;
    filter: brightness(1.5);
}

#block_externalhistory .card-showall h6 {
    color: #FFFA;
    display: block;
    line-height: 20px;
    width: auto;
    overflow: hidden;
    text-wrap: nowrap;
    text-indent: 0;
    padding-right: 3px;
    opacity: 1;
    transition: cubic-bezier(.19,1,.22,1) .6s;
}

#block_externalhistory .card-showall i.fa-chevron-right::before {
    color: white !important;
    line-height: 20px;
}

#block_externalhistory .cards-ext {
    position: relative;
    min-height: 100px;
    padding-right: 120px; /* espaço reservado para o ícone */
    /*background: linear-gradient(90deg, #454545 0%, #555555 100%);*/
    background-color: var(--bg-color);
}

.cards-ext::after {
    content: "";
    position: absolute;
    top: 10px;
    right: 5px;
    /*transform: translateY(-50%);*/
    width: 100px;
    height: 100px;
    background-image: var(--bg-url);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    pointer-events: none;
}