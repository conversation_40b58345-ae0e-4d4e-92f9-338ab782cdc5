<?php

namespace block_externalhistory\external;

use \external_api;
use \external_value;
use \external_single_structure;
use \external_function_parameters;
use \context_user;

use \block_externalhistory\forms\externalhistory_form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/externallib.php');
global $CFG;

class externalhistory_api extends external_api{

    public static function externalhistory_manager_mobile_allowed_from_ajax()
    {
        return true;
    }

    public static function externalhistory_manager_mobile_parameters()
    {
        return new external_function_parameters([
            'actionForm' => new external_value(PARAM_INT, 'action to do'),
            'data' => new external_value(PARAM_RAW, 'data to save/change'),

        ]);
    }
    public static function externalhistory_manager_mobile_returns()
    {
        return new external_single_structure([
            'status' => new external_value(PARAM_INT, 'status to return'),
            'data' => new external_value(PARAM_RAW, 'data to return'),
        ]);
    }


    public static function externalhistory_manager_mobile($actionForm, $data){
        global $DB;
        $raw_params = ['actionForm' => $actionForm, 'data' => $data];
        // Param validation
        $params = self::validate_parameters(self::externalhistory_manager_mobile_parameters(), $raw_params);

        require_login();

        $objectReturn = [
            'status' => 0,
            'data' => ''
        ];

        try {
            $paramsActionValues = json_decode($raw_params['data'], true);

            switch ($raw_params['actionForm']) {
                case 1:
                    $paramsActionValues = self::sanitize_form_data($paramsActionValues);
                    $certfile = $paramsActionValues['certfile'];

                    $clean_data = [];
                    foreach ($paramsActionValues as $key => $value) {
                        if (is_object($value)) {
                            $value = (array) $value;
                        }
                        if (is_array($value)) {
                            $clean_data[$key] = clean_param_array($value, PARAM_RAW);
                        } else {
                            $clean_data[$key] = clean_param($value, PARAM_RAW);
                        }
                        $_POST[$key] = $clean_data[$key];
                    }

                    $_POST['sesskey'] = sesskey();
                    $_POST['submitbutton'] = get_string('action:add', 'block_externalhistory');

                    $form  = new externalhistory_form(['userid' => $paramsActionValues['userid']], true);
                    $form->set_data($clean_data);

                    if ($data_from_form = $form->get_data()) {
                        $table = 'block_externalhistory';

                        $addFile = false;
                        $itemIdToAdd = 0;

                        if(empty($data_from_form->id)){
                            unset(
                                $data_from_form->id,
                                $data_from_form->submitbutton
                            );
                            $result = $DB->insert_record($table, $data_from_form);
                            if($result) {
                                $itemIdToAdd = $result;
                                $objectReturn['status'] = 1;
                                $objectReturn['data'] = ['message' => 'OK'];
                                $addFile = true;
                            } else {
                                $objectReturn['data'] = ['message' => 'ERRO'];
                            }
                        }else{
                            if($DB->update_record($table, $data_from_form)){
                                $itemIdToAdd = $data_from_form->id;
                                $objectReturn['status'] = 1;
                                $objectReturn['data'] = ['message' => 'OK'];
                                $addFile = true;
                            } else {
                                $objectReturn['data'] = ['message' => 'ERRO'];
                            }
                        }

                        if(!empty($certfile) && $certfile > 0 && $addFile && $itemIdToAdd > 0){

                            $context = context_user::instance($paramsActionValues['userid']);

                            $component = 'block_externalhistory';
                            $filearea = 'certfile';
                            $itemid = $itemIdToAdd;

                            $draft_file = null;
                            $fs = get_file_storage();
                            $files = $fs->get_area_files($context->id, 'user', 'draft', $params['draftitemid'], 'itemid, filepath, filename', false);
                            foreach ($files as $file) {
                                if ($file->is_valid_image()) {
                                    $draft_file = $file;
                                    break;
                                }
                            }
                            $draftitemid = $draft_file->get_itemid();

                            $fileoptions = array(
                                'subdirs' => 0,
                                'maxbytes' => 0,
                                'maxfiles' => 1,
                                'context' => $context
                            );

                            file_save_draft_area_files($draftitemid, $context->id, $component, $filearea, $itemid, $fileoptions);

                        }
                        
                    } else {
                        // Validation failed
                        $errors = [];
                        if (!empty($form->_errors)) {
                            foreach ($form->_errors as $field => $message) {
                                $errors[] = ['field' => $field, 'message' => $message];
                            }
                        }
                        
                        $fls = array();
                        $vlderrors = $form->validation($clean_data, $fls);
                        $errors = array_merge($errors, $vlderrors);

                        $objectReturn['data'] = [
                            'status' => 0,
                            'response' => 'Validation failed.',
                            'errors' => $errors,
                            'submitStatus' => $form->is_submitted(),
                            'cancelStatus' => $form->is_cancelled(),
                            'validatedStatus' => $form->is_validated(),
                        ];
                    }
                    break;
            
                default:
                $objectReturn['data'] = ['teste' => 'Retorno Padrão'];
                    break;
            }
        } catch (\Throwable $th) {
            $objectReturn['status'] = 0;
            $objectReturn['data'] = [
                'message' => $th->getMessage(),
                'line' => $th->getLine()
            ];
        }

        $objectReturn['data'] = json_encode($objectReturn['data']);
        return $objectReturn;
    }

    public static function sanitize_form_data($data) {
        foreach ($data as $key => $value) {
            if (is_null($value)) {
                $data[$key] = '';
            } elseif (is_array($value)) {
                $data[$key] = self::sanitize_form_data($value);
            } elseif (is_string($value)) {
                // Remove barras invertidas adicionadas por JSON, etc.
                $value = stripslashes($value);
                // Remove espaços e normaliza
                $value = trim($value);
                // Opcional: converte \u00f3 para ó
                $value = json_decode('"' . addcslashes($value, '"\\') . '"');
                $data[$key] = $value;
            }
        }
        return $data;
    }
}