<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * TODO describe file index
 *
 * @package    block_externalhistory
 * @copyright  2024 YOUR NAME <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require('../../config.php');

require_login();

$id = optional_param('id', 0, PARAM_INT);


$data  = new \block_externalhistory\forms\externalhistory_form([
    'userid' => $USER->id
]);


if ($data->is_cancelled()) {
    $urlredirect = new  moodle_url('/local/user/extracurricularsolutions.php', []);
    redirect($urlredirect);
}

if ($data->get_data()) {
    $table = 'block_externalhistory';

    $dataobject = $data->get_data();

    if(empty($dataobject->id)){
        unset(
            $dataobject->id,
            $dataobject->submitbutton
        );
        $result = $DB->insert_record($table, $dataobject);

        // Defina o contexto onde o arquivo será armazenado
        $context = context_user::instance($USER->id); // Substitua pelo contexto apropriado

        // Defina os parâmetros da área de arquivos do plugin
        $component = 'block_externalhistory'; // Substitua pelo componente do seu plugin
        $filearea = 'certfile'; // Substitua pela área de arquivos específica
        $itemid = $result; // Pode ser o ID de um registro na tabela do seu plugin, ou 0 para algo genérico

        // Obtenha o draft itemid do formulário
        $draftitemid = file_get_submitted_draft_itemid('certfile');

        // Prepare a área de arquivos
        $fileoptions = array(
            'subdirs' => 0,
            'maxbytes' => 0,
            'maxfiles' => 1, // Limite de arquivos
            'context' => $context
        );

        // Mova o arquivo do rascunho para a área de arquivos do plugin
        file_save_draft_area_files($draftitemid, $context->id, $component, $filearea, $itemid, $fileoptions);

        $message = 'Adicionado com sucesso!';
        $urlredirect = new moodle_url('/local/user/extracurricularsolutions.php', []);            
        redirect($urlredirect, $message, 2, \core\output\notification::NOTIFY_SUCCESS);        
    }else{
        if($DB->update_record($table, $dataobject)){
            // Defina o contexto onde o arquivo será armazenado
            $context = context_user::instance($USER->id); // Substitua pelo contexto apropriado

            // Defina os parâmetros da área de arquivos do plugin
            $component = 'block_externalhistory'; // Substitua pelo componente do seu plugin
            $filearea = 'certfile'; // Substitua pela área de arquivos específica
            $itemid = $dataobject->id; // Pode ser o ID de um registro na tabela do seu plugin, ou 0 para algo genérico

            // Obtenha o draft itemid do formulário
            $draftitemid = file_get_submitted_draft_itemid('certfile');

            // Prepare a área de arquivos
            $fileoptions = array(
                'subdirs' => 0,
                'maxbytes' => 0,
                'maxfiles' => 1, // Limite de arquivos
                'context' => $context
            );

            // Mova o arquivo do rascunho para a área de arquivos do plugin
            file_save_draft_area_files($draftitemid, $context->id, $component, $filearea, $itemid, $fileoptions);

            $message = 'Atualizado com sucesso!';
            $urlredirect = new moodle_url('/local/user/extracurricularsolutions.php', []);            
            redirect($urlredirect, $message, 2, \core\output\notification::NOTIFY_SUCCESS);                

        }
    }
}



$url = new moodle_url('/blocks/externalhistory/edit.php', []);
$PAGE->set_url($url);
$PAGE->set_context(context_system::instance());



$urlbase = new moodle_url('/local/user/index.php', ['id' => $USER->id]);
$title = get_string('profile');
$PAGE->navbar->add($title, $urlbase);

$title = get_string('title', 'block_externalhistory');
$urlbase = new moodle_url('/local/user/extracurricularsolutions.php', []);
$PAGE->navbar->add($title, $urlbase);

if($id == 0){
    $stitle = get_string('action:add', 'block_externalhistory');
}else{
    $stitle = get_string('action:edit', 'block_externalhistory');    
}


$PAGE->navbar->add($stitle);

$PAGE->set_heading($SITE->fullname);
echo $OUTPUT->header();

echo '<div class="page-header mb-5">' . $OUTPUT->heading($stitle) . '</div>';


$list = $DB->get_record('block_externalhistory',['id' => $id,'userid' => $USER->id]);

if($id > 0){
    if(empty($list)){
        echo "Não há certificado para o Usuário com esse ID";
    }else{
        $data->set_data($list);
        $data->display() ;        
    }
}else{
    $data->display() ;
}


echo $OUTPUT->footer();







