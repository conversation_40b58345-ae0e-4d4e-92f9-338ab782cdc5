<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * TODO describe file locallib
 *
 * @package    block_externalhistory
 * @copyright  2024 YOUR NAME <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
function block_externalhistory_urlpdf($itemid){
    global $DB,$USER;

    $params = [
        'itemid'    => $itemid,
        'filearea'  => 'certfile',
        'contextid' => context_user::instance($USER->id)->id,
        'component' => 'block_externalhistory',
        'userid'    => $USER->id
    ];
    
    $sql = "SELECT id FROM {files} WHERE
    itemid          = :itemid 
    AND filearea    = :filearea
    AND contextid   = :contextid
    AND component   = :component
    AND userid      = :userid
    AND (mimetype IS NOT NULL OR mimetype != '')
    ORDER BY id DESC
    ";
    $record = $DB->get_record_sql($sql,$params);

    if(!$record){
        return '#';
    }    
    
    $fs = get_file_storage();
    $file = $fs->get_file_by_id($record->id);

    if(!$file){
        return '#';
    }
  
    $url = moodle_url::make_pluginfile_url(
        $file->get_contextid(),
        $file->get_component(),
        $file->get_filearea(),
        $file->get_itemid(),
        $file->get_filepath(),
        $file->get_filename(),
        false // Do not force download of the file.
    );
    
    return $url->out();    
}

function block_externalhistory_list(int $qde = 0){
    global $USER,$DB,$OUTPUT;
    if($qde > 0){
        $list = $DB->get_records('block_externalhistory',['userid' => $USER->id],'id desc','*',0,$qde);        
    }else{
        $list = $DB->get_records('block_externalhistory',['userid' => $USER->id],'id desc');        
    }
    $cards = [];
    foreach($list as $key => $itemhist){
        $min = ($itemhist->hourscompleted / 60);
        if($min < 1){
            $itemhist->hourscompleted = $itemhist->hourscompleted . ' Segundos';
        }elseif($min < 60){
            $itemhist->hourscompleted = $min . ' Minutos';
        }else{
        $hs = floor($min/60);
        $mn = $min % 60;
            $itemhist->hourscompleted = $hs." Hs";
            if($mn > 0){
                $itemhist->hourscompleted .= ' ' . $mn." Min";
            }
        }
        $bgcor[0] = '#0d6efd';
        $bgcor[1] = '#0d6efd';
        $bgcor[2] = '#0d6efd';
        $bgcor[3] = '#0d6efd';
        //$bgcor[1] = '#4dd4ac';
        //$bgcor[2] = '#3dd5f3';
        //$bgcor[3] = '#8540f5';
        $indexcor = $key % 4;
        $itemhist->bgcor = $bgcor[$indexcor];
        $itemhist->startdate = date("d/m/Y",$itemhist->startdate);
        $itemhist->enddate = date("d/m/Y",$itemhist->enddate);
        $itemhist->urlpdf = block_externalhistory_urlpdf($itemhist->id);
        $itemhist->img = $OUTPUT->image_url('icon_solution', 'block_externalhistory')->out();
        $itemhist->bg = $OUTPUT->get_generated_image_for_id($key);
        $itemhist->json = json_encode($itemhist);
        $cards[] = $itemhist;
    }
    return $cards;
}

function block_externalhistory_solutions(){
    global $DB;
    $sql = "SELECT DISTINCT fc.modality FROM mdl_local_custom_fields_course fc WHERE fc.modality != ''";
    $data = array_keys($DB->get_records_sql($sql));

    //Soluções Pré-Definidas
    $tiposSolucoes = [];

    $data = array_merge($data,$tiposSolucoes);


    $newdata = [];
    foreach($data as $item){
        $it = explode(',',$item);
        foreach($it as $i){
            $newdata[$i] = $i;
        }
    }
    ksort($newdata);

    return array_unique($newdata);
    
}